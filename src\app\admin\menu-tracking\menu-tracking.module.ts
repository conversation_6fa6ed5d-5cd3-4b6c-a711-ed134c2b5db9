import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MenuTrackingRoutingModule } from './menu-tracking-routing.module';
import { FormsModule } from '@angular/forms';
import { NbIconModule, NbButtonModule, NbSelectModule, NbCardModule, NbListModule, NbDialogModule, NbToastrModule, NbSpinnerModule, NbInputModule } from '@nebular/theme';
import { MenuTrackingComponent } from './menu-tracking.component';


@NgModule({
  declarations: [
    MenuTrackingComponent
  ],
  imports: [
    CommonModule,
    NbIconModule,
    NbButtonModule,
    NbButtonModule,
    FormsModule,
    NbSelectModule,
    NbCardModule,
    NbListModule,
    NbDialogModule,
    NbToastrModule,
    NbSpinnerModule,
    NbInputModule,
    MenuTrackingRoutingModule
  ]
})
export class MenuTrackingModule { }
