.menu-waiting-thread-container {
  .removal-container {
    .col-status {
      width: 85px;

      .badge {
        margin-right: 20px;
        width: 120px !important;
      }
    }

    .color-card {
      background-color: #333333;

      .color-text-title {
        color: #57b8ff;
      }

      .color-text-list-item {
        color: rgba(255, 255, 255, 0.87);
      }
    }

    .filter-area {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 0 0 15px 0 !important;
      padding: 0px !important;
      background: none !important;
      border-radius: 8px;
    }

    .left-area {
      display: flex;
      align-items: center;
      gap: 10px;
      flex-wrap: nowrap; /* Empêche le retour à la ligne */
      width: auto; /* Ajustement de la largeur */
    }

    .filter-label {
      font-size: 16px;
      font-weight: 600;
      color: #2d3e50;
      white-space: nowrap; /* Empêche le retour à la ligne du texte */
    }



    .filter-input {
      width: 300px; /* Largeur fixe ou ajustable selon vos besoins */
      height: 40px;
    }

    .right-area {
      display: flex;
      align-items: center;
      gap: 10px;
      white-space: nowrap;
    }

    .search-btn {
      height: 30px;
      padding: 0 15px;
      display: flex;
      align-items: center;
      gap: 8px;
      font-weight: 500;
      white-space: nowrap;
    }
    .reset-btn {
      background: #ffffff;
      color: #0B305C;
      nb-icon {
        color: #0B305C;
      }

    }

  }
}


.element {
  display: flex;
  flex-direction: column;
  flex: 1 1 auto;
  overflow: auto;

  .col.col-paginator {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    width: 100% !important;
  }

  .paginator {
    font-weight: bold;
    color: #353535;
    height: 35px !important;
    font-size: 14px !important;
    border-bottom: 0 !important;
  }

  .list-elt-header {
    @include vh-between;
    padding-top: 7px;
    padding-bottom: 7px;
    font-size: 13px;

    .colored {
      color: red !important;
      font-size: 25px;
      display: flex;
      width: 100%;
      justify-content: center;
      margin: 35px;
    }

    .col {
      height: 100%;
      display: flex;
      align-items: center;
    }

    .col-num {
      width: 4%;
    }

    .col-name {
      width: 13%;
    }

    .col-immatriculation {
      width: 12%;
    }

    .col-ae-ref {
      width: 7%;
    }

    .col-truck-type {
      width: 10%;
    }

    .col-item {
      width: 7%;
    }

    .col-qty {
      width: 8%;
    }

    .col-time {
      width: 12%;
    }

    .col-entry-weight {
      width: 12%;
    }

    .center {
      justify-content: center;
    }

    .col-status {
      width: 15%;
      display: flex;
      align-items: center;
      justify-content: center;

      .badge {
        font-size: 12px;
        position: relative;
        width: 103px;
      }

      span {
        background: #027a37;
        border-radius: 4px;
        color: $color-secondary;
        padding: 3px 8px;
      }
    }

    .col-details {
      min-width: calc(100% - 970px);
      overflow: hidden;
      justify-content: center;

      .product-choice {
        background-image: url("./../../images/icons/ciment.png");
        background: no-repeat center contain;
        width: 19px;
        height: 31px;
      }
    }

    .col-action {
      width: 18%;

      .action-icons {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        width: 70%;
      }
    }

    button {
      text-transform: none !important;
      background-color: #edf1f7;
      padding: 5px;
      border-radius: 5px;
    }
  }
}

.edit-container {
  .row {
    display: flex;
    justify-content: space-between;

    .input {
      width: 45%;
    }
  }
}

.not-found {
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: center;
  width: 100%;
}

.no-radius {
  border: none !important;
  border-radius: 0px !important;
}


.update-aes-container {
  .contain {
    display: flex;
    flex-direction: row !important;
    justify-content: space-between !important;
    width: 100%;

    .title {
      padding: 3px;
    }
  }

  .update-form-contain {
    display: flex;
    flex-direction: column;
    gap: 2px;

    .form-group {
      display: flex;
      overflow: hidden;
      flex-direction: row;
      justify-content: space-between;
    }
  }
}

.newManualAe-container {
  height: 53em;
  width: 48em;

  nb-card-header {
    text-align: center;
    font-size: 18px;

  }

  .contain {
    .group {
      display: flex;
      flex-direction: row;
      margin-bottom: 1.1em;
      justify-content: space-between;

      label {
        font-size: 15px;
        padding-top: 12px;
      }

      input {
        border: none;
        border-bottom: 1px solid rgb(28, 28, 28);
        width: 70%;

      }

      nb-select {
        width: 70%;
      }
    }

  }
}


.update-infos-container {
  .title {
    padding: 7px !important;
  }
}

.reset-container {
  p {
    span {
      color: red;
    }
  }
}

.blink {
  animation: blinkingB 1s infinite;
  color: #ffffff !important;
}

@keyframes blinkingB {
  0% {
    background-color: transparent;
  }

  100% {
    // background-color: hsl(356, 79%, 60%);
    background-color: hsl(146, 81%, 40%);
    // background-color: hsl(205, 100%, 38%);
  }
}

// Styles pour l'alignement horizontal des tabsets et pagination
.menu-waiting-thread-container {
  .tabset-pagination-container {
    display: flex;
    justify-content: space-between;
    align-items: stretch; /* Permet aux éléments enfants de prendre la même hauteur */
    min-height: 48px; /* Hauteur minimale pour uniformiser */
    padding: 0 20px;
    border-bottom: 1px solid #edf1f7;
    background-color: #f8f9fa;
  }

  .tabset-pagination-container nb-tabset {
    display: flex;
    align-items: center;
    min-width: 400px; /* Largeur minimale pour éviter le chevauchement */
    max-width: 60%; /* Limite pour ne pas affecter la pagination */
    flex-shrink: 0; /* Empêche la compression des tabsets */
  }

  .tabset-pagination-container nb-tabset ::ng-deep .tabset {
    height: 48px; /* Hauteur fixe pour les tabsets */
    display: flex;
    align-items: center;
    margin: 0 !important;
  }

  .tabset-pagination-container nb-tabset ::ng-deep .tab {
    height: 48px; /* Même hauteur pour les onglets */
    display: flex;
    align-items: center;
    min-width: 180px; /* Largeur minimale par onglet */
    flex: 1; /* Répartit l'espace équitablement entre les onglets */
  }

  .tabset-pagination-container nb-tabset ::ng-deep .tab-link {
    height: 48px; /* Même hauteur pour les liens des onglets */
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 20px;
    white-space: nowrap; /* Empêche le retour à la ligne */
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
  }

  .pagination-wrapper {
    display: flex;
    align-items: center;
    height: 48px; /* Même hauteur que les tabsets */
    padding: 0 8px;
    flex-shrink: 0; /* Empêche la compression de la pagination */
    min-width: 200px; /* Largeur minimale pour la pagination */
  }

  .pagination-wrapper ::ng-deep .pagination-container {
    height: 48px;
    display: flex;
    align-items: center;
  }

  .tab-content {
    padding: 0;
  }

  .no-data-row {
    height: auto !important;
  }

  @media (max-width: 768px) {
    .tabset-pagination-container {
      flex-direction: column;
      align-items: stretch;
      min-height: auto;
      padding: 10px;
    }

    .tabset-pagination-container nb-tabset {
      min-width: auto;
      max-width: 100%;
      margin-bottom: 10px;
    }

    .tabset-pagination-container nb-tabset ::ng-deep .tab {
      min-width: 120px; /* Largeur réduite sur mobile */
    }

    .pagination-wrapper {
      width: 100%;
      justify-content: center;
      min-width: auto;
      height: 48px;
    }
  }
}
