import { Component, OnInit } from '@angular/core';
import { Chart } from 'chart.js';
import { MenuReportingService } from '../menu-reporting.service';
import * as moment from 'moment-timezone';

@Component({
  selector: 'clw-tile-chart-removals',
  templateUrl: './tile-chart-removals.component.html',
  styles: [
  ]
})
export class TileChartRemovalsComponent implements OnInit {

  isLoading: boolean;
  chartType: any;
  chart: Chart;
  statsData: any = [];


  chartTypes: any[] = [
    { code: 'month', label: 'MOIS' },
    { code: 'year', label: 'Année' },
  ];

  currentDate: any;

  constructor(
    private reportingSrv: MenuReportingService,
  ) { }

  async ngOnInit(): Promise<void> {
    this.chartType = this.chartTypes[0];
    await this.refresh();
  }

  async refresh() {
    this.isLoading = true;
    try {
      this.statsData = await this.reportingSrv.getAEsByRenderType(this.chartType.code);
      this.currentDate = moment(this.reportingSrv.startDate).format((this.chartType.code === 'year') ? 'YYYY' : 'MMMM')
        || moment().format((this.chartType.code === 'year') ? 'YYYY' : 'MMMM');
      this.renderChart();
    } catch (error) {
      return error;
    } finally {
      this.isLoading = false;
    }
  }
  renderChart() {
    const datasets = [];
    let labels = [];
    const label = { pickup: 'Bons en pickup' };

    const colors = { pickup: '#0d7d3d' };
    labels = this.statsData.pickup[this.chartType.code].labels;
    datasets.push(
      {
        label: label.pickup,
        fill: false,
        backgroundColor: colors.pickup,
        borderColor: colors.pickup,
        data: this.statsData.pickup[this.chartType.code].data
      }
    );

    if (this.chart) { this.chart.destroy(); }

    this.chart = new Chart('canvas-global-items', {
      type: 'line',
      data: {
        labels,
        datasets
      },
      options: {}
    });
  }


}
