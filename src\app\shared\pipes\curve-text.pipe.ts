import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'curveText'
})
export class CurveTextPipe implements PipeTransform {

  transform(text: string, radius: number): any {
    text = text?.split(' ')[0];
    const textArray = this.truncateString(text, 14)?.split('') || [];

    const deg = 70 / textArray.length;
    let origin = 0;
    const newArray: any[] = [];
    textArray.forEach((e) => {
      newArray.push({ letter: e, style: `height: ${radius}px; position: absolute; transform: rotate(${origin}deg); transform-origin: 0 100 % ` });

      origin += deg;
    });
    return newArray;
  }

  truncateString(str: string, size: number): string {
    if (!str) { return ''; }
    return (str.length > size) ? `${str.substring(0, size - 6)}` : str;
  }

}
