import { Carrier } from "./carrier";

export interface Driver {
  _id?: string;
  cni?: string;
  tel1?: string;
  tel2?: string;
  driverLicense?: string;
  issueDate?: number;
  licenseExpireDate?: number | string;
  driverCode?: string;
  fname?: string;
  lname?: string;
  fullname?: string;
  code?: number;
  carrierRef?: number;
  trainings?: Training[];
  carrier: Partial<Carrier>
}

export class Training {
  trainingType: string;
  trainerName: string;
  date: {
    start: number | string;
    end: number | string;
    expiredDate: number | string;
  }
}

interface PaginationParams {
  offset?: number;
  limit?: number;
}


export interface DriverParams extends PaginationParams {
  code?: string | number;
  fullname?: string;
}

export interface DriversResponse {
  status: number;
  message: string;
  data: {
    data: Driver[];
    count: number;
  };
}
