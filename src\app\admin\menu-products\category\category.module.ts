import { NbIconModule, NbButtonModule, NbSelectModule, NbCardModule, NbListModule, NbDialogModule, NbToastrModule, NbSpinnerModule, NbInputModule } from '@nebular/theme';
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { CategoryRoutingModule } from './category-routing.module';
import { CategoryComponent } from './category.component';
import { FormsModule } from '@angular/forms';


@NgModule({
  declarations: [
    CategoryComponent
  ],
  imports: [
    CommonModule,
    CategoryRoutingModule,
    NbIconModule,
    NbButtonModule,
    NbButtonModule,
    FormsModule,
    NbSelectModule,
    NbCardModule,
    NbListModule,
    NbDialogModule,
    NbToastrModule,
    NbSpinnerModule,
    NbInputModule
  ]
})
export class CategoryModule { }
