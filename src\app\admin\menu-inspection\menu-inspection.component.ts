import { Location } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { NbDialogRef, NbDialogService, NbToastrService } from '@nebular/theme';
import { KpiInspectionCheck, Status } from 'src/app/shared/models/kpiInspectionCheck';
import { MenuInspectionService } from './menu-inspection.service';
import { CommonService } from 'src/app/shared/services/common.service';

@Component({
  selector: 'clw-menu-inspection',
  templateUrl: './menu-inspection.component.html',
  styles: []
})
export class MenuInspectionComponent implements OnInit {
  isLoading: boolean;
  isConfirmLoading: boolean;
  inspections: KpiInspectionCheck[] = [];
  selectedSlabel: any[];
  carriers: any[];
  inspection: KpiInspectionCheck;
  selectedStatus: Status;
  dialogRef: NbDialogRef<any>;
  editdialogRef: NbDialogRef<any>;
  deletedialogRef: NbDialogRef<any>;
  currStatus = [
    {
      code: 0,
      label: 'Optionnel',
    },
    {
      code: 1,
      label: 'Obligatoire',
    }
  ]

  constructor(
    private inspectSrv: MenuInspectionService,
    private commonService: CommonService,
    private toastSrv: NbToastrService,
    private dialogSvr: NbDialogService,
    private router: Router,
    private location: Location,
  ) { }

  async ngOnInit(): Promise<void> {
    await this.getInspection();
  }

  goBack() {
    if (window.history.length > 1) {
      this.location.back()
    } else {
      this.router.navigate(['/home'])
    }
  }

  async getInspection() {
    try {
      this.isLoading = true;
      this.inspections = await this.inspectSrv.getInspection();
    } catch (error) {
      this.toastSrv.warning('Impossible de récupérer les données', 'Erreur de connexion');
    } finally { 
      this.isLoading = false; 
    }
  }

  openAddModal(dialog: any): any {
    this.inspection = {
      label: '',
      code: '',
      status: {code: 0, label: 'Optionnel'},
    }
    this.dialogRef = this.dialogSvr.open(dialog, { closeOnBackdropClick: false, autoFocus: true })
    this.dialogRef.onClose.subscribe(async (result) => {
      if (!result) { return; }
      this.inspections = await this.inspectSrv.getInspection();
      this.inspection = {
        label: '',
        code: '',
        status: {code: 0, label: 'Optionnel'},
      }
    });
  }

  openEditModal(dailog: any, inspection: KpiInspectionCheck): any {
    this.inspection = inspection;
    this.editdialogRef = this.dialogSvr.open(dailog, {})
    this.editdialogRef.onClose.subscribe(async (result) => {
      if (!result) { return }
      this.inspections = await this.inspectSrv.getInspection();
    });
  }

  openDeleteModal(dailog: any, inspection: KpiInspectionCheck): any {
    this.inspection = inspection;
    this.deletedialogRef = this.dialogSvr.open(dailog, {})
    this.deletedialogRef.onClose.subscribe(async (result) => {
      if (!result) { return }
      this.inspections = await this.inspectSrv.getInspection();
    });
  }

  async insertInspection(): Promise<any> {
    this.isConfirmLoading = true;
    try {
      if (!this.inspection.label || !this.inspection.status) {
        return this.toastSrv.danger("veuillez renseigner le(s) champ(s) vide(s)", 'Donnée(s) manquante(s)');
      }

      this.inspection = {
        code: this.inspection.code,
        label: this.inspection.label,
        status: this.selectedStatus,
      }

      await this.inspectSrv.addInspection(this.inspection);
      this.dialogRef.close(true);
      this.toastSrv.success('Donnée ajoutée avec succès', 'Ajout réussi');
    } catch (error) {
      this.toastSrv.danger('Nous n\'avons pas pu ajouter cette donnée', 'Echec de l\'ajout')
    }
    finally {
      this.isConfirmLoading = false;
    }
  }

  async updateInspection(): Promise<any> {
    try {
      this.isConfirmLoading = true;
      if (!this.inspection?.label || !this.inspection?.status) {
        return this.toastSrv.danger('Veuillez renseigner tous les champs', 'Donnée(s) manquante(s)');
      }
      const id = this.inspection._id;
      this.inspection = {
        label: this.inspection.label,
        status: this.selectedStatus,
      }
      await this.inspectSrv.editInspection(this.inspection, id );
      this.editdialogRef.close();
      this.inspections = await this.inspectSrv.getInspection();
      this.toastSrv.success('Valeurs editées avec succès', 'Edition réussie');
    } catch (error) {
      this.toastSrv.danger('Nous n\'avons pas pu éditer cette valeur de l inspection', 'Echec d\'édition')
    }
    finally {
      this.isConfirmLoading = false;
    }
  }

  async deleteInspection(): Promise<any> {
    try {
      this.isConfirmLoading = true;
      if (!this.inspection)
        this.toastSrv.danger('Aucune donnée trouvée', 'Echec d\'affichage')
      await this.inspectSrv.deleteInspection(this.inspection);
      this.deletedialogRef.close(true);
      this.toastSrv.success('Valeurs supprimées avec succès', 'Suppression réussie');
    } catch (error) {
      this.toastSrv.danger('Nous n\'avons pas pu supprimer cette valeur', 'Echec d\'edition')
    } finally { this.isConfirmLoading = false; }
  }

  selectStatus(status: Status): void {
    this.selectedStatus = status;
  }
}
