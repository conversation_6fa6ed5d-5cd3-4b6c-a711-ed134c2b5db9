import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { BaseUrlService } from 'src/app/shared/services/base-url.service';
import { environment } from 'src/environments/environment';
import * as moment from 'moment';

@Injectable({
  providedIn: 'root'
})
export class MenuReportingService {

  isLoading: Boolean;
  BASE_URL: string;

  startDate: any;
  endDate: any;
  orderType: string;

  constructor(
    private http: HttpClient,
    private baseUrlSrv: BaseUrlService,
  ) {
    this.BASE_URL = `${this.baseUrlSrv.getOrigin()}${environment.basePath}`;
  }

  async getNumberStatusAEs(parm?) {
    const { FreightHandlingCode } = parm;
    let params = this.generateQueryParams();
    if (FreightHandlingCode) { params = params.append('FreightHandlingCode', FreightHandlingCode); }
    return await this.http.get(`${this.BASE_URL}/statistics/status`, { params }).toPromise();
  }

  async getAEsByRenderType(range: string) {
    try {
      let params = new HttpParams();
      params = params.append('start', `${this.startDate || moment().format('MM-DD-YYYY')}`);
      params = params.append('end', `${this.endDate || moment().format('MM-DD-YYYY')}`);
      if (this.orderType) params = params.append('OrderType', this.orderType);
      if (range) { params = params.append('rangeType', range); }

      return await this.http.get(`${this.BASE_URL}/statistics/render-type`, { params }).toPromise();
    } catch (error) {
      return error
    }
  }

  async getStatsProduct(query) {
    try {
      const { FreightHandlingCode } = query;
      let params = this.generateQueryParams();
      if (FreightHandlingCode) { params = params.append('FreightHandlingCode', FreightHandlingCode); }
      return await this.http.get(`${this.BASE_URL}/statistics/product`, { params }).toPromise();
    } catch (error) {
      return error;
    }
  }

  async getEffectives(query) {
    const { FreightHandlingCode } = query;
    let params = this.generateQueryParams();
    if (FreightHandlingCode) { params = params.append('FreightHandlingCode', FreightHandlingCode); }
    return await this.http.get(`${this.BASE_URL}/statistics/effectives`, { params }).toPromise();
  }

  async getCarriersRanking(): Promise<any[]> {
    const params = this.generateQueryParams();
    return await this.http.get<any[]>(`${this.BASE_URL}/statistics/carriers/ranking`, { params }).toPromise();
  }

  async getTimes(query) {
    const { FreightHandlingCode } = query;
    let params = this.generateQueryParams();
    if (FreightHandlingCode) { params = params.append('FreightHandlingCode', FreightHandlingCode); }

    return await this.http.get(`${this.BASE_URL}/statistics/times`, { params }).toPromise();
  }

  async getAverageParkTimes(query): Promise<any> {
    const { FreightHandlingCode } = query;
    let params = this.generateQueryParams();
    if (FreightHandlingCode) { params = params.append('FreightHandlingCode', FreightHandlingCode); }

    return await this.http.get(`${this.BASE_URL}/statistics/average-times`, { params }).toPromise();
  }

  async getAverageResponseTime(query): Promise<any> {
    const { FreightHandlingCode } = query;
    let params = this.generateQueryParams();
    if (FreightHandlingCode) { params = params.append('FreightHandlingCode', FreightHandlingCode); }

    return await this.http.get(`${this.BASE_URL}/statistics/average-response-time`, { params }).toPromise();
  }
  async getAverageResponseTimePerCarrier(): Promise<any[]> {
    const params = this.generateQueryParams();
    return await this.http.get<any[]>(`${this.BASE_URL}/statistics/average-response-time-per-carrier`, { params }).toPromise();
  }
  async getAverageDeliveryTimePerCarrier(): Promise<any> {
    const params = this.generateQueryParams();
    return await this.http.get(`${this.BASE_URL}/statistics/average-delivery-time-per-carrier`, { params }).toPromise();
  }

  async getStatusInspections(query) {
    const { type } = query;
    let params = this.generateQueryParams();
    if (type) { params = params.append('type', type); }
    return await this.http.get(`${this.BASE_URL}/statistics/ispections/status`, { params }).toPromise();
  }

  async getStatusAEs(query: any): Promise<Object> {
    const { FreightHandlingCode } = query;
    let params = this.generateQueryParams();
    if (FreightHandlingCode) { params = params.append('FreightHandlingCode', FreightHandlingCode); }
    return await this.http.get(`${this.BASE_URL}/statistics/status-aes`, { params }).toPromise();
  }

  private generateQueryParams() {
    let params = new HttpParams();
    if (this.startDate && this.endDate) {
      params = params.append('start', `${this.startDate}`);
      params = params.append('end', `${this.endDate}`);
    }
    if (this.orderType) params = params.append('OrderType', this.orderType);
    return params;
  }
}
