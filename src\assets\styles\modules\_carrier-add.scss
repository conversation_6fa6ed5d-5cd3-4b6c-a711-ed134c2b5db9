.add-carrier-container {
    border: transparent !important;

    .remove-padding {
        padding: 10px !important;
        .width-img {
            width: 125px;
            height: 125px;
        }
    }

    .tab-padding{
        padding: 0 !important;
    }

    .flipcard-body {
        width: 37vw;
        justify-content: center;
        align-items: center;
        display: flex;
      }

    .not-found {
        display: flex;
        justify-content: center;
        flex-direction: column;
        align-items: center;
        width: 100%;
    }

    .list-elt-header {
        .col {
            font-weight: bold;
        }
    }

    .list-elt-header,
    .list-elt {
        @include vh-between;
        height: 50px;
        padding-top: 7px;
        padding-bottom: 7px;
        font-size: 13px;

        .col {
            height: 100%;
            display: flex;
            align-items: center;
        }

        .col-num {
            width: 3%;
            min-width: unset !important;
            max-width: unset !important;
        }
        .col-ref {
            width: 15%;
            min-width: unset !important;
            max-width: unset !important;
        }
        .col-truc-type {
            width: 12%;
        }
        .col-matriculation {
            width: 12%;
        }
        .col-store {
            width: 10%;
        }
        .col-description {
            width: 15%;
        }
        .col-weight {
            width: 5%;
        }
        .col-carrier-ref {
            width: 13%;
        }
        .col-vol {
            width: 8%;
        }
        .col-driver-code {
            width: 15%;
        }
        .col-permit {
            width: 10%;
        }
        .col-fullname {
            width: 25%;
        }
        .col-phone {
            width: 17%;
        }
        .col-cni {
            width: 10%;
        }
        .col-tain-num {
            width: 5%;
        }
        .col-tain-label {
            width: 20%;
        }
        .col-tain-trainer {
            width: 20%;
        }
        .col-tain-date {
            width: 25%;
        }
        .col-tain-action {
            width: 10%;
        }

        .col-action {
            width: 20%;
            min-width: unset !important;
            max-width: unset !important;
            justify-content: flex-end;

            .action-icons {
                display: flex;
                align-items: center;
                justify-content: flex-end;
            }
        }
        button {
            text-transform: none !important;
            background-color: #edf1f7;
            padding: 5px;
            border-radius: 5px;
        }
    }
    .carrier-card-header {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 10px 45px;
        width: 35%;
        .image-container {
            width: 100%;
        }
        .carrier-image {
            height: 290px;
            width: 65%;
            border-radius: 50%;
            display: inline-block;
        }

        .carrier-profile {
            flex: 0 0 57%;
            .carrier-name {
                font-size: 22px !important;
                text-align: justify;
                font-family: $font-bold !important;
            }
            .carrier-function {
                font-size: 16px !important;
                text-align: justify;
                font-family: $font-regular !important;
            }

            .icon-button {
                width: 0;
                height: 0;
            }
        }
    }
    .overflow {
        overflow-y: auto !important;
        padding: 0 !important;
        display: flex;
        justify-content: space-between;
    }
    .form-container {
        width: 65%;
    }
    .form-group {
        padding: 8px 0;
        display: flex;
        align-items: center;
        .attribute-block {
            display: flex;
            align-items: center;
            flex: 0 0 35%;
            .label {
                margin-left: 5px;
                font-family: $font-bold;
                font-size: 14px;
                color: black;
            }
            .icon-btn {
                background: #f5f5f5;
                height: 30px;
                width: 30px;
                border-radius: 50%;
                display: inline-block;
                color: $color-primary;
                padding: 5px;
            }
        }
        .input-width {
            width: 100% !important;
            border-right: transparent !important;
            border-top: transparent !important;
            border-left: transparent !important;
            background: white !important;
            font-family: $font-regular;
            font-size: 13px;
        }
        .text {
            display: flex;
            align-items: justify;
            flex: 0 0 48%;
            font-family: $font-regular;
            font-size: 13px;
            color: grey;
        }
    }
    .name-container {
        display: flex;
        flex-direction: column;
    }
    .form--footer {
        display: flex;
        justify-content: justify;
    }

    .btn-border {
        border-radius: 5px !important;
        padding: 7px !important;
    }
    .add-title {
        font-size: 16px;
        font-family: $font-bold;
    }
    .insert-border {
        border-bottom: 1px solid #e4e9f2;
    }
    .acces-rigth {
        margin-bottom: 11px;
        padding-bottom: 11px;
        @include vh-between;
        .rigth-label {
            font-family: $font-regular;
            font-size: 14px;
        }
    }
    .label-title {
        margin-bottom: 20px;
        font-size: 16px;
        font-family: $font-bold;
    }
    .block {
        padding: 0 2%;
        border: none;
        .menus-block {
            width: 100%;
            display: flex;
            flex-wrap: wrap;
            .menu-elt {
                border-radius: 15px;
                height: 160px;
                display: flex;
                flex-direction: column;
                margin-bottom: 22px;
                margin-right: 24px;
                cursor: pointer;
                transition: all 0.2s ease-in-out;
                outline: none;
                &:hover {
                    transform: scale(1.1);
                }
                align-items: flex-start;
                padding-top: 20px;
                .user-image {
                    height: 120px;
                    width: 120px;
                    background-color: #bbb;
                    border-radius: 50%;
                    display: inline-block;
                    // height: 80%;
                    position: relative;
                    .small-img {
                        height: 125px;
                        width: 125px;
                    }
                    .title-block {
                        font-family: $font-regular;
                        font-weight: bold;
                        font-size: 18px;
                        position: absolute;
                        left: 54px;
                        top: -16%;
                        text-transform: uppercase;
                    }
                }
                .text-block {
                    height: 10%;
                    text-align: justify;
                    font-family: $font-regular;
                    color: #ea7d1e;
                    padding-top: 15px;
                    font-size: 20px;
                }
            }
            .not-found-block {
                height: 90%;
                width: 90%;
                .image-block {
                    @include illustration-mixin;
                    img {
                        width: 140px;
                    }
                }
            }
        }
    }

    .add-truck {
        width: 35vw;
        .form-container {
            width: 100%;
        }
    }
}
