.menu-removal-container {
  .tabset-pagination-container {
    display: flex;
    justify-content: space-between;
    align-items: stretch; /* Permet aux éléments enfants de prendre la même hauteur */
    min-height: 48px; /* Hauteur minimale pour uniformiser */
    padding: 0 20px;
    border-bottom: 1px solid #edf1f7;
    background-color: #f8f9fa;
  }

  .tabset-pagination-container nb-tabset {
    display: flex;
    align-items: center;
    width: auto; /* Largeur automatique basée sur le contenu */
    flex-shrink: 0; /* Empêche la compression des tabsets */
  }

  .tabset-pagination-container nb-tabset ::ng-deep .tabset {
    height: 48px; /* Hauteur fixe pour les tabsets */
    display: flex;
    align-items: center;
    margin: 0 !important;
  }

  .tabset-pagination-container nb-tabset ::ng-deep .tab {
    height: 48px; /* Même hauteur pour les onglets */
    display: flex;
    align-items: center;
    width: auto; /* Largeur automatique basée sur le contenu */
    flex: 0 0 auto; /* Ne grandit pas, ne rétrécit pas, taille automatique */
  }

  .tabset-pagination-container nb-tabset ::ng-deep .tab-link {
    height: 48px; /* Même hauteur pour les liens des onglets */
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 20px;
    white-space: nowrap; /* Empêche le retour à la ligne */
    width: auto; /* Largeur automatique basée sur le contenu */
  }

  .pagination-wrapper {
    display: flex;
    align-items: center;
    height: 48px; /* Même hauteur que les tabsets */
    padding: 0 8px;
    flex-shrink: 0; /* Empêche la compression de la pagination */
    min-width: 200px; /* Largeur minimale pour la pagination */
  }

  .pagination-wrapper ::ng-deep .pagination-container {
    height: 48px;
    display: flex;
    align-items: center;
  }

  .tab-content {
    padding: 0;
  }

  .no-data-row {
    height: auto !important;
  }

  @media (max-width: 768px) {
    .tabset-pagination-container {
      flex-direction: column;
      align-items: stretch;
      min-height: auto;
      padding: 10px;
    }

    .tabset-pagination-container nb-tabset {
      min-width: auto;
      max-width: 100%;
      margin-bottom: 10px;
    }

    .tabset-pagination-container nb-tabset ::ng-deep .tab {
      min-width: 120px; /* Largeur réduite sur mobile */
    }

    .pagination-wrapper {
      width: 100%;
      justify-content: center;
      min-width: auto;
      height: 48px;
    }
  }
}
