<div class="tile-chart-container tile-chart-truck-container">
  <nb-card accent="success" nbSpinnerMessage="Chargement des données">
      <nb-card-header class="card-header-wrapper">
          <div class="card-header">
              Evolution des sorties de camions
          </div>
      </nb-card-header>
      <nb-card-body>
          <div class="graphics-contain">
              <!-- <div class="card-header">
                  <div class="filter-elt select">
                      <nb-select placeholder="" status="success">
                          <nb-option [value]="option" *ngFor="let option of chartTypes">{{option?.label}}</nb-option>
                      </nb-select>
                  </div>
              </div> -->
              <div class="canvas-contain">
                  <canvas id="canvas-truck" height="270px" width="450px">{{truck}}</canvas>
              </div>
          </div>
      </nb-card-body>
  </nb-card>
</div>
