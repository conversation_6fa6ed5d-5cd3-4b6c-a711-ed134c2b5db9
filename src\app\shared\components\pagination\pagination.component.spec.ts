import { ComponentFixture, TestBed } from '@angular/core/testing';
import { PaginationComponent } from './pagination.component';
import { PaginationService } from '../../services/pagination.service';
import { FormsModule } from '@angular/forms';

describe('PaginationComponent', () => {
  let component: PaginationComponent;
  let fixture: ComponentFixture<PaginationComponent>;
  let paginationService: PaginationService;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [PaginationComponent],
      imports: [FormsModule],
      providers: [PaginationService]
    }).compileComponents();

    fixture = TestBed.createComponent(PaginationComponent);
    component = fixture.componentInstance;
    paginationService = TestBed.inject(PaginationService);

    component.config = {
      currentPage: 1,
      itemsPerPage: 10,
      totalItems: 100
    };

    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should emit page change event', () => {
    spyOn(component.pageChange, 'emit');
    component.onPageChange(2);
    expect(component.pageChange.emit).toHaveBeenCalledWith(2);
  });

  it('should update pagination on config change', () => {
    spyOn(paginationService, 'calculatePagination');

    component.config = {
      currentPage: 2,
      itemsPerPage: 20,
      totalItems: 200
    };

    component.ngOnChanges({
      config: {
        currentValue: component.config,
        previousValue: null,
        firstChange: false,
        isFirstChange: () => false
      }
    });

    expect(paginationService.calculatePagination).toHaveBeenCalled();
  });
});