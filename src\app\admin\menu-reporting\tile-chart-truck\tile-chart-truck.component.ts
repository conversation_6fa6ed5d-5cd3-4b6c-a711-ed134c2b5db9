import { Component, OnInit } from '@angular/core';
import { Chart, registerables } from 'chart.js';


Chart.register(...registerables)

@Component({
  selector: 'clw-tile-chart-truck',
  templateUrl: './tile-chart-truck.component.html',
  styles: [
  ]
})
export class TileChartTruckComponent implements OnInit {

 truck : any;
 isLoading: boolean;

  constructor() { }

  async ngOnInit(): Promise<any> {
    this.renderChart()
  }

  renderChart() {
    this.isLoading = true;
    this.truck = new Chart('canvas-truck', {
      type: 'line',
      data: {
        labels: ['Lundi', 'Mardi', 'Mercredi', '<PERSON><PERSON>', 'Vendredi', 'Samedi', 'Dimanche'],
        datasets: [{
          label: 'Mes données',
          data: [65, 59, 80, 81, 56, 55, 40],
          // fill: false,
          borderColor: 'rgb(75, 192, 192)',
          // tension: 0.1
        }]
      },
      options: {},
    });
    this.isLoading = false;
  }

}
