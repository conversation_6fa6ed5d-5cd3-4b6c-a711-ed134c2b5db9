import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { MaterialInspectionsRoutingModule } from './material-inspections-routing.module';
import { MaterialInspectionsComponent } from './material-inspections.component';
import { NbTabsetModule, NbCardModule, NbIconModule, NbListModule, NbBadgeModule, NbToggleModule, NbButtonModule, NbSpinnerModule, NbTooltipModule, NbSelectModule, NbInputModule, NbAutocompleteModule, NbDatepickerModule } from '@nebular/theme';
import { SharedModule } from 'src/app/shared/shared.module';
import { FormsModule } from '@angular/forms';


@NgModule({
  declarations: [
    MaterialInspectionsComponent
  ],
  imports: [
    FormsModule,
    CommonModule,
    NbTabsetModule,
    NbCardModule,
    NbIconModule,
    NbListModule,
    NbBadgeModule,
    NbToggleModule,
    NbButtonModule,
    NbSpinnerModule,
    NbTooltipModule,
    NbSelectModule,
    NbInputModule,
    SharedModule,
    NbDatepickerModule,
    NbAutocompleteModule,
    MaterialInspectionsRoutingModule
  ]
})
export class MaterialInspectionsModule { }
