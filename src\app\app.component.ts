import { NavigationEnd, Router } from '@angular/router';
import { Component } from '@angular/core';
import { AuthService } from './shared/services/auth.service';
import { NbSidebarService, NbSidebarState } from '@nebular/theme';
import { Subject } from 'rxjs';
import { shareReplay } from 'rxjs/operators';

@Component({
  selector: 'clw-root',
  templateUrl: './app.component.html',
  styles: [],
})
export class AppComponent {
  title = 'cimencam-logistique-web';
  state: NbSidebarState = 'expanded';

  protected layoutSize$ = new Subject();
  protected layoutSizeChange$ = this.layoutSize$.pipe(
    shareReplay({ refCount: true }),
  );

  isHome: boolean;
  isCallScreen: boolean;
  isTruckScreen: boolean

  constructor(
    private router: Router,
    private authSvr: AuthService,
    private sidebarSrv: NbSidebarService,
  ) {
    this.router.events.subscribe((event) => {
      if (event instanceof NavigationEnd) {
        this.isHome = ['/', '/new-home', '/new-home/login'].includes(event.url);
        this.isCallScreen = (event.url).includes('/client/call-screen');
        this.isTruckScreen = (event.url).includes('/client/truck-call-screen');
      }
    });
  }


  ngOnInit(): void {

  }

  /*   toggleSidebar() {
      this.sidebarSrv.toggle(true, 'right');
    } */

  isUserConnected() {
    return this.authSvr.isUserConnected();
  }

  toggleSidebar(): boolean {
    this.sidebarSrv.toggle(true, 'right');
    this.layoutSize$.next(null);
    return false;
  }

  setCallScreenStyle() {
    return {
      'padding': '0px !important',
    }
  }









}
