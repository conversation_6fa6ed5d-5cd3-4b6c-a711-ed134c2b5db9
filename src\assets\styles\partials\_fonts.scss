@font-face {
    font-family: 'Lato-Regular';
    font-style: normal;
    src: url('./../../fonts/Lato/Lato-Regular.ttf');
}

@font-face {
    font-family: 'Lato-Light';
    font-style: normal;
    src: url('./../../fonts/Lato/Lato-Light.ttf');
}

@font-face {
    font-family: 'Lato-Regular';
    font-style: normal;
    src: url('./../../fonts/Lato/Lato-Regular.ttf');
}

@font-face {
    font-family: 'Lato-Bold';
    font-style: normal;
    src: url('./../../fonts/Lato/Lato-Bold.ttf');
}

@font-face {
    font-family: 'Lato-Black';
    font-style: normal;
    src: url('./../../fonts/Lato/Lato-Black.ttf');
}

@font-face {
    font-family: 'Lato-Italic';
    font-style: normal;
    src: url('./../../fonts/Lato/Lato-Italic.ttf');
}

@font-face {
    font-family: 'Mont';
    font-style: normal;
    src: url('./../../fonts/Mont/Mont-Regular.otf');
}

@font-face {
    font-family: 'Mont-Bold';
    font-style: bold;
    src: url('./../../fonts/Mont/Mont-Bold.otf');
}

@font-face {
  font-family: 'Metropolis-Black';
  font-style: bold;
  src: url('../../fonts/metropolis/Metropolis-Black.otf');
}

@font-face {
  font-family: 'Metropolis-Black';
  font-style: normal;
  src: url('../../fonts/metropolis/Metropolis-Black.otf');
}

@font-face {
  font-family: 'Metropolis-Bold';
  font-style: normal;
  src: url('../../fonts/metropolis/Metropolis-Bold.otf');
}

@font-face {
  font-family: 'Metropolis-Light';
  font-style: normal;
  src: url('../../fonts/metropolis/Metropolis-Light.otf');
}

@font-face {
  font-family: 'Metropolis-Regular';
  font-style: normal;
  src: url('../../fonts/metropolis/Metropolis-Regular.otf');
}

@font-face {
  font-family: 'Metropolis-Medium';
  font-style: normal;
  src: url('../../fonts/metropolis/Metropolis-Medium.otf');
}

@font-face {
  font-family: 'Metropolis-ExtraBold';
  font-style: normal;
  src: url('../../fonts/metropolis/Metropolis-ExtraBold.otf');
}

@font-face {
  font-family: 'Metropolis-ExtraLight';
  font-style: normal;
  src: url('../../fonts/metropolis/Metropolis-ExtraLight.otf');
}
