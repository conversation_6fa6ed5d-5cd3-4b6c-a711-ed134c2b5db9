import { Component, OnInit, TemplateRef, ChangeDetectorRef } from '@angular/core';
import { NbDialogRef, NbDialogService, NbToastrService } from '@nebular/theme';
import * as moment from 'moment';
import { StorageService } from 'src/app/shared/services/storage.service';
import { User } from 'src/app/shared/models/user';
import { UsersListService } from './users-list.service';
import { ImageCompressor } from 'image-compressor'
import { AuthService } from 'src/app/shared/services/auth.service';
import { Observable, of } from 'rxjs';
import { Location } from '@angular/common';
import { Router } from '@angular/router';
import { PaginationConfig } from 'src/app/shared/services/pagination.service';
import { RemovalService } from 'src/app/client/menu-removal/removal-management/removal.service';
import { NbBooleanInput } from '@nebular/theme/components/helpers';

@Component({
  selector: 'clw-users-list',
  templateUrl: './users-list.component.html',
})
export class UsersListComponent implements OnInit {
  min: any;
  orders: any;
  isLoading: boolean;
  rangeFilter: { start: any; end: any } = { start: null, end: null };
  users = [];
  flipped: boolean;
  name: string = '';
  fname!: string;
  department!: string;
  connectedUser!: string;
  userSrv: any;
  total: number;
  endIndex: number;
  startIndex: number;
  isRequest: boolean;
  currUser: User;
  detailsdialogRef: NbDialogRef<any>;
  user: User;
  newUser: User;
  isEdit: boolean = false;
  BASE_URL: string;
  addUser: User;
  factories: string[];
  currFactory: any;

  // Configuration de la pagination
  paginationConfig: PaginationConfig = {
    currentPage: 1,
    itemsPerPage: 10,
    totalItems: 0,
    maxSize: 10
  };

  password: string;

  deleteDailogRef: NbDialogRef<any>;
  deacticvateDailogRef: NbDialogRef<any>;

  imgResultAfterCompress: string;


  adddialogRef: NbDialogRef<any>;
  dialogRef: NbDialogRef<any>;
  file: any;
  filename: any;
  imageSrc: string;

  names = [];
  filteredNames$: Observable<string[]>;
  checked: NbBooleanInput;
  constructor(
    private storageSvr: StorageService,
    private dialogService: NbDialogService,
    private usersSrv: UsersListService,
    private toastrSrv: NbToastrService,
    private removalSvr: RemovalService,
    private authSvr: AuthService,
    private location: Location,
    private router: Router,
    private cdr: ChangeDetectorRef,
  ) { }

  async ngOnInit(): Promise<void> {
    this.flipped = false;
    this.rangeFilter = { start: null, end: null };
    this.getAllUsers();
    this.getDataForFilter();
  }

  updateDateStart(startDate: any): void {
    this.min = moment(startDate).startOf('day').toDate();
    this.rangeFilter.start = moment(startDate).startOf('day').valueOf();
  }

  toggle() {
    this.flipped = !this.flipped;
  }

  updateDateEnd(endDate: any): void {
    this.rangeFilter.end = moment(endDate).endOf('day').toDate();
  }

  async getAllUsers(): Promise<void> {
    try {
      this.isLoading = true;

      this.connectedUser = this.storageSvr.getObject('user');

      const options = {
        userLname: this.name,
        limit: this.paginationConfig.itemsPerPage,
        offset: (this.paginationConfig.currentPage - 1) * this.paginationConfig.itemsPerPage
      };

      const { data, count } = await this.usersSrv.getAllUsers(options);

      this.users = data;
      this.total = count;

      // Créer une nouvelle instance de PaginationConfig pour forcer la détection des changements
      this.paginationConfig = {
        currentPage: this.paginationConfig.currentPage,
        itemsPerPage: this.paginationConfig.itemsPerPage,
        totalItems: count,
        maxSize: 10
      };

      // Pour la compatibilité avec le code existant
      this.endIndex = Math.min((this.paginationConfig.currentPage * this.paginationConfig.itemsPerPage), count);
      this.startIndex = count === 0 ? 0 : (this.paginationConfig.currentPage - 1) * this.paginationConfig.itemsPerPage + 1;

      // Vérification si la page actuelle est valide
      const totalPages = Math.ceil(this.paginationConfig.totalItems / this.paginationConfig.itemsPerPage);
      if (this.paginationConfig.currentPage > totalPages && totalPages > 0) {
        this.paginationConfig = {
          currentPage: totalPages,
          itemsPerPage: this.paginationConfig.itemsPerPage,
          totalItems: this.paginationConfig.totalItems,
          maxSize: 10
        };
        this.cdr.detectChanges();
        await this.getAllUsers();
        return;
      }

      this.names = this.users?.map((user: { lname: string }) => user?.lname);
      this.filteredNames$ = of(this.names);

    } catch (error) {
      this.toastrSrv.danger(
        'Impossible de récupérer la liste des utilisateurs',
        `Erreur connexion: ${error?.message || 'Inconnue'}`
      );
    } finally {
      this.isLoading = false;
    }
  }


  search() {
    // Réinitialiser la pagination à la première page lors de la recherche
    this.paginationConfig = {
      ...this.paginationConfig,
      currentPage: 1
    };
    this.getAllUsers();
  }

  resetFilter() {
    this.name = '';
    // Réinitialiser la pagination à la première page
    this.paginationConfig = {
      ...this.paginationConfig,
      currentPage: 1
    };
    this.getAllUsers();
  }

  /**
   * Méthode appelée lorsque l'utilisateur change de page
   * @param page Numéro de la page sélectionnée
   */
  onPageChange(page: number): void {
    if (this.isLoading) return; // Éviter les requêtes multiples pendant le chargement

    // Créer une nouvelle instance de PaginationConfig pour forcer la détection des changements
    this.paginationConfig = {
      currentPage: page,
      itemsPerPage: this.paginationConfig.itemsPerPage,
      totalItems: this.total,
      maxSize: 10
    };

    // Forcer la détection des changements
    this.cdr.detectChanges();

    this.getAllUsers();
  }

  /**
   * Méthode appelée lorsque l'utilisateur change le nombre d'éléments par page
   * @param itemsPerPage Nombre d'éléments par page sélectionné
   */
  onItemsPerPageChange(itemsPerPage: number): void {
    // Créer une nouvelle instance de PaginationConfig pour forcer la détection des changements
    this.paginationConfig = {
      currentPage: 1, // Revenir à la première page
      itemsPerPage: itemsPerPage,
      totalItems: this.total,
      maxSize: 10
    };

    // Forcer la détection des changements
    this.cdr.detectChanges();

    this.getAllUsers();
  }

  private filterStore(value: string): string[] {
    const filterValue = value?.toLowerCase();
    return this.names.filter(optionValue => optionValue?.toLowerCase().includes(filterValue)).sort((a, b) => {
      return a.toString().toLowerCase().indexOf(filterValue?.toLowerCase()) - b.toString().toLowerCase().indexOf(filterValue?.toLowerCase());
    });;
  }

  onModelStoreChange(value: string): void {
    this.filteredNames$ = of(this.filterStore(value));

    // Réinitialiser la pagination à la première page lors du changement de filtre
    this.paginationConfig = {
      ...this.paginationConfig,
      currentPage: 1
    };
  }
  async onSelectionStoreChange($event: any): Promise<void> {
    this.name = $event;

    // Réinitialiser la pagination à la première page lors de la sélection d'un filtre
    this.paginationConfig = {
      ...this.paginationConfig,
      currentPage: 1
    };

    await this.getAllUsers();
  }

  openDetailsUser(dialog?: TemplateRef<any>, user?: any): any {
    if (user !== null) {

      this.currUser = user;
    }

    this.detailsdialogRef = this.dialogService.open(dialog, { context: '' });
    this.detailsdialogRef.onClose.subscribe(() => {
      this.flipped = false
    });
  }

  back() {

    this.flipped = false;
  }
  onToggleChange(factory: any, event) {
    if (this.currUser !== null) {

      this.currUser.rigths[`${factory}`] = event;
    }
  }

  edit() {
    this.imageSrc = this.currUser.img;
    this.isEdit = true;
    this.toggle();


  }
  close() {
    this.isEdit = false;
  }

  async deleteUser(): Promise<void> {
    this.isLoading = true;
    try {
      await this.usersSrv.deleteUser(this.currUser);
      this.deleteDailogRef.close()
      this.toastrSrv.success('Utilisateur a été supprimé avec succès.', 'Opération réussie');
    }
    catch (error) {
      this.toastrSrv.danger('veuillez verifier votre connexion internet', 'erreur de connexion');
    } finally {
      this.isEdit = false;
      this.isLoading = false;
      await this.getAllUsers()
    }
  }

  openDeleteModal(dailog: any): void {
    this.deleteDailogRef = this.dialogService.open(dailog, {})
    this.detailsdialogRef.close()
  }

  openActionDialog(dailog: any): void {
    this.deacticvateDailogRef = this.dialogService.open(dailog, {})
    this.detailsdialogRef.close()
  }
  async updateUsers(): Promise<any> {
    this.isLoading = true;
    if (this.password) {
      this.currUser.passwordClear = this.password
    }
    try {
      this.currUser.img = this.imageSrc;

      await this.usersSrv.updateUser(this.currUser);
      this.toastrSrv.success('vos informations ont été mise à jour', 'modifications effectuée(s)');
      this.flipped = false;
    }
    catch (error) {
      return this.toastrSrv.danger('veuillez verifier votre connexion internet', 'erreur de connexion');

    } finally {
      this.isEdit = false;
      this.isLoading = false;
    }

  }

  async deactivateUsers(): Promise<any> {
    this.isLoading = true;
    try {
      this.currUser.enable = this.currUser.enable === false ? true : false;
      await this.usersSrv.updateUser(this.currUser);
      this.toastrSrv.success('Utilisateur a été supprimé avec succès.', 'Opération réussie');
      this.deacticvateDailogRef.close()
    }
    catch (error) {
      return this.toastrSrv.danger('veuillez verifier votre connexion internet', 'erreur de connexion');
    } finally {
      this.isLoading = false;
    }

  }

  async openNewUser(dialog?: TemplateRef<any>): Promise<void> {
    this.resetUser();

    this.adddialogRef = this.dialogService.open(dialog, { context: '' });
    this.adddialogRef.onClose.subscribe(async () => {
      this.addUser = null;
    });
  }

  async insertUsers(): Promise<any> {
    this.isLoading = true;
    try {
      if (!this.addUser?.lname || !this.addUser?.fname || !this.addUser?.email || !this.addUser?.tel) {
        this.toastrSrv.danger("Veuillez renseigner le(s) champ(s) vide(s)", 'Donnée(s) manquante(s)');
        return;
      }
      if (!this.authSvr.validateEmail(this.addUser.email)) {
        this.toastrSrv.danger('Verifiez votre email', 'Données incorrectes');
        return;
      }

      if (!this.imageSrc || this.imageSrc === '') {
        await this.usersSrv.insertUser(this.addUser);
        this.toastrSrv.success('Utilisateur Ajouté', 'Opération réussie !');
        this.adddialogRef.close();
        this.getAllUsers();
        return;
      }
      this.compressFile(this.imageSrc, async (compressedSrc) => {

        this.addUser.img = compressedSrc
        await this.usersSrv.insertUser(this.addUser);
        this.toastrSrv.success('Utilisateur Ajouté', 'Opération réussie !');
        this.adddialogRef.close();
        this.getAllUsers();

      })
    }
    catch (error) {
      return this.toastrSrv.danger('Impossile de creer cet utilisateur', 'Erreur connexion !');

    } finally {
      this.isEdit = false;
      this.isLoading = false;
    }

  }

  resetUser() {
    this.addUser = {
      lname: '',
      fname: '',
      tel: '',
      email: '',
      img: '',
      rigths: {
        // Super Administrateur
        isFullRigth: false,

        // Menu Administration
        isAdmin: false,
        canManageUsers: false,
        canManageStores: false,
        canManageCarriers: false,
        canManageInspections: false,
        canManageOrders: false,

        // Menu Gestion Enlèvements
        canViewHistory: false,
        canAllocate: false,
        canViewTransports: false,
        canViewLoadedTrucks: false,
        canViewQueue: false,
        canRemove: false,
        canAllocateDock: false,
        canViewBrokenTrucks: false,
        canViewInspections: false,

        // Menu Reporting
        canViewReporting: false,
        canViewRenderReporting: false,
        canViewPickupReporting: false,

        // Menu Écran d'appel
        canFollow: false,
        canViewParking: false,
        canViewFactoryLoading: false,

        // Autres autorisations
        canSaveTruck: false,
        canManageRemovalsAuth: false,
        isInspector: false,
        canOrder: false,
        allStore: false,
      }
    }
    this.imageSrc = '';
  }

  async getDataForFilter() {
    try {
      const data = await this.removalSvr.getFilterData({
        // ...this.filterForm,
        // startDate: moment(this.filterForm.startDate).format('MM-DD-YYYY'),
        // endDate: moment(this.filterForm.endDate).format('MM-DD-YYYY'),
        keyForFilters: ['BusinessUnit']
      },);

      const result = data?.dataBusinessUnit ?? [];
      this.factories = this.removeDuplicatesAndNullsAndSpaces(result)

    } catch (error) {
      return error;
    }
  }

  removeDuplicatesAndNullsAndSpaces<T>(data: string[]): string[] {
    const trimmedData = data.map((item) => item.trim());
    const uniqueSet = new Set(trimmedData.filter((item) => item !== null && item !== ''));
    return Array.from(uniqueSet);
  }



  getFile(fileDataSet: any): any {

    this.file = fileDataSet.target.files[0];
    const type = this.file.type;
    if (!type.includes('image')) {
      this.file = null;
      return this.toastrSrv.danger('Veuillez importer uniquement les images', 'Donnés incorrectes');

    }
    this.filename = this.file.name;

    const reader = new FileReader();
    if (fileDataSet.target.files && fileDataSet.target.files.length) {
      const [file] = fileDataSet.target.files;
      reader.readAsDataURL(file);

      reader.onload = () => {
        this.imageSrc = reader.result as string;
      };

    }
  }

  compressFile(imageSrc: string, callback: any): any {
    const imageCompressor = new ImageCompressor;

    const compressorSettings = {
      toWidth: 150,
      toHeight: 150,
      mimeType: 'image/png',
      mode: 'strict',
      quality: 0.8,
      grayScale: false,
      sepia: false,
      threshold: false,
      vReverse: false,
      hReverse: false,
      speed: 'low'
    };
    imageCompressor.run(imageSrc, compressorSettings, callback);

  }

  goBack() {
    if (window.history.length > 1) {
      this.location.back()
    } else {
      this.router.navigate(['/home'])
    }
  }

  /**
   * Retourne le texte du tooltip pour le champ de recherche
   * Cette méthode évite l'erreur ExpressionChangedAfterItHasBeenCheckedError
   */
  getTooltipText(): string {
    return this.name || 'Rechercher';
  }


}
