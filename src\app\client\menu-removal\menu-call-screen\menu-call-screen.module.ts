import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { MenuCallScreenRoutingModule } from './menu-call-screen-routing.module';
import { NbBadgeModule, NbButtonModule, NbCardModule, NbDatepickerModule, NbDialogModule, NbIconModule, NbInputModule, NbListModule, NbSelectModule, NbSpinnerModule, NbTabsetModule, NbTooltipModule } from '@nebular/theme';
import { SharedModule } from 'src/app/shared/shared.module';
import { FormsModule } from '@angular/forms';
import { RemovalManagementModule } from '../removal-management/removal-management.module';
import { MenuCallScreenComponent, StatusRemovalPipe } from './menu-call-screen.component';


@NgModule({
  declarations: [
    MenuCallScreenComponent,
    StatusRemovalPipe,
  ],
  imports: [
    CommonModule,
    MenuCallScreenRoutingModule,
    NbTabsetModule,
    NbCardModule,
    NbTabsetModule,
    NbIconModule,
    NbListModule,
    NbBadgeModule,
    NbDialogModule,
    NbButtonModule,
    NbSpinnerModule,
    NbTooltipModule,
    NbSelectModule,
    NbInputModule,
    SharedModule,
    NbDatepickerModule,
    RemovalManagementModule,
    FormsModule
  ]
})
export class MenuCallScreenModule { }
