import { DatePipe } from '@angular/common';
import { Component, OnInit, TemplateRef, ChangeDetectorRef, ViewChild } from '@angular/core';
import { UntypedFormControl } from '@angular/forms';
import { NbDialogRef, NbDialogService, NbToastRef, NbToastrService } from '@nebular/theme';
import * as moment from 'moment';
import { AuthorizationRemoval, GroupesOrders } from 'src/app/shared/models/authorization-removal';
import { RenderTypeValueLibrary, StatusAESLibrary } from 'src/app/shared/models/render-type.enum';
import { GetStatusRemovalsPipe } from 'src/app/shared/pipes/get-status-removals.pipe';
import { CommonService } from 'src/app/shared/services/common.service';
import { RemovalService } from '../removal-management/removal.service';
import { CartItem } from 'src/app/shared/models/cart';
import { Order } from 'src/app/shared/models/order';
import { PaginationConfig } from 'src/app/shared/services/pagination.service';

@Component({
  selector: 'clw-loaded-truck',
  templateUrl: './loaded-truck.component.html'
})
export class LoadedTruckComponent implements OnInit {

  @ViewChild('weightExceedsToleranceDialog', { static: false })
  weightExceedsToleranceDialogTemplate: TemplateRef<any>;

  formControl = new UntypedFormControl(new Date());
  dialogRef: NbDialogRef<any>;
  detailDetailRef: NbDialogRef<any>;
  dialogExport: NbDialogRef<any>;
  confirmationDialogRef: NbDialogRef<any>;
  finalTruckTonnage: number;
  initialTruckTonnage: number;
  exceededWeight: string;

  removals: any[];
  finalWeightRemovals: any[];

  loading = false;

  getStatusRemovalsPipe = new GetStatusRemovalsPipe();

  AE: any;
  Product: any;
  carrierLabel: any;
  removal: AuthorizationRemoval;

  aeCarriers: any;
  code = false;
  selectBtn: any;
  datePlusthree: any;
  datePlusfive: any;
  Orderstatus: any;
  isLoading: boolean;

  offset: number = 0;
  limit: number = 20;
  startDate: Date;
  endDate: Date;
  endIndex: number = 20;
  startIndex: number = 1;
  total: number = 0;
  rangeFilter: any;

  // Configuration de la pagination pour l'onglet "Camion non pesée"
  unweighedConfig: PaginationConfig = {
    currentPage: 1,
    itemsPerPage: 20,
    totalItems: 0,
    maxSize: 10
  };

  // Configuration de la pagination pour l'onglet "Camion Sortie"
  exitedConfig: PaginationConfig = {
    currentPage: 1,
    itemsPerPage: 20,
    totalItems: 0,
    maxSize: 10
  };

  // Pour la compatibilité avec le code existant
  paginationConfig: PaginationConfig;

  // Configuration de pagination dynamique qui change selon l'onglet actif
  currentPaginationConfig: PaginationConfig;

  // Onglet actuellement actif
  activeTab: string = 'Camion non pesée';

  statusLabels: any = [];
  filteredStatus$: any;
  AELabels: any = [];
  filteredAE$: any;
  filteredProducts$: any;
  filteredCarrier$: any;
  filterProductLabels: any[] = [];
  ProductLabels: any[] = [];
  CarrierLabels: any[] = [];
  dataOrderTypeLabels: any[] = [];
  carriers: any;


  filterForm = {
    status: 500,
    LoadNumber: '',
    ItemNumber: '',
    company: '',
    shipping: '',
    carrierLabel: '',
    OrderType: '',
    startDate: moment().startOf('year').format('MM-DD-YYYY'),
    endDate: moment().endOf('year').format('MM-DD-YYYY')
  }
  currentRemoval: AuthorizationRemoval;
  dataForFilter: any;
  dataAELabels: any = [];
  dataProducts: any = [];
  dataSoldToDescLabels: any = [];
  dataShipToDescLabels: any = [];
  dataTransporters: any = [];


  constructor(
    private datePipe: DatePipe,
    protected commonSrv: CommonService,
    private dialogSvr: NbDialogService,
    private removalSvr: RemovalService,
    private toastrSvr: NbToastrService,
    private cdr: ChangeDetectorRef
  ) { }

  async ngOnInit(): Promise<void> {
    // Initialiser les configurations de pagination pour chaque onglet
    this.unweighedConfig = {
      currentPage: 1,
      itemsPerPage: this.limit,
      totalItems: 0,
      maxSize: 10
    };

    this.exitedConfig = {
      currentPage: 1,
      itemsPerPage: this.limit,
      totalItems: 0,
      maxSize: 10
    };

    // Utiliser unweighedConfig comme configuration par défaut pour la compatibilité
    this.paginationConfig = this.unweighedConfig;

    // Initialiser la configuration de pagination actuelle avec l'onglet par défaut
    this.currentPaginationConfig = this.unweighedConfig;

    // Forcer la détection des changements pour éviter les problèmes d'affichage
    this.cdr.detectChanges();

    await this.getAuthorization();
    await this.getDataForFilter();
  }

  FreightHandlingCode(HandlingCode: any) {
    return RenderTypeValueLibrary[HandlingCode];
  }

  async getDataForFilter() {
    try {
      this.loading = true;
      this.dataForFilter = await this.removalSvr.getFilterData({
        ...this.filterForm,
        keyForFilters: ['LoadNumber', 'ItemNumber', 'carrier.label', 'ShipToDescription', 'SoldToDescription', 'OrderType']
      },);
      this.dataAELabels = this.dataForFilter?.dataLoadNumber ?? [];
      this.dataTransporters = this.dataForFilter?.datacarrierlabel ?? [];
      this.dataShipToDescLabels = this.dataForFilter?.dataShipToDescription ?? [];
      this.dataProducts = this.dataForFilter?.dataItemNumber ?? [];
      this.dataSoldToDescLabels = this.dataForFilter?.dataSoldToDescription ?? [];
      this.dataOrderTypeLabels = this.dataForFilter?.dataOrderType ?? [];
    } catch (error) {
      return error;
    } finally { this.loading = false }
  }

  async getAuthorization(): Promise<void> {
    try {
      this.isLoading = true;

      // Récupérer les camions non pesés (finalTruckTonnage n'existe pas)
      const unweighedOptions = {
        limit: this.unweighedConfig.itemsPerPage,
        offset: (this.unweighedConfig.currentPage - 1) * this.unweighedConfig.itemsPerPage,
        ...this.filterForm,
        finalTruckTonnage: JSON.stringify({ $exists: false })
      };

      const { count: unweighedCount, data: unweighedData } = await this.removalSvr.getAuthorizationRemoval(unweighedOptions);
      this.removals = unweighedData;
      this.removals?.forEach((removal: AuthorizationRemoval) => {
        const result = this.calculateTotalEmptyBagWeight(removal?.groupedOrders);
        removal['tolerance'] = Math.round(result * 1000) / 1000; // Rounds to 3 decimal places
      });

      // Mettre à jour la configuration de pagination pour l'onglet "Camion non pesée"
      this.unweighedConfig = {
        currentPage: this.unweighedConfig.currentPage,
        itemsPerPage: this.unweighedConfig.itemsPerPage,
        totalItems: unweighedCount,
        maxSize: 10
      };

      // Récupérer les camions pesés (finalTruckTonnage existe)
      const exitedOptions = {
        limit: this.exitedConfig.itemsPerPage,
        offset: (this.exitedConfig.currentPage - 1) * this.exitedConfig.itemsPerPage,
        ...this.filterForm,
        finalTruckTonnage: JSON.stringify({ $exists: true })
      };

      const { count: exitedCount, data: exitedData } = await this.removalSvr.getAuthorizationRemoval(exitedOptions);
      this.finalWeightRemovals = exitedData;

      // Mettre à jour la configuration de pagination pour l'onglet "Camion Sortie"
      this.exitedConfig = {
        currentPage: this.exitedConfig.currentPage,
        itemsPerPage: this.exitedConfig.itemsPerPage,
        totalItems: exitedCount,
        maxSize: 10
      };

      // Mettre à jour la configuration principale en fonction de l'onglet actif
      if (this.activeTab === 'Camion non pesée') {
        this.total = unweighedCount;
        this.paginationConfig = this.unweighedConfig;
        this.currentPaginationConfig = this.unweighedConfig;

        // Pour la compatibilité avec le code existant
        this.endIndex = Math.min((this.unweighedConfig.currentPage * this.unweighedConfig.itemsPerPage), unweighedCount);
        this.startIndex = unweighedCount === 0 ? 0 : (this.unweighedConfig.currentPage - 1) * this.unweighedConfig.itemsPerPage + 1;

        // Vérification si la page actuelle est valide
        const totalPages = Math.ceil(unweighedCount / this.unweighedConfig.itemsPerPage);
        if (this.unweighedConfig.currentPage > totalPages && totalPages > 0) {
          this.unweighedConfig = {
            currentPage: totalPages,
            itemsPerPage: this.unweighedConfig.itemsPerPage,
            totalItems: unweighedCount,
            maxSize: 10
          };
          this.paginationConfig = this.unweighedConfig;
          this.currentPaginationConfig = this.unweighedConfig;
          this.cdr.detectChanges();
          await this.getAuthorization();
          return;
        }
      } else {
        this.total = exitedCount;
        this.paginationConfig = this.exitedConfig;
        this.currentPaginationConfig = this.exitedConfig;

        // Pour la compatibilité avec le code existant
        this.endIndex = Math.min((this.exitedConfig.currentPage * this.exitedConfig.itemsPerPage), exitedCount);
        this.startIndex = exitedCount === 0 ? 0 : (this.exitedConfig.currentPage - 1) * this.exitedConfig.itemsPerPage + 1;

        // Vérification si la page actuelle est valide
        const totalPages = Math.ceil(exitedCount / this.exitedConfig.itemsPerPage);
        if (this.exitedConfig.currentPage > totalPages && totalPages > 0) {
          this.exitedConfig = {
            currentPage: totalPages,
            itemsPerPage: this.exitedConfig.itemsPerPage,
            totalItems: exitedCount,
            maxSize: 10
          };
          this.paginationConfig = this.exitedConfig;
          this.currentPaginationConfig = this.exitedConfig;
          this.cdr.detectChanges();
          await this.getAuthorization();
          return;
        }
      }

      // Forcer la détection des changements
      this.cdr.detectChanges();
    }
    catch (error) {
      console.log(error);
      this.toastrSvr.danger('Impossible de récupérer la liste des camions chargés', 'Erreur connexion !');
    } finally {
      this.isLoading = false;
    }
  }

  async reset() {
    this.filterForm = {
      status: 500,
      LoadNumber: '',
      ItemNumber: '',
      company: '',
      shipping: '',
      carrierLabel: '',
      OrderType: '',
      startDate: moment().startOf('year').format('MM-DD-YYYY'),
      endDate: moment().endOf('year').format('MM-DD-YYYY')
    }

    // Réinitialiser les configurations de pagination
    this.unweighedConfig = {
      currentPage: 1,
      itemsPerPage: this.limit,
      totalItems: 0,
      maxSize: 10
    };

    this.exitedConfig = {
      currentPage: 1,
      itemsPerPage: this.limit,
      totalItems: 0,
      maxSize: 10
    };

    // Mettre à jour la configuration principale et actuelle
    if (this.activeTab === 'Camion non pesée') {
      this.paginationConfig = this.unweighedConfig;
      this.currentPaginationConfig = this.unweighedConfig;
    } else {
      this.paginationConfig = this.exitedConfig;
      this.currentPaginationConfig = this.exitedConfig;
    }

    // Forcer la détection des changements
    this.cdr.detectChanges();

    await this.getAuthorization();
    await this.getDataForFilter();
  }
  getConcatenatedLabels(groupedOrders: GroupesOrders): string {
     return groupedOrders?.map((order: Order) =>
       order?.cart?.items
         .filter(item => item?.quantityDelivery > 0)
         .map(item => `${item?.product?.label} (${item?.packaging?.label})`)
         .join(', ')
     ).join(', ') || 'N/A';
   }


  onModelAEChange(value: any): void {
    if (value == null)
      this.dataAELabels = this.dataForFilter?.dataLoadNumber
    if (value) {
      this.loading = true;
      this.dataAELabels = this.dataForFilter?.dataLoadNumber.filter((data: number) => JSON.stringify(data).includes(value)).sort((a, b) => {
        return a.toString().toLowerCase().indexOf(value.toLowerCase()) - b.toString().toLowerCase().indexOf(value.toLowerCase());
      });
      this.loading = false;
    }
  }

  onModelCompanyChange(value: any): void {
    if (value == null)
      this.dataSoldToDescLabels = this.dataForFilter?.dataSoldToDescription;

    if (value)
      return this.dataSoldToDescLabels = this.dataForFilter?.dataSoldToDescription.filter((data: string) =>
        data.toLowerCase().includes(value.toLowerCase())).sort((a, b) => {
          return a.toString().toLowerCase().indexOf(value.toLowerCase()) - b.toString().toLowerCase().indexOf(value.toLowerCase());
        });
  }

  onModelDeliveryPointChange(value: any): void {
    if (value == null)
      this.dataShipToDescLabels = this.dataForFilter?.dataShipToDescription;

    return this.dataShipToDescLabels = this.dataForFilter?.dataShipToDescription.filter((data: string) =>
      data.toLowerCase().includes(value.toLowerCase())).sort((a, b) => {
        return a.toString().toLowerCase().indexOf(value.toLowerCase()) - b.toString().toLowerCase().indexOf(value.toLowerCase());
      });
  }

  async onSelectionAEChange(event: any, keyToReset: string): Promise<void> {
    if (event === 'reset' && keyToReset) {
      this.filterForm[keyToReset] = '';
    }
    if (event)
      await this.getAuthorization();
  }

  onModelProductsChange(value: any): void {
    if (value == null)
      this.dataProducts = this.dataForFilter?.dataItemNumber;

    if (value)
      this.dataProducts = this.dataForFilter?.dataItemNumber.filter((data: string) => data.toLowerCase().includes(value.toLowerCase())).sort((a, b) => {
        return a.toString().toLowerCase().indexOf(value.toLowerCase()) - b.toString().toLowerCase().indexOf(value.toLowerCase());
      });;
  }

  onModelCarrierChange(value: any): void {
    if (value === null)
      this.dataTransporters = this.dataForFilter?.datacarrierlabel;

    if (value)
      this.dataTransporters = this.dataForFilter?.datacarrierlabel?.filter((data: string) =>
        data.toLowerCase().includes(value?.toLowerCase())).sort((a, b) => {
          return a.toString().toLowerCase().indexOf(value?.toLowerCase()) - b.toString().toLowerCase().indexOf(value?.toLowerCase());
        });
  }

  onModelTypeChange(value: any): void {
    if (value == null)
      this.dataOrderTypeLabels = this.dataForFilter?.dataOrderType;

    return this.dataOrderTypeLabels = this.dataForFilter?.dataOrderType?.filter((data: string) =>
      data?.toLowerCase().includes(value?.toLowerCase())).sort((a, b) => {
        return a.toString().toLowerCase().indexOf(value?.toLowerCase()) - b.toString().toLowerCase().indexOf(value?.toLowerCase());
      });;
  }

  truncate(desc: string, length: number): string {
    if (!desc) { return ''; }
    return this.commonSrv.truncateString(desc, length);
  }

  /**
   * Méthode appelée lorsque l'utilisateur change de page dans l'onglet "Camion non pesée"
   * @param page Numéro de la page sélectionnée
   */
  onUnweighedPageChange(page: number): void {
    if (this.isLoading) return; // Éviter les requêtes multiples pendant le chargement

    // Créer une nouvelle instance de PaginationConfig pour forcer la détection des changements
    this.unweighedConfig = {
      currentPage: page,
      itemsPerPage: this.limit,
      totalItems: this.total,
      maxSize: 10
    };

    // Mettre à jour la configuration principale pour la compatibilité
    this.paginationConfig = this.unweighedConfig;

    // Mettre à jour la configuration actuelle si c'est l'onglet actif
    if (this.activeTab === 'Camion non pesée') {
      this.currentPaginationConfig = this.unweighedConfig;
    }

    // Forcer la détection des changements
    this.cdr.detectChanges();

    this.getAuthorization();
  }

  /**
   * Méthode appelée lorsque l'utilisateur change le nombre d'éléments par page dans l'onglet "Camion non pesée"
   * @param itemsPerPage Nombre d'éléments par page sélectionné
   */
  onUnweighedItemsPerPageChange(itemsPerPage: number): void {
    // Mettre à jour la limite pour la compatibilité avec le code existant
    this.limit = itemsPerPage;

    // Créer une nouvelle instance de PaginationConfig pour forcer la détection des changements
    this.unweighedConfig = {
      currentPage: 1, // Revenir à la première page
      itemsPerPage: itemsPerPage,
      totalItems: this.total,
      maxSize: 10
    };

    // Mettre à jour la configuration principale pour la compatibilité
    this.paginationConfig = this.unweighedConfig;

    // Mettre à jour la configuration actuelle si c'est l'onglet actif
    if (this.activeTab === 'Camion non pesée') {
      this.currentPaginationConfig = this.unweighedConfig;
    }

    // Forcer la détection des changements
    this.cdr.detectChanges();

    this.getAuthorization();
  }

  /**
   * Méthode appelée lorsque l'utilisateur change de page dans l'onglet "Camion Sortie"
   * @param page Numéro de la page sélectionnée
   */
  onExitedPageChange(page: number): void {
    if (this.isLoading) return; // Éviter les requêtes multiples pendant le chargement

    // Créer une nouvelle instance de PaginationConfig pour forcer la détection des changements
    this.exitedConfig = {
      currentPage: page,
      itemsPerPage: this.limit,
      totalItems: this.total,
      maxSize: 10
    };

    // Mettre à jour la configuration principale pour la compatibilité
    this.paginationConfig = this.exitedConfig;

    // Mettre à jour la configuration actuelle si c'est l'onglet actif
    if (this.activeTab === 'Camion Sortie') {
      this.currentPaginationConfig = this.exitedConfig;
    }

    // Forcer la détection des changements
    this.cdr.detectChanges();

    this.getAuthorization();
  }

  /**
   * Méthode appelée lorsque l'utilisateur change le nombre d'éléments par page dans l'onglet "Camion Sortie"
   * @param itemsPerPage Nombre d'éléments par page sélectionné
   */
  onExitedItemsPerPageChange(itemsPerPage: number): void {
    // Mettre à jour la limite pour la compatibilité avec le code existant
    this.limit = itemsPerPage;

    // Créer une nouvelle instance de PaginationConfig pour forcer la détection des changements
    this.exitedConfig = {
      currentPage: 1, // Revenir à la première page
      itemsPerPage: itemsPerPage,
      totalItems: this.total,
      maxSize: 10
    };

    // Mettre à jour la configuration principale pour la compatibilité
    this.paginationConfig = this.exitedConfig;

    // Mettre à jour la configuration actuelle si c'est l'onglet actif
    if (this.activeTab === 'Camion Sortie') {
      this.currentPaginationConfig = this.exitedConfig;
    }

    // Forcer la détection des changements
    this.cdr.detectChanges();

    this.getAuthorization();
  }

  // Méthodes pour la compatibilité avec le code existant
  onPageChange(page: number): void {
    // Déterminer quelle configuration est actuellement active
    if (this.paginationConfig === this.unweighedConfig) {
      this.onUnweighedPageChange(page);
    } else {
      this.onExitedPageChange(page);
    }
  }

  onItemsPerPageChange(itemsPerPage: number): void {
    // Déterminer quelle configuration est actuellement active
    if (this.paginationConfig === this.unweighedConfig) {
      this.onUnweighedItemsPerPageChange(itemsPerPage);
    } else {
      this.onExitedItemsPerPageChange(itemsPerPage);
    }
  }

  // Méthodes de compatibilité avec l'ancien code
  async previousPage(): Promise<any> {
    this.onPageChange(this.paginationConfig.currentPage - 1);
  }

  async nextPage(): Promise<any> {
    this.onPageChange(this.paginationConfig.currentPage + 1);
  }

  /**
   * Méthode appelée lors du changement d'onglet
   * @param event Événement de changement d'onglet
   */
  onTabChange(event: any): void {
    // Mettre à jour l'onglet actif
    this.activeTab = event.tabTitle;

    // Mettre à jour la configuration active en fonction de l'onglet sélectionné
    if (event.tabTitle === 'Camion non pesée') {
      this.paginationConfig = this.unweighedConfig;
      this.currentPaginationConfig = this.unweighedConfig;
    } else if (event.tabTitle === 'Camion Sortie') {
      this.paginationConfig = this.exitedConfig;
      this.currentPaginationConfig = this.exitedConfig;
    }

    // Forcer la détection des changements
    this.cdr.detectChanges();
  }

  /**
   * Méthodes unifiées pour la pagination dans la barre horizontale
   */
  onCurrentPageChange(page: number): void {
    if (this.activeTab === 'Camion non pesée') {
      this.onUnweighedPageChange(page);
    } else if (this.activeTab === 'Camion Sortie') {
      this.onExitedPageChange(page);
    }
  }

  onCurrentItemsPerPageChange(itemsPerPage: number): void {
    if (this.activeTab === 'Camion non pesée') {
      this.onUnweighedItemsPerPageChange(itemsPerPage);
    } else if (this.activeTab === 'Camion Sortie') {
      this.onExitedItemsPerPageChange(itemsPerPage);
    }
  }

  isEnabled(removal?: AuthorizationRemoval) {
    const data = removal || this.currentRemoval;
    return data?.enable == false ? false : true;
  }

  async openDetailModal(dailog?: any, removal?: any): Promise<void> {
    this.removal = removal;
    this.detailDetailRef = this.dialogSvr.open(dailog, {});
  }

  openSecondScaleModal(dialog: TemplateRef<any>, removal: AuthorizationRemoval) {
    this.currentRemoval = removal;
    this.dialogRef = this.dialogSvr.open(dialog);
  }

  async saveTruckSecondScale() {
    try {
      this.isLoading = true;
      if (!this.finalTruckTonnage) {
        this.toastrSvr.danger(`Veuillez renseigner la tonnage finale`, 'Données incorrectes !');
        return;
      }

      const productWeightInKg = this.commonSrv.convertWeight(Number(this.currentRemoval.TransactionQuantity), 'kg');
      const currentWeight = (productWeightInKg ?? 0) + (this.currentRemoval.initialTruckTonnage ?? 0);
      const emptyBagsWeight = this.calculateTotalEmptyBagWeight(this.currentRemoval?.groupedOrders);
      console.log('emptyBagsWeight', emptyBagsWeight);

      if (this.finalTruckTonnage > currentWeight + emptyBagsWeight) {
        const exceededWeight = this.finalTruckTonnage - (currentWeight + emptyBagsWeight);
        this.exceededWeight = exceededWeight.toFixed(2);

        // Au lieu de bloquer, on affiche une boîte de dialogue de confirmation
        this.isLoading = false;
        this.openWeightExceedsToleranceDialog();
        return;
      }

      // Si le poids est dans la tolérance, on procède directement
      await this.finalizeTruckExit();
    } catch (error) {
      this.toastrSvr.danger(`Une erreur est survenu lors de la mise a jour de ce BC`, 'Erreur de mise à jour')
    } finally {
      this.isLoading = false;
    }
  }

  openWeightExceedsToleranceDialog() {
    // Fermer la première modale avant d'ouvrir la seconde
    this.dialogRef.close();

    // Ouvrir la modale de confirmation
    const confirmDialogRef = this.dialogSvr.open(this.weightExceedsToleranceDialogTemplate);

    confirmDialogRef.onClose.subscribe(async (confirmed: boolean) => {
      if (confirmed) {
        // L'utilisateur a confirmé malgré le dépassement de poids
        await this.finalizeTruckExit();
      }
    });
  }

  async finalizeTruckExit() {
    try {
      this.isLoading = true;
      const loaderDate = this.commonSrv.getDurationDate(this.currentRemoval?.departureTime);
      const loaderTime = this.commonSrv.convertDurationToTimestamp(loaderDate);

      await this.removalSvr.updateAe(this.currentRemoval?._id, { finalTruckTonnage: this.finalTruckTonnage, loaderTime });
      await this.getAuthorization();
      this.toastrSvr.success(`Ce BC a été mise à jour avec succès`, `Mise à jour réussi`);
      // Ne pas essayer de fermer la première modale car elle est déjà fermée
    } catch (error) {
      this.toastrSvr.danger(`Une erreur est survenu lors de la mise a jour de ce BC`, 'Erreur de mise à jour');
    } finally {
      this.isLoading = false;
    }
  }



  calculateTotalEmptyBagWeight(groupOrders: GroupesOrders): number {
    const bagWeights: { [key: number]: number } = {
      50: 0.112, // 50kg bag ~ 800g
      25: 0.076, // 25kg bag ~ 500g
      5: 0.041  // 5kg bag ~ 200g
    };

    let totalWeightInKG = 0;

    for (const order of groupOrders) {

      for( const item of order?.cart?.items) {
      const bagSize = item?.packaging?.unit?.value;
      const bagWeight = bagWeights[bagSize] || 0;
      totalWeightInKG += (item?.quantity ?? 0) * bagWeight;
      }
    }

    return totalWeightInKG;
  }


  openDialogExport(dailog: any): void {
    this.dialogExport = this.dialogSvr.open(dailog, {});
  }

  async exportAeToExcel(): Promise<void | NbToastRef> {
    if (!this.startDate || !this.endDate) {
      return this.toastrSvr.danger('Veuillez renseigner une date de début et de fin', 'Donnés incorrectes !');
    }

    if (this.startDate > this.endDate) {
      return this.toastrSvr.danger('Veuillez renseigner une date de début inférieure à celle de la date de fin', 'Donnés incorrectes !');
    }

    const options = {
      startDate: moment(this.startDate).format('MM-DD-YYYY'),
      endDate: moment(this.endDate).format('MM-DD-YYYY'),
      LoadNumber: this.AE,
      ItemNumber: this.Product,
      carrierLabel: this.carrierLabel,
      status: 500,
    }
    try {

      this.isLoading = true;
      let dataAElist = (await this.removalSvr.getAuthorizationRemoval(options)).data;
      dataAElist = dataAElist.map((elt) => {
        const data = {};
        data['N° AE'] = elt?.LoadNumber;
        data['PRODUIT'] = elt?.ItemNumber;
        data['STATUT'] = this.getStatusRemovalsPipe.transform(elt?.enable == false ? 0 : elt?.status);
        data['STATUT JDE'] = StatusAESLibrary[elt?.LoadStatus];
        data['N° COMMANDE'] = elt?.appReference;
        data['N° DE CHARGEMENT'] = elt?.ShipmentNumber;
        data['QUANTITÉ'] = elt?.TransactionQuantity;
        data['CODE_ADRESSE'] = elt?.ShipToDescription;
        data['CODE_CLIENT'] = elt?.SoldToDescription;
        data['DATE CRÉATION'] = this.datePipe.transform(elt?.dates?.created, 'dd/MM/YYYY');
        return data;
      });

      this.removalSvr.exportAEAsExcelFile(dataAElist, 'Liste_AES_TRANSPORT_VALIDEE');
      this.dialogExport.close();
    } catch (error) {
      this.toastrSvr.danger(error.error.message, 'ERREUR!');
      return error;
    } finally { this.isLoading = false; }
  }

}
