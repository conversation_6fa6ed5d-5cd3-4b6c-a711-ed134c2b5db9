import { NbIconModule, NbInputModule, NbButtonModule, NbCardModule, NbSpinnerModule, NbTooltipModule, NbToastrModule, NbSelectModule, NbAutocompleteModule } from '@nebular/theme';
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { StoresRoutingModule } from './stores-routing.module';
import { StoresComponent } from './stores.component';
import { FormsModule } from '@angular/forms';


@NgModule({
  declarations: [
    StoresComponent
  ],
  imports: [
    CommonModule,
    StoresRoutingModule,
    FormsModule,
    NbIconModule,
    NbInputModule,
    NbButtonModule,
    NbCardModule,
    NbSpinnerModule,
    NbTooltipModule,
    NbToastrModule,
    NbSelectModule,
    NbAutocompleteModule,
    NbInputModule,
    
  ]
})
export class StoresModule { }
