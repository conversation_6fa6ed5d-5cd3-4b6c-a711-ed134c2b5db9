import { RenderTypeValue } from './../../../shared/models/render-type.enum';
import { Component, OnInit } from '@angular/core';
import { MenuReportingService } from '../menu-reporting.service';

@Component({
  selector: 'clw-tile-aes-render',
  templateUrl: './tile-aes-render.component.html',
  styles: [
  ]
})
export class TileAesRenderComponent implements OnInit {

  isLoading: boolean;
  data: any;

  constructor(
    private reportingSrv: MenuReportingService
  ) { }

  async ngOnInit() {
    this.refresh();
  }

  async refresh() {
    this.isLoading = true;
    try {
      const query = {
        FreightHandlingCode: RenderTypeValue.RENDER
      }
      this.data = await this.reportingSrv.getStatusAEs(query);
      this.isLoading = false;
    } catch (error) {
      console.log(error);
      this.isLoading = false;
    }
  }
}
