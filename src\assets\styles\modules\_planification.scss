.planification-container {
    .cards {
        display: flex;
        justify-content: space-between;
        nb-card {
            width: 48%;
            height: 538px;
            .card-container {
                padding: 3%;

                .header-title {
                    display: flex;
                    justify-content: space-between;
                    padding: 1rem 1.5rem;

                    .button {
                        display: flex;
                        align-items: center;

                        button {
                            min-width: 100px;
                            display: flex;
                            justify-content: space-evenly;
                            font-size: 14px;
                        }
                    }
                }
            }

           .footer{
            font-weight: 600;
            display: flex;
            justify-content: flex-end;
            font-size: 13px;
           }
        }
    }

    .no-border{
        border: none !important;
    }
    .nothing-container{
        display: flex;
        justify-content: center;
    }
    .types-order {
        display: flex;
        justify-content: space-between;
        flex-direction: column;
    }

    .form-row{
        display: flex;
        justify-content: space-between;
    }

    .form-group {
        margin-top: 10px;
        .input {
            width: 100%;
            margin-top: 2px;
            nb-select {
                max-width: 100% !important;
                width: 100%;
            }
        }
        .input-qty {
            width: 100%;
            input {
                display: flex;
                justify-content: flex-end;
            }
        }
    }

    .order-item {
        width: 45%;
    }
    .order-qty {
        width: 15%;
    }
    label {
        margin-bottom: 2px;
        color: #222222;
        font-weight: normal;
        font-size: 14px;
    }

    h4 {
        color: #848384;
        font-size: 14px;
        margin-bottom: 10px;
        letter-spacing: 1.2px;
        font-weight: normal;
        font-family: "Lato-Regular";
    }
    .product-side {
        nb-list-item {
            padding-left: 2.5rem;
            padding-top: 0 !important;
            padding-bottom: 0 !important;
            padding-right: 0 !important;
        }

        .margin{
            margin-right: 15px;
        }

        .button {
            display: flex;
            margin-top: 23px;
            padding: 0;

            nb-icon {
                width: 24px;
            }
        }
    }
    .list-elt{
        .col{
            font-size: 13px;
        }
        .col-date-ref {
            width: 30%;
        }
        .col-qty{
            width: 20%;
        }
        .col-quarter{
            width: 20%;
        }
        .col-lorries{
            width: 30%;
        }
        .col-lorries{
            width: 30%;
        }
        .col-action-ref{
            width: 10%;
        }

    }

    .form-footer {
        display: flex;
        justify-content: space-between;
        padding: 1rem 5.5rem;
    }

    button {
        text-transform: none !important;
    }
    h2{
        font-size: 13px !important;
        font-weight: 600 !important;
    }
    .form-footer {
        width: 100%;
        display: flex;
        justify-content: center;
        margin:  10px 0;
        button {
         margin: 0 30px;
         max-width: 120px;
        }
      }


}

.del-dialog-container {
    nb-card {
        width: 380px;
        h1 {
            font-style: normal;
            font-weight: normal;
            font-size: 12px;
            line-height: 16px;
            color: #000000;
        }

        nb-card-footer{
            display: flex;
            justify-content: space-between;
        }
    }
}
