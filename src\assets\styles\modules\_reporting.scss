.reporting-container {

    //   padding: 0px 10%;
    //   min-width: 1303px;
    .filter-container {
        height: 45px;
        gap: 1rem;

        .left-block {
            @include v-center;
            width: 100%;

            .filter-label {
                margin-right: 15px;
            }

            .filter-elt {
                margin-right: 15px;

                .filter-elt input date {
                    margin-bottom: 0px !important;
                }
            }
        }

        .filter-elt.select {
            width: 20%;
            max-width: 220px;
        }

        .filter-elt.date {
            width: 13%;
            max-width: 130px;
        }

        .filter-elt.input {
            width: 17%;
            min-width: 100px;

            input {
                width: 100%;
                padding-top: 3px;
                padding-bottom: 3px;
                box-sizing: border-box;
            }
        }
    }

    .reporting.row {
        height: 410px;
        margin: 0px;
        display: flex;
        justify-content: space-between;
    }

    .line2 {
        height: 400px;
    }

    .height {
        height: 410px;
    }

    .reporting.row .tile {
        height: 320px;

        &.col-1 {
            width: calc(25% - 11.25px);
        }

        &.ae-charge {
            width: 74%;
        }

        &.col-2 {
            width: calc(50% - 7.5px);
        }

        &.col-3 {
            width: calc(25% - 7.5px);
        }
    }

    .graphics {
        height: 450px;
    }
}