import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MenuRemovalRoutingModule } from './menu-removal-routing.module';
import { HeaderDeliveryComponent } from './components/header-delivery/header-delivery.component';
import { StatusFilterComponent } from './components/status-filter/status-filter.component';
import { OrderCartComponent } from './components/order-cart/order-cart.component';
import { ImportSectionComponent } from './components/import-section/import-section.component';


@NgModule({
  declarations: [],
  imports: [
    CommonModule,
    MenuRemovalRoutingModule,
  ]
})
export class MenuRemovalModule { }
