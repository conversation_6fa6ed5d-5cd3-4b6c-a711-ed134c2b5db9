import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'getImgStyle'
})
export class GetImgStylePipe implements PipeTransform {

  transform(imgUrl?: string, defaultImge?: string): unknown {
    const image = imgUrl ? imgUrl : defaultImge;
    return {
      'background-image': `url('${image}')`,
      'background-repeat': 'no-repeat',
      'background-position': 'center',
      'background-size': 'contain',
    };
  }

}
