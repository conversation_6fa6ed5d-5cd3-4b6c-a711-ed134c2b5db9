import { Component, OnInit } from '@angular/core';
import { MenuReportingService } from '../menu-reporting.service';

@Component({
  selector: 'clw-tile-carriers',
  templateUrl: './tile-carriers.component.html',
  styles: [
  ]
})
export class TileCarriersComponent implements OnInit {
  isLoading: boolean;
  data: any[];
  reponseTimedata: any;
  deliveryTimedata: any;

  constructor(
    private reportingSrv: MenuReportingService
  ) { }

  async ngOnInit() {
    await this.refresh();
  }

  async refresh() {
    this.isLoading = true;
    try {
      this.data = await this.reportingSrv.getCarriersRanking();
      this.isLoading = false;
    } catch (error) {
      console.log(error);
      this.isLoading = false;
    }
  }

}
