FROM node:18-alpine AS builder

ARG build_environment_command='build:ssr'

COPY package*.json /tmp/

RUN cd /tmp && npm i --legacy-peer-deps && npm i -g typescript @angular/cli

COPY . /tmp/

RUN cd /tmp && npm run $build_environment_command



# FROM node:14.16.0-alpine3.11
FROM node:18-alpine AS production

USER root

# Expose the port the app runs in
EXPOSE 4000

RUN NODE_PATH=/usr/lib/node_modules

RUN mkdir -p /usr/src/app

WORKDIR /tmp

ADD package.json ./package.json

COPY ./ssl /tmp/ssl

COPY --from=builder /tmp/dist/cimencam-logistique-web/server /tmp/dist/cimencam-logistique-web/server

COPY --from=builder /tmp/dist/cimencam-logistique-web/browser /tmp/dist/cimencam-logistique-web/browser

CMD npm run serve:ssr
