import { OrderCancellationRequestStatus } from "../enums/orderCancel.enum";
import { Cart } from "./cart.model";
import { Removal } from "./removal.model";
import { Company, User } from "./user";

export class Order {
  _id?: string;
  appReference?: string;
  payment?: Payment;
  cart?: Cart;
  status?: OrderStatus;
  carrier?: any;
  statusDelivery?: OrderStatusDelivery;
  customerReference?: string;
  erpReference?: string;
  customerDeliveryDestination?: string;
  user?: Partial<User>;
  company?: Partial<Company>;
  dates?: {
    created: number;
    paid?: number;
    validated?: number;
  };
  created_at?: number;
  removals: Removal[];
  canceledJdeNumbers?: string[];
  messageCancellation?: string;
  cancellationStatus?: OrderCancellationRequestStatus;
  erpError: unknown;
  rejectReason?: string;
  cancelReason?: string;
  nberModif?: string;

}

export enum OrderStatus {
  CREATED = 100,
  PAID = 200,
  VALIDATED = 300,
  FAILD = 400,
  CREDIT_REJECTED = 99,
  CREDIT_IN_VALIDATION = 101,
  CREDIT_IN_AWAIT_VALIDATION = 102,
  CANCELLED = 500,
  RETREIVED = 600,
}
export enum OrderStatusDelivery {
    WAITING = 100,
    PROCESS = 200,
    COMPLETED = 300,
  }

export class Payment {
  mode: {
    id: number;
    label: string;
  };
  clientOption?: {
    id: number;
    label: string;
    icon: string;
    destination: string;
  };
  data?: {
    reference: string;
    amount: number;
    bank: {
      id: number;
      label: string;
    }
  };
  tel?: number;
  processingNumber?: string | number;
  transactionId?: string;
  isQ1?: boolean;
}

export enum PaymentMode {
  ORANGE_MONEY = 0,
  MOBILE_MONEY = 1,
  M2U = 2,
  AFRILAND = 3,
  MY_ACCOUNT = 4,
  EXPRESS_EXCHANGE = 5,
  CREDIT = 6,
  VISA = 7,
  LOW_BALANCE = 8,
  EU = 9
}

export enum OrderValidation {
  CORDO_RH = 1,
  DRH = 2
}

export interface CancellationData {
  cancellationStatus: OrderCancellationRequestStatus;
  messageCancellation: string;
}

export type FilterType = 'orderNumber' | 'client' | 'product' | 'deliveryPoint';

export interface Filter {
  type: FilterType;
  value: string;
}






