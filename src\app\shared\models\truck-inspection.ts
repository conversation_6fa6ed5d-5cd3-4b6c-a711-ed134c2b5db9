export class TruckInspection {
    _id?: string;
    decision?: string;
    type?: InspectionType;
    code?: string;
    carrier?: any;
    driver?: string;
    date?: number;
    tel?: string;
    tractor?: any;
    checkList?: Check[];
    status?: boolean;
    enable?: boolean;
    user: {
        _id?: string;
        fullname?: string;
    };
    truckTonnage: number;
    dockCode?: number;
    dockTimeStart?: number;
    truckStatus?: number;
    dateTruckStartBrokenDown: number;
    images?: any[];
    driverTel?: string;
    driverName?: string;
    truckOrigin?: string;
    truckDestination?: string;
    statusJacks?: boolean;
    statusSideBoard?: boolean;
    licenseExpirationDate?: string;

}

export interface Check {
    label: string;
    state: boolean;
    comment: string;
}

export enum InspectionType {
    RENDER = 'A0',
    PICKUP = 'C9',
    TRUCK = 'C8',
}