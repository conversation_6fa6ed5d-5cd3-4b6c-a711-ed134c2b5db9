<div class="carrier-details">
  <div class="label-users-list">
    <button nbButton (click)="goBack()">
      <nb-icon icon="undo-outline" status="success"></nb-icon>
    </button>
    <h6>Détail du transporteur {{label | uppercase}}</h6>
  </div>
  <nb-card>
    <nb-card-body class="rm-padding">
      <nb-tabset>
        <nb-tab tabTitle="Informations Générales">
          <!--    <div class="block-general-infor" *ngIf="!isEdit">
            <div class="general-infor">
              <div class="carrier-label">
                <p>Raison sociale</p>
                <input nbInput readonly class="carrier-input" fieldSize="small" status="basic" type="text"
                  placeholder="Votre raison sociale" value="{{selectedCarrier?.label | uppercase}}">
              </div>
              <div class="carrier-label">
                <p>Numero Identifiant Unique</p>
                <input nbInput readonly class="carrier-input" fieldSize="small" status="basic" type="text"
                  placeholder="Votre NIU" value="{{selectedCarrier?.niu}}">
              </div>
              <div class="carrier-label">
                <p>N° Tel</p>
                <input nbInput readonly class="carrier-input" fieldSize="small" status="basic" type="text"
                  placeholder="Votre n° tel" value="{{selectedCarrier?.tel}}">
              </div>
              <div class="carrier-label">
                <p>Adresse </p>
                <input nbInput readonly class="carrier-input" fieldSize="small" status="basic" type="text"
                  placeholder="Votre e-mail" value="{{selectedCarrier?.email}}">
              </div>
              <div class="carrier-label">
                <p>Localisation</p>
                <input nbInput readonly class="carrier-input" fieldSize="small" status="basic" type="text"
                  placeholder="Votre Localisation" value="{{selectedCarrier?.localisation}}">
              </div>
            </div>
            <div class="carrier-logo" [ngStyle]="getCarrierLogo(selectedCarrier?.logo)"></div>
            <button nbButton status="basic" nbTooltip="Editer" nbTooltipPlacement="top" nbTooltipStatus="default"
              (click)="showEditDialog()">
              <nb-icon icon="edit-2-outline" status="warning" [options]="{ animation: { type: 'zoom' }}"></nb-icon>
            </button>
          </div> -->


          <div class="block-general-infor">
            <div class="general-infor">
              <div class="carrier-label">
                <p>Raison sociale</p>
                <input nbInput class="carrier" [readonly]='!isEdit' fieldSize="small" status="basic" type="text"
                  placeholder="Votre raison sociale" [(ngModel)]="selectedCarrier.label">
              </div>
              <div class="carrier-label">
                <p>Numero Identifiant Unique</p>
                <input nbInput class="carrier" [readonly]='!isEdit' fieldSize="small" status="basic" type="text"
                  placeholder="Votre NIU" [(ngModel)]="selectedCarrier.niu">
              </div>
              <div class="carrier-label">
                <p>N° X3</p>
                <input nbInput class="carrier" [readonly]='!isEdit' fieldSize="small" status="basic" type="number"
                  placeholder="Votre n° X3" [(ngModel)]="selectedCarrier.njde">
              </div>
              <div class="carrier-label">
                <p>N° Tel</p>
                <input nbInput class="carrier" [readonly]='!isEdit' fieldSize="small" status="basic" type="number"
                  placeholder="Votre n° tel" [(ngModel)]="selectedCarrier.tel">
              </div>
              <div class="carrier-label">
                <p> DeuxièmeN° Tel</p>
                <input nbInput class="carrier" [readonly]='!isEdit' fieldSize="small" status="basic" type="number"
                  placeholder="Votre n° tel" [(ngModel)]="selectedCarrier.tel1">
              </div>
              <div class="carrier-label">
                <p>Adresse </p>
                <input nbInput class="carrier" [readonly]='!isEdit' fieldSize="small" status="basic" type="text"
                  placeholder="Votre e-mail" [(ngModel)]="selectedCarrier.email">
              </div>
              <div class="carrier-label">
                <p>Deuxième Adresse </p>
                <input nbInput class="carrier" [readonly]='!isEdit' fieldSize="small" status="basic" type="text"
                  placeholder="Votre e-mail" [(ngModel)]="selectedCarrier.email1">
              </div>
              <div class="carrier-label">
                <p> Troisième Adresse</p>
                <input nbInput class="carrier" [readonly]='!isEdit' fieldSize="small" status="basic" type="text"
                  placeholder="Votre e-mail" [(ngModel)]="selectedCarrier.email2">
              </div>
              <!--  <div class="carrier-label">
                <p>Localisation</p>
                <input nbInput class="carrier" [readonly]='!isEdit' fieldSize="small" status="basic" type="text"
                  placeholder="Votre Localisation" [(ngModel)]="selectedCarrier.localisation">
              </div> -->
            </div>
            <label for="file">
              <div class="carrier-logo" [ngStyle]="getCarrierLogo(selectedCarrier.logo)"
                nbTooltip="{{(imgResultAfterCompress) ? 'Modifier votre photo de profil' : 'Ajouter une photo de profil'}}">
              </div>
            </label>
            <input type="file" id="file" style="display: none;" (change)='getFile($event)' *ngIf='isEdit'>
            <button nbButton status="basic" nbTooltip="Retour" nbTooltipPlacement="top" nbTooltipStatus="default"
              (click)="getUndo()" class="undo-btn" *ngIf='isEdit'>
              <nb-icon icon="undo-outline" status="basic" [options]="{ animation: { type: 'zoom' }}"></nb-icon>
            </button>
            <button nbButton status="basic" nbTooltip="Sauvegarder" nbTooltipPlacement="top" nbTooltipStatus="default"
              (click)="updateCarriers()" *ngIf='isEdit'>
              <nb-icon icon="save-outline" status="success" [options]="{ animation: { type: 'zoom' }}"></nb-icon>
            </button>
            <button nbButton status="basic" nbTooltip="Editer" class="undo-btn" nbTooltipPlacement="top"
              nbTooltipStatus="default" (click)="showEditDialog()" *ngIf='!isEdit'>
              <nb-icon icon="edit-2-outline" status="warning" [options]="{ animation: { type: 'zoom' }}"></nb-icon>
            </button>
            <button nbButton status="basic" nbTooltip="Supprimer" nbTooltipPlacement="top" nbTooltipStatus="default"
              (click)="deleteCarrier(deleteCarrierdialog)" *ngIf='!isEdit'>
              <nb-icon icon="trash-2-outline" status="danger" [options]="{ animation: { type: 'zoom' }}"></nb-icon>
            </button>
          </div>
        </nb-tab>

        <nb-tab tabTitle="Liste des Camions" badgeText='{{truckTotal}}' badgeStatus='success' badgePosition='top right'>
          <div class="truck-list">
            <nb-list class="element-head">
              <nb-list-item class="list-elt-header list-head">
                <div class="col col-paginator">
                  <div class="left-area">
                    <div class="filter-label">Filtrer par :</div>
                    <div class="input-auto-style">
                      <input type="text" (ngModelChange)="onModelTruckMatChange($event)" placeholder="Immatriculation"
                        fieldSize="small" nbInput class="empty-input" [nbAutocomplete]="autoComplete2"
                        [(ngModel)]="immatriculation" />
                    </div>
                    <nb-autocomplete #autoComplete2 (selectedChange)="onSelectionTruckChange($event)">
                      <nb-option *ngFor="let option of filteredTruckMat$ | async" [value]="option">{{option}}
                      </nb-option>
                    </nb-autocomplete>
                  </div>

                  <div class="btn-contain">
                    <div class="file-upload-group">
                      <label for="truckInput" class="file-upload-label"><nb-icon icon="download-outline"
                          size="tiny"></nb-icon> Importer un fichier</label>
                      <input nbButton type="file" value="" outline status="success" fieldSize="small" id="truckInput"
                        class="file-upload-input" accept=".xls,.xlsx" (change)="trucksUpload($event)">
                    </div>
                    <div class="btn-style">
                      <button nbButton outline status="success" fieldSize="small" class="refresh-btn"
                        (click)="getTruckRefresh()">
                        <nb-icon icon="refresh-outline"></nb-icon>
                        reinitialiser
                      </button>
                    </div>
                    <div class="btn-add-driver">
                      <button nbButton status="success" class="search-btn" size="small"
                        (click)='openAddTrucsModal(addTruckDialog)'>
                        <nb-icon icon="plus-square-outline"></nb-icon>
                        Ajouter un Camion
                      </button>
                    </div>

                  </div>
                </div>
              </nb-list-item>
              <nb-list-item class="list-elt-header">
                <div class="col col-num">N°</div>
                <div class="col col-ref">Référence</div>
                <div class="col col-truc-type ">Type de camion</div>
                <div class="col col-matriculation">Immatriculation</div>
                <div class="col col-carrier-ref">Ref transporteur</div>
                <div class="col col-weight">Poids</div>
                <!-- <div class="col col-vol">Volume</div>
                <div class="col col-description">Description</div> -->

                <div class="col col-action">
                  <div class="pagination">
                    {{truckStartIndex}} - {{truckEndIndex}} sur {{truckTotal}}
                    <div class="actions">
                      <nb-icon icon="arrowhead-left" status="basic" [options]="{ animation: { type: 'zoom' } }"
                        (click)="previousPage()"></nb-icon>
                      <nb-icon icon="arrowhead-right" status="basic" [options]="{ animation: { type: 'zoom' } }"
                        (click)="nextPage()"></nb-icon>
                    </div>
                  </div>
                  <div class="action-icons"></div>
                </div>
              </nb-list-item>
            </nb-list>
            <nb-list class="element-head scrool-style" [nbSpinner]="isLoading" nbSpinnerStatus="primary">
              <nb-list-item class="list-elt" *ngFor="let truck of trucks; index as i">
                <div class="col col-num">{{i+1 }}</div>
                <div class="col col-ref">{{truck?.code || 'N/A' }}</div>
                <div class="col col-truc-type">{{getTrucksTypes(truck?.type) || 'N/A' }}</div>
                <div class="col col-matriculation">{{truck?.immatriculation || 'N/A' }}</div>
                <div class="col col-carrier-ref">{{truck?.carrierRef || 'N/A' }}</div>
                <div class="col col-weight">{{truck?.capacity + ' '+ 'T' || 'N/A' }}</div>
                <!-- <div class="col col-vol">{{truck?.volume || 'N/A' }}</div>
                <div class="col col-description">{{truncate(truck?.desc, 30) || 'N/A' }}</div> -->

                <div class="col col-action">
                  <div class="action-icons">
                    <button nbTooltip="Detail" nbButton ghost nbTooltipPlacement="top" nbTooltipStatus
                      (click)="showDetailTruckDialog(detailtruckdialog, truck)">
                      <nb-icon icon="file-text-outline" status="basic" [options]="{ animation: { type: 'zoom' } }">
                      </nb-icon>
                    </button>
                    <button nbTooltip="Modifier" nbButton ghost nbTooltipPlacement="top" nbTooltipStatus
                      (click)="openEditTruckModal(editTruckDialog, truck)">
                      <nb-icon icon="edit-2-outline" status="warning" [options]="{ animation: { type: 'zoom' } }">
                      </nb-icon>
                    </button>
                    <button nbTooltip="{{truck?.enable? 'Desactiver' : 'Activer'}}" nbButton ghost
                      nbTooltipPlacement="top" (click)="editStatusTruck(editStatusdialog, truck)">
                      <nb-icon icon="{{truck?.enable? 'eye-outline' : 'eye-off-outline'}}"
                        status="{{truck?.enable?  'success' : 'danger'}}" [options]="{ animation: { type: 'zoom' } }">
                      </nb-icon>
                    </button>
                    <button nbTooltip="Supprimer" nbButton ghost nbTooltipPlacement="top"
                      (click)="deleteTruck(deleteTruckdialog, truck)">
                      <nb-icon icon="trash-2-outline" status="danger" [options]="{ animation: { type: 'zoom' } }">
                      </nb-icon>
                    </button>

                    <button nbTooltip="Exporter qrcode" nbButton ghost nbTooltipPlacement="top"
                      (click)="openDialogPDF(pdfViewDialog, truck)">
                      <nb-icon icon="image-outline" status="success" [options]="{ animation: { type: 'zoom' } }">
                      </nb-icon>
                    </button>
                  </div>
                </div>
              </nb-list-item>
              <nb-list-item *ngIf='trucks?.length === 0'>
                <div class="not-found">

                  <img src="../../../../assets/images/icons/shipped.png" alt="">
                  <h6>
                    Aucun camion trouvé
                  </h6>
                </div>
              </nb-list-item>
            </nb-list>
          </div>
        </nb-tab>

        <nb-tab tabTitle="Liste des Chauffeurs" badgeText='{{driverTotal}}' badgeStatus='success'
          badgePosition='top right'>
          <nb-list class="element-head">
            <nb-list-item class="list-elt-header list-head">
              <div class="col col-paginator">

                <div class="left-area">
                  <div class="filter-label">Filtrer par :</div>
                  <div class="input-auto-style">
                    <input type="text" (ngModelChange)="onModelDriverNameChange($event)" placeholder="Nom du Chauffeur"
                      fieldSize="small" nbInput class="empty-input" [nbAutocomplete]="autoComplete1"
                      [(ngModel)]="driverName" />
                  </div>
                  <nb-autocomplete #autoComplete1 (selectedChange)="onSelectionDriverNameChange($event)">
                    <nb-option *ngFor="let option of filteredDriverName$ | async" [value]="option">{{option}}
                    </nb-option>
                  </nb-autocomplete>
                </div>
                <div class="btn-contain">
                  <div class="file-upload-group">
                    <label for="truckInput" class="file-upload-label"><nb-icon icon="download-outline"
                        size="tiny"></nb-icon> Importer un fichier</label>
                    <input nbButton type="file" value="" outline status="success" fieldSize="small" id="truckInput"
                      class="file-upload-input" accept=".xls,.xlsx" (change)="driversUpload($event)">
                  </div>
                  <div class="btn-style">
                    <button nbButton outline status="success" fieldSize="small" class="refresh-btn"
                      (click)="getDriverRefresh()">
                      <nb-icon icon="refresh-outline"></nb-icon>
                      reinitialiser
                    </button>
                  </div>
                  <div class="btn-add-driver">
                    <button nbButton status="success" class="search-btn" size="small"
                      (click)='openAddDriverModal(addDriverDialog)'>
                      <nb-icon icon="plus-square-outline"></nb-icon>
                      Ajouter un Chauffeur
                    </button>
                  </div>
                </div>
              </div>
            </nb-list-item>
            <nb-list-item class="list-elt-header">
              <div class="col col-num">N°</div>
              <div class="col col-driver-code">Code Chauffeur</div>
              <div class="col col-permit">N° Permis</div>
              <div class="col col-phone">Date Expiration</div>
              <div class="col col-fullname">Nom Complet</div>
              <div class="col col-phone">Téléphone</div>
              <div class="col col-cni">CNI</div>

              <div class="col col-action">
                <div class="pagination">
                  <!-- <input nbInput fieldSize="small" status="primary" type="number" class="paging-input" (keydown)="onPagKeyDown($event)" [(ngModel)]="offset"> -->
                  {{driverStartIndex}} - {{driverEndIndex}} sur {{driverTotal}}
                  <div class="actions">
                    <nb-icon icon="arrowhead-left" status="basic" [options]="{ animation: { type: 'zoom' } }"
                      (click)="driverPreviousPage()"></nb-icon>
                    <nb-icon icon="arrowhead-right" status="basic" [options]="{ animation: { type: 'zoom' } }"
                      (click)="driverNextPage()"></nb-icon>
                  </div>
                </div>
              </div>
            </nb-list-item>
          </nb-list>
          <nb-list class="element-head scrool-style" [nbSpinner]="isLoading" nbSpinnerStatus="primary">
            <nb-list-item class="list-elt" *ngFor="let driver of drivers; index as i">
              <div class="col col-num">{{i+1}}</div>
              <div class="col col-driver-code">{{driver?.driverCode ?? 'N/A'}}</div>
              <div class="col col-permit">{{driver?.driverLicense ?? 'N/A'}}</div>
              <div class="col col-phone">{{(driver?.issueDate | date: 'dd/MM/yyyy') ?? 'N/A'}}</div>
              <div class="col col-fullname">{{(truncate(driver?.fullname, 25)) ?? 'N/A'}}</div>
              <div class="col col-phone">{{driver?.tel1 ?? 'N/A'}}</div>
              <div class="col col-cni">{{driver?.cni ?? 'N/A'}}</div>

              <div class="col col-action">
                <div class="action-icons">
                  <div class="col col-action">
                    <div class="action-icons">
                      <button nbTooltip="Detail" nbButton ghost nbTooltipPlacement="top" nbTooltipStatus
                        (click)="showDetailDriverDialog(detailDriverDialog, driver)">
                        <nb-icon icon="file-text-outline" status="basic" [options]="{ animation: { type: 'zoom' } }">
                        </nb-icon>
                      </button>
                      <button nbTooltip="Modifier" nbButton ghost nbTooltipPlacement="top"
                        (click)="openEditDriverModal(editDriverDialog,driver)">
                        <nb-icon icon="edit-2-outline" status="warning" [options]="{ animation: { type: 'zoom' } }">
                        </nb-icon>
                      </button>
                      <button nbTooltip="Supprimer" nbButton ghost nbTooltipPlacement="top"
                        (click)="deleteDriver(deleteDriverdialog,driver)">
                        <nb-icon icon="trash-2-outline" status="danger" [options]="{ animation: { type: 'zoom' } }">
                        </nb-icon>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </nb-list-item>
            <nb-list-item *ngIf='drivers?.length === 0'>
              <div class="not-found">

                <img src="../../../../assets/images/icons/people.png" alt="">
                <h6>
                  Aucun Chauffeur trouvé
                </h6>
              </div>
            </nb-list-item>
          </nb-list>
        </nb-tab>
      </nb-tabset>
    </nb-card-body>
  </nb-card>

</div>

<!-- <ng-template #editdialog let-data let-ref="dialogRef">
  <nb-card>
    <nb-card-header>Informations Générales</nb-card-header>
    <nb-card-body>
      <div class="general-infor">
        <div class="carrier-label">
          <p>Raison sociale</p>
          <input nbInput fullWidth fieldSize="small" status="basic" type="text" placeholder="Votre raison sociale"
            value="{{selectedCarrier?.label}}">
        </div>
        <div class="carrier-label">
          <p>Numero Identifiant Unique</p>
          <input nbInput fullWidth fieldSize="small" status="basic" type="text" placeholder="Votre NIU"
            value="{{selectedCarrier?.niu}}">
        </div>
        <div class="carrier-label">
          <p>N° Tel</p>
          <input nbInput fullWidth fieldSize="small" status="basic" type="text" placeholder="Votre n° tel"
            value="{{selectedCarrier?.tel}}">
        </div>
        <div class="carrier-label">
          <p>Adresse </p>
          <input nbInput fullWidth fieldSize="small" status="basic" type="text" placeholder="Votre e-mail"
            value="{{selectedCarrier?.email}}">
        </div>
        <div class="carrier-label">
          <p>Localisation</p>
          <input nbInput fullWidth fieldSize="small" status="basic" type="text" placeholder="Votre Localisation"
            value="{{selectedCarrier?.localisation}}">
        </div>
      </div>
    </nb-card-body>
    <nb-card-footer>
      <button nbButton (click)="ref.close()" status="basic" class="btn-margin" ghost>
        <nb-icon icon="close-square-outline"></nb-icon>
        Annuler
      </button>
      <button nbButton (click)="updateCarriers()" status="success" outline>
        <nb-icon icon="done-all-outline"></nb-icon>
        Enregistrer
      </button>
    </nb-card-footer>
  </nb-card>
</ng-template> -->

<ng-template #detailtruckdialog let-data let-ref="dialogRef">
  <nb-card class="detail-truck">
    <nb-card-header>
      <h5>Details du camion</h5>
    </nb-card-header>
    <nb-card-body>
      <div class="truck-label">
        <div class="container">
          <p>Immatriculation</p>
          <div>{{selectedTruck?.immatriculation || 'N/A'}}</div>
        </div>
        <!-- div class="container">
          <p>Chauffeur</p>
          <div>{{selectedTruck?.driver || 'N/A'}}</div>
        </div> -->
        <div class="container">
          <p>Type</p>
          <div>{{getTrucksTypes(selectedTruck?.type) || 'N/A'}}</div>
        </div>
        <div class="container">
          <p>Ref Transporteur</p>
          <div>{{selectedTruck?.carrierRef || 'N/A'}}</div>
        </div>
        <!-- <div class="container">
          <p>Volume</p>
          <div>{{selectedTruck?.volume || 'N/A'}}</div>
        </div> -->
        <div class="container">
          <p>Poids</p>
          <div>{{selectedTruck?.capacity + ' '+ 'T'}}</div>
        </div>
        <!-- <div class="container">
          <p>Description</p>
          <div>{{selectedTruck?.desc || 'N/A'}}</div>
        </div> -->
      </div>
    </nb-card-body>
    <nb-card-footer>
      <button nbButton (click)="ref.close()" outline status="basic">
        <nb-icon icon="close-square-outline" status='basic'></nb-icon>
        Fermer
      </button>
    </nb-card-footer>
  </nb-card>
</ng-template>

<ng-template #editStatusdialog let-data let-ref="dialogRef">
  <nb-card>
    <nb-card-header>Confirmation</nb-card-header>
    <nb-card-body>
      Voulez-vous vraiment {{selectedTruck?.enable ? 'Désactiver': 'Activer' }} le camion immatriculé <span
        class="bold">{{selectedTruck?.immatriculation}}</span> du transporteur <span
        class="bold">{{selectedCarrier?.label}}</span>
    </nb-card-body>
    <nb-card-footer>
      <button nbButton ghost status="basic" class="undo-btn" (click)="ref.close(false)">
        <nb-icon icon="close-square-outline"></nb-icon>
        Annuler
      </button>
      <button nbButton outline status="primary" (click)="ref.close(true)" [disabled]="isLoading" class="btn-border"
        (click)="ref.close(true)">
        <nb-icon icon="done-all-outline"></nb-icon>
        Confimer
      </button>
    </nb-card-footer>
  </nb-card>
</ng-template>

<ng-template #deleteCarrierdialog let-data let-ref="dialogRef">
  <nb-card>
    <nb-card-header>Confirmation</nb-card-header>
    <nb-card-body>
      Voulez-vous vraiment Supprimmer le transporteur <span class="bold">{{selectedCarrier?.label}} ?</span>
    </nb-card-body>
    <nb-card-footer>
      <button nbButton ghost status="basic" (click)="ref.close()">
        <nb-icon icon="close-square-outline"></nb-icon>
        Annuler
      </button>
      <button nbButton outline status="danger" (click)="ref.close(true)" [disabled]="isLoading" class="btn-border"
        (click)="ref.close(true)">
        <nb-icon icon="trash-2-outline"></nb-icon>
        Supprimer
      </button>
    </nb-card-footer>
  </nb-card>
</ng-template>

<ng-template #deleteTruckdialog let-data let-ref="dialogRef">
  <nb-card>
    <nb-card-header>Confirmation</nb-card-header>
    <nb-card-body>
      Voulez-vous vraiment Supprimmer le camion <span class="bold">{{selectedTruck?.type}}
        ({{selectedTruck?.immatriculation}})?</span>
    </nb-card-body>
    <nb-card-footer>
      <button nbButton ghost status="basic" (click)="ref.close()">
        <nb-icon icon="close-square-outline"></nb-icon>
        Annuler
      </button>
      <button nbButton outline status="danger" (click)="ref.close(true)" [disabled]="isLoading" class="btn-border"
        (click)="ref.close(true)">
        <nb-icon icon="trash-2-outline"></nb-icon>
        Supprimer
      </button>
    </nb-card-footer>
  </nb-card>
</ng-template>

<ng-template #deleteDriverdialog let-data let-ref="dialogRef">
  <nb-card>
    <nb-card-header>Confirmation</nb-card-header>
    <nb-card-body>
      Voulez-vous vraiment Enlever <span class="bold">{{selectedDriver?.fullname}} de la liste des chauffeurs?</span>
    </nb-card-body>
    <nb-card-footer>
      <button nbButton ghost (click)="ref.close()">
        <nb-icon icon="close-square-outline"></nb-icon>
        Annuler
      </button>
      <button nbButton outline status="danger" [disabled]="isLoading" class="btn-border" (click)="ref.close(true)">
        <nb-icon icon="trash-2-outline"></nb-icon>
        Supprimer
      </button>
    </nb-card-footer>
  </nb-card>
</ng-template>

<ng-template #editTruckDialog let-data let-ref="dialogRef">
  <div class="add-carrier-container">
    <nb-card class="add-truck">
      <nb-card-header class="form--header">Modifier le Camion</nb-card-header>

      <nb-card-body>
        <div class="add-container">
          <div class="form-container">
            <div class="form-group">
              <div class="attribute-block">
                <label class="label" for="nameInput">Type de camion</label>
              </div>
              <nb-select class="carrier-name input-width" fullWidth placeholder="Veuillez entrer le Type de camion"
                status='primary' size="small" class="empty-input" [(selected)]='selectedTruck.type'>
                <nb-option [value]='type.value' *ngFor="let type of trucksTypes">{{type?.label}}
                </nb-option>
              </nb-select>
              <!-- <input nbInput class="carrier-name input-width" status="basic" type="text"
                placeholder="Veuillez entrer le Type de camion" [(ngModel)]='selectedTruck.type'> -->
            </div>
            <div class="form-group">
              <div class="attribute-block">
                <label class="label" for="nameInput">Immatriculation</label>
              </div>
              <input nbInput class="carrier-name input-width" status="basic" type="text"
                placeholder="Veuillez entrer l'Immatriculation" [(ngModel)]='selectedTruck.immatriculation'>
            </div>
            <div class="form-group">
              <div class="attribute-block">
                <label class="label" for="nameInput">Poids</label>
              </div>
              <nb-select class="carrier-name input-width" fullWidth placeholder="Veuillez entrer le Poids du camion"
                status='primary' size="small" class="empty-input" [(selected)]='selectedTruck.capacity'>
                <nb-option [value]='capacity.value' *ngFor="let capacity of trucksCapacity">{{capacity?.label}}
                </nb-option>
              </nb-select>
              <!-- <input nbInput class="carrier-name input-width" status="basic" type="number"
                placeholder="Veuillez entrer Le Poids" [(ngModel)]='selectedTruck.capacity'> -->
            </div>
            <!-- <div class="form-group">
              <div class="attribute-block">
                <label class="label" for="nameInput">Volume</label>
              </div>
              <input nbInput class="carrier-name input-width" status="basic" type="text"
                placeholder="Veuillez entrer Le Volume" [(ngModel)]='selectedTruck.volume'>
            </div> -->
            <!-- <div class="form-group">
              <div class="attribute-block">
                <label class="label" for="nameInput">Description</label>
              </div>
              <textarea nbInput class="carrier-name input-width" status="basic" type="text"
                placeholder="Veuillez entrer Votre Description" [(ngModel)]='selectedTruck.desc'></textarea>
            </div> -->
          </div>
        </div>
      </nb-card-body>
      <nb-card-footer>
        <button nbButton ghost status="basic" (click)="ref.close()">
          <nb-icon icon="close-square-outline"></nb-icon>
          Annuler
        </button>
        <button nbButton outline status="success" [disabled]="isLoading" class="btn-border" (click)="editTruck()">
          <nb-icon icon="save-outline"></nb-icon>
          Enregistrer
        </button>
      </nb-card-footer>
    </nb-card>
  </div>
</ng-template>

<ng-template #editDriverDialog let-data let-ref="dialogRef">
  <div class="add-carrier-container">
    <nb-flip-card [showToggleButton]="false" [flipped]="flipped">
      <nb-card-front>
        <nb-card class="add-truck">
          <nb-card-header class="form--header">Modifier les informations du Chauffeur</nb-card-header>
          <nb-card-body>
            <nb-tabset>
              <nb-tab tabTitle="Informations Générales" class="tab-padding">
                <div class="add-container">
                  <div class="form-container">
                    <div class="form-group">
                      <div class="attribute-block">
                        <label class="label" for="nameInput">Code Chauffeur</label>
                      </div>
                      <input nbInput class="carrier-name input-width" status="basic" type="text"
                        placeholder="Veuillez entrer Le Code chauffeur" [(ngModel)]='selectedDriver.driverCode'>
                    </div>
                    <div class="form-group">
                      <div class="attribute-block">
                        <label class="label" for="nameInput"> No dePermis</label>
                      </div>
                      <input nbInput class="carrier-name input-width" status="basic" type="text"
                        placeholder="Veuillez entrer le Permis" [(ngModel)]='selectedDriver.driverLicense'>
                    </div>
                    <div class="form-group">
                      <div class="attribute-block">
                        <label class="label" for="nameInput">Nom</label>
                      </div>
                      <input nbInput class="carrier-name input-width" status="basic" type="text"
                        placeholder="Veuillez entrer Le Nom " [(ngModel)]='selectedDriver.lname'>
                    </div>
                    <div class="form-group">
                      <div class="attribute-block">
                        <label class="label" for="nameInput">Prenom</label>
                      </div>
                      <input nbInput class="carrier-name input-width" status="basic" type="text"
                        placeholder="Veuillez entrer Le Prenom" [(ngModel)]='selectedDriver.fname'>
                    </div>

                    <div class="form-group">
                      <div class="attribute-block">
                        <label class="label" for="nameInput">Téléphone</label>
                      </div>
                      <input nbInput class="carrier-name input-width" status="basic" type="number"
                        placeholder="Veuillez entrer Votre Contact" [(ngModel)]='selectedDriver.tel1'>
                    </div>
                    <div class="form-group">
                      <div class="attribute-block">
                        <label class="label" for="nameInput">CNI</label>
                      </div>
                      <input nbInput class="carrier-name input-width" status="basic" type="text"
                        placeholder="Veuillez entrer Le numero de CNI" [(ngModel)]='selectedDriver.cni'>
                    </div>
                    <div class="form-group">
                      <div class="attribute-block">
                        <label class="label" for="nameInput">Date d'Expiration du permis</label>
                      </div>
                      <input nbInput [nbDatepicker]="datepicker" class="carrier-name input-width"
                        placeholder="Veuillez choisir une date" [(ngModel)]='selectedDriver.licenseExpireDate'>
                      <nb-datepicker #datepicker></nb-datepicker>
                    </div>
                  </div>
                </div>
              </nb-tab>
              <nb-tab tabTitle="Formations" class="tab-padding">
                <nb-list class="element-head">
                  <nb-list-item class="list-elt-header list-head">
                    <div class="col col-paginator">
                      <div class="btn-contain">
                        <button nbButton status="success" class="search-btn" size="small" (click)='addTraining()'>
                          <nb-icon icon="plus-square-outline"></nb-icon>
                          Ajouter une formation
                        </button>
                      </div>
                    </div>
                  </nb-list-item>
                  <nb-list-item class="list-elt-header">
                    <div class="col col-tain-num">N°</div>
                    <div class="col col-tain-label">Libelé</div>
                    <div class="col col-tain-trainer">Formateur</div>
                    <div class="col col-tain-date">Date de début</div>
                    <div class="col col-tain-date">Date de fin</div>
                    <div class="col col-tain-date">Date d'expiration</div>

                    <div class="col col-tain-action"></div>
                  </nb-list-item>
                </nb-list>
                <nb-list class="element-head scrool-style" [nbSpinner]="isLoading" nbSpinnerStatus="primary">
                  <nb-list-item class="list-elt" *ngFor="let training of selectedDriver.trainings; index as i">
                    <div class="col col-tain-num">{{i+1}}</div>
                    <div class="col col-tain-label">{{(truncate(training?.trainingType, 10)) ?? 'N/A'}}</div>
                    <div class="col col-tain-trainer">{{(truncate(training?.trainerName, 10)) ?? 'N/A'}}</div>
                    <div class="col col-tain-date">{{(training?.date?.start | date:'dd/MM/YYYY') ?? 'N/A'}}</div>
                    <div class="col col-tain-date">{{(training?.date?.end | date:'dd/MM/YYYY') ?? 'N/A'}}</div>
                    <div class="col col-tain-date">{{(training?.date?.expiredDate | date:'dd/MM/YYYY') ?? 'N/A'}}</div>
                    <div class="col col-tain-action">
                      <button nbTooltip="Modifier" nbButton ghost nbTooltipPlacement="top" (click)="editTraining(i)">
                        <nb-icon icon="edit-2-outline" status="warning" [options]="{ animation: { type: 'zoom' } }">
                        </nb-icon>
                      </button>
                    </div>

                  </nb-list-item>
                  <nb-list-item *ngIf='selectedDriver.trainings?.length === 0 || !selectedDriver.trainings'>
                    <div class="not-found">

                      <img src="../../../../assets/images/icons/npm.png" alt="">
                      <h6>
                        Aucune formation trouvé
                      </h6>
                    </div>
                  </nb-list-item>
                </nb-list>
              </nb-tab>
            </nb-tabset>
          </nb-card-body>
          <nb-card-footer>
            <button nbButton ghost status="basic" (click)="ref.close()">
              <nb-icon icon="close-square-outline"></nb-icon>
              Annuler
            </button>
            <button nbButton outline status="success" [disabled]="isLoading" class="btn-border" (click)="editDriver()">
              <nb-icon icon="save-outline"></nb-icon>
              Enregistrer
            </button>
          </nb-card-footer>
        </nb-card>
      </nb-card-front>

      <nb-card-back>
        <nb-card class="add-truck" *ngIf="flipped&&isNewTraining">
          <nb-card-header class="form--header">Ajouter une formation</nb-card-header>
          <nb-card-body>
            <div class="add-container">
              <div class="form-container">
                <div class="form-group">
                  <div class="attribute-block">
                    <label class="label" for="nameInput">Libelé</label>
                  </div>
                  <input nbInput class="carrier-name input-width" status="basic" type="text"
                    placeholder="Entrez un Libelé" [(ngModel)]='selectedTraining.trainingType'>
                </div>
                <div class="form-group">
                  <div class="attribute-block">
                    <label class="label" for="nameInput">Nom du Formateur</label>
                  </div>
                  <input nbInput class="carrier-name input-width" status="basic" type="text" placeholder="Entrez un nom"
                    [(ngModel)]='selectedTraining.trainerName'>
                </div>
                <div class="form-group">
                  <div class="attribute-block">
                    <label class="label" for="nameInput">Date début</label>
                  </div>
                  <input nbInput class="carrier-name input-width" status="basic" [nbDatepicker]="trainDatePickerStart"
                    [(ngModel)]='trainDateStart' (ngModelChange)="updateTrainDateStart()">
                  <nb-datepicker #trainDatePickerStart></nb-datepicker>
                </div>
                <div class="form-group">
                  <div class="attribute-block">
                    <label class="label" for="nameInput">Date de fin</label>
                  </div>
                  <input nbInput class="carrier-name input-width" status="basic" [nbDatepicker]="trainDatePickerEnd"
                    [(ngModel)]='trainDateEnd' (ngModelChange)="updateTrainDateEnd()">
                  <nb-datepicker #trainDatePickerEnd></nb-datepicker>
                </div>
                <div class="form-group">
                  <div class="attribute-block">
                    <label class="label" for="nameInput">Date de d'expiration</label>
                  </div>
                  <input nbInput class="carrier-name input-width" status="basic" [nbDatepicker]="trainDatePickerexpired"
                    [(ngModel)]='trainDateExpired' (ngModelChange)="updateTrainDateExpired()">
                  <nb-datepicker #trainDatePickerexpired></nb-datepicker>
                </div>
              </div>
            </div>
          </nb-card-body>
          <nb-card-footer>
            <button nbButton ghost status="basic" (click)="toggle()">
              <nb-icon icon="close-square-outline"></nb-icon>
              Annuler
            </button>
            <button nbButton outline status="success" [disabled]="isLoading" class="btn-border"
              (click)="pushTraining()">
              <nb-icon icon="save-outline"></nb-icon>
              Enregistrer
            </button>
          </nb-card-footer>
        </nb-card>
        <nb-card class="add-truck" *ngIf="flipped&&isEditTraining">
          <nb-card-header class="form--header">Modifier les informations de la formation</nb-card-header>
          <nb-card-body>
            <div class="add-container">
              <div class="form-container">
                <div class="form-group">
                  <div class="attribute-block">
                    <label class="label" for="nameInput">Libelé</label>
                  </div>
                  <input nbInput class="carrier-name input-width" status="basic" type="text"
                    placeholder="Libelé inconnu"
                    [(ngModel)]='selectedDriver.trainings[selectedTrainingIndex].trainingType' readonly>
                </div>
                <div class="form-group">
                  <div class="attribute-block">
                    <label class="label" for="nameInput">Nom du Formateur</label>
                  </div>
                  <input nbInput class="carrier-name input-width" status="basic" type="text" placeholder="Nom inconnu"
                    [(ngModel)]='selectedDriver.trainings[selectedTrainingIndex].trainerName' readonly>
                </div>
                <div class="form-group">
                  <div class="attribute-block">
                    <label class="label" for="nameInput">Date début</label>
                  </div>
                  <input nbInput class="carrier-name input-width" status="basic" [nbDatepicker]="trainDatePickerStart"
                    [(ngModel)]='trainDateStart' (ngModelChange)="updateTrainDateStart()">
                  <nb-datepicker #trainDatePickerStart></nb-datepicker>
                </div>
                <div class="form-group">
                  <div class="attribute-block">
                    <label class="label" for="nameInput">Date de fin</label>
                  </div>
                  <input nbInput class="carrier-name input-width" status="basic" [nbDatepicker]="trainDatePickerEnd"
                    [(ngModel)]='trainDateEnd' (ngModelChange)="updateTrainDateEnd()">
                  <nb-datepicker #trainDatePickerEnd></nb-datepicker>
                </div>
                <div class="form-group">
                  <div class="attribute-block">
                    <label class="label" for="nameInput">Date de d'expiration</label>
                  </div>
                  <input nbInput class="carrier-name input-width" status="basic" [nbDatepicker]="trainDatePickerexpired"
                    [(ngModel)]='trainDateExpired' (ngModelChange)="updateTrainDateExpired()">
                  <nb-datepicker #trainDatePickerexpired></nb-datepicker>
                </div>

              </div>
            </div>
          </nb-card-body>
          <nb-card-footer>
            <button nbButton ghost status="basic" (click)="toggle()">
              <nb-icon icon="close-square-outline"></nb-icon>
              Annuler
            </button>
            <button nbButton outline status="success" [disabled]="isLoading" class="btn-border" (click)="editDriver()">
              <nb-icon icon="save-outline"></nb-icon>
              Enregistrer
            </button>
          </nb-card-footer>
        </nb-card>
      </nb-card-back>
    </nb-flip-card>
  </div>
</ng-template>

<ng-template #detailDriverDialog let-data let-ref="dialogRef">
  <div class="add-carrier-container">
    <nb-card class="add-truck">
      <nb-card-header class="form--header">Informations du Chauffeur</nb-card-header>
      <nb-card-body>

        <nb-tabset>
          <nb-tab tabTitle="Informations Générales" class="tab-padding">
            <div class="add-container">
              <div class="form-container">
                <div class="form-group">
                  <div class="attribute-block">
                    <label class="label" for="nameInput">Code Chauffeur</label>
                  </div>
                  <input nbInput class="carrier-name input-width" status="basic" type="text"
                    placeholder="Permis inconnu" readonly [ngModel]='selectedDriver.driverCode'>
                </div>
                <div class="form-group">
                  <div class="attribute-block">
                    <label class="label" for="nameInput">Permis</label>
                  </div>
                  <input nbInput class="carrier-name input-width" status="basic" type="text"
                    placeholder="Permis inconnu" readonly [ngModel]='selectedDriver.driverLicense'>
                </div>
                <div class="form-group">
                  <div class="attribute-block">
                    <label class="label" for="nameInput">Nom Complet</label>
                  </div>
                  <input nbInput class="carrier-name input-width" status="basic" type="text"
                    placeholder="Nom complet inconnu" readonly [ngModel]='selectedDriver.fullname'>
                </div>
                <div class="form-group">
                  <div class="attribute-block">
                    <label class="label" for="nameInput">Téléphone</label>
                  </div>
                  <input nbInput class="carrier-name input-width" status="basic" type="text"
                    placeholder="Numéro de téléphone inconnu" readonly [ngModel]='selectedDriver.tel1'>
                </div>
                <div class="form-group">
                  <div class="attribute-block">
                    <label class="label" for="nameInput">CNI</label>
                  </div>
                  <input nbInput class="carrier-name input-width" status="basic" type="text" placeholder="CNI inconnu"
                    readonly [ngModel]='selectedDriver.cni'>
                </div>
                <div class="form-group">
                  <div class="attribute-block">
                    <label class="label" for="nameInput">Date d'Expiration du permis</label>
                  </div>
                  <input nbInput [nbDatepicker]="datepicker" class="carrier-name input-width"
                    placeholder="Date d'expiration du permis inconnu" [(ngModel)]='selectedDriver.licenseExpireDate'
                    readonly>
                  <nb-datepicker #datepicker></nb-datepicker>
                </div>
              </div>
            </div>
          </nb-tab>
          <nb-tab tabTitle="Formations" class="tab-padding">
            <nb-list class="element-head">
              <nb-list-item class="list-elt-header">
                <div class="col col-tain-num">N°</div>
                <div class="col col-tain-label">Libelé</div>
                <div class="col col-tain-trainer">Formateur</div>
                <div class="col col-tain-date">Date de début</div>
                <div class="col col-tain-date">Date de fin</div>
                <div class="col col-tain-date">Date d'expiration</div>
              </nb-list-item>
            </nb-list>
            <nb-list class="element-head scrool-style" [nbSpinner]="isLoading" nbSpinnerStatus="primary">
              <nb-list-item class="list-elt" *ngFor="let training of selectedDriver.trainings; index as i">
                <div class="col col-tain-train">{{i+1}}</div>
                <div class="col col-tain-label">{{(truncate(training?.trainingType, 10)) ?? 'N/A'}}</div>
                <div class="col col-tain-trainer">{{(truncate(training?.trainerName, 10)) ?? 'N/A'}}</div>
                <div class="col col-tain-date">{{(training?.date?.start | date:'dd/MM/YYYY') ?? 'N/A'}}</div>
                <div class="col col-tain-date">{{(training?.date?.end | date:'dd/MM/YYYY') ?? 'N/A'}}</div>
                <div class="col col-tain-date">{{(training?.date?.expiredDate | date:'dd/MM/YYYY') ?? 'N/A'}}</div>
              </nb-list-item>
              <nb-list-item *ngIf='selectedDriver.trainings?.length === 0 || !selectedDriver.trainings'>
                <div class="not-found">

                  <img src="../../../../assets/images/icons/npm.png" alt="">
                  <h6>
                    Aucune formation trouvé
                  </h6>
                </div>
              </nb-list-item>
            </nb-list>
          </nb-tab>
        </nb-tabset>
      </nb-card-body>
      <nb-card-footer>
        <button nbButton ghost status="basic" (click)="ref.close()">
          <nb-icon icon="close-square-outline"></nb-icon>
          Annuler
        </button>
      </nb-card-footer>

    </nb-card>
  </div>
</ng-template>

<ng-template #detailDriverDialog let-data let-ref="dialogRef">
  <div class="add-carrier-container">
    <nb-card class="add-truck">
      <nb-card-header class="form--header">Informations du Chauffeur</nb-card-header>
      <nb-card-body>

        <nb-tabset>
          <nb-tab tabTitle="Informations Générales" class="tab-padding">
            <div class="add-container">
              <div class="form-container">
                <div class="form-group">
                  <div class="attribute-block">
                    <label class="label" for="nameInput">Permis</label>
                  </div>
                  <input nbInput class="carrier-name input-width" status="basic" type="text"
                    placeholder="Permis inconnu" readonly [ngModel]='selectedDriver.driverLicense'>
                </div>
                <div class="form-group">
                  <div class="attribute-block">
                    <label class="label" for="nameInput">Nom Complet</label>
                  </div>
                  <input nbInput class="carrier-name input-width" status="basic" type="text"
                    placeholder="Nom complet inconnu" readonly [ngModel]='selectedDriver.fullname'>
                </div>
                <div class="form-group">
                  <div class="attribute-block">
                    <label class="label" for="nameInput">Téléphone</label>
                  </div>
                  <input nbInput class="carrier-name input-width" status="basic" type="text"
                    placeholder="Numéro de téléphone inconnu" readonly [ngModel]='selectedDriver.tel1'>
                </div>
                <div class="form-group">
                  <div class="attribute-block">
                    <label class="label" for="nameInput">CNI</label>
                  </div>
                  <input nbInput class="carrier-name input-width" status="basic" type="text" placeholder="CNI inconnu"
                    readonly [ngModel]='selectedDriver.cni'>
                </div>
              </div>
            </div>
          </nb-tab>
          <nb-tab tabTitle="Formations" class="tab-padding">
            <nb-list class="element-head">
              <nb-list-item class="list-elt-header">
                <div class="col col-tain-num">N°</div>
                <div class="col col-tain-label">Libelé</div>
                <div class="col col-tain-trainer">Formateur</div>
                <div class="col col-tain-date">Date de début</div>
                <div class="col col-tain-date">Date de fin</div>

              </nb-list-item>
            </nb-list>
            <nb-list class="element-head scrool-style" [nbSpinner]="isLoading" nbSpinnerStatus="primary">
              <nb-list-item class="list-elt" *ngFor="let training of selectedDriver.trainings; index as i">
                <div class="col col-tain-train">{{i+1}}</div>
                <div class="col col-tain-label">{{(truncate(training?.trainingType, 10)) ?? 'N/A'}}</div>
                <div class="col col-tain-trainer">{{(truncate(training?.trainerName, 10)) ?? 'N/A'}}</div>
                <div class="col col-tain-date">{{(training?.date?.start | date:'dd/MM/YYYY') ?? 'N/A'}}</div>
                <div class="col col-tain-date">{{(training?.date?.end | date:'dd/MM/YYYY') ?? 'N/A'}}</div>
                <div class="col col-tain-date">{{(training?.date?.expiredDate | date:'dd/MM/YYYY') ?? 'N/A'}}</div>
              </nb-list-item>
              <nb-list-item *ngIf='selectedDriver.trainings?.length === 0 || !selectedDriver.trainings'>
                <div class="not-found">

                  <img src="../../../../assets/images/icons/npm.png" alt="">
                  <h6>
                    Aucune formation trouvé
                  </h6>
                </div>
              </nb-list-item>
            </nb-list>
          </nb-tab>
        </nb-tabset>
      </nb-card-body>
      <nb-card-footer>
        <button nbButton ghost status="basic" (click)="ref.close()">
          <nb-icon icon="close-square-outline"></nb-icon>
          Annuler
        </button>
      </nb-card-footer>

    </nb-card>
  </div>
</ng-template>

<ng-template #addTruckDialog let-data let-ref="dialogRef">
  <div class="add-carrier-container">
    <nb-card class="add-truck">
      <nb-card-header class="form--header">Nouveau Camion</nb-card-header>


      <nb-card-body>
        <div class="add-container">
          <div class="form-container">
            <!-- <div class="form-group">
              <div class="attribute-block">
                <label class="label" for="nameInput">Type de camion</label>
              </div>
              <input nbInput class="carrier-name input-width" status="basic" type="text"
                placeholder="Veuillez entrer le Type de camion" [(ngModel)]='selectedTruck.type'>
            </div> -->
            <div class="form-group">
              <div class="attribute-block">
                <label class="label" for="nameInput">Type de camion</label>
              </div>
              <nb-select class="carrier-name input-width" fullWidth placeholder="Veuillez entrer le type de camion"
                status='primary' size="small" class="empty-input" [(selected)]='selectedTruck.type'>
                <nb-option [value]='type.value' *ngFor="let type of trucksTypes">{{type?.label}}
                </nb-option>
              </nb-select>
            </div>
            <div class="form-group">
              <div class="attribute-block">
                <label class="label" for="nameInput">Immatriculation</label>
              </div>
              <input nbInput class="carrier-name input-width" status="basic" type="text"
                placeholder="Veuillez entrer l'immatriculation" [(ngModel)]='selectedTruck.immatriculation'>
            </div>
            <div class="form-group">
              <div class="attribute-block">
                <label class="label" for="nameInput">Poids</label>
              </div>
              <nb-select class="carrier-name input-width" fullWidth placeholder="Veuillez entrer le poids du camion"
                status='primary' size="small" class="empty-input" [(selected)]='selectedTruck.capacity'>
                <nb-option [value]='capacity.value' *ngFor="let capacity of trucksCapacity">{{capacity?.label}}
                </nb-option>
              </nb-select>
              <!-- <input nbInput class="carrier-name input-width" status="basic" type="number"
                placeholder="Veuillez entrer Le Poids" [(ngModel)]='selectedTruck.capacity'> -->
            </div>
            <!-- <div class="form-group">
              <div class="attribute-block">
                <label class="label" for="nameInput">Volume</label>
              </div>
              <input nbInput class="carrier-name input-width" status="basic" type="text"
                placeholder="Veuillez entrer Le Volume" [(ngModel)]='selectedTruck.volume'>
            </div>
            <div class="form-group">
              <div class="attribute-block">
                <label class="label" for="nameInput">Description</label>
              </div>
              <textarea nbInput class="carrier-name input-width" status="basic" type="text"
                placeholder="Veuillez entrer Votre Description" [(ngModel)]='selectedTruck.desc'></textarea>
            </div> -->
          </div>
        </div>
      </nb-card-body>
      <nb-card-footer>
        <button nbButton ghost status="basic" (click)="ref.close()">
          <nb-icon icon="close-square-outline"></nb-icon>
          Annuler
        </button>
        <button nbButton outline status="success" [disabled]="isLoading" class="btn-border" (click)="addTruck()">
          <nb-icon icon="save-outline"></nb-icon>
          Enregistrer
        </button>
      </nb-card-footer>
    </nb-card>
  </div>
</ng-template>

<ng-template #addDriverDialog let-data let-ref="dialogRef">
  <div class="add-carrier-container">
    <nb-flip-card [showToggleButton]="false" [flipped]="flipped">
      <nb-card-front>
        <nb-card class="add-truck">
          <nb-card-header class="form--header">Ajouter les informations du Chauffeur</nb-card-header>
          <nb-card-body>
            <nb-tabset>
              <nb-tab tabTitle="Informations Générales" class="tab-padding">
                <div class="add-container">
                  <div class="form-container">
                    <div class="form-group">
                      <div class="attribute-block">
                        <label class="label" for="nameInput">Code chauffeur</label>
                      </div>
                      <input nbInput class="carrier-name input-width" status="basic" type="text"
                        placeholder="Veuillez entrer Le Code Chauffeur" [(ngModel)]='selectedDriver.driverCode'>
                    </div>

                    <div class="form-group">
                      <div class="attribute-block">
                        <label class="label" for="nameInput">No de Permis</label>
                      </div>
                      <input nbInput class="carrier-name input-width" status="basic" type="text"
                        placeholder="Veuillez entrer e Permis" [(ngModel)]='selectedDriver.driverLicense'>
                    </div>

                    <div class="form-group">
                      <div class="attribute-block">
                        <label class="label" for="nameInput">Nom</label>
                      </div>
                      <input nbInput class="carrier-name input-width" status="basic" type="text"
                        placeholder="Veuillez entrer Le Nom " [(ngModel)]='selectedDriver.lname'>
                    </div>

                    <div class="form-group">
                      <div class="attribute-block">
                        <label class="label" for="nameInput">Prenom</label>
                      </div>
                      <input nbInput class="carrier-name input-width" status="basic" type="text"
                        placeholder="Veuillez entrer Le Prenom" [(ngModel)]='selectedDriver.fname'>
                    </div>

                    <div class="form-group">
                      <div class="attribute-block">
                        <label class="label" for="nameInput">Téléphone</label>
                      </div>
                      <input nbInput class="carrier-name input-width" status="basic" type="tel"
                        placeholder="Veuillez entrer Votre Contact" [(ngModel)]='selectedDriver.tel1'>
                    </div>

                    <div class="form-group">
                      <div class="attribute-block">
                        <label class="label" for="nameInput">Deuxieme téléphone</label>
                      </div>
                      <input nbInput class="carrier-name input-width" status="basic" type="tel"
                        placeholder="Veuillez entrer Votre Contact" [(ngModel)]='selectedDriver.tel2'>
                    </div>

                    <div class="form-group">
                      <div class="attribute-block">
                        <label class="label" for="nameInput">CNI</label>
                      </div>
                      <input nbInput class="carrier-name input-width" status="basic" type="text"
                        placeholder="Veuillez entrer Le numero de CNI" [(ngModel)]='selectedDriver.cni'>
                    </div>

                    <div class="form-group">
                      <div class="attribute-block">
                        <label class="label" for="nameInput">Date d'Expiration du permis</label>
                      </div>
                      <input nbInput [nbDatepicker]="datepicker" class="carrier-name input-width"
                        placeholder="Veuillez choisir une date" [(ngModel)]='selectedDriver.licenseExpireDate'>
                      <nb-datepicker #datepicker></nb-datepicker>
                    </div>
                  </div>
                </div>
              </nb-tab>
              <nb-tab tabTitle="Formations" class="tab-padding">
                <nb-list class="element-head">
                  <nb-list-item class="list-elt-header list-head">
                    <div class="col col-paginator">
                      <div class="btn-contain">
                        <button nbButton status="success" class="search-btn" size="small" (click)='addTraining()'>
                          <nb-icon icon="plus-square-outline"></nb-icon>
                          Ajouter une formation
                        </button>
                      </div>
                    </div>
                  </nb-list-item>
                  <nb-list-item class="list-elt-header">
                    <div class="col col-tain-num">N°</div>
                    <div class="col col-tain-label">Libelé</div>
                    <div class="col col-tain-trainer">Formateur</div>
                    <div class="col col-tain-date">Date de début</div>
                    <div class="col col-tain-date">Date de fin</div>
                    <div class="col col-tain-date">Date d'expiration</div>

                    <div class="col col-tain-action"></div>
                  </nb-list-item>
                </nb-list>
                <nb-list class="element-head scrool-style" [nbSpinner]="isLoading" nbSpinnerStatus="primary">
                  <nb-list-item class="list-elt" *ngFor="let training of selectedDriver?.trainings; index as i">
                    <div class="col col-tain-num">{{i+1}}</div>
                    <div class="col col-tain-label">{{(truncate(training?.trainingType, 10)) ?? 'N/A'}}</div>
                    <div class="col col-tain-trainer">{{(truncate(training?.trainerName, 10)) ?? 'N/A'}}</div>
                    <div class="col col-tain-date">{{(training?.date?.start | date:'dd/MM/YYYY') ?? 'N/A'}}</div>
                    <div class="col col-tain-date">{{(training?.date?.end | date:'dd/MM/YYYY') ?? 'N/A'}}</div>
                    <div class="col col-tain-date">{{(training?.date?.expiredDate | date:'dd/MM/YYYY') ?? 'N/A'}}</div>
                    <div class="col col-tain-action">
                      <button nbTooltip="Modifier" nbButton ghost nbTooltipPlacement="top" (click)="editTraining(i)">
                        <nb-icon icon="edit-2-outline" status="warning" [options]="{ animation: { type: 'zoom' } }">
                        </nb-icon>
                      </button>
                    </div>

                  </nb-list-item>
                  <nb-list-item *ngIf='selectedDriver.trainings?.length === 0 || !selectedDriver.trainings'>
                    <div class="not-found">

                      <img src="../../../../assets/images/icons/npm.png" alt="">
                      <h6>
                        Aucune formation trouvé
                      </h6>
                    </div>
                  </nb-list-item>
                </nb-list>
              </nb-tab>
            </nb-tabset>
          </nb-card-body>
          <nb-card-footer>
            <button nbButton ghost status="basic" (click)="ref.close()">
              <nb-icon icon="close-square-outline"></nb-icon>
              Annuler
            </button>
            <button nbButton outline status="success" [disabled]="isLoading" class="btn-border" (click)="addDriver()">
              <nb-icon icon="save-outline"></nb-icon>
              Enregistrer
            </button>
          </nb-card-footer>
        </nb-card>
      </nb-card-front>

      <nb-card-back>
        <nb-card class="add-truck" *ngIf="flipped&&isNewTraining">
          <nb-card-header class="form--header">Ajouter une formation</nb-card-header>
          <nb-card-body>
            <div class="add-container">
              <div class="form-container">
                <div class="form-group">
                  <div class="attribute-block">
                    <label class="label" for="nameInput">Libelé</label>
                  </div>
                  <input nbInput class="carrier-name input-width" status="basic" type="text"
                    placeholder="Entrez un Libelé" [(ngModel)]='selectedTraining.trainingType'>
                </div>
                <div class="form-group">
                  <div class="attribute-block">
                    <label class="label" for="nameInput">Nom du Formateur</label>
                  </div>
                  <input nbInput class="carrier-name input-width" status="basic" type="text" placeholder="Entrez un nom"
                    [(ngModel)]='selectedTraining.trainerName'>
                </div>
                <div class="form-group">
                  <div class="attribute-block">
                    <label class="label" for="nameInput">Date début</label>
                  </div>
                  <input nbInput class="carrier-name input-width" status="basic" [nbDatepicker]="trainDatePickerStart"
                    [(ngModel)]='trainDateStart' (ngModelChange)="updateTrainDateStart()">
                  <nb-datepicker #trainDatePickerStart></nb-datepicker>
                </div>
                <div class="form-group">
                  <div class="attribute-block">
                    <label class="label" for="nameInput">Date de fin</label>
                  </div>
                  <input nbInput class="carrier-name input-width" status="basic" [nbDatepicker]="trainDatePickerEnd"
                    [(ngModel)]='trainDateEnd' (ngModelChange)="updateTrainDateEnd()">
                  <nb-datepicker #trainDatePickerEnd></nb-datepicker>
                </div>
                <div class="form-group">
                  <div class="attribute-block">
                    <label class="label" for="nameInput">Date d'expiration</label>
                  </div>
                  <input nbInput class="carrier-name input-width" status="basic" [nbDatepicker]="trainDatePickerexpired"
                    [(ngModel)]='trainDateExpired' (ngModelChange)="updateTrainDateExpired()">
                  <nb-datepicker #trainDatePickerexpired></nb-datepicker>
                </div>
              </div>
            </div>
          </nb-card-body>
          <nb-card-footer>
            <button nbButton ghost status="basic" (click)="toggle()">
              <nb-icon icon="close-square-outline"></nb-icon>
              Annuler
            </button>
            <button nbButton outline status="success" [disabled]="isLoading" class="btn-border"
              (click)="pushTraining()">
              <nb-icon icon="save-outline"></nb-icon>
              Enregistrer
            </button>
          </nb-card-footer>
        </nb-card>
        <nb-card class="add-truck" *ngIf="flipped&&isEditTraining">
          <nb-card-header class="form--header">Modifier Formation</nb-card-header>
          <nb-card-body>
            <div class="add-container">
              <div class="form-container">
                <div class="form-group">
                  <div class="attribute-block">
                    <label class="label" for="nameInput">Libelé</label>
                  </div>
                  <input nbInput class="carrier-name input-width" status="basic" type="text"
                    placeholder="Libelé inconnu"
                    [(ngModel)]='selectedDriver.trainings[selectedTrainingIndex].trainingType' readonly>
                </div>
                <div class="form-group">
                  <div class="attribute-block">
                    <label class="label" for="nameInput">Nom du Formateur</label>
                  </div>
                  <input nbInput class="carrier-name input-width" status="basic" type="text" placeholder="Nom inconnu"
                    [(ngModel)]='selectedDriver.trainings[selectedTrainingIndex].trainerName' readonly>
                </div>
                <div class="form-group">
                  <div class="attribute-block">
                    <label class="label" for="nameInput">Date début</label>
                  </div>
                  <input nbInput class="carrier-name input-width" status="basic" [nbDatepicker]="trainDatePickerStart"
                    [(ngModel)]='trainDateStart' (ngModelChange)="updateTrainDateStart()">
                  <nb-datepicker #trainDatePickerStart></nb-datepicker>
                </div>
                <div class="form-group">
                  <div class="attribute-block">
                    <label class="label" for="nameInput">Date de fin</label>
                  </div>
                  <input nbInput class="carrier-name input-width" status="basic" [nbDatepicker]="trainDatePickerEnd"
                    [(ngModel)]='trainDateEnd' (ngModelChange)="updateTrainDateEnd()">
                  <nb-datepicker #trainDatePickerEnd></nb-datepicker>
                </div>
                <div class="form-group">
                  <div class="attribute-block">
                    <label class="label" for="nameInput">Date d'expiration</label>
                  </div>
                  <input nbInput class="carrier-name input-width" status="basic" [nbDatepicker]="trainDatePickerexpired"
                    [(ngModel)]='trainDateExpired' (ngModelChange)="updateTrainDateExpired()">
                  <nb-datepicker #trainDatePickerexpired></nb-datepicker>
                </div>
              </div>
            </div>
          </nb-card-body>
          <nb-card-footer>
            <button nbButton ghost status="basic" (click)="toggle()">
              <nb-icon icon="close-square-outline"></nb-icon>
              Annuler
            </button>
            <button nbButton outline status="success" [disabled]="isLoading" class="btn-border"
              (click)="pushEditTraining()">
              <nb-icon icon="save-outline"></nb-icon>
              Enregistrer
            </button>
          </nb-card-footer>
        </nb-card>
      </nb-card-back>
    </nb-flip-card>
  </div>
</ng-template>

<ng-template #pdfViewDialog let-data let-ref="dialogRef">
  <nb-card accent="primary" [nbSpinner]="isRequest" nbSpinnerStatus="primary" nbSpinnerMessage="" style="height: 29vh;">
    <nb-card-header class="form--header">
      <div class="header_text">Aperçu</div>
    </nb-card-header>

    <nb-card-body>
      <!--    <pdf-viewer [src]="getPdf(selectedCheckout)" [render-text]="true" [original-size]="false"
        style=" width: 400px; height: 500px">
      </pdf-viewer> -->

      <!-- <div style="width: 60vw; height: 100%;">
        <ng2-pdfjs-viewer #pdfViewerOnDemand [downloadFileName]="pdfName" [pdfSrc]="byteArray" zoom='page-fit'>
        </ng2-pdfjs-viewer>
      </div> -->

      <p class="disclaimer-rib">
        Cliquez sur le bouton ci-dessous pour exporter au format PDF le Qr Code du camion
        {{selectedTruck?.immatriculation}}.
      </p>

    </nb-card-body>

    <nb-card-footer class="form--footer">

      <button nbButton outline status="basic" class="" [disabled]="isRequest" (click)="ref.close()">
        <nb-icon icon="close-square-outline" [options]="{ animation: { type: 'zoom' } }" class="remove-icon">
        </nb-icon>
        Fermer
      </button>

      <a nbButton outline status="success" href="{{link?.pdfLink}}" target="_blank" rel="" class="format-elmt"
        (click)="onTypeSelected(link)">
        <nb-icon icon="download-outline" [options]="{ animation: { type: 'zoom' } }" class="remove-icon">
        </nb-icon>
        Exporter
      </a>

    </nb-card-footer>
  </nb-card>

</ng-template>