import { Component, OnInit } from '@angular/core';
import { MenuReportingService } from '../menu-reporting.service';
import { RenderTypeValue } from 'src/app/shared/models/render-type.enum';
import { RenderType } from 'src/app/shared/models/authorization-removal';

@Component({
  selector: 'clw-tile-times',
  templateUrl: './tile-times.component.html',
  styles: [
  ]
})
export class TileTimesComponent implements OnInit {
  isLoading: boolean;
  data: any;
  parkTimes: any;
  dataInspection: any;
  queuingTime: any;
  dockingTime: any;
  avgResponseTime: any;

  constructor(
    private reportingSrv: MenuReportingService) { }

  async ngOnInit() {
    await this.getData();
  }

  async getData(): Promise<void> {
    try {
      this.isLoading = true;
      const query = {
        type: RenderTypeValue.PICKUP
      }
      this.dataInspection = await this.reportingSrv.getStatusInspections(query);
      this.data = await this.reportingSrv.getTimes({ FreightHandlingCode: RenderType.PICKUP });
      this.parkTimes = await this.reportingSrv.getAverageParkTimes({ FreightHandlingCode: RenderType.PICKUP });
      this.avgResponseTime = await this.reportingSrv.getAverageResponseTime({ FreightHandlingCode: RenderType.PICKUP });
      

      const average = this.data.queuingTime;
      const hour = Math.floor(average / 60);
      const min = Math.floor(average - (hour * 60));
      this.queuingTime = { hour, min };
      console.log(this.queuingTime);
      

      const averageDock = this.data.dockingTime;
      const hourDock = Math.floor(averageDock / 60);
      const minDock = Math.floor((averageDock - hourDock) * 60);
      this.dockingTime = { hourDock, minDock };
    } catch (error) {
      return error;
    } finally { this.isLoading = false; }
  }

  millisecondsToTime(ms: number): string {
    const days = Math.floor(ms / (1000 * 60 * 60 * 24));
    const hours = Math.floor((ms % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((ms % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((ms % (1000 * 60)) / 1000);

    const daysStr = days.toString().padStart(2, '0');
    const hoursStr = hours.toString().padStart(2, '0');
    const minutesStr = minutes.toString().padStart(2, '0');
    const secondsStr = seconds.toString().padStart(2, '0');

    return `${daysStr} jour(s) ${hoursStr}:${minutesStr}:${secondsStr}`;
  }

}
