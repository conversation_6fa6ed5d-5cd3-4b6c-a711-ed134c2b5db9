import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { UsersListRoutingModule } from './users-list-routing.module';
import { UsersListComponent } from './users-list.component';
import { NbButtonModule, NbCardModule, NbDatepickerModule, NbDialogModule, NbIconModule, NbInputModule, NbSpinnerModule, NbTabsetModule, NbToggleModule, NbTooltipModule, NbAutocompleteModule } from '@nebular/theme';
import { FormsModule } from '@angular/forms';
import { SharedModule } from 'src/app/shared/shared.module';


@NgModule({
  declarations: [
    UsersListComponent
  ],
  imports: [
    CommonModule,
    UsersListRoutingModule,
    FormsModule,
    SharedModule,
    NbIconModule,
    NbInputModule,
    NbCardModule,
    NbButtonModule,
    NbTooltipModule,
    NbDatepickerModule,
    NbDialogModule.forChild(),
    NbTabsetModule,
    NbToggleModule,
    NbSpinnerModule,
    NbAutocompleteModule


  ]
})
export class UsersListModule { }
