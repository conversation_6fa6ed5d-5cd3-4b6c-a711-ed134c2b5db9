import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BaseUrlService } from 'src/app/shared/services/base-url.service';
import { environment } from 'src/environments/environment';
import * as XLSX from 'xlsx';
import * as FileSaver from 'file-saver';
import { AuthorizationRemoval, NewManualAes, UpdateQueuesAesType } from 'src/app/shared/models/authorization-removal';
import { Dock } from 'src/app/shared/models/dock'; // Ensure this path is correct
import { lastValueFrom } from 'rxjs';
import { StatusRemovals } from 'src/app/shared/models/status-removal.enum';
import { Carrier } from 'src/app/shared/models/carrier';

@Injectable({
  providedIn: 'root'
})
export class RemovalService {
  BASE_URL: string;
  selectedAE: any;
  selectedCarrier: any;
  selectedTruck: any;
  date = {
    shippmentDate: '',
    deliveryDate: '',
  };
  shippement: any;
  data: any;

  EXCEL_TYPE = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8';
  EXCEL_EXTENSION = '.xlsx';

  constructor(
    private baseUrlSrv: BaseUrlService,
    private http: HttpClient
  ) {
    this.BASE_URL = `${this.baseUrlSrv.getOrigin()}${environment.basePath}`;
  }
  async getAuthorizationRemoval(param?: any): Promise<{ data: AuthorizationRemoval[], count: number }> {
    const { startDate, endDate, company, shipping, dockCode, matriculation, finalTruckTonnage, projection,
      SoldTo, ShipTo, ShipTo1, FreightHandlingCode, status, LoadNumber, ItemNumber, OrderType, carrierLabel, offset,
      limit, LoadStatus, soldto, sort, way, inspection } = param;

    let params = new HttpParams();

    // if (param?.moved) { params = params.append('moved', `${param?.moved}`) }
    if (param?.product) { params = params.append('product', `${param?.product}`) }
    if (inspection) { params = params.append('inspection.status', `${param?.inspection}`) }
    if (param?.immatriculation) { params = params.append('immatriculation', `${param?.immatriculation}`) }
    if (param?.enable) { params = params.append('enable', `${param?.enable}`) }
    if (soldto) { params = params.append('companies.erpSoldToId', `${param?.soldto}`) }

    if (status) { params = params.append('status', `${status}`) }
    if (LoadNumber) { params = params.append('LoadNumber', LoadNumber) }
    // if (LoadStatus) { params = params.append('LoadStatus', `${LoadStatus}`) }
    if (ItemNumber) { params = params.append('ItemNumber', `${ItemNumber}`) }
    if (carrierLabel) { params = params.append('carrier', `${carrierLabel}`) }
    if (company) { params = params.append('SoldToDescription', `${company}`) }
    if (shipping) { params = params.append('ShipToDescription', `${shipping}`) }
    if (SoldTo) { params = params.append('SoldTo', `${SoldTo}`) }
    if (OrderType) { params = params.append('OrderType', `${OrderType}`) }
    if (ShipTo) { params = params.append('ShipTo1', `${ShipTo}`) }
    if (ShipTo1) { params = params.append('ShipTo2', `${ShipTo1}`) }
    if (sort) { params = params.append('sort', `${param?.sort}`) } if (SoldTo) { params = params.append('SoldTo', `${SoldTo}`) }

    if (way) { params = params.append('way', `${param?.way}`) }
    if (offset) { params = params.append('offset', `${offset}`) }
    if (limit) { params = params.append('limit', `${limit}`) }
    if (FreightHandlingCode) { params = params.append('FreightHandlingCode', `${FreightHandlingCode}`); }
    if (startDate) { params = params.append('start', `${startDate}`) }
    if (endDate) { params = params.append('end', `${endDate}`) }

    if (dockCode) { params = params.append('dockCode', `${dockCode}`) }
    if (finalTruckTonnage) { params = params.append('finalTruckTonnage', `${finalTruckTonnage}`) }
    if (matriculation) { params = params.append('carrier.truck.immatriculation', `${matriculation}`) }
    if (projection) { params = params.append('project', `${projection}`) }

    // params = params.append('project', 'LoadNumber,dates.created,SoldToDescription,carrier,ItemNumber,status,TransactionQuantity,ShipTo, ')

    return await lastValueFrom(this.http.get<{ data: AuthorizationRemoval[], count: number }>(`${this.BASE_URL}/authorization-removal`, { params }));
  }

  async getAuthorizationRemovalById(id: string) {
    const data = await this.http.get<any>(`${this.BASE_URL}/authorization-removal/${id}`).toPromise();
    return data;
  }

  async getCarrierAuthorizationRemoval(): Promise<{ data: Carrier[], count: number }> {
    return await lastValueFrom(this.http.get<{ data: Carrier[], count: number }>(`${this.BASE_URL}/carriers/authorization-removal`, {}));
  };

  // async getAllAuthorizationRemovalLabels(query: any): Promise<any> {
  //   const { FreightHandlingCode } = query;
  //   let params = new HttpParams();
  //   if (FreightHandlingCode) { params = params.append('FreightHandlingCode', FreightHandlingCode) }

  //   return await this.http.get(`${this.BASE_URL}/authorization-removal-labels`, { params }).toPromise();
  // }

  // async getAllAuthorizationRemovalLoadings(): Promise<{ data: any, total: number }> {
  //   return await this.http.get<{ data: any, total: number }>(`${this.BASE_URL}/authorization-removal-loadings`).toPromise();
  // }

  // async getAllItemNumberAuthorizationRemoval(): Promise<any> {
  //   return await this.http.get(`${this.BASE_URL}/authorization-removal-itemNumbers`, {}).toPromise();
  // }

  // async getAuthRemovalByLoadNumber(query: any): Promise<any> {
  //   const { LoadNumber } = query;
  //   let params = new HttpParams();
  //   if (LoadNumber) { params = params.append('LoadNumber', LoadNumber) }
  //   return await this.http.get(`${this.BASE_URL}/authorization-removal-by`, { params }).toPromise();
  // }

  async getParkTimes(loadNumber: number) {
    return await this.http.get(`${this.BASE_URL}/authorization-removal/get-park-times/${loadNumber}`).toPromise();
  }

  async getTruckJourney(loadNumber: string): Promise<any> {
    return await this.http.get(`${this.BASE_URL}/authorization-removal/get-truck-journey/${loadNumber}`).toPromise();
  }

  // async getCarriers(): Promise<{ data: any, total: number }> { return await this.http.get<{ data: any, total: number }>(`${this.BASE_URL}/carriers`, {}).toPromise(); }
  // async getTrucks(): Promise<any> { return await this.http.get(`${this.BASE_URL}/trucks`, {}).toPromise(); }
  // async getDrivers(): Promise<any> { return await this.http.get(`${this.BASE_URL}/drivers`, {}).toPromise(); }

  async getCompanies(): Promise<any> { return await this.http.get(`${this.BASE_URL}/companies`, {}).toPromise(); }

  // async getAllAuthorizationRemovalShipTODescriptionA0(): Promise<any> { return await this.http.get(`${this.BASE_URL}/authorization-removal-shiptodescriptions-a0`, {}).toPromise(); }

  // async getAllAuthorizationRemovalSoldTODescriptionA0(): Promise<any> { return await this.http.get(`${this.BASE_URL}/authorization-removal-soldtodescriptions-a0`, {}).toPromise(); }

  // async getAllAuthorizationRemovalSoldTODescriptionC9(): Promise<any> { return await this.http.get(`${this.BASE_URL}/authorization-removal-soldtodescriptions-c9`, {}).toPromise(); }

  // async getAllAuthorizationRemovalShipTODescriptionc9(): Promise<any> { return await this.http.get(`${this.BASE_URL}/authorization-removal-shiptodescriptions-c9`, {}).toPromise(); }


  async getTruckByDock(param: any): Promise<Dock[]> {
    let params = new HttpParams();
    if (param?.dock) { params = params.append('dock', `${param?.dock}`) }
    return await this.http.get<Dock[]>(`${this.BASE_URL}/docks/trucks`, { params }).toPromise();
  }

  async getDocks(): Promise<any> { return await this.http.get(`${this.BASE_URL}/docks`, {}).toPromise(); }

  async updateCarrierAuthorizationRemoval(id: string, param: any): Promise<any> {
    return await this.http.put(`${this.BASE_URL}/authorization-removal/${id}/carriers`, param).toPromise();
  }

  async updateQueuesAes(param: UpdateQueuesAesType): Promise<any> {
    return await this.http.put(`${this.BASE_URL}/replaceQueuesAes/authorization-removal`, param).toPromise();
  }

  async resetAes(param: AuthorizationRemoval): Promise<any> {
    return await this.http.put(`${this.BASE_URL}/reset/authorization-removal`, param).toPromise();
  }


  async updateDock(id: string, param: any): Promise<any> {

    return await this.http.put(`${this.BASE_URL}authorization-removal/dock/${id}`, { ...param }).toPromise();
  }

  async updateAe(id: string, param: any): Promise<any> {
    return await this.http.put(`${this.BASE_URL}/authorization-removal/${id}`, param).toPromise();
  }

  async deleteAE(id: any): Promise<any> {
    return await this.http.delete(`${this.BASE_URL}/authorization-removal/${id}`).toPromise();

  }

  async getFilterData(param: any): Promise<any> {
    let params = new HttpParams();

    if (param?.FreightHandlingCode) { params = params.append('FreightHandlingCode', `${param.FreightHandlingCode}`); }
    if (param?.status) { params = params.append('status', `${param.status}`); }
    if (param?.SoldTo) { params = params.append('SoldTo', `${param.SoldTo}`); }
    if (param?.startDate) { params = params.append('start', `${param?.startDate}`) }
    if (param?.endDate) { params = params.append('end', `${param?.endDate}`) }
    if (param?.keyForFilters) { params = params.append('keyForFilters', `${param?.keyForFilters}`) }
    if (param?.ShipTo) { params = params.append('ShipTo1', `${param.ShipTo}`) }
    if (param?.ShipTo1) { params = params.append('ShipTo2', `${param.ShipTo1}`) }

    return await lastValueFrom(this.http.get<{ data: any, total: number }>(`${this.BASE_URL}/authorization-removal/filterElement`, { params }));
  }

  // async getFilterDatasHistory(param: any): Promise<any> {
  //   let params = new HttpParams();

  //   if (param?.FreightHandlingCode) { params = params.append('FreightHandlingCode', `${param.FreightHandlingCode}`); }
  //   if (param?.status) { params = params.append('status', `${param.status}`); }
  //   if (param?.SoldTo) { params = params.append('SoldTo', `${param.SoldTo}`); }
  //   if (param?.startDate) { params = params.append('start', `${param?.startDate}`) }
  //   if (param?.endDate) { params = params.append('end', `${param?.endDate}`) }
  //   if (param?.keyForFilters) { params = params.append('keyForFilters', `${param?.keyForFilters}`) }
  //   if (param?.ShipTo) { params = params.append('ShipTo1', `${param.ShipTo}`) }
  //   if (param?.ShipTo1) { params = params.append('ShipTo2', `${param.ShipTo1}`) }

  //   return await this.http.get<{ data: any, total: number }>(`${this.BASE_URL}/authorization-removal-filterElement-history`, { params }).toPromise();
  // }

  exportAEAsExcelFile(json: any[], excelFileName: string): void {
    const worksheet: XLSX.WorkSheet = XLSX.utils.json_to_sheet(json);
    worksheet['!cols'] = [{ wch: 15 }, { wch: 20 }, { wch: 20 }, { wch: 20 }];
    const workbook: XLSX.WorkBook = {
      Sheets: { 'Liste des AEs': worksheet },
      SheetNames: ['Liste des AEs'],
    };
    const excelBuffer: any = XLSX.write(workbook, {
      bookType: 'xlsx',
      type: 'array'
    });
    this.saveAsExcelFile(excelBuffer, excelFileName);
  }

  saveAsExcelFile(buffer: any, fileName: string): void {
    const data: Blob = new Blob([buffer], {
      type: this.EXCEL_TYPE
    });
    FileSaver.saveAs(data, fileName + '_export_' + new Date().getDate() + ':' + new Date().getHours() + ':' + new Date().getMinutes() + ':' + new Date().getSeconds() + this.EXCEL_EXTENSION);
  }

  async newAes(param: NewManualAes): Promise<any> {
    return await this.http.post(`${this.BASE_URL}/new-manuale-aes/authorization-removal`, param).toPromise();
  }

  async getAllInspections(param: any): Promise<{ data: any[], total: number }> {
    const { startDate, endDate, driver, status, LoadStatus,
      LoadNumber, carrierLabel, truck, offset,
      limit } = param;

    let params = new HttpParams();

    if (status) { params = params.append('status', `${status}`) }
    // if (LoadStatus) { params = params.append('authorizationRemoval.LoadStatus', `${LoadStatus}`) }
    if (LoadNumber) { params = params.append('LoadNumber', LoadNumber) }
    if (carrierLabel) { params = params.append('carrier.label', `${carrierLabel}`) }
    if (driver) { params = params.append('carrier.driver.fullname', `${driver}`) }
    if (truck) { params = params.append('carrier.truckImmatriculation', `${truck}`) }
    if (offset) { params = params.append('offset', `${offset}`) }
    if (limit) { params = params.append('limit', `${limit}`) }
    if (startDate) { params = params.append('start', `${startDate}`) }
    if (endDate) { params = params.append('end', `${endDate}`) }
    // params = params.append('enable', true);

    return await this.http.get<{ data: any[], total: number }>(`${this.BASE_URL}/inspections`, { params }).toPromise();
  }
  async getTruckInspections(param: any): Promise<{ data: any[], total: number }> {
    const { startDate, endDate, driver, status, truckStatus,
      carrierLabel, truck, offset, LoadNumber,
      limit } = param;

    let params = new HttpParams();

    if (status) { params = params.append('status', `${status}`) }
    if (LoadNumber) { params = params.append('LoadNumber', `${LoadNumber}`) }
    if (truckStatus) { params = params.append('truckStatus', `${truckStatus}`) }
    if (carrierLabel) { params = params.append('carrier.label', `${carrierLabel}`) }
    if (driver) { params = params.append('driver', `${driver}`) }
    if (truck) { params = params.append('tractor.label', `${truck}`) }
    if (offset) { params = params.append('offset', `${offset}`) }
    if (limit) { params = params.append('limit', `${limit}`) }
    if (startDate) { params = params.append('start', `${startDate}`) }
    if (endDate) { params = params.append('end', `${endDate}`) }
    // params = params.append('enable', true);

    return await this.http.get<{ data: any[], total: number }>(`${this.BASE_URL}/truck-inspection`, { params }).toPromise();
  }

  // async getFilterDatasHistoryForInspections(param: any): Promise<any> {
  //   let params = new HttpParams();

  //   if (param?.FreightHandlingCode) { params = params.append('FreightHandlingCode', `${param.FreightHandlingCode}`); }
  //   if (param?.status) { params = params.append('status', `${param.status}`); }
  //   if (param?.SoldTo) { params = params.append('SoldTo', `${param.SoldTo}`); }
  //   if (param?.startDate) { params = params.append('start', `${param?.startDate}`) }
  //   if (param?.endDate) { params = params.append('end', `${param?.endDate}`) }
  //   if (param?.keyForFilters) { params = params.append('keyForFilters', `${param?.keyForFilters}`) }

  //   return await this.http.get<{ data: any, total: number }>(`${this.BASE_URL}/inspection-data-filterElement-history`, { params }).toPromise();
  // }

  async updateInspection(id: string, param: any): Promise<any> {
    return await this.http.put(`${this.BASE_URL}/inspections-data/${id}`, param).toPromise();
  }

  async createAuthorization(data: AuthorizationRemoval): Promise<any> {
    return await this.http.post(`${this.BASE_URL}/authorization-removal`, data).toPromise();
  }

  async downloadPdf(loadNumber: string): Promise<Blob> {
    return await lastValueFrom(this.http.get(`${this.BASE_URL}/authorization-removal/download-pdf/${loadNumber}`, { responseType: 'blob' }));
  }

}
