# Documentation du Module d'Authentification - Application Web Cadyst Logistique

## Aperçu

Le module d'authentification de l'application web Cadyst Logistique gère l'ensemble du processus d'authentification des utilisateurs, y compris la connexion, le rafraîchissement des tokens, et la déconnexion. Il partage de nombreuses similitudes avec l'application mobile tout en offrant des fonctionnalités spécifiques pour l'interface web.

## Structure et Composants

### 1. Service d'authentification (`AuthService`)

**Emplacement :** `/src/app/shared/services/auth.service.ts`

**Méthodes principales :**
- `signin(credentials)` : Authentification avec identifiants
- `resetPassword(oldPassword, newPassword)` : Réinitialisation du mot de passe
- `signout()` : Déconnexion de l'utilisateur
- `refreshToken()` : Rafraîchissement du token d'accès
- `getUserById(userId)` : Récupération des informations utilisateur
- `updateUserInDatabase(userId, newUser)` : Mise à jour des informations utilisateur
- `isUserConnected()` : Vérification de l'état de connexion

**Gestion des erreurs :**
- Codes d'erreur HTTP (400, 403, 404) traités avec messages appropriés
- Notifications toast pour informer l'utilisateur

**Particularités web :**
- Utilisation de NbToastrService pour les notifications visuelles
- Stockage de l'image utilisateur (avatar) dans le localStorage
- Redirection vers 'new-home' après déconnexion

### 2. Intercepteurs HTTP

**Emplacement :** `/src/app/interceptors/`

**Composants :**
- `RefreshTokenInterceptor` : Gestion des tokens expirés
- `RequestInterceptor` : Ajout des en-têtes d'authentification

**Fonctionnalités :**
- Ajout automatique des tokens aux requêtes HTTP
- Détection des erreurs d'expiration de token
- Rafraîchissement automatique des tokens expirés
- Gestion des requêtes en attente pendant le rafraîchissement

### 3. Service de stockage (`StorageService`)

**Emplacement :** `/src/app/shared/services/storage.service.ts`

**Fonctionnalités :**
- Stockage sécurisé des informations d'authentification
- Méthodes pour sauvegarder/récupérer des objets et chaînes
- Chiffrement AES-256 des données sensibles (actuellement désactivé)

### 4. Guards d'authentification

**Emplacement :** `/src/app/shared/guards/`

**Composants :**
- `AuthGuard` : Protection des routes requérant une authentification
- `AdminGuard` : Protection des routes réservées aux administrateurs

**Fonctionnalités :**
- Vérification de l'authentification avant l'accès aux routes
- Redirection vers la page de connexion si non authentifié
- Vérification des rôles utilisateur pour les routes restreintes

## Modèles de données

### 1. Modèle d'identifiants (`Credentials`)

**Structure :**
```typescript
export interface Credentials {
    login?: string;
    password?: string;
}
```

### 2. Modèle utilisateur (`User`)

**Propriétés clés :**
- Identifiants (`_id`, `email`, `matricule`)
- Informations personnelles (`fname`, `lname`, `tel`)
- Droits et rôles (`role`, `rigths`)
- Ressources (`img` pour l'avatar)

## Flux d'authentification

1. **Connexion :**
   - L'utilisateur saisit ses identifiants (login/mot de passe)
   - `AuthService.signin()` envoie les identifiants à l'API
   - Les tokens, informations utilisateur et avatar sont stockés via `StorageService`
   - Redirection vers la page appropriée

2. **Vérification d'authentification :**
   - `AuthService.isUserConnected()` vérifie la présence des données utilisateur
   - Les guards utilisent cette méthode pour protéger les routes

3. **Rafraîchissement automatique des tokens :**
   - `RefreshTokenInterceptor` détecte les erreurs 401
   - Appelle `AuthService.refreshToken()` pour obtenir un nouveau token
   - Réexécute la requête originale avec le nouveau token

4. **Déconnexion :**
   - `AuthService.signout()` efface les données locales
   - Redirection vers la page d'accueil

## Intégration avec l'interface utilisateur

- Utilisation du framework Nebular pour les composants UI
- Notifications toast pour le feedback utilisateur
- Support des thèmes clairs/sombres
- Composants réactifs pour différentes tailles d'écran

## Sécurité

- Tokens JWT avec expiration configurée côté serveur
- Stockage local potentiellement chiffré (code prêt mais commenté)
- Rafraîchissement automatique des tokens expirés
- Protection des routes via guards Angular

## Recommandations pour la refonte

1. **Amélioration de la sécurité :**
   - Activer le chiffrement AES pour le stockage local
   - Implémenter des vérifications CSRF
   - Ajouter l'authentification à deux facteurs
   - Gérer les sessions multiples

2. **Améliorations UX :**
   - Ajouter la fonctionnalité "Se souvenir de moi"
   - Implémenter le verrouillage de compte après tentatives échouées
   - Améliorer les formulaires avec validation en temps réel
   - Ajouter des animations de transition entre les états

3. **Architecture :**
   - Adopter NgRx/Redux pour la gestion d'état
   - Centraliser la logique d'authentification dans un module dédié
   - Améliorer la séparation des préoccupations avec les facades de service
   - Mettre en place des tests unitaires et d'intégration complets

4. **Maintenance :**
   - Uniformiser la gestion des erreurs entre desktop et mobile
   - Améliorer la journalisation côté client
   - Nettoyer le code commenté
   - Implémenter des métriques de performance utilisateur