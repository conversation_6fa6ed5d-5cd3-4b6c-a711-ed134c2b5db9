import { environment } from 'src/environments/environment';
import { BaseUrlService } from './base-url.service';
import { Credentials } from '../models/credentials';
import { StorageService } from './storage.service';
import { FormStatus } from '../models/form-status';
import { HttpClient } from '@angular/common/http';
import { NbToastrService } from '@nebular/theme';
import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { User } from '../models/user';
import { throwError } from 'rxjs';
import get from 'lodash-es/get';

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  BASE_URL: string;
  formStatus: FormStatus;


  constructor(
    private storageSrv: StorageService,
    private router: Router,
    private toastSrv: NbToastrService,
    private baseUrlSrv: BaseUrlService,
    private http: HttpClient
  ) {
    this.BASE_URL = `${this.baseUrlSrv.getOrigin()}${environment.basePath}`;
    this.formStatus = { isActive: false };
  }


  async signin(credentials: Credentials): Promise<any> {
    try {
      this.storageSrv.clear();
      const authData: any = await this.http.post(`${this.BASE_URL}/auth`, credentials).toPromise();
      // console.log(authData);
      this.storageSrv.setObject('oauth', authData.oauth);
      this.storageSrv.setObject('user', authData.user);
      this.storageSrv.setObject('img', authData.img);
      // this.toastSrv.success('Vos identifications ont été vérifiés', 'Connexion réussi');
      // this.updateUserData(authData.user._id);
    } catch (error) {

      if (error.status === 400) {
        this.toastSrv.danger(`Le mot de passe que vous avez renseigné n'est pas valide.`);
      }

      if (error.status === 403) {
        this.toastSrv.danger(`Le mot de passe que vous avez renseigné n'est pas valide`, 'Mot de passe incorrect');
      }

      if (error.status === 404) {
        this.toastSrv.danger(`L'adresse email que vous avez renseignée n'est pas valide.`);
      }

      if (![400, 403, 404].includes(error.status)) {
        this.toastSrv.danger(`Une erreur s'est produite. Veuillez vérifier votre connexion internet.`);
      }
      console.log(error);
      return Promise.reject(error);
    }
  }

  async resetPassword(oldPassword: any, newPassword: any): Promise<any> {
    try {
      const user = this.storageSrv.getObject('user');
      const id = get(user, '_id');
      const token = this.storageSrv.getObject('oauth').access_token;


      const data = { token, oldPassword, newPassword };

      this.storageSrv.clear();
      await this.http.post(`${this.BASE_URL}/reset-password/${id}`, data).toPromise();
      this.toastSrv.success('Votre mot de passe a été réinitilatilsé avec succès  ', 'Réinitilalisation réussi');
    } catch (error) {

      /*  if (error.status === 400) {
         this.toastSrv.danger(`Le mot de passe que vous avez renseigné n'est pas valide.`);
       }

       if (error.status === 403) {
         this.toastSrv.danger(`Le mot de passe que vous avez renseigné n'est pas valide`, 'Mot de passe incorrect');
       }

       if (error.status === 404) {
         this.toastSrv.danger(`L'adresse email que vous avez renseignée n'est pas valide.`);
       }

       if (![400, 403, 404].includes(error.status)) {
         this.toastSrv.danger(`Une erreur s'est produite. Veuillez vérifier votre connexion internet.`);
       } */
      console.log(error);
      return Promise.reject(error);
    }
  }

  async getUserById(userId: string): Promise<User> {
    const url = `${environment.apiUrl}${environment.basePath}/users/${userId}`;
    const newUser: User = await this.http.get<User>(url).toPromise();
    this.storageSrv.setObject('user', newUser);
    return Promise.resolve(newUser);
  }
  async updateUserData(userId: string): Promise<any> {
    const url = `${environment.apiUrl}${environment.basePath}/users/${userId}`;
    const user: User = await this.http.get<User>(url).toPromise();
    return await this.storageSrv.setObject('user', user);
  }

  async updateUserInDatabase(userId: string, newUser: User): Promise<User> {
    const url = `${environment.apiUrl}${environment.basePath}/users/${userId}`;
    const user: User = await this.http.put<User>(url, newUser).toPromise();
    this.storageSrv.setObject('user', user);
    return Promise.resolve(user);
  }


  async signout(): Promise<any> {
    await this.storageSrv.clear();
    this.router.navigate(['/new-home']);
  }

  // getUser(): User {
  //   let user = {} as User;
  //   try {
  //     user = this.storageSrv.getObject('user') as User;
  //   } catch (error) { }
  //   return user;
  // }

  validateEmail(email: string): any {
    const regularExpression = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
    return regularExpression.test(String(email).toLowerCase());
  }

  isUserConnected(): boolean {
    return !!this.storageSrv.getObject('user');
  }

  refreshToken(): any {
    const oauth = this.storageSrv.getObject('oauth');
    if (!oauth) {
      this.router.navigate(['home']);
      this.toastSrv.info(`Veuillez vous authentifier à nouveau`, `Session expirée`);
      return throwError(new Error('empty oauth object'));
    }

    return this.http.post(`${this.BASE_URL}/auth/refresh`, {
      refresh_token: oauth.refresh_token
    });
  }


}
