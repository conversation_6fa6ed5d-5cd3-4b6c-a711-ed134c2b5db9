//hero
.hero {
  position: relative;
  padding: 4rem 0;
  background-image: url('./../../images/fluide-header.png');
  overflow: hidden;
  z-index: 1;
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;


  .hero-content {
    text-align: center;
    margin: 0 auto;
    z-index: 2;

    .hero-image {
      max-width: 100%;
      height: auto;
      margin-bottom: 2rem;
    }

    .brand {
      color: $color-primary;
      font-size: 3rem;
      margin-bottom: 1.5rem;
      font-family: $font-mont-bold;
    }

    .description {
      font-size: 2rem;
      line-height: 1.6;
      margin-bottom: 2rem;
      font-weight: $fw-bold;
      font-family: $font-mont-bold;
      color: $color-muted;
    }

    .login-btn {
      border: 1px solid $color-fourth;
      padding: 0.8em;
      border-radius: 2em;
      background-color: $color-fourth;
      width: 7rem;
      font-size: 1em;
      color: $color-secondary;
      cursor: pointer;
      font-family: $font-mont-bold;
      font-weight: $fw-bold;
      transition: background 0.3s ease;
      text-align: center;

      &:hover {
        background: darken($color-primary, 10%);
        border: 1px solid $color-primary;
      }
    }
  }
}

.container {
  @include vh-center;
  width: 100%;
  height: 100%;
  position: relative;
}

@media(max-width: 1250px) {
  .brand {
    color: $color-primary;
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
    font-family: $font-mont-bold;
  }
}



// Features Section
.features-container {
  padding: 4rem 0;
  background-image: url("./../../images/world.png");
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;

  .features {
    @include vh-between;
    gap: 10em;
    margin-left: 9em;
    margin-right: 9em;
  }

  h2 {
    font-size: 1.5em;
    color: $color-muted;
    margin-bottom: 2rem;
    font-weight: $fw-bold;
    font-family: $font-mont-bold;
  }

  .features-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-gap: 2rem;

    nb-card {
      border: 1px solid #0387664D;

      .feature-list {
        display: flex;
        align-items: flex-start;
        gap: 1em;

        img {
          max-width: 50px;
          height: auto;
        }

        div {
          h3 {
            margin-bottom: 0.5em;
            font-size: 1rem;
            font-family: $font-mont-bold;
            font-weight: 800;
            color: $color-muted;
          }

          ul {
            list-style: none;
            padding: 0;

            li {
              margin-bottom: 0.2rem;
              padding-left: 1rem;
              position: relative;
              font-size: 0.9rem;
              font-weight: 500;
              font-family: $font-mont-bold;
              color: $color-muted;

              &::before {
                content: '•';
                color: $color-primary;
                position: absolute;
                left: 0;
              }
            }
          }
        }
      }
    }
  }

  @media (max-width: 1250px) {
    .features {
      gap: 0rem;
      margin-left: 3rem;
      margin-right: 3rem;
      flex-direction: column;
    }

    .features-grid {
      grid-gap: 1.5rem;
    }

    nb-card {
      .feature-list {
        img {
          max-width: 40px;
        }

        div {
          h3 {
            font-size: 0.9rem;
          }

          ul li {
            font-size: 0.8rem;
          }
        }
      }
    }
  }

  // @media (max-width: 1250px) {
  //   .features {
  //     gap: 3em;
  //     margin-left: 3em;
  //     margin-right: 3em;
  //     flex-direction: column;
  //     align-items: center;
  //   }

  //   .features-grid {
  //     grid-template-columns: 2fr;
  //     grid-gap: 1rem;
  //   }

  //   nb-card {
  //     .feature-list {
  //       flex-direction: column;
  //       align-items: center;
  //       text-align: center;

  //       img {
  //         max-width: 40px;
  //         margin-bottom: 1rem;
  //       }

  //       div {
  //         h3 {
  //           font-size: 0.9rem;
  //         }

  //         ul li {
  //           font-size: 0.8rem;
  //         }
  //       }
  //     }
  //   }
  // }
}

.features img {
  max-width: 100%;
  height: auto;
}

.features-grid {
  margin-bottom: 2rem;
}



.limit {
  background-color: $color-limit;
  height: 7px;
}


// <!-- Platform Preview Section -->
.platform-preview-container {
  background: rgb(65, 156, 251, 0.1);
  opacity: 0.9;
  padding: 4rem 0;

  .platform-preview {
    display: flex;
    justify-content: space-between;
    margin-left: 9em;
    margin-right: 9em;

  }

  .laptop-container {
    max-width: 800px;
    width: 100%;
    margin-right: 2rem;
    position: relative;

    .circle-green {
      position: absolute;
      background-color: $color-circle;
      height: 150px;
      width: 150px;
      border-radius: 100%;
      top: -60px;
      right: 0;
      z-index: 0;
    }

    img {
      position: relative;
      width: 100%;
      height: auto;
      z-index: 1;
    }
  }

  .advantages {
    max-width: 1000px;
    width: 100%;
    @include h-center;
    flex-direction: column;

    h2 {
      font-size: 1.5rem;
      color: $color-muted;
      margin-bottom: 1em;
      font-weight: $fw-bold;
      font-family: $font-mont-bold !important;
    }

    .advantage-items {
      display: flex;
      flex-wrap: wrap;
      gap: 1.5rem;

      .advantage-item {
        @include v-center;
        justify-content: start;
        gap: 0.5rem;
        text-align: left;

        .advantage-info {
          width: 100%;
          padding-left: 1rem;

          h3 {
            font-size: 18px;
            font-weight: 500;
            color: #201F21;
            margin-bottom: 0.8rem;
            font-family: $font-mont-bold !important;
          }

          p {
            font-size: 0.9rem;
            font-weight: 500;
            font-family: $font-mont-bold;
            color: #201F21;
          }
        }
      }
    }
  }

  @media (max-width: 1250px) {
    .platform-preview {
      flex-direction: column;
      align-items: center;
      margin-left: 5rem;
      margin-right: 5rem
    }

    .laptop-container {
      max-width: 100%;
      margin-bottom: 2rem;
    }

    .advantages {
      padding: 0 1rem;
    }

    .advantage-items {
      flex-direction: column;
      gap: 1rem;
    }

    .advantage-item {
      width: 100%;
      justify-content: center;
      text-align: center;
    }

    .advantage-info {
      width: 100%;
      padding-left: 0;
    }

    img {
      max-width: 50%;
    }

    .circle-green {
      top: -80px;
      right: 5%;
      width: 120px;
      height: 120px;
    }
  }
}




// FAQ Section
.faq-container {
  padding: 4rem 0;
  background-color: $color-secondary;

  .faq {
    margin-left: 9em;
    margin-right: 9em;
  }

  h2 {
    font-size: 1.5em;
    color: $color-muted;
    text-align: center;
    margin-bottom: 1em;
    font-family: $font-mont-bold !important;
  }

  .accordion {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-gap: 1rem;
    gap: 1rem;

    @media (max-width: 1250px) {
      grid-template-columns: -1fr;
    }

    .accordion-item {
      margin-bottom: 1rem;
      border: 1px solid #ddd;
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
      transition: box-shadow 0.3s ease-in-out;
      display: flex;
      flex-direction: column;
      height: fit-content;

      .accordion-header {
        @include vh-between;
        padding: 2.5rem;
        font-weight: 800;
        color: $color-muted;
        font-size: 1rem;
        cursor: pointer;
        border-bottom: 1px solid #ddd;

        .question-text {
          font-family: $font-mont-regular;
          text-align: justify;
          font-weight: 800;
          font-size: 1rem;
        }
      }

      .accordion-body {
        text-align: justify;
        color: $color-muted;
        font-weight: $fw-primary;
        font-family: $font-mont-bold;
        font-size: 0.9rem;
        height: auto;
        opacity: 0;
        max-height: 0;
        overflow: hidden;
        transition: all 0.3s ease-in-out;

        &.open {
          padding: 2.5rem;
          opacity: 1;
          max-height: 1000px;
        }
      }
    }
  }

  .custom-icon {
    font-size: 1.5rem;
    margin-left: 2rem;
  }

  .plus {
    font-size: 1.5rem;
    color: $color-primary;
  }

  .minus {
    font-size: 1.5rem;
    color: red;
  }
}

@media (max-width: 1250px) {
  .faq-container {
    .faq {
      margin-left: 5em;
      margin-right: 5em;
    }
  }
}


// Footer
.footer-content {
  @include vh-center;
  padding: 1rem 2rem;
  background-color: $bg-color-primary;
  height: 8rem;

  .footer-links {
    display: flex;
    gap: 2rem;

    a {
      color: $color-secondary !important;
      text-decoration: none;
      font-weight: 500;
      font-size: 0.9rem;
      font-family: $font-mont-bold
    }

    p {
      color: $color-secondary;
      font-weight: 500;
      font-size: 0.9rem;
      font-family: $font-mont-bold
    }
  }
}