import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { TruckAllocationDockRoutingModule } from './truck-allocation-dock-routing.module';
import { StatusRemovalPipe, TruckAllocationDockComponent } from './truck-allocation-dock.component';
import { DragDropModule } from '@angular/cdk/drag-drop';
import { NbIconModule, NbFormFieldModule, NbAutocompleteModule, NbCardModule, NbInputModule, NbTooltipModule, NbButtonModule, NbSpinnerModule, NbDialogModule, NbSelectModule } from '@nebular/theme';
import { SharedModule } from 'src/app/shared/shared.module';
import { FormsModule } from '@angular/forms';
import { ConfirmDialogComponent } from './confirm-dialog/confirm-dialog.component';


@NgModule({
  declarations: [
    TruckAllocationDockComponent,
    StatusRemovalPipe,
    ConfirmDialogComponent
  ],
  imports: [
    CommonModule,
    FormsModule,
    NbIconModule,
    NbFormFieldModule,
    NbAutocompleteModule,
    NbCardModule,
    NbInputModule,
    NbTooltipModule,
    NbButtonModule,
    DragDropModule,
    NbSpinnerModule,
    NbDialogModule,
    SharedModule,
    NbSelectModule,
    TruckAllocationDockRoutingModule
  ]
})
export class TruckAllocationDockModule { }
