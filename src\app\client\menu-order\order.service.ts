import { BaseUrlService } from 'src/app/shared/services/base-url.service';
import { StorageService } from 'src/app/shared/services/storage.service';
import { CategoryProduct } from 'src/app/shared/models/category-product';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Packaging } from 'src/app/shared/models/packaging';
import { environment } from 'src/environments/environment';
import { Price } from 'src/app/shared/models/price';
import { Order } from 'src/app/shared/models/order';
import { Store } from './../../shared/models/store';
import { User } from 'src/app/shared/models/user';
import { Injectable } from '@angular/core';
import isEmpty from 'lodash-es/isEmpty';
import * as moment from 'moment';
import get from 'lodash-es/get';
import { Observable, from, lastValueFrom, of } from 'rxjs';
import { catchError, map } from 'rxjs/operators';

// Interfaces pour améliorer le typage
export interface OrderSearchParams {
  offset?: number;
  limit?: number;
  erpReference?: string;
  clientName?: string;
  deliveryPoint?: string;
  startDate?: string;
  companyName?: string;
  userId?: string;
  product?: string;
  endDate?: string;
  status?: number;
  statusDelivery?: number;
}

export interface OrderSearchFilters {
  erpReference?: string;
  clientName?: string;
  startDate?: string;
  endDate?: string;
  status?: number;
}

export interface OrderSearchOptions {
  limit?: number;
  offset?: number;
  filters?: OrderSearchFilters;
}

// Interface étendue pour l'objet Order retourné par l'API
export interface OrderResponse {
  data: Order[];
  count: number;
}

@Injectable({
  providedIn: 'root'
})
export class OrderService {
  private readonly BASE_URL: string;
  currentOrder: Order;

  constructor(
    private http: HttpClient,
    private baseUrlSrv: BaseUrlService,
    private storageSrv: StorageService,
  ) {
    this.BASE_URL = `${this.baseUrlSrv.getOrigin()}${environment.basePath}`;
  }

  /**
   * Définit la commande actuelle
   */
  setOrder(order: Order): void {
    this.currentOrder = order;
  }

  /**
   * Récupère la commande actuelle
   */
  getOrder(): Order {
    return this.currentOrder;
  }

  /**
   * Récupère les commandes avec filtrage optionnel
   * @param options Options de recherche et de filtrage
   * @returns Observable avec les données des commandes et le comptage
   */
  getOrders(options: OrderSearchOptions): Promise<OrderResponse> {
    return this.searchOrdersInternal(this.buildParams(options));
  }

    async getFilterData(param: any): Promise<any> {
      let params = new HttpParams();
  
      if (param?.FreightHandlingCode) { params = params.append('FreightHandlingCode', `${param.FreightHandlingCode}`); }
      if (param?.status) { params = params.append('status', `${param.status}`); }
   
      if (param?.keyForFilters) { params = params.append('keyForFilters', `${param?.keyForFilters}`) }

  
      return await lastValueFrom(this.http.get<{ data: any, total: number }>(`${this.BASE_URL}/orders/filterElement`, { params }));
    }
  

  /**
   * Recherche des commandes avec des critères spécifiques
   * @param params Paramètres de recherche
   * @returns Observable avec les données des commandes et le comptage
   */
  searchOrders(params: OrderSearchParams): Promise<OrderResponse> {
    return this.searchOrdersInternal(params);
  }

  /**
   * Fonction interne pour gérer la recherche de commandes
   * @param params Paramètres de recherche
   * @returns Promise avec les données et le comptage
   */
  private async searchOrdersInternal(params: OrderSearchParams): Promise<OrderResponse> {
    try {
      
      // Construction des paramètres pour la recherche
      let queryParams = this.buildHttpParams(params);
      
  
      
      // URL de recherche
      const url = `${this.BASE_URL}/orders`;
      
      // Effectuer la requête
      const response = await this.http.get<OrderResponse>(url, { params: queryParams }).toPromise();
      
      // Si nous avons une réponse, appliquer les filtres côté client
      if (response && response.data && response.data.length > 0) {
        const filteredData = this.applyClientSideFilters(response.data, params);
        
        return {
          data: filteredData,
          count: response.count
        };
      }
      
      return response || { data: [], count: 0 };
    } catch (error) {
      console.error('Erreur lors de la recherche des commandes:', error);
      throw error;
    }
  }

  /**
   * Construit les paramètres HTTP à partir des options de recherche
   */
  private buildParams(options: OrderSearchOptions): OrderSearchParams {
    const params: OrderSearchParams = {
      offset: options.offset !== undefined ? options.offset : 0,
      limit: options.limit !== undefined ? options.limit : 20,
    };

    // Ajouter les filtres s'ils existent
    if (options.filters) {
      if (options.filters.erpReference) {
        params.erpReference = options.filters.erpReference;
      }
      if (options.filters.clientName) {
        params.clientName = options.filters.clientName;
      }
      if (options.filters.startDate) {
        params.startDate = options.filters.startDate;
      }
      if (options.filters.endDate) {
        params.endDate = options.filters.endDate;
      }
      if (options.filters.status) {
        params.status = options.filters.status;
      }
    }

    return params;
  }

  /**
   * Construit les paramètres HTTP à partir des paramètres de recherche
   */
  private buildHttpParams(params: OrderSearchParams): HttpParams {
    let httpParams = new HttpParams();
    
    // Pagination
    if (params.offset !== undefined) {
      httpParams = httpParams.append('offset', params.offset.toString());
    }
    if (params.limit !== undefined) {
      httpParams = httpParams.append('limit', params.limit.toString());
    }
        // Si c'est un utilisateur normal, ajouter son ID dans les paramètres
        if (params.userId) {
          httpParams = httpParams.append('user._id', params.userId);
        }
    
    // Filtres serveur
    if (params.erpReference) {
      httpParams = httpParams.append('erpReference', params.erpReference);
    }
    if (params.statusDelivery) {
      httpParams = httpParams.append('statusDelivery', params.statusDelivery);
    }

    if (params.deliveryPoint) {
      httpParams = httpParams.append('cart.shipping.label', params.deliveryPoint);
    }
   
    if (params.companyName) {
      httpParams = httpParams.append('company.name', params.companyName);
    }
    if (params.product) {
      httpParams = httpParams.append('cart.items.product.label', params.product);
    }
    if (params.status) {
      httpParams = httpParams.append('status', params.status.toString());
    }
    
    // Filtres de date
    if (params.startDate) {
      httpParams = httpParams.append('created_at_gte', new Date(params.startDate).getTime().toString());
    }
    if (params.endDate) {
      // Ajouter un jour à la date de fin pour inclure cette journée entière
      const endDate = new Date(params.endDate);
      endDate.setDate(endDate.getDate() + 1);
      httpParams = httpParams.append('created_at_lte', endDate.getTime().toString());
    }
    
    return httpParams;
  }

  /**
   * Applique des filtres côté client aux résultats
   */
  private applyClientSideFilters(data: Order[], params: OrderSearchParams): Order[] {
    let filteredData = [...data];
    
    // Filtre par référence ERP (numéro X3)
    if (params.erpReference && params.erpReference.trim() !== '') {
      const erpRefLower = params.erpReference.toString().toLowerCase().trim();
      filteredData = filteredData.filter(order => {
        const orderErpRef = (order.erpReference || '').toString().toLowerCase();
        return orderErpRef.includes(erpRefLower);
      });
    }
    
    // Filtre par nom client
    if (params.clientName && params.clientName.trim() !== '') {
      const clientNameLower = params.clientName.toLowerCase().trim();
      filteredData = filteredData.filter(order => {
        if (!order.user) return false;
        
        const fullName = `${order.user.firstName || ''} ${order.user.lastName || ''}`.toLowerCase();
        const companyName = (order.company?.name || '').toLowerCase();
        
        return fullName.includes(clientNameLower) || companyName.includes(clientNameLower);
      });
    }
    
    // Filtre par date de création
    if (params.startDate || params.endDate) {
      filteredData = filteredData.filter(order => {
        const createdDate = order.dates?.created || order.created_at;
        if (!createdDate) return false;
        
        const orderDate = new Date(createdDate);
        
        if (params.startDate) {
          const startDate = new Date(params.startDate);
          startDate.setHours(0, 0, 0, 0);
          if (orderDate < startDate) return false;
        }
        
        if (params.endDate) {
          const endDate = new Date(params.endDate);
          endDate.setHours(23, 59, 59, 999);
          if (orderDate > endDate) return false;
        }
        
        return true;
      });
    }
    
    return filteredData;
  }

  /**
   * Crée une nouvelle commande
   */
  async createOrder(): Promise<any> {
    try {
      const order = this.storageSrv.getObject('order');
      return await this.http.post(`${this.BASE_URL}/orders`, order).toPromise();
    } catch (error) {
      console.error('Erreur lors de la création de la commande:', error);
      throw error;
    }
  }


  async updateOrder(order: Order): Promise<any> {
    try {
      return await this.http.put(`${this.BASE_URL}/orders/${order?._id}`, order).toPromise();
    } catch (error) {
      console.error('Erreur lors de la mise  a jour de la commande:', error);
      throw error;
    }
  }


  /**
   * Récupère les types de produits
   */
  async getProductTypes(store: Store): Promise<CategoryProduct[]> {
    try {
      return await this.http.get<CategoryProduct[]>(`${this.BASE_URL}/category-products`).toPromise();
    } catch (error) {
      console.error('Erreur lors de la récupération des types de produits:', error);
      return [];
    }
  }

  /**
   * Récupère les emballages
   */
  async getPackagings(type: CategoryProduct): Promise<Packaging[]> {
    try {
      return await this.http.get<Packaging[]>(`${this.BASE_URL}/packagings`).toPromise();
    } catch (error) {
      console.error('Erreur lors de la récupération des emballages:', error);
      return [];
    }
  }

  /**
   * Récupère les commandes d'un utilisateur
   */
  async getUserOrders(user: User): Promise<Order[]> {
    try {
      return await this.http.get<Order[]>(`${this.BASE_URL}/orders/user/${user._id}`).toPromise();
    } catch (error) {
      console.error('Erreur lors de la récupération des commandes utilisateur:', error);
      return [];
    }
  }

  /**
   * Récupère les articles par emballage
   */
  async getItemsByPackaging(packaging: Packaging, productCategory: CategoryProduct, store: Store): Promise<Price[]> {
    try {
      const params = new HttpParams()
        .append('packagingCode', packaging.code)
        .append('categoryCode', productCategory.code)
        .append('storeRef', store.jdeReference);
      
      return await this.http.get<Price[]>(`${this.BASE_URL}/prices-by`, { params }).toPromise();
    } catch (error) {
      console.error('Erreur lors de la récupération des articles par emballage:', error);
      return [];
    }
  }

  /**
   * Récupère les magasins
   */

}
