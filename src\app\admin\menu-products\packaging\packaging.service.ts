import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BaseUrlService } from 'src/app/shared/services/base-url.service';
import { environment } from 'src/environments/environment';
@Injectable({
  providedIn: 'root'
})
export class PackagingService {



  BASE_URL: string;
  currentPackaging: any;

  constructor(
    private http: HttpClient,
    private baseUrlSrv: BaseUrlService,

  ) {
    this.BASE_URL = `${this.baseUrlSrv.getOrigin()}${environment.basePath}`;

  }

  async getPackagings(): Promise<any> {

    return await this.http.get(`${this.BASE_URL}/packagings`).toPromise();

  }

  async deletePackagings(): Promise<any> {
    return await this.http.delete(`${this.BASE_URL}/packagings/${this.currentPackaging._id}`).toPromise();
  }

  async editPackagings(packaging: any): Promise<any> {
    return await this.http.put(`${this.BASE_URL}/packagings/${this.currentPackaging._id}`, packaging).toPromise();
  }

  async addPackagings(packaging: any): Promise<any> {
    return await this.http.post(`${this.BASE_URL}/packaging/`, packaging).toPromise();
  }


  generateQueryParams(param: any): any {
    let params = new HttpParams();

    const { offset, limit } = param;

    if (offset) {
      params = params.append('offset', `${offset}`);
    }
    if (limit) {
      params = params.append('limit', `${limit}`);
    }


    return params;
  }}
