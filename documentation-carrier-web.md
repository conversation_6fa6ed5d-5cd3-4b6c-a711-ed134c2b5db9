# Documentation du Module de Gestion des Transporteurs - Application Web Cadyst Logistique

## Aperçu

Le module de gestion des transporteurs de l'application web Cadyst Logistique permet aux administrateurs de gérer les transporteurs, leurs camions et leurs chauffeurs. Il offre une interface complète pour créer, consulter, modifier et supprimer des transporteurs, ainsi que pour gérer leurs ressources associées comme les véhicules et le personnel.

## Structure et Composants

### 1. Service des Transporteurs (`CarrierService`)

**Emplacement :** `/src/app/admin/carrier/carriers.service.ts`

**Méthodes principales :**
- `getAllCarriers(param)` : Récupération des transporteurs avec pagination
- `getAlltrucks(param)` : Récupération des camions d'un transporteur
- `getAllDrivers(param)` : Récupération des chauffeurs d'un transporteur
- `updateCarrier(carrier)` : Mise à jour d'un transporteur
- `deleteCarrier(carrier)` : Suppression d'un transporteur
- `insertCarrier(carrier)` : Création d'un transporteur
- `insertTrucks(trucks)` : Ajout de camions pour un transporteur
- `deleteTruck(truck)` : Suppression d'un camion
- `insertDrivers(drivers)` : Ajout de chauffeurs pour un transporteur
- `deleteDriver(driver)` : Suppression d'un chauffeur
- `getPdfQrCode(truck)` : Génération d'un QR code PDF pour un camion

**État et propriétés :**
- `BASE_URL` : URL de base pour les requêtes API
- `selectedCarrier` : Transporteur actuellement sélectionné

### 2. Composant de Liste des Transporteurs (`CarrierListComponent`)

**Emplacement :** `/src/app/admin/carrier/carrier-list/carrier-list.component.ts`

**Fonctionnalités :**
- Affichage paginé des transporteurs
- Filtrage basique des transporteurs
- Navigation vers les détails d'un transporteur
- Ajout de nouveaux transporteurs
- Interface de compression des images pour les logos

**Méthodes principales :**
- `getAllCarriers()` : Récupération des transporteurs avec gestion de la pagination
- `showdetail(carrier)` : Navigation vers les détails d'un transporteur
- `openInsertCarrier()` : Redirection vers le formulaire d'ajout de transporteur
- `compressFile()` : Compression des images de logo

### 3. Composant de Détail des Transporteurs (`CarrierDetailComponent`)

**Emplacement :** `/src/app/admin/carrier/carrier-detail/carrier-detail.component.ts`

**Fonctionnalités :**
- Affichage et modification des informations du transporteur
- Gestion des camions (liste, ajout, modification, suppression)
- Gestion des chauffeurs (liste, ajout, modification, suppression)
- Génération de QR codes pour les camions
- Gestion des formations des chauffeurs
- Upload de fichiers Excel pour importer des camions ou chauffeurs

**Méthodes principales :**
- `updateCarriers()` : Mise à jour des informations du transporteur
- `deleteCarrier()` : Suppression d'un transporteur
- `getAllTrucks()` / `getAllDrivers()` : Récupération des ressources associées
- `addTruck()` / `addDriver()` : Ajout de ressources
- `editTruck()` / `editDriver()` : Modification de ressources
- `deleteTruck()` / `deleteDriver()` : Suppression de ressources
- `openDialogPDF()` : Génération d'un QR code pour un camion
- `trucksUpload()` / `driversUpload()` : Import via Excel

### 4. Composant d'Ajout de Transporteur (`CarrierAddComponent`)

**Emplacement :** `/src/app/admin/carrier/carrier-add/carrier-add.component.ts`

**Fonctionnalités :**
- Formulaire de création d'un nouveau transporteur
- Ajout de camions et chauffeurs au moment de la création
- Interface de gestion des formations pour les chauffeurs
- Compression des images de logo

**Méthodes principales :**
- `addCarrier()` : Création d'un transporteur avec ses ressources
- `addTruck()` / `addDriver()` : Ajout de ressources pendant la création
- `compressFile()` : Compression des images de logo

## Modèles de données

### 1. Transporteur (`Carrier`)

**Emplacement :** `/src/app/shared/models/carrier.ts`

**Structure :**
```typescript
export interface Carrier {
  _id?: string;
  label?: string;      // Nom du transporteur
  code?: number;       // Code unique du transporteur
  rccm?: string;       // Registre du Commerce et du Crédit Mobilier
  niu?: string;        // Numéro d'Identification Unique
  njde?: number;       // Numéro JDE (ERP)
  tel?: number;        // Téléphone principal
  tel1?: number;       // Téléphone alternatif 1
  tel2?: number;       // Téléphone alternatif 2
  email?: string;      // Email principal
  email1?: string;     // Email alternatif 1
  email2?: string;     // Email alternatif 2
  logo?: string;       // Logo en base64
  localisation?: string; // Adresse ou localisation
}
```

### 2. Camion (`Truck`)

**Emplacement :** `/src/app/shared/models/trucks.ts`

**Structure :**
```typescript
export interface Truck {
  _id?: string;
  immatriculation?: string; // Plaque d'immatriculation
  desc?: string;          // Description
  code?: number;          // Code unique
  type?: string;          // Type de camion (PLATEAU, BENNE, VRAQUIER)
  capacity?: number;      // Capacité en tonnes
  volume?: number;        // Volume en m³
  enable?: boolean;       // État d'activation
  status?: number;        // Statut (opérationnel, en panne...)
  carrierRef?: number;    // Référence au transporteur
  dateStartBrokenDown?: number; // Date de début de panne
  dateEndBrokenDown?: number;   // Date de fin de panne
  dock?: any;             // Quai assigné
}
```

## Flux utilisateur

### 1. Consultation des transporteurs

1. L'administrateur accède à la liste des transporteurs
2. Pagination automatique avec 15 transporteurs par page
3. Possibilité de filtrer par nom
4. Clic sur un transporteur pour accéder à ses détails

### 2. Création d'un transporteur

1. Accès au formulaire de création
2. Saisie des informations de base (nom, codes, contacts)
3. Upload optionnel d'un logo (avec compression automatique)
4. Possibilité d'ajouter des camions et chauffeurs directement
5. Soumission du formulaire et traitement API
6. Redirection vers la liste des transporteurs

### 3. Gestion des camions

1. Depuis la page de détail d'un transporteur, accès à l'onglet des camions
2. Affichage paginé des camions existants
3. Ajout de nouveaux camions via formulaire ou import Excel
4. Possibilité de modifier, supprimer ou désactiver un camion
5. Génération de QR codes pour les camions

### 4. Gestion des chauffeurs

1. Depuis la page de détail d'un transporteur, accès à l'onglet des chauffeurs
2. Affichage paginé des chauffeurs existants
3. Ajout de nouveaux chauffeurs via formulaire ou import Excel
4. Possibilité de modifier ou supprimer un chauffeur
5. Gestion des formations (ajout, modification) des chauffeurs

## Particularités techniques

### 1. Compression des images

Utilisation de la bibliothèque `ImageCompressor` pour réduire la taille des logos :
```typescript
const compressorSettings = {
  toWidth: 150,
  toHeight: 150,
  mimeType: 'image/png',
  mode: 'strict',
  quality: 0.8,
  // ...
};
imageCompressor.run(imageSrc, compressorSettings, callback);
```

### 2. Import via Excel

Utilisation de la bibliothèque `xlsx` pour traiter les fichiers Excel :
```typescript
fileReader.onload = async (e) => {
  let binaryData = e.target.result;
  let workbook = xlsx.read(binaryData, { type: 'binary' });
  const sheetName = workbook.SheetNames[0];
  const data = xlsx.utils.sheet_to_json(workbook.Sheets[sheetName], {
    raw: false,
  });
  // Traitement des données...
}
```

### 3. Gestion des types et capacités de camions

Utilisation d'enums sous forme d'objets pour les types et capacités :
```typescript
trucksTypes = [
  { value: 1, label: 'PLATEAU' },
  { value: 2, label: 'BENNE' },
  { value: 3, label: 'VRAQUIER' },
];

trucksCapacity = [
  { value: 5, label: '5 tonnes' },
  { value: 10, label: '10 tonnes' },
  // ...
];
```

### 4. Gestion des formations des chauffeurs

Structure complexe pour les formations des chauffeurs :
```typescript
selectedTraining = {
  trainingType: '',      // Type de formation
  trainerName: '',       // Nom du formateur
  date: {
    start: '',           // Date de début
    end: '',             // Date de fin
    expiredDate: '',     // Date d'expiration
  }
}
```

## Recommandations pour la refonte

1. **Architecture et code :**
   - Adopter une approche par modules plus stricte
   - Utiliser des formulaires réactifs (ReactiveForm) pour la validation
   - Séparer les composants pour réduire leur complexité
   - Implémenter une gestion d'état plus robuste (NgRx)

2. **Interface utilisateur :**
   - Moderniser l'UI avec des composants plus interactifs
   - Améliorer le filtrage avancé (multi-critères)
   - Ajouter des tableaux triables avec colonnes personnalisables
   - Implémenter un système de notifications pour les actions longues

3. **Fonctionnalités avancées :**
   - Ajouter un système de versionnement des modifications
   - Implémenter une carte pour visualiser la localisation des transporteurs
   - Ajouter des tableaux de bord de performance par transporteur
   - Améliorer l'import/export avec validation des données

4. **Performance :**
   - Optimiser le chargement des listes avec virtualisation
   - Améliorer la gestion des images (lazy loading)
   - Mettre en cache les données fréquemment utilisées
   - Optimiser le nombre de requêtes API

5. **Sécurité :**
   - Renforcer la validation des entrées utilisateur
   - Ajouter des confirmations pour les actions destructives
   - Améliorer la gestion des erreurs
   - Implémenter des journaux d'audit pour les modifications