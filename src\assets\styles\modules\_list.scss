.filter-area {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 20px 0;
  padding: 15px;
  background-color: #f7f9fc;
  border-radius: 8px;

  .left-area {
    display: flex;
    align-items: center;
    gap: 20px;
    flex-wrap: wrap;
    width: 80%;

    .filter-label {
      font-size: 16px;
      font-weight: 500;
      color: #2d3e50;
    }

    .filter-group {
      display: flex;
      align-items: center;
      gap: 10px;

      input {
        width: 250px;
        height: 40px;
        border: 1px solid #e4e9f2;
        border-radius: 4px;
        padding: 0 15px;
        font-size: 14px;
        transition: all 0.3s ease;

        &:focus {
          border-color: #3366ff;
          box-shadow: 0 0 0 2px rgba(51, 102, 255, 0.1);
        }

        &::placeholder {
          color: #8f9bb3;
        }
      }
    }
  }

  .right-area {
    .search-btn {
      height: 40px;
      padding: 0 20px;
      display: flex;
      align-items: center;
      gap: 8px;
      font-weight: 500;
    }
  }

  .right-area {
    align-items: flex-end;
    @include v-center;
    gap: 1em;

    .search-btn {
      font-family: $font-regular;
      font-size: 14px;
      background: $bg-color-primary;
      border-radius: 6px;
      padding: 6px 20px !important;
      color: $color-secondary;

      nb-icon {
        margin-right: 10px;
      }
    }
  }
}

.empty-list {
  justify-content: center;
  align-items: center;
  gap: 1em;
  font-size: 22px;
  flex-direction: column;
  height: 100%;

  img {
    max-width: 30%;
  }
}

.element-head {
  display: flex;
  flex-direction: column;
  flex: 1 1 auto;
  overflow: auto;

  .col.col-paginator {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    width: 100% !important;
  }

  .paginator {
    font-weight: bold;
    color: #353535;
    height: 35px !important;
    font-size: 14px !important;
    border-bottom: 0 !important;
  }

  .list-elt-header {
    @include vh-between;
    height: 50px;
    padding-top: 7px;
    padding-bottom: 7px;
    font-size: 13px;

    .col {
      height: 100%;
      display: flex;
      align-items: center;
    }

    .col-num {
      min-width: 40px;
      max-width: 40px;
    }

    .col-ref {
      min-width: 150px;
      max-width: 100px;
    }

    .col-jde {
      min-width: 140px;
      max-width: 140px;
    }

    .col-date {
      min-width: 120px;
      max-width: 120px;
    }

    .col-hour {
      min-width: 100px;
      max-width: 100px;
    }

    .col-name {
      max-width: 150px;
      min-width: 150px;
    }

    .col-status {
      min-width: 100px;
      max-width: 100px;
      display: flex;
      align-items: center;
      justify-content: center;

      .badge {
        font-size: 12px;
        position: relative;
        width: 103px;
      }

      span {
        background: #027a37;
        border-radius: 4px;
        color: $color-secondary;
        padding: 3px 8px;
      }
    }

    .col-details {
      min-width: calc(100% - 970px);
      overflow: hidden;
      justify-content: center;

      .product-choice {
        background-image: url("./../../images/icons/ciment.png");
        background: no-repeat center contain;
        width: 19px;
        height: 31px;
      }
    }

    .col-action {
      max-width: 140px;
      min-width: 140px;

      .action-icons {

        display: flex;
        align-items: center;
        justify-content: flex-end;
        width: 70%;
        gap: 0.5em;
      }
    }

    button {
      text-transform: none !important;
      background-color: #edf1f7;
      padding: 5px;
      border-radius: 5px;
    }
  }
}