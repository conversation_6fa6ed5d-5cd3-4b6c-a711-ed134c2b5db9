import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { AcceptedRemovalManagementComponent } from './accepted-removal-management.component';

const routes: Routes = [
  {path: '', component: AcceptedRemovalManagementComponent}
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class AcceptedRemovalManagementRoutingModule { }
