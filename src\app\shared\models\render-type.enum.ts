export enum RenderTypeValue {
    PICKUP = 'C9',
    RENDER = 'A0'
}

export enum StatusAES {
    CREATED = "21",
    CREATED_APPROVED = "29",
    CHECK_IN = "35",
    TRANSIT = "50",
    PARTIAL_LOADING = "60",
    COMPLETE = "80",
    CANCEL = "99",
}

export const RenderTypeValueLibrary = {
    [RenderTypeValue.PICKUP]: "PICK UP",
    [RenderTypeValue.RENDER]: "RENDU",
}
export const StatusAESLibrary = {
    [StatusAES.CREATED]: "Créer",
    [StatusAES.CREATED_APPROVED]: "C<PERSON>er et approuver",
    [StatusAES.CHECK_IN]: "En usine",
    [StatusAES.TRANSIT]: "Transit",
    [StatusAES.PARTIAL_LOADING]: `Statut "60"`,
    [StatusAES.COMPLETE]: "Charger",
    [StatusAES.CANCEL]: "Annulée",
}

export enum FactoryTypeLibrary {
    NEW_PARKING = 'Nouveau Parking',
    FACTORY_PARKING = 'Parking Usine',
    CHARGED_FACTORY = 'Chargement Usine'
}

export const QueueStatus = {
    [FactoryTypeLibrary.NEW_PARKING]: 700,
    [FactoryTypeLibrary.FACTORY_PARKING]: 400,
    [FactoryTypeLibrary.CHARGED_FACTORY]: 450
}