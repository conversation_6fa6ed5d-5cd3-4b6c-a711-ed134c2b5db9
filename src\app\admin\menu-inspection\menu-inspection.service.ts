import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { lastValueFrom } from 'rxjs';
import { KpiInspectionCheck, InspectionResponse } from 'src/app/shared/models/kpiInspectionCheck';
import { BaseUrlService } from 'src/app/shared/services/base-url.service';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root'
})
export class MenuInspectionService {
  BASE_URL: string;

  constructor(
    private http: HttpClient,
    private baseUrlSrv: BaseUrlService,
  ) {
    this.BASE_URL = `${this.baseUrlSrv.getOrigin()}${environment.basePath}`;
  }

  async getInspection(): Promise<KpiInspectionCheck[]> {
    const response = await lastValueFrom(this.http.get<InspectionResponse>(`${this.BASE_URL}/kpi-inspection-checks`));
    return response.data.filter(inspection => inspection.enable === true || inspection.enable === undefined);
  }

  async addInspection(inspection: KpiInspectionCheck): Promise<any> {
    return await lastValueFrom(this.http.post(`${this.BASE_URL}/kpi-inspection-checks`, inspection));
  }

  async editInspection(inspection: KpiInspectionCheck, id: any): Promise<any> {
    return await lastValueFrom(this.http.put(`${this.BASE_URL}/kpi-inspection-checks/${id}`, inspection));
  }

  async deleteInspection(inspection: KpiInspectionCheck): Promise<any> {
    return await lastValueFrom(this.http.delete(`${this.BASE_URL}/kpi-inspection-checks/${inspection._id}`));
    }
}
