#detailDialog {
  .order-detail-container {
 
  }

  nb-card {
   
  }
}



.order-infor {
  &.order-pick-up {
    width: 100%;
  }

  .order-table {
    width: 100%;

    tr {
      border-bottom: 1px solid lightgray;
      font-size: 13px;
      line-height: 25px;

      .title {
        font-family: $font-bold;
      }

      .order-right {
        display: flex;
        justify-content: flex-end;
        font-family: $font-regular;
        font-size: 11px;
      }
    }
  }
}

.timelines {
  display: flex;
  justify-content: space-between;

  .journees {
    width: 75%;
    .truck-journey {
      display: flex;
      justify-content: space-between;

      .col-container-timeline {
        display: flex;
        align-items: center;
        // min-width: 130px;
        // max-width: 180px;
        // width: 35%;

        .text1 {
          position: relative;
          font-size: 10px;
          margin: -4px 4px 6rem 0px;
        }

        .div {
          width: 20px;
          height: 100px;
          border: 2px solid #878585;
          border-left-color: transparent;
          border-bottom-right-radius: 10%;
          border-top-right-radius: 10%;
        }

        .div-global-text{
          width: 7.4rem;
        }

        .div-transparent {
          width: 20px;
          height: 100px;
          border: 2px solid transparent;
        }

        .text2 {
          font-size: 10px;
          margin-left: 0.5rem;
          background-color:   rgb(234, 142, 49)
        }

        .total-hours-value {
          font-size: 10px;
          padding: 5px;
          text-align: center;
          border-radius: 22px;
          border: 2px solid #0F7B3F;
          background-color: #0F7B3F1C;
        }
      }
    }

    .truck-journey:not(:first-of-type) {
      margin-block-start: -10px;
    }
  }

  .col-total-hours {
    display: flex;
    flex-direction: column;
    align-items: center;

    .total-hours-text {
      font-size: 12px;
      text-align: center;
    }

    .total-hours-value {
      font-size: 10px;
      padding: 5px;
      text-align: center;
      border-radius: 22px;
      border: 2px solid #0F7B3F;
      background-color: #0F7B3F1C;
    }
  }
}

.flex {
  display: flex;
}

.timeline {
  position: relative;
  max-width: 46em;
  margin-block-start: -10px;

  &:before {
    background-color: #878585;
    content: '';
    margin-left: -1px;
    position: absolute;
    top: 0;
    left: 2em;
    width: 2px;
    height: 100%;
  }
}

.timeline-end {
  position: relative;
  max-width: 46em;
  margin-block-start: -10px;

  &:before {
    background-color: #878585;
    margin-left: -1px;
    position: absolute;
    top: 0;
    left: 2em;
    width: 2px;
    height: 100%;
  }
}

.timeline-event {
  position: relative;

  &:hover {

    .timeline-event-icon {
      background-color: rgb(205, 15, 59);
    }
  }
}

.timeline-event-copy {
  padding: 2em;
  position: relative;
  top: -1.875em;
  left: 1em;
  width: 100%;

  p:not(.timeline-event-thumbnail) {
    padding-bottom: 1.2em;
  }
}

.timeline-event-icon {
  transform: rotate(45deg);
  background-color: #878585;
  outline: 4px solid white;
  display: block;
  margin: 0.5em 0.5em 0.5em -0.5em;
  position: absolute;
  top: 0;
  left: 2em;
  width: 1em;
  height: 1em;
}

.timeline-event-thumbnail {
  font-size: 0.75em;

  display: inline-block;
  margin-bottom: 1.2em;
  padding: 0.25em 1em 0.2em 1em;
}

.timeline-header {
  .col-header {
    font-size: 16px;
  }
}