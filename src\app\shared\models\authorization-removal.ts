import { Order } from "./order";
import { StatusRemovals } from "./status-removal.enum";

export interface AuthorizationRemoval {
  _id?: string;
  data?: any;
  groupedOrders?: Partial<any[]>;
  LoadNumber?: string
  ItemNumber?: any[];
  BusinessUnit?: string;
  DateRequestedShipment?: Date;
  RequestedDate?: string;
  OrderType?: string
  DeliveryNote?: string;
  DeliveryNoteInstruction?: string;
  FreightHandlingCode?: string;
  appReference?: string;
  LastStatusCode?: string;
  erpReference?: string[];
  LoadStatus?: string;
  Alpha?: string;
  ModeofTransport?: string;
  NextStatusCode?: string;
  OrderTypeDescription?: string;
  PrimaryVehicleID?: string;
  PromisedDeliveryDate?: string;
  PromisedDeliveryDate1?: string;
  PromisedShipmentDate?: string;
  QuantityShipped1?: string;
  SalesOrderNumber?: string;
  ShipTo?: string;
  ShipToDescription?: string;
  ShipmentNumber?: string;
  ShipmentStatus?: string;
  ShortItemNumber?: string;
  SoldToDescription?: string[];
  UnitofMeasure?: string;
  VehicleType?: string;
  ZoneNumber?: string;
  TransactionQuantity?: string;
  QuantityShipped?: string;
  updateDateRequested?: string;
  OrderCreationTime?: string;
  status?: StatusRemovals;
  inspection?: {
    id?: string,
    status?: boolean,
    parkTime?: number
  }
  carrier?: any; // TODO create carrier model
  dates?: {
    created: number;
    assigned?: number;
    responded?: number;
    validated?: number;
    charged?: number;
    updated?: number;
    notCharged?: number;
  };
  SoldTo?: string | any;
  enable?: boolean;
  departureTime?: number;
  dockTimeStart?: number;
  dockCode?: number;
  parkTime?: number;
  newParkTimes?: any;
  factoryParkTimes?: any;
  initialTruckTonnage?: number;
  finalTruckTonnage?: number;
}



// export enum AeStatus {
//   NON_ASSIGNEE = 100,
//   VALIDEE = 200,
//   EN_ATTENTE_VALIDATION = 300,
//   CHARGE = 400,
//   BROKENDOWN = 600,
//   INSPECTE = 500,
// }

export enum parkingStatus {
  NEW = 700,
  USINE = 400,
  FACTORY = 450
}

export interface UpdateQueuesAesType {
  currentId: string;
  replaceId: string;
}
export declare type ObjectType<T> = {
  [key: string]: T
}

export class NewManualAes implements AuthorizationRemoval {
  _id?: string;
  data?: any;
  LoadNumber?: string;
  ItemNumber?: any[];
  Alpha?: string;
  BusinessUnit?: string;
  DateRequestedShipment?: Date;
  RequestedDate?: string;
  DeliveryNote?: string;
  DeliveryNoteInstruction?: string;
  FreightHandlingCode?: string;
  appReference?: any;
  LastStatusCode?: string;
  LoadStatus?: string;
  ModeofTransport?: string;
  NextStatusCode?: string;
  OrderType?: string
  OrderTypeDescription?: string;
  PrimaryVehicleID?: string;
  PromisedDeliveryDate?: string;
  PromisedDeliveryDate1?: string;
  PromisedShipmentDate?: string;
  QuantityShipped1?: string;
  SalesOrderNumber?: string;
  ShipTo?: string;
  ShipToDescription?: string;
  ShipmentNumber?: string;
  ShipmentStatus?: string;
  ShortItemNumber?: string;
  SoldToDescription?: string[];
  UnitofMeasure?: string;
  VehicleType?: string;
  ZoneNumber?: string;
  TransactionQuantity?: string;
  QuantityShipped?: string;
  updateDateRequested?: string;
  OrderCreationTime?: string;
  status?: StatusRemovals;
  inspection?: { id?: string; status?: boolean; parkTime?: number; };
  carrier?: any;
  SoldTo?: any;
  enable?: boolean;
  departureTime?: number;
  dockTimeStart?: number;
  dockCode?: number;
  parkTime?: number;
  finalTruckTonnage?: number;
  initialTruckTonnage?: number;
}
export enum RenderType {
  PICKUP = 'C9',
  RENDER = 'A0',
}

export enum OrderType {
  EXTERN = 'SO',
  INTERN = 'ST',
}

export enum StatusAEsJde {
  NOTAPPROVED = "21",
  APPROVED = "29",
  CHECK_IN = "35",
  OUTPUT = "50",
  LOADSTATUS60 = "60",
  INVOICED = "80",
  REJECTED = "99",
}


export  declare type  GroupesOrders = Partial<Order[]>;
