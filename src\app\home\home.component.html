<div class="home-container" >

  <div class="home-modal" [@openLoginForm]="isFormActive()? 'open' : 'closed'" [nbSpinner]="isLoading" nbSpinnerStatus="primary">
    <section class="auth-section">
      <div class="form-container">
        <div class="login-form">
          <header class="form-header">
            Hello, pour vous connecter, veuillez renseigner vos identifiants de connexion:
          </header>
          <div class="input-container">
            <div class="form-field">
              <label class="form-field-label">E-mail</label>
              <div class="form-field-value">
                <input class="input" type="text" [(ngModel)]="credentials.login">
              </div>
            </div>

            <div class="form-field">
              <label class="form-field-label">Mot de passe</label>
              <div class="form-field-value">
                <input class="input" type="password" [(ngModel)]="credentials.password">
              </div>
            </div>

          </div>
          <footer class="form-footer">
            <button nbButton status="success" class="btn login-btn" (click)='login()'>Connexion</button>
            <button nbButton ghost class="btn forgotten-btn">Mot de passe oublié?</button>
          </footer>
        </div>
        <div class="right-desc">
          <header class="description-header">En cas de difficulté, n'hésitez pas à nous contacter</header>
          <button nbButton class="btn-signup">Nous contacter</button>
          <footer class="description-footer">Merci de votre fidélité</footer>
        </div>
      </div>
    </section>
  </div>


  <section class="banner-section">
    <div class="connexion">
      <div class="icon icon_whitelogo"></div>
    </div>
  </section>
  <div class="service-container">
    <div class="our-services">
      <h6>Nos Services</h6>
      <div class="empty-space"></div>
    </div>
    <div class="services-list">
      <div class="order">
        <div class="order-icon">
          <div class="icon icon_shopping-bag"></div>
        </div>
        <h6>Initiation des commandes</h6>
        <div class="more-infos">Choix des produits, indication des quantités; assignation du transporteur</div>
      </div>
      <div class="order">
        <div class="order-icon">
          <div class="icon icon_shipped"></div>
        </div>
        <h6>Gestion des enlèvements</h6>
        <div class="more-infos">Sélection du quart temps, information sur la quantité, le poids du camion, le nombre de
          camion</div>
      </div>
      <div class="order">
        <div class="order-icon">
          <div class="icon icon_calendar"></div>
        </div>
        <h6>Planification des enlèvements</h6>
        <div class="more-infos">Sélection du quart temps, information sur la quantité, le poids du camion, le nombre de
          camion</div>
      </div>
      <div class="order">
        <div class="order-icon">
          <div class="icon icon_track"></div>
        </div>
        <h6>Tracking des commandes</h6>
        <div class="more-infos">Notification des parties prenantes a chaque étape de la commande (entrée du camion à
          l’usine, assignation du qai de chargement , sortie du camion de l’usine )</div>
      </div>
      <div class="order">
        <div class="order-icon">
          <div class="icon icon_notification"></div>
        </div>
        <h6>Notification</h6>
        <div class="more-infos">Choix des produits, indication des quantités; assignation du transporteur</div>
      </div>
      <div class="order">
        <div class="order-icon">
          <div class="icon icon_linechart"></div>
        </div>
        <h6>Reporting</h6>
        <div class="more-infos">Choix des produits, indication des quantités; assignation du transporteur</div>
      </div>
    </div>
  </div>
  <div class="green-footer">
    <div class="footer-container">
      <div class="footer-text">Mentions légales</div>
      <div class="footer-text">Politique des cookies </div>
      <div class="footer-text">Votre avis</div>
    </div>
  </div>


</div>
