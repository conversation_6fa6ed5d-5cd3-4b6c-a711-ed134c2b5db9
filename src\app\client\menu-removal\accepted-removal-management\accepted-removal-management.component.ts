import { DatePipe } from '@angular/common';
import { Component, OnInit, Pipe, PipeTransform, TemplateRef, ChangeDetectorRef } from '@angular/core';
import { UntypedFormControl } from '@angular/forms';
import { Router } from '@angular/router';
import { NbDialogRef, NbDialogService, NbToastRef, NbToastrService } from '@nebular/theme';
import { AuthorizationRemoval } from 'src/app/shared/models/authorization-removal';
import { RenderTypeValueLibrary, StatusAESLibrary } from 'src/app/shared/models/render-type.enum';
import { CommonService } from 'src/app/shared/services/common.service';
import { RemovalService } from '../removal-management/removal.service';
import { GetStatusRemovalsPipe } from 'src/app/shared/pipes/get-status-removals.pipe';
import { PaginationConfig } from 'src/app/shared/services/pagination.service';
import * as moment from 'moment';


@Component({
  selector: 'clw-accepted-removal-management',
  templateUrl: './accepted-removal-management.component.html'
})
export class AcceptedRemovalManagementComponent implements OnInit {

  formControl = new UntypedFormControl(new Date());
  dialogRef: NbDialogRef<any>;
  detailDetailRef: NbDialogRef<any>;
  dialogExport: NbDialogRef<any>;



  removals: any[];

  loading = false;

  getStatusRemovalsPipe = new GetStatusRemovalsPipe();


  statusFilter: 300;
  transporteur: any;
  produit: any;
  AE: any;
  Product: any;
  carrierLabel: any;
  removal: AuthorizationRemoval;

  aeCarriers: any;
  code = false;
  selectBtn: any;
  datePlusthree: any;
  datePlusfive: any;
  Orderstatus: any;
  isLoading: boolean;

  offset = 1;
  limit = 20;
  startDate: Date;
  endDate: Date;
  endIndex = 20;
  startIndex = 1;
  total: number;
  rangeFilter: any;

  // Configuration de la pagination
  paginationConfig: PaginationConfig = {
    currentPage: 1,
    itemsPerPage: 20, // Même valeur que limit pour la cohérence
    totalItems: 0,
    maxSize: 10
  };
  statusLabels: any = [];
  filteredStatus$: any;
  AELabels: any = [];
  filteredAE$: any;
  filteredProducts$: any;
  filteredCarrier$: any;
  filterProductLabels: any[] = [];
  ProductLabels: any[] = [];
  CarrierLabels: any[] = [];
  dataOrderTypeLabels: any[] = [];
  carriers: any;

  filterForm = {
    status: 200,
    LoadNumber: '',
    ItemNumber: '',
    company: '',
    shipping: '',
    carrierLabel: '',
    OrderType: '',
    startDate: moment().startOf('year').format('MM-DD-YYYY'),
    endDate: moment().endOf('year').format('MM-DD-YYYY')
  }
  currentRemoval: AuthorizationRemoval;
  dataForFilter: any;
  dataAELabels: any = [];
  dataProducts: any = [];
  dataSoldToDescLabels: any = [];
  dataShipToDescLabels: any = [];
  dataTransporters: any = [];


  constructor(
    private datePipe: DatePipe,
    private commonSrv: CommonService,
    private dialogSvr: NbDialogService,
    private removalSvr: RemovalService,
    private toastrSvr: NbToastrService,
    private cdr: ChangeDetectorRef,
  ) { }

  async ngOnInit(): Promise<void> {
    // Initialiser la pagination avec des valeurs par défaut
    this.paginationConfig = {
      currentPage: 1,
      itemsPerPage: 20,
      totalItems: 0,
      maxSize: 10
    };

    // Utiliser setTimeout pour éviter l'erreur ExpressionChangedAfterItHasBeenCheckedError
    setTimeout(() => {
      this.getAuthorization();
      this.getDataForFilter();
    });
  }

  FreightHandlingCode(HandlingCode: any) {
    return RenderTypeValueLibrary[HandlingCode];
  }

  async getDataForFilter() {
    try {
      this.loading = true;
      this.dataForFilter = await this.removalSvr.getFilterData({
        ...this.filterForm,
        keyForFilters: ['LoadNumber', 'ItemNumber', 'Alpha', 'ShipToDescription', 'SoldToDescription', 'OrderType']
      },);
      this.dataAELabels = this.dataForFilter?.dataLoadNumber ?? [];
      this.dataTransporters = this.dataForFilter?.dataAlpha ?? [];
      this.dataShipToDescLabels = this.dataForFilter?.dataShipToDescription ?? [];
      this.dataProducts = this.dataForFilter?.dataItemNumber ?? [];
      this.dataSoldToDescLabels = this.dataForFilter?.dataSoldToDescription ?? [];
      this.dataOrderTypeLabels = this.dataForFilter?.dataOrderType ?? [];
    } catch (error) {
      return error;
    } finally { this.loading = false }
  }

  async getAuthorization(): Promise<void> {
    try {
      this.isLoading = true;

      const options = {
        limit: this.paginationConfig.itemsPerPage,
        offset: (this.paginationConfig.currentPage - 1) * this.paginationConfig.itemsPerPage,
        ...this.filterForm,
      };

      const { count, data } = await this.removalSvr.getAuthorizationRemoval(options);
      this.removals = data;
      this.total = count;

      // Créer une nouvelle instance de PaginationConfig pour forcer la détection des changements
      this.paginationConfig = {
        currentPage: this.paginationConfig.currentPage,
        itemsPerPage: this.paginationConfig.itemsPerPage,
        totalItems: count,
        maxSize: 10
      };

      // Pour la compatibilité avec le code existant
      this.endIndex = Math.min((this.paginationConfig.currentPage * this.paginationConfig.itemsPerPage), count);
      this.startIndex = count === 0 ? 0 : (this.paginationConfig.currentPage - 1) * this.paginationConfig.itemsPerPage + 1;

      // Vérification si la page actuelle est valide
      const totalPages = Math.ceil(this.paginationConfig.totalItems / this.paginationConfig.itemsPerPage);
      if (this.paginationConfig.currentPage > totalPages && totalPages > 0) {
        this.paginationConfig = {
          currentPage: totalPages,
          itemsPerPage: this.paginationConfig.itemsPerPage,
          totalItems: this.paginationConfig.totalItems,
          maxSize: 10
        };
        this.cdr.detectChanges();
        await this.getAuthorization();
        return;
      }

      // Forcer la détection des changements
      this.cdr.detectChanges();
    }
    catch (error) {
      console.log(error);
      this.toastrSvr.danger('Impossible de récupérer la liste des AE', 'Erreur connexion !');
    } finally {
      this.isLoading = false;
    }
  }

  async reset() {
    this.filterForm = {
      status: 200,
      LoadNumber: '',
      ItemNumber: '',
      company: '',
      shipping: '',
      carrierLabel: '',
      OrderType: '',
      startDate: moment().startOf('year').format('MM-DD-YYYY'),
      endDate: moment().endOf('year').format('MM-DD-YYYY')
    }

    // Réinitialiser la pagination avec une nouvelle instance
    this.paginationConfig = {
      currentPage: 1,
      itemsPerPage: this.limit,
      totalItems: 0,
      maxSize: 10
    };

    // Forcer la détection des changements
    this.cdr.detectChanges();

    await this.getAuthorization();
    await this.getDataForFilter();
  }
  getConcatenatedLabels(itemNumber: any[]): string {
    return itemNumber?.map((item: any) => `${item?.product?.label} (${item?.packaging?.label})`).join(', ') || 'N/A';
  }

  onModelAEChange(value: any): void {
    if (value == null)
      this.dataAELabels = this.dataForFilter?.dataLoadNumber
    if (value) {
      this.loading = true;
      this.dataAELabels = this.dataForFilter?.dataLoadNumber.filter((data: number) => JSON.stringify(data).includes(value)).sort((a, b) => {
        return a.toString().toLowerCase().indexOf(value.toLowerCase()) - b.toString().toLowerCase().indexOf(value.toLowerCase());
      });
      this.loading = false;
    }
  }

  onModelCompanyChange(value: any): void {
    if (value == null)
      this.dataSoldToDescLabels = this.dataForFilter?.dataSoldToDescription;

    if (value)
      return this.dataSoldToDescLabels = this.dataForFilter?.dataSoldToDescription.filter((data: string) =>
        data.toLowerCase().includes(value.toLowerCase())).sort((a, b) => {
          return a.toString().toLowerCase().indexOf(value.toLowerCase()) - b.toString().toLowerCase().indexOf(value.toLowerCase());
        });
  }

  onModelDeliveryPointChange(value: any): void {
    if (value == null)
      this.dataShipToDescLabels = this.dataForFilter?.dataShipToDescription;

    return this.dataShipToDescLabels = this.dataForFilter?.dataShipToDescription.filter((data: string) =>
      data.toLowerCase().includes(value.toLowerCase())).sort((a, b) => {
        return a.toString().toLowerCase().indexOf(value.toLowerCase()) - b.toString().toLowerCase().indexOf(value.toLowerCase());
      });
  }

  async onSelectionAEChange(event: any, keyToReset: string): Promise<void> {
    if (event === 'reset' && keyToReset) {
      this.filterForm[keyToReset] = '';
    }

    // Réinitialiser la pagination à la première page lors du changement de filtre
    if (event) {
      this.paginationConfig = {
        ...this.paginationConfig,
        currentPage: 1
      };

      // Forcer la détection des changements
      this.cdr.detectChanges();

      await this.getAuthorization();
    }
  }

  onModelProductsChange(value: any): void {
    if (value == null)
      this.dataProducts = this.dataForFilter?.dataItemNumber;

    if (value)
      this.dataProducts = this.dataForFilter?.dataItemNumber.filter((data: string) => data.toLowerCase().includes(value.toLowerCase())).sort((a, b) => {
        return a.toString().toLowerCase().indexOf(value.toLowerCase()) - b.toString().toLowerCase().indexOf(value.toLowerCase());
      });;
  }

  onModelCarrierChange(value: any): void {
    if (value === null)
      this.dataTransporters = this.dataForFilter?.dataAlpha;

    if (value)
      this.dataTransporters = this.dataForFilter?.dataAlpha.filter((data: string) =>
        data.toLowerCase().includes(value?.toLowerCase())).sort((a, b) => {
          return a.toString().toLowerCase().indexOf(value?.toLowerCase()) - b.toString().toLowerCase().indexOf(value?.toLowerCase());
        });
  }

  onModelTypeChange(value: any): void {
    if (value == null)
      this.dataOrderTypeLabels = this.dataForFilter?.dataOrderType;

    return this.dataOrderTypeLabels = this.dataForFilter?.dataOrderType?.filter((data: string) =>
      data?.toLowerCase().includes(value?.toLowerCase())).sort((a, b) => {
        return a.toString().toLowerCase().indexOf(value?.toLowerCase()) - b.toString().toLowerCase().indexOf(value?.toLowerCase());
      });;
  }

  truncate(desc: string, length: number): string {
    if (!desc) { return ''; }
    return this.commonSrv.truncateString(desc, length);
  }

  /**
   * Méthode appelée lorsque l'utilisateur change de page
   * @param page Numéro de la page sélectionnée
   */
  onPageChange(page: number): void {
    if (this.isLoading) return; // Éviter les requêtes multiples pendant le chargement

    // Créer une nouvelle instance de PaginationConfig pour forcer la détection des changements
    this.paginationConfig = {
      currentPage: page,
      itemsPerPage: this.paginationConfig.itemsPerPage,
      totalItems: this.total,
      maxSize: 10
    };

    // Forcer la détection des changements
    this.cdr.detectChanges();

    this.getAuthorization();
  }

  /**
   * Méthode appelée lorsque l'utilisateur change le nombre d'éléments par page
   * @param itemsPerPage Nombre d'éléments par page sélectionné
   */
  onItemsPerPageChange(itemsPerPage: number): void {
    // Mettre à jour la limite pour la compatibilité avec le code existant
    this.limit = itemsPerPage;

    // Créer une nouvelle instance de PaginationConfig pour forcer la détection des changements
    this.paginationConfig = {
      currentPage: 1, // Revenir à la première page
      itemsPerPage: itemsPerPage,
      totalItems: this.total,
      maxSize: 10
    };

    // Forcer la détection des changements
    this.cdr.detectChanges();

    this.getAuthorization();
  }

  // Méthodes de compatibilité avec l'ancien code
  async previousPage(): Promise<any> {
    this.onPageChange(this.paginationConfig.currentPage - 1);
  }

  async nextPage(): Promise<any> {
    this.onPageChange(this.paginationConfig.currentPage + 1);
  }

  isEnabled(removal?: AuthorizationRemoval) {
    const data = removal || this.currentRemoval;
    return data?.enable == false ? false : true;
  }

  async openDetailModal(dailog?: any, removal?: any): Promise<void> {
    this.removal = removal;
    this.detailDetailRef = this.dialogSvr.open(dailog, {});
  }

  openEnableRemoval(dialog: TemplateRef<any>, removal: AuthorizationRemoval) {
    this.currentRemoval = removal;
    this.dialogRef = this.dialogSvr.open(dialog);
    this.dialogRef.onClose.subscribe(async (result) => {
      if (result) {
        try {
          this.isLoading = true;
          await this.removalSvr.updateAe(removal._id, { enable: !this.isEnabled(removal) });
          await this.getAuthorization();
          this.toastrSvr.success(`Cette AE a été mise à jour avec succès`, `Mise à jour réussi`);
        } catch (error) {
          this.toastrSvr.danger(`Une erreur est survenu lors de la mise a jour de cette AE`, 'Erreur de mise à jour')
        } finally {
          this.isLoading = false;
        }
      }
    })
  }

  openDialogExport(dailog: any): void {
    this.dialogExport = this.dialogSvr.open(dailog, {});
  }

  async exportAeToExcel(): Promise<void | NbToastRef> {
    if (!this.startDate || !this.endDate) {
      return this.toastrSvr.danger('Veuillez renseigner une date de début et de fin', 'Donnés incorrectes !');
    }

    if (this.startDate > this.endDate) {
      return this.toastrSvr.danger('Veuillez renseigner une date de début inférieure à celle de la date de fin', 'Donnés incorrectes !');
    }

    const options = {
      startDate: moment(this.startDate).format('MM-DD-YYYY'),
      endDate: moment(this.endDate).format('MM-DD-YYYY'),
      LoadNumber: this.AE,
      ItemNumber: this.Product,
      carrierLabel: this.carrierLabel,
      status: 200,
    }
    try {

      this.isLoading = true;
      let dataAElist = (await this.removalSvr.getAuthorizationRemoval(options)).data;
      dataAElist = dataAElist.map((elt) => {
        const data = {};
        data['N° AE'] = elt?.LoadNumber;
        data['PRODUIT'] = elt?.ItemNumber;
        data['STATUT'] = this.getStatusRemovalsPipe.transform(elt?.enable == false ? 0 : elt?.status);
        data['STATUT JDE'] = StatusAESLibrary[elt?.LoadStatus];
        data['N° COMMANDE'] = elt?.appReference;
        data['N° DE CHARGEMENT'] = elt?.ShipmentNumber;
        data['QUANTITÉ'] = elt?.TransactionQuantity;
        data['CODE_ADRESSE'] = elt?.ShipToDescription;
        data['CODE_CLIENT'] = elt?.SoldToDescription;
        data['DATE CRÉATION'] = this.datePipe.transform(elt?.dates?.created, 'dd/MM/YYYY');
        return data;
      });

      this.removalSvr.exportAEAsExcelFile(dataAElist, 'Liste_AES_TRANSPORT_VALIDEE');
      this.dialogExport.close();
    } catch (error) {
      this.toastrSvr.danger(error.error.message, 'ERREUR!');
      return error;
    } finally { this.isLoading = false; }
  }

}
