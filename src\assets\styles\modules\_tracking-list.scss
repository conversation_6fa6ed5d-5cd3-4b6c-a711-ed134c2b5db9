.tracking-container {
  filter-container {
    .left-block {
        .filter-elt {
            width: 14%;
        }
        .store-filter {
            width: 23%;
        }
        .product-filter {
            width: 29%;
        }
    }

    label {
        text-transform: uppercase;
    }
    .btn-contain {
        @include v-center;
        .space {
            margin-right: 15px;
        }
        .search-btn {
            width: 50%;
            margin-right: 5px;
        }
        .color-text {
            color: #fff;
        }
    }
}

.card-container {
  height: 70vh;
}

nb-card-body {
    max-height: 65vh;
}

.not-found {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    min-height: 200px;
    img {
        height: 140px;
    }
    h1 {
        font-size: 18px;
    }
}
.list-elt {
    .col-number {
        width: 60px;
    }
    .col-immatriculation {
        width: 210px;
    }

    .col-driver {
      width: 220px;
    }

    .col-ae-number {
      width: 150px;
    }

    .col-quantity {
      width: 150px;
    }

    .col-time {
      width: 200px;
    }


    .col-actions {
        width: calc(100% - 990px);
        display: flex;
        justify-content: flex-end;
        button {
            background: none;
        }
    }
}
}

.tracking-container {
  nb-card {
      .row {
          display: flex;
          justify-content: space-between;
          .input {
              width: 45%;
          }
      }
  }
}

.localisation-container {
  width: 80vw;
  height: 80vh;

  .location-container {
    display: flex;


      .map{
        width: 90%;
        height: 100%;
      }

    .right-block {
      width: 10%;
      padding: 10px;
    }
  }
}

.detail-location-container {
  width: 40vw;
  height: 60vh;

  .location-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .center {
      width: 70%;
      border-top: 1px solid black;
      border-bottom: 1px solid black;
      padding: 21px 0;
    }
    .remove-icon {
      width: 40px;
      display: flex;
      align-items: center;
    }
  }

  .detail-header {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .first-section, .second-section{
    display: flex;
    justify-content: space-between;
  }
  .first-section {
    p{
      font-size: 16px;
      font-weight: 700;
    }
  }
}
