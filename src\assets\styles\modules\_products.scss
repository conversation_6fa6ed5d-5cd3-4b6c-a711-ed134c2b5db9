.products-container {
    .btn-contain {
        @include v-center;
        .space {
            margin-right: 15px;
        }
        .search-btn {
            // width: 50%;
            margin-right: 5px;
        }
        .color-text {
            color: #fff;
        }
    }
    label {
        text-transform: uppercase;
    }
    .cards {
        display: flex;
        justify-content: space-between;
        margin-top: 25px;
        nb-card {
            width: 100%;
            .card-container {
                padding: 3% 0 3% 2%;
                .product-side {
                    display: flex;
                    justify-content: flex-start;
                    row-gap: 4vw;
                    column-gap: 4vw;
                    flex-wrap: wrap;
                    .product {
                        align-items: center;
                        justify-content: center;
                        display: flex;
                        flex-direction: column;
                        width: 15%;
                        box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.09);
                        border-radius: 13px;
                        padding: 30px 5px;
                        img {
                            height: 83px;
                            // width: fit-content;
                            display: flex;
                            justify-content: center;
                            margin-bottom: 5px;
                        }

                        nb-icon {
                            height: 83px;
                            width: fit-content;
                            display: flex;
                            justify-content: center;
                            margin-bottom: 5px;
                        }

                        label {
                            font-weight: 600;
                            font-size: 10px;
                            line-height: 18px;
                            text-align: center;
                            color: #353535;
                        }

                        &:hover {
                            @include scale-effect;
                        }
                    }
                }


                @media (max-width: 1300px) {
                    .product{
                        width: 20% !important
                    }
                }
            }
        }
    }
}

.detail-product-container {
    @include vh-center;

    .flipcard-body {
        width: 30vw;
        display: flex;
        align-items: center;

        span {
            font-weight: bold;
        }
    }
    nb-card {
        .image {
            display: flex;
            justify-content: center;
            img {
                width: 100px;
            }
        }
        border-radius: 9px;
        .form--header {
            display: flex;
            justify-content: space-between;
            .action-icons {
                display: flex;
                justify-content: flex-end;
            }
        }

        .detail-container {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;

            span {
                font-weight: bold;
            }

            .image {
                img {
                    width: 100px;
                }
            }

            .row {
                display: flex;
                flex-direction: column;
                flex-wrap: wrap;
                width: 100%;
                text-align: center;

                .title {
                    font-size: 17px;
                    font-weight: 600;
                    margin-top: 10px;
                }

                .value {
                    font-size: 15px;
                    text-transform: uppercase;
                }
            }
        }
    }
}

.add-product-container {
    nb-card {
        .image {
            display: flex;
            justify-content: center;
            img {
                width: 100px;
            }
        }
    }
}
