// Styles pour la modale des autorisations utilisateur
.container-privilege {
  padding: 0 10px;
  max-height: 70vh;
  overflow-y: auto;

  // Style pour le mode lecture seule
  &.view-only-mode {
    nb-toggle {
      pointer-events: none;
      opacity: 0.7;
    }
  }

  .view-mode-notice {
    display: flex;
    align-items: center;
    margin: 10px 0;
    padding: 8px 12px;
    background-color: rgba(65, 156, 251, 0.1);
    border-radius: 6px;
    border-left: 4px solid $color-fourth;

    nb-icon {
      color: $color-fourth;
      margin-right: 10px;
    }

    span {
      color: #666;
      font-size: 0.9rem;
    }
  }

  // Style pour la barre de défilement
  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(65, 156, 251, 0.5);
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: rgba(65, 156, 251, 0.7);
  }

  .section-header {
    display: flex;
    align-items: center;
    margin-top: 20px;
    margin-bottom: 10px;
    padding: 8px 12px;
    background-color: rgba(65, 156, 251, 0.1);
    border-radius: 6px;
    border-left: 4px solid $color-fourth;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

    .section-icon {
      color: $color-fourth;
      margin-right: 10px;
      font-size: 1.2rem;
    }

    .section-title {
      margin: 0;
      color: $color-fourth;
      font-weight: bold;
      font-size: 1.1rem;
    }
  }

  .acces-rigth {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    padding: 6px 10px;
    transition: all 0.2s ease;

    &:hover {
      background-color: rgba(0, 0, 0, 0.02);
      border-radius: 4px;
    }

    &.insert-border {
      border-bottom: 1px dashed rgba(0, 0, 0, 0.05);
      padding-bottom: 10px;
      margin-bottom: 10px;
    }

    .rigth-label {
      flex: 1;
      font-size: 0.95rem;
    }

    .label-title {
      font-weight: bold;
      color: $color-fourth;
      font-size: 1rem;
    }

    nb-toggle {
      margin-left: 15px;

      // Style pour les toggles désactivés (en lecture seule)
      &[disabled] {
        opacity: 0.7;
        pointer-events: none;
      }
    }
  }

  // Style pour le premier élément après un header de section
  .section-header + .acces-rigth {
    background-color: rgba(65, 156, 251, 0.03);
    border-radius: 4px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.02);
  }
}

// Style pour les titres de section en dehors du container-privilege
h5.section-title {
  margin-top: 15px;
  margin-bottom: 10px;
  padding-bottom: 5px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  color: $color-fourth;
  font-weight: bold;
}

// Style pour les onglets de la modale
nb-tabset {
  .tab-link {
    font-weight: bold;

    &.active {
      color: $color-fourth !important;
      border-color: $color-fourth !important;
    }
  }
}

// Style pour la modale des utilisateurs
.details-user-container {
  box-shadow: none !important;

  nb-card-header.user-card-header {
    display: flex;
    align-items: center;
    padding: 15px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);

    .user-image {
      width: 80px;
      height: 80px;
      border-radius: 50%;
      background-size: cover;
      background-position: center;
      border: 2px solid $color-fourth;
      box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);
    }

    .user-profile {
      margin-left: 15px;

      .user-name {
        font-weight: bold;
        font-size: 1.2rem;
        color: $color-fourth;
      }

      .user-function {
        color: #666;
        font-size: 0.9rem;
      }
    }

    .btn-actions {
      margin-left: auto;

      button {
        margin-left: 5px;
      }
    }
  }

  // Style pour les formulaires
  .form-group {
    margin-bottom: 15px;

    .attribute-block {
      display: flex;
      align-items: center;
      margin-bottom: 5px;

      .icon-btn {
        color: $color-fourth;
        margin-right: 10px;
      }

      .label {
        font-weight: bold;
        color: #666;
      }
    }

    .text {
      padding: 8px 10px;
      background-color: rgba(0, 0, 0, 0.02);
      border-radius: 4px;
      color: #333;
    }

    input {
      width: 100%;
      padding: 8px;
      border-radius: 4px;
      border: 1px solid rgba(0, 0, 0, 0.1);

      &:focus {
        border-color: $color-fourth;
        box-shadow: 0 0 0 2px rgba(65, 156, 251, 0.2);
      }
    }
  }
}
