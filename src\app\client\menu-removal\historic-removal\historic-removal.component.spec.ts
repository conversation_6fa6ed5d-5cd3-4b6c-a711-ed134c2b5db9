import { ComponentFixture, TestBed } from '@angular/core/testing';

import { HistoricRemovalComponent } from './historic-removal.component';

describe('HistoricRemovalComponent', () => {
  let component: HistoricRemovalComponent;
  let fixture: ComponentFixture<HistoricRemovalComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ HistoricRemovalComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(HistoricRemovalComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
