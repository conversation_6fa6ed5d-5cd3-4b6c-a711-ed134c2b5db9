<div class="menu-waiting-thread-container">
  <div class="header">
    <h1 class="title" style="color: #fff;
      display: flex;
      justify-content: center;
      margin: 0;">Ecran d'appel - Chargement usine matières
    </h1>
  </div>

  <div class="removal-container">

    <nb-card class="color-card no-radius">
      <nb-list class="element">
        <nb-list-item class="list-elt-header paginator">
          <nb-icon icon="undo-outline" (click)="goBack()" [options]="{ animation: { type: 'zoom' } }"
            nbTooltip="Page d'accueil" class="remove-icon" status="basic">
          </nb-icon>
        </nb-list-item>
        <nb-list-item class="list-elt-header color-text-title paginator">
          <div class="col col-num">N°</div>
          <div class="col col-name">Transporteur</div>
          <div class="col col-name">Conducteur</div>
          <div class="col col-qty">Tonnage</div>
          <div class="col col-qty">Quai</div>
          <div class="col col-time">Temps d'attente</div>
          <div class="col col-immatriculation">Immatriculation</div>
          <div class="col col-entry-weight">Pesé en entrer</div>
          <div class="col col-status center">Statut</div>
        </nb-list-item>
      </nb-list>
      <nb-list class="element scrool-style" [nbSpinner]="displayLoading" nbSpinnerStatus="primary">
        <nb-list-item class="list-elt-header color-text-list-item"
          *ngFor="let inspection of inspections | filterDockCode; index as i">
          <div class="col col-num">{{ i + 1 }}</div>
          <div class="col col-name">{{ inspection?.carrier?.label || "Non renseigner" }}</div>
          <div class="col col-name">{{ inspection?.driver || "Non renseigner" }}</div>
          <div class="col col-qty">{{ inspection?.truckTonnage }}</div>
          <div class="col col-qty">{{ inspection?.dockCode | getDock: docks }}</div>
          <div class="col col-time">
            <p [ngStyle]="inspection?.dockTimeStart | colorStyleWaiting">
              {{commonSvr.getDurationDate(inspection?.dockTimeStart)}}
            </p>
          </div>
          <div class="col col-immatriculation">
            {{ inspection?.tractor?.label || "Non renseigner"}}
          </div>
          <div class="col col-entry-weight">80T</div>
          <div class="col col-status" *ngIf="!inspection?.dockCode || inspection?.dockCode === 0">
            <nb-badge status="warning" text="EN ATTENTE" class="badge" position="top start"></nb-badge>
          </div>
          <div class="col col-status" *ngIf="inspection?.dockCode && inspection?.dockCode !== 0">
            <nb-badge status="info" text="EN CHARGEMENT" class="badge" position="top start"></nb-badge>
          </div>
        </nb-list-item>
        <nb-list-item class="list-elt-header" *ngIf="!inspections?.length">
          <div class="not-found">
            <img src="../../../../assets/images/icons/shipped.png" alt="" />
            <h6>Aucun camion trouvé</h6>
          </div>
        </nb-list-item>
      </nb-list>
    </nb-card>
  </div>
</div>