import { CommonService } from './../../../shared/services/common.service';
import { Driver } from 'src/app/shared/models/drivers';
import { Truck } from 'src/app/shared/models/trucks';
import {
  Component,
  OnInit,
  Pipe,
  PipeTransform,
  ViewEncapsulation,
  TemplateRef,
} from '@angular/core';
import { Router } from '@angular/router';
import { NbDialogRef, NbDialogService } from '@nebular/theme';
import { NbToastrService } from '@nebular/theme';
import { ImageCompressor } from 'image-compressor';
import { Carrier } from 'src/app/shared/models/carrier';
import { CarrierService } from '../carriers.service';
import { Location } from '@angular/common';
import * as moment from 'moment';
import { Observable, of } from 'rxjs';
import * as xlsx from 'xlsx';
import { StorageService } from 'src/app/shared/services/storage.service';

@Component({
  selector: 'clw-carrier-detail',
  templateUrl: './carrier-detail.component.html',
  styles: [],
  encapsulation: ViewEncapsulation.None,
})
export class CarrierDetailComponent implements OnInit {
  limit = 20;
  offset;
  endIndex: number;
  startIndex: number;
  rangeFilter: { start: any; end: any };
  isLoading: boolean;
  total: number;
  label: any;

  isEdit = false;
  isEnable = false;

  driversData = [];

  editeditdialogRef: NbDialogRef<any>;
  truckdialog: NbDialogRef<any>;
  disabledialog: NbDialogRef<any>;
  truckenabledialog: NbDialogRef<any>;
  dialogRef: NbDialogRef<any>;

  connectedUser: any;
  selectedCarrier: Carrier;
  imgResultAfterCompress: string;
  trucks: Truck[];
  drivers: Driver[];
  currCarrier: any;
  imageSrc: any;
  file: any;
  filename: any;
  driverName: any;
  immatriculation: any;

  selectedTruck: Truck;
  selectedDriver: Driver;
  driverenabledialog: NbDialogRef<unknown>;
  flipped: boolean;
  filteredDriverName$: Observable<string[]>;
  filteredTruckMat$: Observable<string[]>;
  // filterDriverName$: any
  DriverNameLabels = [];
  trucksMatLabels = [];

  pdfName: any;
  byteArray: Uint8Array;

  isRequest: boolean;
  selectedType: any;
  link: any;
  selectedTrainingIndex: any;

  trainDateStart: any;
  trainDateEnd: any;
  trainDateExpired: any;
  isNewTraining: boolean;
  isEditTraining: boolean;
  selectedTraining: any;

  driverOffset = 1;
  driverLimit = 20;
  driverEndIndex = 20;
  driverStartIndex = 1;
  driverTotal: number;

  truckOffset = 1;
  truckLimit = 20;
  truckEndIndex = 20;
  truckStartIndex = 1;
  truckTotal: number;
  driverCodesInDB: any;

  trucksTypes = [
    { value: 1, label: 'PLATEAU' },
    { value: 2, label: 'BENNE' },
    { value: 3, label: 'VRAQUIER' },
  ];

  trucksCapacity = [
    { value: 5, label: '5 tonnes' },
    { value: 10, label: '10 tonnes' },
    { value: 15, label: '15 tonnes' },
    { value: 20, label: '20 tonnes' },
    { value: 25, label: '25 tonnes' },
    { value: 35, label: '35 tonnes' },
  ];
  driverNameAll: any;
  filterLabel: any;
  filterLabels: any;

  constructor(
    private carrierSvr: CarrierService,
    private dialogSvr: NbDialogService,
    private toastrSvr: NbToastrService,
    private storageSvr: StorageService,
    private router: Router,
    private location: Location,
    private commonSrv: CommonService
  ) { }

  async ngOnInit(): Promise<void> {
    try {
      this.isLoading = true;
      this.trainDateExpired = '';
      this.addTraining();
      this.selectedCarrier = this.storageSvr.getObject('currCarrier');
      // this.selectedCarrier = this.carrierSvr.selectedCarrier;
      if (!this.selectedCarrier) {
        this.router.navigate(['/admin/carrier/carrier-list']);
      }
      this.label = this.selectedCarrier?.label;
      this.reformatCarrier();
      await this.getAllTrucks();
      await this.getAllDrivers();

      this.offset;
      this.limit = 20;
      this.endIndex = 20;
      this.startIndex = 1;
      this.rangeFilter = { start: null, end: null };

      this.trucks.forEach((elt: any) =>
        this.trucksMatLabels.push(elt?.immatriculation)
      );
      this.filteredTruckMat$ = of(this.trucksMatLabels);

      this.drivers.forEach((elt: any) =>
        this.DriverNameLabels.push(elt?.fullname)
      );
      this.filteredDriverName$ = of(this.DriverNameLabels);
    } catch (error) {
      this.toastrSvr.danger(
        "Nous n'avons pas pu charger cette page",
        'Erreur de connexion'
      );
    } finally {
      this.isLoading = false;
    }
  }

  getTrucksCapacity(value: any) {
    const typesName = {
      5: '5 t',
      10: '10 t',
      15: '15 t',
      20: '20 t',
      25: '25 t',
    };
    return typesName[value];
  }

  getTrucksTypes(value: any) {
    const typesName = { 1: 'PLATEAU', 2: 'BENNE', 3: 'VRAQUIER' };
    return typesName[value] || value;
  }

  reformatCarrier() {
    this.selectedCarrier.email = !!this.selectedCarrier.email
      ? this.selectedCarrier?.email
      : '';
    this.selectedCarrier.label = !!this.selectedCarrier.label
      ? this.selectedCarrier?.label
      : '';
    this.selectedCarrier.localisation = !!this.selectedCarrier.localisation
      ? this.selectedCarrier?.localisation
      : '';
    this.selectedCarrier.niu = !!this.selectedCarrier.niu
      ? this.selectedCarrier.niu
      : '';
    this.selectedCarrier.njde = !!this.selectedCarrier.njde
      ? this.selectedCarrier.njde
      : 0;
    this.selectedCarrier.rccm = !!this.selectedCarrier.rccm
      ? this.selectedCarrier.rccm
      : '';
    this.selectedCarrier.tel = !!this.selectedCarrier.tel
      ? this.selectedCarrier.tel
      : 0;
  }

  toggle(value?: boolean) {
    this.flipped = value != undefined ? value : !this.flipped;
    if (this.flipped === false) {
      this.isNewTraining = false;
      this.isEditTraining = false;
    }
  }

  editTraining(index: any) {
    this.isEditTraining = true;
    this.trainDateStart = new Date(
      this.selectedDriver.trainings[index]?.date?.start
    );
    this.trainDateEnd = new Date(
      this.selectedDriver.trainings[index]?.date?.end
    );
    this.trainDateExpired = new Date(
      this.selectedDriver.trainings[index]?.date?.expiredDate
    );
    this.selectedTrainingIndex = index;
    this.toggle(true);
  }

  addTraining() {
    this.isNewTraining = true;
    this.trainDateStart = '';
    this.trainDateEnd = '';
    this.trainDateExpired = '';
    this.selectedTraining = {
      trainingType: '',
      trainerName: '',
      date: {
        start: '',
        end: '',
        expiredDate: '',
      },
    };

    this.toggle(true);
  }

  verifyDates() {
    const isInvalidDate = this.selectedTraining?.date?.start > this.selectedTraining?.date?.end ||
      this.selectedTraining?.date?.end > this.selectedTraining?.date?.expiredDate;

    if (isInvalidDate) {
      this.toastrSvr.danger(
        'La date de debut ne doit pas etre superieur a la date de fin',
        'Erreur de Dates'
      );
    }

    return isInvalidDate;
  }

  pushTraining() {
    const date = this.verifyDates();
    if (date)
      return;

    this.selectedDriver.trainings.push(this.selectedTraining);
    this.toggle(false);
    this.isNewTraining = false;
  }
  pushEditTraining() {
    const date = this.verifyDates();
    if (date)
      return;
    this.toggle(false);
    this.isNewTraining = false;
  }
  updateTrainDateStart(): any {
    this.isNewTraining
      ? (this.selectedTraining.date.start = moment(
        this.trainDateStart
      ).valueOf())
      : (this.selectedDriver.trainings[this.selectedTrainingIndex].date.start =
        moment(this.trainDateStart).valueOf());
  }
  updateTrainDateEnd() {
    this.isNewTraining
      ? (this.selectedTraining.date.end = moment(this.trainDateEnd).valueOf())
      : (this.selectedDriver.trainings[this.selectedTrainingIndex].date.end =
        moment(this.trainDateEnd).valueOf());
  }
  updateTrainDateExpired() {
    this.isNewTraining
      ? (this.selectedTraining.date.expiredDate = moment(
        this.trainDateExpired
      ).valueOf())
      : (this.selectedDriver.trainings[
        this.selectedTrainingIndex
      ].date.expiredDate = moment(this.trainDateExpired).valueOf());
  }

  showEditDialog() {
    this.isEdit = true;
  }

  showDetailTruckDialog(dailog: TemplateRef<any>, truck: Truck): void {
    this.selectedTruck = { ...truck };
    this.truckdialog = this.dialogSvr.open(dailog, {});
  }

  showDetailDriverDialog(dailog: TemplateRef<any>, driver: Driver): void {
    this.selectedDriver = { ...driver };
    this.dialogRef = this.dialogSvr.open(dailog, {});
  }

  async updateCarriers(): Promise<any> {
    this.isLoading = true;
    try {
      this.selectedCarrier.logo = this.imageSrc;
      await this.carrierSvr.updateCarrier(this.selectedCarrier);
      this.toastrSvr.success(
        'vos informations ont été mise à jour',
        'Modification effectuée'
      );
    } catch (error) {
      return this.toastrSvr.danger(
        'Nous avons pas pu enregistrer vos modifications',
        'Modification échouée'
      );
    } finally {
      this.isEdit = false;
      this.isLoading = false;
    }
  }

  async deleteCarrier(dailog: any): Promise<any> {
    this.dialogRef = this.dialogSvr.open(dailog, {});
    this.dialogRef.onClose.subscribe(async (result): Promise<any> => {
      if (result) {
        try {
          this.isLoading = true;
          await this.carrierSvr.deleteCarrier(this.selectedCarrier);
          this.toastrSvr.success(
            'Ce transporteur a été supprimé avec succès',
            'Suppression effectuée(s)'
          );
          this.router.navigate(['admin/carrier']);
        } catch (error) {
          return this.toastrSvr.danger(
            'Nous avons pas pu supprimé ce transporteurs',
            'Suppression échouée'
          );
        } finally {
          this.isEdit = false;
          this.isLoading = false;
        }
      }
    });
  }

  async deleteTruck(dailog: any, truck): Promise<any> {
    this.selectedTruck = truck;
    this.dialogRef = this.dialogSvr.open(dailog, {});
    this.dialogRef.onClose.subscribe(async (result): Promise<any> => {
      if (result) {
        try {
          this.isLoading = true;
          await this.carrierSvr.deleteTruck(truck);
          await this.getAllTrucks();
          this.toastrSvr.success(
            'Ce camion a été supprimé avec succès',
            'Suppression effectuée(s)'
          );
        } catch (error) {
          return this.toastrSvr.danger(
            'Nous avons pas pu supprimé ce camions',
            'Suppression échouée'
          );
        } finally {
          this.isEdit = false;
          this.isLoading = false;
        }
      }
    });
  }

  async deleteDriver(dailog: any, driver: Driver): Promise<any> {
    this.selectedDriver = driver;
    this.dialogRef = this.dialogSvr.open(dailog, {});
    this.dialogRef.onClose.subscribe(async (result): Promise<any> => {
      if (result) {
        try {
          this.isLoading = true;
          await this.carrierSvr.deleteDriver(driver);
          await this.getAllDrivers();
          this.toastrSvr.success(
            'Ce chauffeur a été supprimé de la liste avec succès',
            'Suppression effectuée(s)'
          );
        } catch (error) {
          return this.toastrSvr.danger(
            'Nous avons pas pu supprimé ce chauffeurs',
            'Suppression échouée'
          );
        } finally {
          this.isEdit = false;
          this.isLoading = false;
        }
      }
    });
  }

  getUndo() {
    this.isEdit = false;
  }

  editStatusTruck(dailog?: any, truck?: Truck) {
    this.selectedTruck = { ...truck };
    this.truckenabledialog = this.dialogSvr.open(dailog, {});
    this.truckenabledialog.onClose.subscribe(async (result: boolean) => {
      if (!result) {
        return;
      }
      await this.carrierSvr.updateTrucksCarrier(this.selectedTruck._id, {
        enable: !this.selectedTruck.enable,
      });
      await this.getAllTrucks();
    });
  }

  showEnableStatus(dailog?: any) {
    this.truckenabledialog = this.dialogSvr.open(dailog, {});
    this.truckenabledialog.onClose.subscribe(async () => {
      this.isEnable = false;
    });
  }

  truncate(str: any, size: number) {
    return this.commonSrv.truncateString(str, size);
  }

  getRemovalList(): any {
    /*     this.loading = true;
        setTimeout(() => { this.loading = false }, 1000) */
  }

  async nextPage(): Promise<any> {
    if (this.truckEndIndex >= this.truckTotal) {
      this.truckEndIndex = this.truckTotal;
      return;
    }
    this.truckOffset += 1;
    this.truckStartIndex = (this.truckOffset - 1) * this.truckLimit;
    this.truckEndIndex = this.truckOffset * this.truckLimit;
    if (this.truckStartIndex <= 0) {
      this.truckStartIndex = 1;
    }
    await this.getAllTrucks();
  }

  async previousPage(): Promise<any> {
    this.truckOffset -= 1;
    if (this.truckOffset <= 0) {
      this.truckOffset = 1;
    }
    this.truckStartIndex = (this.truckOffset - 1) * this.truckLimit;
    if (this.truckStartIndex === 0) {
      this.truckStartIndex = 1;
    }
    this.truckEndIndex = this.truckOffset * this.truckLimit;
    await this.getAllTrucks();
  }

  async driverNextPage(): Promise<any> {
    if (this.driverEndIndex >= this.driverTotal) {
      this.driverEndIndex = this.driverTotal;
      return;
    }
    this.driverOffset += 1;
    this.driverStartIndex = (this.driverOffset - 1) * this.driverLimit;
    this.driverEndIndex = this.driverOffset * this.driverLimit;
    if (this.driverStartIndex <= 0) {
      this.driverStartIndex = 1;
    }
    await this.getAllDrivers();
  }

  async driverPreviousPage(): Promise<any> {
    this.driverOffset -= 1;
    if (this.driverOffset <= 0) {
      this.driverOffset = 1;
    }
    this.driverStartIndex = (this.driverOffset - 1) * this.driverLimit;
    if (this.driverStartIndex === 0) {
      this.driverStartIndex = 1;
    }
    this.driverEndIndex = this.driverOffset * this.driverLimit;
    await this.getAllDrivers();
  }

  async getAllTrucks() {
    try {
      const options = {
        code: this.selectedCarrier.code,
        immatriculation: this.filterLabels,
        offset: this.truckOffset,
        limit: this.truckLimit,
      };

      console.log('Options envoyées:', options);
      const response = await this.carrierSvr.getAlltrucks(options);
      console.log('Réponse API:', response);
      
      if (response && Array.isArray(response.data)) {
        this.trucks = response.data;
        this.truckTotal = response.count;
        console.log('Trucks après assignation:', this.trucks);
        console.log('Total après assignation:', this.truckTotal);
      } else {
        this.trucks = [];
        this.truckTotal = 0;
        console.log('Réponse invalide ou tableau vide');
      }

      this.truckEndIndex =
        this.truckOffset * this.truckLimit < this.truckTotal
          ? this.truckOffset * this.truckLimit
          : this.truckTotal;
      this.truckStartIndex =
        this.truckTotal === 0
          ? this.truckTotal
          : (this.truckOffset - 1) * this.truckLimit + 1 || this.startIndex;
    } catch (error) {
      console.error('Erreur détaillée:', error);
      this.toastrSvr.danger(
        "Erreur lors de la récupération des camions",
        "Erreur"
      );
    }
  }

  onModelTruckMatChange(value: string): void {
    this.filteredTruckMat$ = of(this.filterTruckMat(value));
  }

  private filterTruckMat(value: string): string[] {
    const filterValues = value?.toLowerCase();
    return this.trucksMatLabels?.filter((optionValue) =>
      optionValue.toLowerCase().includes(filterValues)
    ).sort((a, b) => {
      return a.toString().toLowerCase().indexOf(filterValues?.toLowerCase()) - b.toString().toLowerCase().indexOf(filterValues?.toLowerCase());
    });;
  }

  async onSelectionTruckChange($event: string): Promise<void> {
    this.filterLabels = $event;
    // this.filteredTruckMat$ = of(this.trucksMatLabels);
    await this.getAllTrucks();
  }

  async getAllDrivers() {
    try {
      const options = {
        code: this.selectedCarrier.code,
        fullname: this.filterLabel,
        offset: this.driverOffset,
        limit: this.driverLimit,
      };
      const response = await this.carrierSvr.getAllDrivers(options);
      if (response?.data?.data) {
        this.drivers = response.data.data;
        this.driverTotal = response.data.count;
        this.driverEndIndex =
          this.driverOffset * this.driverLimit < this.driverTotal
            ? this.driverOffset * this.driverLimit
            : this.driverTotal;
        this.driverStartIndex =
          this.driverTotal === 0
            ? 0
            : (this.driverOffset - 1) * this.driverLimit + 1;
      }
    } catch (error) {
      this.toastrSvr.danger(
        "Erreur lors de la récupération des chauffeurs",
        "Erreur"
      );
      console.error('Erreur getAllDrivers:', error);
    }
  }

  onModelDriverNameChange(value: string): void {
    this.filteredDriverName$ = of(this.filterDriverName(value));
  }

  private filterDriverName(value: string): string[] {
    const filterValue = value?.toLowerCase();
    return this.DriverNameLabels?.filter((optionValue) =>
      optionValue?.toLowerCase().includes(filterValue)
    ).sort((a, b) => {
      return a.toString().toLowerCase().indexOf(filterValue?.toLowerCase()) - b.toString().toLowerCase().indexOf(filterValue?.toLowerCase());
    });;
  }

  async onSelectionDriverNameChange($event: string): Promise<void> {
    this.filterLabel = $event;
    // this.filteredDriverName$ = of(this.DriverNameLabels);
    await this.getAllDrivers();
  }

  getCarrierLogo(imgUrl?: string): any {
    const image = imgUrl ? imgUrl : 'assets/images/Transport_Types.png';
    return {
      'background-image': `url(${image})`,
      'background-repeat': 'no-repeat',
      'background-position': 'center',
      'background-size': 'contain',
      width: '400px',
      height: '400px',
    };
  }

  goBack() {
    if (window.history.length > 1) {
      this.location.back();
    } else {
      this.router.navigate(['/home']);
    }
  }

  getFile(fileDataSet: any): any {
    this.file = fileDataSet.target.files[0];
    const type = this.file.type;
    if (!type.includes('image')) {
      this.file = null;
      return this.toastrSvr.danger(
        'Veuillez importer uniquement les images',
        'Donnés incorrectes'
      );
    }
    this.filename = this.file.name;

    const reader = new FileReader();
    if (fileDataSet.target.files && fileDataSet.target.files.length) {
      const [file] = fileDataSet.target.files;
      reader.readAsDataURL(file);

      reader.onload = () => {
        this.imageSrc = reader.result as string;
      };
    }
  }

  compressFile(imageSrc: string, callback: any): any {
    const imageCompressor = new ImageCompressor();

    const compressorSettings = {
      toWidth: 150,
      toHeight: 150,
      mimeType: 'image/png',
      mode: 'strict',
      quality: 0.8,
      grayScale: false,
      sepia: false,
      threshold: false,
      vReverse: false,
      hReverse: false,
      speed: 'low',
    };

    imageCompressor.run(imageSrc, compressorSettings, callback);
  }

  resetTruck() {
    this.selectedTruck = {
      immatriculation: '',
      desc: '',
      type: '',
      capacity: null,
      volume: null,
      enable: false,
    };
  }

  resetDriver() {
    this.selectedDriver = {
      cni: '',
      tel1: '',
      tel2: '',
      driverLicense: '',
      driverCode: '',
      licenseExpireDate: '',
      fname: '',
      lname: '',
      fullname: '',
      trainings: [],
      carrier: {}
    };
  }

  openAddTrucsModal(dialog: TemplateRef<any>) {
    this.resetTruck();
    this.dialogRef = this.dialogSvr.open(dialog, {});
    this.dialogRef.onClose.subscribe(() => {
      this.selectedTruck = null;
    });
  }

  async addTruck(): Promise<any> {
    try {
      this.isLoading = true;
      if (
        !this.selectedTruck.capacity ||
        this.selectedTruck.immatriculation === '' ||
        this.selectedTruck.type === ''
      ) {
        return this.toastrSvr.danger(
          'remplissez tout les champs',
          'Donnés incorrectes'
        );
      }
      this.selectedTruck.code = Date.now();
      (this.selectedTruck.carrierRef = this.selectedCarrier.code),
        await this.carrierSvr.insertTrucks([this.selectedTruck]);
      this.getAllTrucks();
      this.dialogRef.close();
      this.toastrSvr.success('Ce camion a été ajouté', 'Camion ajouté');
    } catch (error) {
      this.toastrSvr.danger(
        "Un problème est survnenu lors de l'enregistrement de ce camion  ",
        'Erreur'
      );
    } finally {
      this.isLoading = false;
    }
  }

  openEditTruckModal(dialog: TemplateRef<any>, truck: Truck) {
    this.selectedTruck = { ...truck };
    this.dialogRef = this.dialogSvr.open(dialog, {});
    this.dialogRef.onClose.subscribe(() => {
      this.selectedTruck = null;
    });
  }

  async addDriver(): Promise<any> {
    try {
      this.isLoading = true;
      if (
        !this.selectedDriver.driverCode ||
        !this.selectedDriver.driverLicense ||
        !this.selectedDriver.lname ||
        !this.selectedDriver.fname ||
        !this.selectedDriver.tel1 ||
        !this.selectedDriver.cni ||
        !this.selectedDriver.licenseExpireDate
      ) {
        return this.toastrSvr.danger(
          'Veuillez remplir tous les champs obligatoires',
          'Données incorrectes'
        );
      }

      this.selectedDriver.fullname = `${this.selectedDriver.lname} ${this.selectedDriver.fname}`;
      this.selectedDriver.code = Date.now();
      this.selectedDriver.carrierRef = this.selectedCarrier.code;
      this.selectedDriver.issueDate = moment().valueOf();
      this.selectedDriver.licenseExpireDate = moment(this.selectedDriver.licenseExpireDate).valueOf();

      await this.carrierSvr.insertDrivers([this.selectedDriver]);
      await this.getAllDrivers();
      this.dialogRef.close();
      this.toastrSvr.success('Le chauffeur a été ajouté avec succès', 'Chauffeur ajouté');
    } catch (error) {
      this.toastrSvr.danger(
        "Un problème est survenu lors de l'enregistrement du chauffeur",
        'Erreur'
      );
    } finally {
      this.isLoading = false;
    }
  }

  openAddDriverModal(dialog: TemplateRef<any>) {
    this.resetDriver();
    this.dialogRef = this.dialogSvr.open(dialog, {});
    this.dialogRef.onClose.subscribe(() => {
      this.selectedDriver = null;
    });
  }

  openEditDriverModal(dialog: TemplateRef<any>, driver: Driver) {
    this.selectedDriver = { ...driver };
    this.toggle(false);
    this.dialogRef = this.dialogSvr.open(dialog, {});
    this.dialogRef.onClose.subscribe(() => {
      this.selectedDriver = null;
    });
  }

  async editTruck(): Promise<any> {
    try {
      this.isLoading = true;
      if (
        !this.selectedTruck.capacity ||
        this.selectedTruck.immatriculation === '' ||
        this.selectedTruck.type === ''
      ) {
        return this.toastrSvr.danger(
          'remplissez tout les champs',
          'Donnés incorrectes'
        );
      }
      await this.carrierSvr.updateTrucksCarrier(
        this.selectedTruck._id,
        this.selectedTruck
      );
      this.dialogRef.close();
      this.getAllTrucks();
      this.toastrSvr.success('Ce camion a été  modifié', 'Camion modifié');
    } catch (error) {
      this.toastrSvr.danger(
        'Un problème est survnenu lors de la modification de ce camion  ',
        'Erreur'
      );
    } finally {
      this.isLoading = false;
    }
  }
  async editDriver(): Promise<any> {
    try {
      this.isLoading = true;
      if (
        this.selectedDriver.cni === '' ||
        this.selectedDriver.tel1 === '' ||
        this.selectedDriver.driverLicense === ''
      ) {
        return this.toastrSvr.danger(
          'remplissez tout les champs',
          'Donnés incorrectes'
        );
      }
      if (this.selectedDriver?.lname) {
        this.selectedDriver.fullname = `${this.selectedDriver?.lname}`;
      }
      if (this.selectedDriver?.fname) {
        this.selectedDriver.fullname += ` ${this.selectedDriver?.fname}`;
      }
      this.selectedDriver.issueDate = moment(
        this.selectedDriver.issueDate
      ).valueOf();
      await this.carrierSvr.updateDriversCarrier(
        this.selectedDriver._id,
        this.selectedDriver
      );
      this.dialogRef.close();
      this.getAllDrivers();
      this.toastrSvr.success(
        'Ce chauffeur a été  modifié',
        'Chauffeur modifié'
      );
    } catch (error) {
      this.toastrSvr.danger(
        'Un problème est survnenu lors de la modification de ce chauffeur  ',
        'Erreur'
      );
    } finally {
      this.isLoading = false;
    }
  }

  async openDialogPDF(dialog: TemplateRef<any>, truck: any): Promise<any> {
    this.selectedTruck = truck;
    this.isLoading = true;
    try {
      this.link = await this.carrierSvr.getPdfQrCode(truck);
      this.dialogRef = this.dialogSvr.open(dialog, {});
      this.dialogRef.onClose.subscribe((result: boolean) => { });
    } catch (error) {
      return this.toastrSvr.danger(
        `Veuillez vérifier votre connexion internet.`,
        'Erreur'
      );
    } finally {
      this.isLoading = false;
    }
  }

  async openPdfView(dialog: TemplateRef<any>, truck: any): Promise<any> {
    this.isLoading = true;
    this.selectedTruck = truck;
    try {
      const data = await this.carrierSvr.getPdfQrCode(truck);
      this.pdfName = truck.immatriculation;
      const byteCharacters = atob(data.pdfLink);
      const byteNumbers = new Array(byteCharacters.length);
      for (let i = 0; i < byteCharacters.length; i++) {
        byteNumbers[i] = byteCharacters.charCodeAt(i);
      }
      this.byteArray = new Uint8Array(byteNumbers);
    } catch (error) {
      console.log('error', error);
      return this.toastrSvr.danger(
        `Veuillez vérifier votre connexion internet`,
        'Erreur'
      );
    } finally {
      this.isLoading = false;
    }

    const dialogRef = this.dialogSvr.open(dialog, {});
    dialogRef.onClose.subscribe(() => {
      this.pdfName = null;
      this.byteArray = null;
    });
  }

  onTypeSelected(type: any): void {
    this.selectedType = type;
    this.dialogRef.close();
  }

  selectDate() {
    document.getElementById('date').click();
  }

  async getDriverRefresh() {
    this.driverName = '';
    this.filterLabel = null;
    await this.getAllDrivers();
  }
  async getTruckRefresh() {
    this.immatriculation = '';
    this.filterLabels = null;
    await this.getAllTrucks();
  }

  async driversUpload(event: any) {
    const target: DataTransfer = <DataTransfer>event.target;

    try {
      if (target.files.length !== 1) this.toastrSvr.danger(`donnes incorrect`);
      this.isLoading = true;
      let dataObject;
      const selectedFile = event.target.files[0];
      const fileReader = new FileReader();
      fileReader.onload = async (e) => {
        let binaryData = e.target.result;
        let workbook = xlsx.read(binaryData, {
          type: 'binary',
          cellDates: true,
        });
        const sheetName = workbook.SheetNames[0];
        const data = xlsx.utils.sheet_to_json(workbook.Sheets[sheetName], {
          raw: false,
        });
        dataObject = data.map((elt: any) => {
          return (elt = {
            ...elt,
            carrier: {
              _id: this.selectedCarrier._id,
              carrierRef: this.selectedCarrier.code,
              label: this.selectedCarrier.label,
            },
          });
        });
        await this.carrierSvr.insertDrivers(dataObject);
        await this.getAllDrivers();
      };
      fileReader.readAsBinaryString(selectedFile);
    } catch (error) {
      this.toastrSvr.danger(`donnes incorrect`);
      return error;
    } finally {
      location.reload();
      // this.isLoading = false;
    }
  }

  async trucksUpload(event: any) {
    const target: DataTransfer = <DataTransfer>event.target;
    try {
      if (target.files.length !== 1) this.toastrSvr.danger(`donnes incorrect`);
      this.isLoading = true;
      let dataObject;
      const selectedFile = target.files[0];
      const fileReader = new FileReader();
      fileReader.onload = async (e) => {
        let binaryData = e.target.result;
        let workbook = xlsx.read(binaryData, { type: 'binary' });
        const sheetName = workbook.SheetNames[0];
        const data = xlsx.utils.sheet_to_json(workbook.Sheets[sheetName], {
          raw: false,
        });
        dataObject = data.map((elt: any) => {
          return (elt = {
            ...elt,
            carrier: {
              _id: this.selectedCarrier._id,
              carrierRef: this.selectedCarrier.code,
              label: this.selectedCarrier.label,
            },
          });
        });
        await this.carrierSvr.insertTrucks(dataObject);
        await this.getAllTrucks();
      };
      fileReader.readAsBinaryString(selectedFile);
    } catch (error) {
      this.toastrSvr.danger(`donnes incorrect`);
      return error;
    } finally {
      // this.isLoading = false;
      location.reload();
    }
  }
}
