@import "./variables";

body {
  font-family: "AvenirNext" !important;
  line-height: 1.2;
  -webkit-background-size: cover;
  -moz-background-size: cover;
  -ms-background-size: cover;
  background-size: cover;
  background-repeat: no-repeat;
  background-color: #e5e5e5;
}

.header {
  width: 100%;
}

textarea::-webkit-scrollbar,
div::-webkit-scrollbar,
nb-list::-webkit-scrollbar,
nb-autocomplete::-webkit-scrollbar {
  width: 0.4em;
}

textarea::-webkit-scrollbar-track,
div::-webkit-scrollbar-track,
nb-list::-webkit-scrollbar-track,
nb-autocomplete::-webkit-scrollbar-track {
  border-radius: 10px;
  box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
}

textarea::-webkit-scrollbar-thumb,
div::-webkit-scrollbar-thumb,
nb-list::-webkit-scrollbar-thumb,
nb-autocomplete::-webkit-scrollbar-thumb {
  border-radius: 10px;
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.5);
}

//  nb-layout-column {
//   background-color: #F3FBFF;
//  }
.nb-theme-default [nbInput].status-basic:disabled {
  font-family: $font-regular !important;
  color: #000 !important;
}

.home-bottom-decoration {
  height: 60px;
  background-image: url(/assets/images/bg-home-bottom.png);
  background-repeat: no-repeat;
  background-position: top;
  background-size: 100% 100%;
}

.nb-theme-default nb-layout .layout .layout-container .content nb-layout-footer {
  box-shadow: none !important;
}

.sidebar-container {
  height: 80% !important;
}

.sidebar-toggle {
  margin-right: 10px;
}

.dialog-assign-content {
  width: 600px;
  text-align: center;

  .important {
    font-family: $font-bold;
  }

  .group {
    line-height: 32px;
  }
}

.header-menu-icon {
  font-size: 2.7rem !important;
}

.nb-theme-default nb-sidebar.compacted nb-menu {
  width: auto !important;
}

// .nb-theme-default nb-layout-header nav.fixed {
//   @include vh-center;
//   flex-direction: column;
//   background-color: $color-primary;
//   color: $color-secondary;
//   font-family: "AvenirNext-DemiBoldItalic";
//   height: auto;
//   padding: 0;

//   .header-container {
//       @include v-center;
//       justify-content: space-between;
//       width: 100%;
//       height: 55px;
//   }
//   .header-container .right-block {
//       @include vh-center;
//       .new-purchase-btn,
//       .login-btn {
//           border: 1px solid #ffffff;
//           color: #fff;
//           padding: 4px 12px;
//           border-radius: 4px;
//           nb-icon {
//               margin-right: 5px;
//           }
//       }
//       .new-purchase-btn {
//           margin-right: 10px;
//       }
//       .login-btn {
//           margin-left: 10px;
//       }
//       .new-purchase-btn:hover,
//       .login-btn:hover {
//           @include scale-effect;
//           color: $color-primary;
//           background-color: #ffffff;
//       }
//   }
// }
.nb-theme-default [nbButton].appearance-outline.status-primary {
  background-color: rgba(51, 102, 255, 0.08);
  border-color: $color-primary !important;
  color: $color-primary !important;
}

.nb-theme-default [nbButton].appearance-filled.status-primary {
  border-color: $color-primary !important;
}

.nb-theme-default [nbInput].status-basic:focus {
  background-color: #fff;
  border-color: $color-primary !important;
}

.scale-effect {
  @include scale-effect;
}

.search-txt {
  color: $color-primary !important;
}

.nb-theme-default [nbButton] {
  font-family: $font-regular !important;
}

.nb-theme-default [nbInput].status-primary {
  border-color: $color-primary !important;
}

.nb-theme-default nb-card.accent-primary {
  border-top-color: $color-primary !important;
}

.nb-theme-default nb-select.appearance-outline.status-primary .select-button {
  border-color: $color-primary !important;
}

.nb-theme-default nb-icon.status-primary {
  color: $color-primary !important;
}

.nb-theme-default [nbButton].appearance-filled.status-primary {
  border-color: $color-primary !important;
  background-color: $color-primary !important;
}

.form--header {
  font-family: $font-bold;
  display: flex;
  justify-content: flex-start;
  font-size: 18px;

  .return-icon {
    margin-right: 5px;
    outline: 0;
    cursor: pointer;
  }
}

.input {
  // margin-bottom: 10px;
}

/* Override CSS fixed navbar */

.form-contain {
  overflow-y: overlay;
  // height: 400px;
}

.nb-theme-default button.status-primary.appearance-outline {
  background: rgba($color: $color-primary, $alpha: 0.08) !important;

  &:hover {
    background: rgba($color: $color-primary, $alpha: 0.16) !important;
  }
}

.nb-theme-default nb-tabset .tab-link {
  font-family: $font-regular !important;
  font-size: 12px !important;
  outline: none !important;
}

// .nb-theme-default nb-tabset .tab a:hover{
//   color: #0d7777 !important;
// }

.nb-theme-default nb-tabset .tab:hover .tab-link::before {
  background-color: $color-primary;
}

.nb-theme-default nb-layout-header nav.fixed {
  @include v-center;
  justify-content: flex-end;
  flex-direction: column;
  background-color: #ffff;
  color: $color-secondary;
  font-family: $font-regular;
  font-weight: 500;
  padding: 10px 0 0 0;

  .header-width {
    width: 89%;
  }

  .header-container {
    @include v-center;
    width: 100%;
    justify-content: space-between;

    .icon_logo {
      cursor: pointer;
    }
  }

  .header-mobile {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: 0 16px;
    height: 55px;
  }

  .header-container .right-block {
    @include vh-center;

    .new-purchase-btn,
    .login-btn {
      align-items: center;
      border: 1px solid $color-primary;
      padding: 9px 12px;
      border-radius: 5px;

      nb-icon {
        margin-right: 5px;
      }
    }

    .new-purchase-btn {
      background-color: $color-primary;
      border-color: $color-primary;
      color: $color-secondary;
      margin-right: 10px;
    }

    .login-btn {
      color: $color-primary;
      margin-left: 10px;
    }

    .new-purchase-btn:hover,
    .login-btn:hover {
      @include scale-effect;
    }
  }
}

/**/
.nb-theme-default nb-select.size-large .select-button.placeholder {
  font-family: $font-regular !important;
  font-size: 13px !important;
}

.form-group input::placeholder {
  font-family: $font-regular !important;
  color: #000 !important;
}

.nb-theme-default nb-layout .layout .layout-container .content .columns nb-layout-column {
  padding: 0;
}

.nb-theme-default nb-sidebar.expanded {
  // width: 130px;
  // max-width: 130px !important;
}

.nb-theme-default nb-layout-header.fixed~.layout-container {
  padding-top: 0;
}

.nb-theme-default [nbButton] {
  @include vh-center;
  font-family: $font-regular;
}

.nb-theme-default nb-card.custom-menu-list {
  margin-bottom: 0px;
  border-style: none;
}

.nb-theme-default .custom-menu-list nb-list-item {
  @include v-center;
  cursor: pointer;
  font-family: $font-regular;

  nb-icon {
    margin-right: 7px;
  }
}

.nb-theme-default .custom-menu-list nb-list-item:hover {
  @include scale-effect;
  color: $color-primary;
}

.nb-theme-default nb-sidebar.compacted {
  // width: 0;
}

.nb-theme-default nb-select .select-button {
  min-width: auto;
  box-sizing: border-box !important;
}

.nb-theme-default nb-sidebar .scrollable {
  // margin-top: -20px;
  background-color: $color-primary !important;
  padding: 1.25rem !important;
}

.bold {
  font-family: $font-bold;
}

.nb-theme-default nb-menu .menu-item>.menu-items {
  padding: 0 1.25rem !important;
}

.nb-theme-default nb-menu .menu-item a {
  color: #fff !important;
}

.nb-theme-default nb-tabset .tab.active .tab-link:hover {
  color: $color-primary !important;
}

.nb-theme-default nb-tabset ul li a span {
  width: 100% !important;
  font-size: 10px;
}

.nb-theme-default nb-tabset .tab.active .tab-link {
  color: $color-primary !important;
  outline: none !important;
  background-color: rgb(11, 48, 92, 0.1) !important;
  border-radius: 9px !important;
}

.nb-theme-default nb-tabset .tab-link {
  outline: none !important;
  margin-right: 30px !important;
}

.nb-theme-default nb-card-footer {
  border-top: 0px solid #edf1f7 !important;
}

.nb-theme-default nb-card-header {
  border-bottom: 0px solid #edf1f7 !important;
}

.nb-theme-default [nbButton].appearance-outline.status-basic,
.nb-theme-default .appearance-outline.status-basic[nbButtonToggle] {
  border-color: transparent !important;
  background-color: transparent !important;
}

.btn-border.border.appearance-outline.size-medium.shape-rectangle.icon-start.status-basic {
  background: #edf1f7 !important;
  border: 1px solid #dfe1e4 !important;
}

.btn-border {
  margin-left: 10px;
}

.nb-theme-default nb-select.appearance-filled.status-basic .select-button.placeholder {
  width: 23rem !important;

  .nb-theme-default [nbInput].shape-rectangle {
    border-radius: 5px !important;
  }

  .nb-theme-default [nbButton].appearance-outline.status-danger,
  .nb-theme-default .appearance-outline.status-danger[nbButtonToggle] {
    align-items: center !important;
    border-radius: 5px !important;
  }
}

// .nb-theme-default [nbButton].appearance-filled.status-basic, .nb-theme-default .appearance-filled.status-basic[nbButtonToggle]:focus{
//   color: $color-secondary !important;
//   background-color: $color-primary !important;
// }

.removal-assign-container nb-card {
  max-height: 95vh;
  border-radius: 8px;
  width: 424px;

  .nb-theme-default [nbInput].size-small:not(.input-full-width) {
    width: 82% !important;
  }
}

.removal-queue-container nb-card {
  max-height: 60vh;
  border-radius: 8px;
  width: 500px;
}

.removal-quai-container nb-card {
  width: 500px !important;
}

// .tabset .tab {
//     width: 100%;
// }

// .menu-removal-container .removal-windows  nb-tabset nb-tab ul li{
//   width: 28% !important;
// }
.nb-theme-default nb-tabset .tab.active .tab-link::before {
  background-color: $color-primary !important;
}

.nb-theme-default nb-tabset .tabset {
  margin-left: auto !important;
  margin-right: auto !important;
}

.nb-theme-default nb-tabset ul li a span {
  font-size: 12px !important;
}

#otherTrainingType {
  resize: none !important;
}

.btn {
  color: #fff;
  text-align: center;
  letter-spacing: 0.5px;
  font-weight: 500;
  font-size: 16px;
  width: 217px;
  height: 45px;
  border-radius: 2px;
  cursor: pointer;
  line-height: 22px;
  font-family: "AvenirNext-DemiBold";
  @include vh-center;
}

.btn-white {
  border: 1px solid #fff;
}

.search-btn:hover {
  @include scale-effect;
}

.btn-primary {
  background-color: $bg-color-primary;
}

.btn-secondary {
  background-color: $color-secondary;
}

.banner-title {
  font-size: 39px;
  line-height: 48px;
  font-family: "AvenirNext-Bold";
}

.banner-comment {
  margin-top: 10px;
  font-style: normal;
  font-weight: normal;
  font-size: 17px;
  line-height: 28px;
  mix-blend-mode: normal;
  opacity: 0.9;
  font-family: "Inter-Regular";
}

.banner-height {
  margin-top: 105px;
}

.banner-mobile {
  margin-top: 56px;
}

.white-txt {
  font-family: "Inter-Bold";
}

.option-list nb-option {
  font-family: $font-bold;
}

nb-accordion nb-accordion-item nb-accordion-item-header-expanded {
  background-color: $color-primary !important;
  color: #fff !important;
}

nb-card-footer {
  @include vh-center;

  .cancel {
    margin-right: 20px;
  }
}

nb-card {
  margin-bottom: 0 !important;
}

nb-card-header {
  font-family: $font-regular !important;
}

.page--header {
  font-family: $font-bold;
  margin-bottom: 5px;
  font-size: 18px;
}

.form--footer {
  @include vh-center;

  button {
    margin: auto 20px;
  }
}

.bold {
  font-family: $font-bold;
}

.item-input {

  &.input[type="text"]:disabled,
  &.input[type="number"]:disabled,
  &.input[type="email"]:disabled,
  &.textarea[type="text"]:disabled,
  &.select-button[disabled] {
    color: #222b45 !important;
    background-color: transparent !important;
    // border-color: none !important;
    border-color: #dc752a !important;
    padding: 0.4375rem 0 !important;
  }
}

::-webkit-input-placeholder {
  color: #222b45 !important;
  font-size: 0.9375rem !important;
  font-weight: 500 !important;
  line-height: 1.5rem !important;
}

select-button[disabled],
.select-button.placeholder {
  color: #222b45 !important;
  font-size: 0.9375rem !important;
  font-family: "Lato-Regular";
  font-weight: 500 !important;
  line-height: 1.5rem !important;
}

select-button[disabled],
.select-button.placeholder {
  color: #222b45 !important;
  font-size: 0.9375rem !important;
  font-family: "Lato-Regular";
  font-weight: 500 !important;
  line-height: 1.5rem !important;
}

nb-option-list.size-medium nb-option {
  font-size: 12px !important;
}

.password-forgot-container {
  max-width: 600px !important;
}

.form--header .paginator {
  width: 100%;
  min-width: 100px;
  @include v-center;
  justify-content: flex-end;
  font-weight: 600;
  font-size: 13px;
}

.form--header .paginator .paginator-container {
  @include vh-center;
}

// ********************
// NEW LIST CUSTUM CSS
.filter-container {
  @include v-center;
  justify-content: space-between;
  width: 100%;
  height: 55px;
}

.nb-theme-default nb-layout-header.fixed~.layout-container {
  padding-top: 0 !important;
  min-height: 100vh;
}

.nb-theme-default nb-layout .layout .layout-container .content nb-layout-footer nav {
  padding: 0 !important;
}

.nb-theme-default nb-layout .layout .layout-container .content .columns nb-layout-column {
  padding: 70px 0 0 0 !important;
}

// new service dropdown list

.nb-theme-default nb-option-list .option-list {
  height: 100%;
  max-height: 13rem !important;
  margin: 0;
  padding: 0;
  overflow: auto;
}

.nb-theme-default [nbInput].size-small:not(.input-full-width) {
  max-width: 24rem;
  width: 100%;
  margin-top: 1em;
  margin-right: 1em;
}

.btn-cancel,
.cancel {
  background-color: rgba(143, 155, 179, 0.08);
  border-color: #8f9bb3 !important;
  color: #8f9bb3 !important;
  border-radius: 5px;
  // &:hover{
  //     background-color: rgba(255,61,113,0.08) !important;
  //     border-color: #ff3d71 !important;
  //     color: #ff3d71 !important;
  // }
}

// .menu-elt{
//     height: 36vh !important;
//   width: 15vw !important;
// }

.modal-container .modal-content nb-card {
  padding-bottom: 0px !important;
  margin-bottom: 10px !important;
}

.edit-order {
  border-radius: 9px !important;
}

.logout-container {
  display: flex;
  height: 20%;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  nb-icon {
    width: 28px;
    height: 28px;
    color: $color-secondary !important;
  }

  span {
    color: $color-secondary !important;
    margin-left: 4%;

    &:hover {
      color: #ff3d71;
    }
  }
}

.order-detail-container {



  .gallery-container {
    text-align: center;

    .thumb-bar {
      display: flex;
      flex-wrap: wrap;
      justify-content: center;
      margin-bottom: 1em;

      .thumb {
        width: 100px;
        height: 100px;
        margin: 0.5em;
        cursor: pointer;
        transition: transform 0.2s;

        &:hover {
          transform: scale(1.1);
        }
      }
    }

    .displayed-img-container {
      position: relative;
      display: inline-block;

      .displayed-img {
        max-width: 600px;
        margin-bottom: 1em;
      }

      button {
        position: absolute;
        top: 10px;
        margin: 2em auto;
        right: -110px;
      }

      .edit {
        position: absolute;
        font-size: 2.5em;
        right: -64px;

      }
    }
  }





  @include vh-center;
  padding-top: 25px;

  nb-card {
    max-height: 95vh;
    border-radius: 9px;
    width: 620px;
  }

  .planning-width {
    width: 620px !important;
  }

  .form--header {
    @include vh-center;
    text-transform: uppercase;
  }

  .header {
    font-family: $font-regular;
    @include vh-center;
    font-size: 18px;
  }

  .order-infor {
    @include vh-center;

    .right-block,
    .left-block {
      width: 50%;
      letter-spacing: 0.75px;
      text-transform: capitalize;
    }

    .left-block {
      display: flex;
      align-items: flex-end;
      flex-direction: column;
    }

    .right-block .title {
      color: rgba(0, 0, 0, 0.48);
      margin-bottom: 5px;
      font-size: 15px;
      font-family: $font-regular !important;
    }

    .left-block .value {
      color: rgba(0, 0, 0, 0.87);
      margin-bottom: 5px;
      font-size: 15px;
      font-family: $font-regular !important;
    }
  }

  .list {
    margin-top: 10px;
    margin-bottom: 10px;

    .form--header {
      text-transform: unset;
    }

    .list-elt-contain {
      box-shadow: 0px 4px 25px rgba(0, 0, 0, 0.09);
      border-radius: 1px;
    }

    .list-elt-header,
    .list-elt-contain {
      padding: 5px 16px;
    }

    .list-elt-header,
    .list-elt-contain .list-elt {
      font-size: 14px;
      @include vh-center;
      height: 43px;

      .col {
        height: 100%;
        padding: 5px 0px;
      }

      .col.col-desc {
        width: calc(100% - 315px);
      }

      .col.col-weight {
        width: 15vw;
        @include vh-center;
      }

      .col.col-price {
        width: 105px;
        @include vh-center;
      }

      .col.col-amount {
        width: 105px;
        @include v-center;
        justify-content: flex-end;
      }

      .col.col-desc .col-desc-elt {
        height: 100%;
        @include v-center;

        .col-desc-elt-contain {
          width: 25px;
          height: 100%;
          .product-image{
            height: 2em;
          }
        }
      }
    }

    .list-elt-header {

      // height: 30px;
      .col {
        font-size: 13px;
        font-family: $font-regular;
        color: rgba(0, 0, 0, 0.65);
        height: 100%;
        text-align: center;
      }

      .col.col-desc {
        text-align: unset;
      }
    }

    .list-elt-contain>div {
      border-bottom: 1px solid rgba(0, 0, 0, 0.24);
    }

    .list-elt-contain>div:last-child {
      border-bottom: none;
    }

    .nb-card-footer {
      font-family: $font-regular !important;
      padding: 30px !important;

      .remove-icon {
        margin-right: 8px !important;

        .list-elt-header {
          @include vh-between;
          padding-top: 7px;
          padding-bottom: 7px;
          font-size: 13px;

          .col {
            display: flex;
            height: 100%;
            align-items: center;
          }

          .col-jde {
            min-width: 140px;
            max-width: 140px;
          }

          /*  .col-date {
          min-width: 140px;
          max-width: 140px;
        } */
          /*  .col-hour {
          min-width: 100px;
          max-width: 100px;
        } */
          .col-name {
            max-width: 150px;
            min-width: 150px;
          }

          .col-status {
            min-width: 100px;
            max-width: 100px;
            display: flex;
            align-items: center;
            justify-content: center;

            span {
              background: #027a37;
              border-radius: 4px;
              color: $color-secondary;
              padding: 3px 8px;
            }
          }

          .col-details {
            overflow: hidden;
            justify-content: center;

            .product-choice {
              background-image: url("./../../images/icons/ciment.png");
              background: no-repeat center contain;
              width: 19px;
              height: 31px;
            }
          }

          .col-action {
            display: flex;
            align-items: center;
            justify-content: flex-end;

            .action-icons {
              @include vh-between;
              width: 60%;
            }
          }
        }
      }

      .nb-theme-default nb-card {
        border-radius: 9px !important;
      }
    }
  }
}

.order-detail-container-export {


  nb-card {
    border-radius: 8px;
    width: 100%;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);

    &-header {
      &.form--header {
        background-color: #f7f9fc;
        border-bottom: 1px solid #ebeff5;
        padding: 16px;
        font-size: 1.25rem;
        font-weight: bold;
        text-align: center;
      }
    }

    &-footer {
      &.form--footer {
        display: flex;
        justify-content: space-between;
        padding: 16px;
        background-color: #f7f9fc;
        border-top: 1px solid #ebeff5;

        .remove-icon {
          margin-right: 8px;
        }

        nb-button {
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 8px 16px;
          font-size: 1rem;
        }
      }
    }
  }

  .export-contenair {
    padding: 20px;

    .filters-export {
      display: flex;
      flex-direction: column;
      gap: 15px;

      .empty-input {
        &.height {
          width: 100%;
          height: 38px;
          padding: 8px;
          border-radius: 4px;
          border: 1px solid #d4d9e0;
          font-size: 1rem;
        }
      }

      nb-autocomplete {
        width: 100%;
      }

      nb-input {
        width: 100%;
      }

      .filter-label {
        width: 100%;
        text-align: center;
        font-size: 0.875rem;
        color: #6c757d;
        margin-top: 10px;
      }

      nb-datepicker {
        width: 100%;
      }
    }
  }
}


.no-padding {
  padding: 0% !important;
}

nb-list-item {
  border-top: none !important;
}

nb-card-footer,
nb-card-header {
  border: none !important;
}

.list-body {
  font-size: 11px !important;
}

nb-card {
  border-radius: 10px !important;
}

// .nb-theme-default nb-toggle .toggle-switcher{
//   height: 18px !important;
//   width: 17px !important;
// }
// .nb-theme-default nb-toggle.status-basic .toggle{
//   height: 20px !important;
//   width: 36px !important;

// }
.nb-theme-default [nbInput].status-basic:hover {
  background-color: $color-secondary !important;
  border-color: $color-primary !important;

  .cards {
    height: 75vh;
    // height: 100%;
  }
}

textarea {
  resize: none !important;
}

h2,
h3,
h4,
h5,
h6 {
  font-family: "Metropolis-Regular" !important;
}

nb-context-menu {

  .menu-title,
  nb-icon {
    color: #000 !important;
  }

  &:hover {

    .menu-title,
    nb-icon {
      color: $color-primary !important;
    }
  }
}

.no-border {
  border: none !important;
}