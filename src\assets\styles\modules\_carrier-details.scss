.carrier-details {
  margin-top: 1%;
  margin-left: 2%;
  margin-right: 6%;

  nb-tab {
    height: 75vh;
  }

  .not-found {
    display: flex;
    justify-content: center;
    flex-direction: column;
    align-items: center;
    width: 100%;
  }

  .block-general-infor {
    display: flex;
    align-items: flex-start;

    .general-infor {
      width: 55%;
      margin-top: 5px;

      .carrier-label {
        margin-bottom: 10px;

        p {
          font-family: $font-regular;
          font-size: 16px;
          padding: 5px 0;
          color: #000000;
          opacity: 0.5;
        }

        .carrier-input {
          color: #000000;
          opacity: 0.5;
          font-size: 13px;
        }

        .carrier {
          color: #000000;
        }
      }
    }

    .carrier-logo {
      width: 40%;
    }
  }

  .list-head {
    height: 70px;
  }

  .list-elt-header {
    .col {
      font-weight: bold;
    }
  }

  .list-elt-header,
  .list-elt {
    @include vh-between;
    height: 50px;
    padding-top: 7px;
    padding-bottom: 7px;
    font-size: 13px;

    .col-paginator {
      display: flex;
      justify-content: space-between;

      .left-area {
        display: flex;
        width: 40%;
        // min-width: 400px;

        .filter-label {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-right: 15px;
        }


        .input-auto-style {
          margin-right: 15px;

          .empty-input {
            width: 70%;
          }
        }
      }

      .btn-contain {
        display: flex;

      }

      .file-upload-group {
        // width: 160px;
        display: flex;
        justify-content: center;
        align-items: center;

        .file-upload-input {
          display: none;
        }

        .file-upload-label {
          cursor: pointer;
          width: 100%;
          padding: 4px 24px;
          color: $color-fourth;
          border: 1px solid $color-primary;
          background-color: #e4fcf4ee;
          margin-bottom: 2px;
          border-radius: 3px;
          margin-right: 15px;
        }
      }

      .btn-style {
        .refresh-btn {
          // width: 109px;
          height: 8px;
          margin-right: 15px;
        }

      }

      // .btn-add-driver {

      // }

    }
  }

  .col {
    height: 100%;
    display: flex;
    align-items: center;
  }

  .col-num {
    width: 5%;
    min-width: unset !important;
    max-width: unset !important;
  }

  .col-ref {
    width: 15%;
    min-width: unset !important;
    max-width: unset !important;
  }

  .col-truc-type {
    width: 12%;
  }

  .col-matriculation {
    width: 12%;
  }

  .col-store {
    width: 10%;
  }

  .col-description {
    width: 15%;
  }

  .col-weight {
    width: 5%;
  }

  .col-carrier-ref {
    width: 13%;
  }

  .col-vol {
    width: 8%;
  }

  .col-driver-code {
    width: 15%;
  }

  .col-permit {
    width: 15%;
  }

  .col-fullname {
    width: 25%;
  }

  .col-phone {
    width: 17%;
  }

  .col-cni {
    width: 10%;
  }

  .col-tain-num {
    width: 5%;
  }

  .col-action {
    width: 20%;
    min-width: unset !important;
    max-width: unset !important;
    justify-content: flex-end;

    .action-icons {
      display: flex;
      align-items: center;
      justify-content: flex-end;
    }

    .pagination {
      width: 215px;
      display: flex;
      justify-items: flex-end;

      .actions {
        display: flex;
      }
    }

    button {
      text-transform: none !important;
      background-color: #edf1f7;
      padding: 5px;
      border-radius: 5px;
    }
  }

  .card-width {
    height: 80vh !important;
  }

  .rm-padding {
    padding: 0px !important;
  }

  .badge {
    position: relative;
    left: 32vw;
    top: 10vh;
  }

  .badge-drivers {
    position: relative;
    left: 48vw;
    top: 10vh;
  }
}

.label-users-list {
  @include v-center;

  h6 {
    margin: 0 !important;
  }
}

.add-carrier {

  .pagination {
    display: flex;
    align-items: center;
    margin-right: 20px;
    justify-content: space-between;
  }

  position: sticky;
  display: flex;
  justify-content: flex-end !important;
  width: auto !important;
}

nb-dialog-container {

  // width: 28vw !important;
  .btn-margin {
    margin-right: 10px !important;
  }

  .carrier-label {
    margin-bottom: 10px;
  }
}

nb-card-header h5 {
  font-size: 16px !important;
}

.truck-label {
  h6 {
    font-family: $font-bold;
    font-size: 15px;
    margin-top: 10px;
    // text-align:center;
  }

  .container {
    display: flex;
    justify-content: center;
    flex-direction: column;
    align-items: center;
    margin: 15px;

    p {
      font-family: $font-regular;
      font-size: 16px;
      font-weight: bold;
      margin: unset !important;
    }

    div {
      margin-top: 5px;
    }
  }
}

.undo-btn {
  margin-right: 15px;
}

.addUSer nb-card-body {
  padding: 0 !important;
}

.addUSer nb-tabset nb-tab {
  padding: 1rem 10px !important;
}

.form-group {
  margin-bottom: 10px;

  .label {
    font-size: 16px;
    line-height: 30px;
    color: #000;
    font-family: $font-regular;
  }

  .user-name {
    width: 31vw;
    font-family: $font-regular;
    font-size: 13px;
  }
}

.detail-truck {
  width: 35vw;
}