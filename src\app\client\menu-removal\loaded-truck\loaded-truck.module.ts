import { NgModule } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { SharedModule } from 'src/app/shared/shared.module';
import { LoadedTruckComponent } from './loaded-truck.component';
import { LoadedTruckRoutingModule } from './loaded-truck-routing.module';
import { NbTabsetModule, NbCardModule, NbAutocompleteModule, NbIconModule, NbListModule, NbBadgeModule, NbDialogModule, NbButtonModule, NbSpinnerModule, NbTooltipModule, NbSelectModule, NbInputModule, NbDatepickerModule } from '@nebular/theme';

@NgModule({
  declarations: [
    LoadedTruckComponent
  ],
  imports: [
    CommonModule,
    FormsModule,
    NbTabsetModule,
    NbCardModule,
    NbTabsetModule,
    NbAutocompleteModule,
    NbIconModule,
    NbListModule,
    NbBadgeModule,
    NbDialogModule,
    NbButtonModule,
    NbSpinnerModule,
    NbTooltipModule,
    NbSelectModule,
    NbInputModule,
    SharedModule,
    NbDatepickerModule,
    LoadedTruckRoutingModule
  ]
})
export class LoadedTruckModule { }
