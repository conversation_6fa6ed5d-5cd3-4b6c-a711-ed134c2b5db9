<!-- order-card.component.html -->
<div class="order-item">

  <nb-card class="cart-recap" *ngFor="let partitonOrder of partitonOrders">
    <div class="order-content">
      <!-- En-tête du bon -->
      <div class="order-header">
        <h2 class="bon-number">N° X3 {{ partitonOrder?.erpReference }}</h2>
        <div class="status">
          <span class="render-pill"
            [ngClass]="'render-' + partitonOrder?.cart?.renderType">{{getStatusRender(partitonOrder?.cart?.renderType)}}</span>
          <button nbButton status="danger" class="modify-button" style="height: 6px;"
            (click)="deletePartition(partitonOrder)">
            Supprimer
          </button>

        </div>


      </div>

      <!-- Section produits -->
      <div class="products-section">
        <div class="section-label">Produits</div>
        <div class="products-container">
          <div class="product-item" *ngFor="let  item of partitonOrder?.cart?.items">
            <img [src]="item?.product?.image" alt="{{ item?.product?.label }}" class="product-image">
            <div class="product-name">{{ item?.product?.label }} </div>
            <div class="product-name">({{item?.packaging?.label}})</div>
            <div class="product-name">Quantité retirée: <span style="color: #0B305C;"> {{item?.quantityShipped ?? 0}} Sacs</span></div>

          </div>
        </div>
      </div>


      <!-- Section informations client -->

      <div class="client-section">
        <div class="info-row">
          <div class="info-label" nbTooltip="{{order?.company?.name }}">Nom Client</div>
          <div class="info-value client-code" nbTooltip="{{order?.company?.name }}">{{ partitonOrder?.company?.name | truncateString:12}}</div>
        </div>
        <div class="info-row quantity-row">
          <div class="info-label">Total retirée</div>
          <div class="info-value quantity">{{getQdtyDelivery(partitonOrder?.cart?.items)}} sacs</div>
        </div>
        <!-- Bouton Modifier -->
        <div class="action-section">
          <button nbButton status="basic" class="modify-button" (click)="modifyPartition(newAesDialog, partitonOrder)">
            Modifier la partition
          </button>
        </div>
      </div>

    </div>
  </nb-card>
  <div class="empty-list empty-list-partition" *ngIf="!partitonOrders?.length">
    <nb-icon icon="file-add-outline"></nb-icon>
    <p>Les bons découpes apparaissent ici.</p>
    <p></p>
  </div>

</div>


<ng-template #newAesDialog let-ref="dialogRef">

  <div class="partition-container">
    <div class="modal-header">
      <img src="../../../../assets/images/close-circle.png" (click)="ref.close(); groupedOrders =[];" class="close"
        alt="liste vide">

    </div>
    <div class="container">

      <div class="partition-card-info">
        <nb-card-body>
          <!-- Header avec numéro de bon et date de création -->
          <div class="left-bloc">
            <div class="order-header">
              <div class="order-icon-container">
                <img src="../../../../assets/images/document-text.png" class="document-icon" alt="liste vide">
              </div>
              <div class="order-info">
                <div class="order-title">
                  <h5>N° X3 {{order?.erpReference}}</h5>
                </div>
                <p class="creation-date">Créer le {{order.created_at | date:'dd/MM/yyyy à HH'}} H
                  {{order.created_at | date:'mm'}}</p>
              </div>
            </div>
            <div class="details-grid">
              <div class="product-icon-container">
                <nb-icon icon="menu-2-outline" class="product-icon"></nb-icon>
              </div>

              <div class="detail-column">
                <p class="section-label" nbTooltip="{{order?.company?.name }}">Nom Client</p>
                <p class="detail-value" nbTooltip="{{order?.company?.name }}">{{order?.company?.name | truncateString:12}}</p>
              </div>
              <div class="detail-column">
                <p class="section-label">Quantité(s)</p>
                <p class="detail-value">{{getQdty(order?.cart?.items)}}</p>
              </div>
              <div class="detail-column">
                <p class="section-label">Livrés</p>
                <p class="detail-value delivered">{{getQdtyDelivery(order?.cart?.items)}} Sacs</p>
              </div>
            </div>

          </div>

          <div class="rigth-bloc">
            <div class="status">
              <span class="render-pill"
                [ngClass]="'render-' + order?.cart?.renderType">{{getStatusRender(order?.cart?.renderType)}}</span>

              <span class="status-pill"
                [ngClass]="'status-' + order.statusDelivery">{{getStatusText(order.statusDelivery)}}</span>
            </div>
          </div>

        </nb-card-body>

        <!-- Indicateur de statut en bas de la carte -->
        <div class="status-bar" [ngClass]="'status-' + order?.statusDelivery"></div>

      </div>
      <div class="products-section">
        <div class="product-icon-container">
          <nb-icon icon="shopping-bag-outline" class="product-icon"></nb-icon>
        </div>
        <div class="product-info">
          <p class="section-label">Produits</p>
          <div class="products-container">
            <div class="product-item" *ngFor="let item of order?.cart?.items">
              <img [src]="item?.product?.image" alt="{{ item?.product?.label }}" class="product-image">
              <div class="product-name">{{ item?.product?.label }} ({{item?.packaging?.label}} ) - {{
                item?.quantity - (item?.quantityShipped ?? 0) }} sac(s)
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="partition-card-main">
        <nb-card-body>
          <!-- Header avec numéro de bon et date de création -->
          <div class="left-bloc">
            <div class="order-header">
              <div class="order-icon-container">
                <nb-icon icon="shopping-bag-outline" class="product-icon"></nb-icon>
              </div>
              <div class="order-info">
                <div class="order-title">
                  <h5>Partitionner</h5>
                </div>

              </div>
            </div>

            <div class="filter-product-main">
              <div class="filters">
                <nb-select status="success" class="nbSelect-width" size="small" placeholder="Selectionner un produit"
                  (selectedChange)="onGetAvaibleQdty($event)">
                  <nb-option [value]="label" *ngFor="let label of order?.cart?.items">
                    {{ label?.product?.label }} {{label?.packaging?.label}}
                  </nb-option>
                </nb-select>

                <input type="number" placeholder="Entrer la quantité" fieldSize="small" nbInput
                  [disabled]="currentItem?.quantityDelivery === currentItem?.quantity"
                  [(ngModel)]="currentItem.quantityShipped" class="empty-input height" />

              </div>

              <div class="available-qdty" *ngIf="avaibleQdty">
                <span class="qdty">
                  Quantité disponible :{{avaibleQdty}}
                </span>
              </div>
            </div>

            <div class="action-buttons">
              <button nbButton filled [disabled]="!currentItem?.quantityShipped"
                [ngClass]="{'disabled-btn': !currentItem?.quantityShipped}" status="primary" class="split-btn"
                (click)="addNewQdty()">
                Enregistrer la partition
              </button>

            </div>
          </div>

          <div class="rigth-bloc">

            <div class="order-header">
              <div class="order-icon-container">
                <nb-icon icon="shopping-bag-outline" class="product-icon"></nb-icon>
              </div>
              <div class="order-info">
                <div class="order-title">
                  <h5>Liste des partitions</h5>
                </div>

              </div>
            </div>

            <div class="ae-partition-list">
              <div class="aes-list" *ngFor="let elts of groupedOrders;">
                <div class="label">
                  <span class="elts">{{elts?.product?.label}} {{elts?.packaging?.label}}</span>

                </div>
                <div class="quantity">
                  <span class="elts">{{elts?.quantityShipped}}</span>
                  <span class="modify" (click)="modifyQdty(elts)">Modifier</span>
                </div>

              </div>
              <div class="empty-list" *ngIf="!groupedOrders.length">
                <nb-icon icon="info-outline"></nb-icon>
                <p>Les produits partionnés apparaissent ici</p>
                <p></p>
              </div>

            </div>


            <div class="action-buttons">
              <button nbButton [disabled]="!groupedOrders.length" [ngClass]="{'disabled-btn': !groupedOrders?.length}"
                filled status="primary" class="split-btn" (click)="save()">
                Enregistrer les partition dans l’AE
              </button>

            </div>
          </div>

        </nb-card-body>

      </div>

    </div>


  </div>
</ng-template>