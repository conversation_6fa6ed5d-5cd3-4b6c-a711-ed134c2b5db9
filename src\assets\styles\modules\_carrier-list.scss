.carriers-list-container {
    margin-left: 2%;
    margin-right: 6%;
    margin-top: 2%;

    .label-carriers-list,
    .right-area {
        display: flex;
        align-items: center;
        h6 {
            margin-left: 10px;
            font-size: 18px;
            font-family: $font-regular;
        }
    }
    .empty-input {
        padding: 4px;
    }
    .right-area {
        justify-content: space-between;
        .search-btn {
            margin-left: 10px;
        }
    }

    .block {
        background: none;
        border: none;
        min-height: 50px;
    }

    .menus-block {
        margin-top: 10px;
        width: 100%;
        display: grid;
        // flex-wrap: wrap;
        padding-top: 40px;
        padding-right: 40px;
        grid-template-columns: repeat(5, 1fr);
        // align-items: center;
        justify-items: center;
        .menu-elt {
            border-radius: 15px;
            height: 160px;
            width: 12%;
            display: flex;
            flex-direction: column;
            // margin-bottom: 22px;
            // margin-right: 24px;
            cursor: pointer;
            transition: all 0.2s ease-in-out;
            outline: none;
            &:hover {
                transform: scale(1.1);
            }
            align-items: flex-start;
            // padding-top: 20px;
            .carrier-image {
                height: 120px;
                width: 120px;
                background-color: #bbb;
                border-radius: 50%;
                display: inline-block;
                // height: 80%;
                position: relative;
                .small-img {
                    height: 125px;
                    width: 125px;
                }
                .title-block {
                    font-family: $font-regular;
                    font-weight: bold;
                    font-size: 18px;
                    position: absolute;
                    left: 54px;
                    top: -16%;
                    text-transform: uppercase;
                }
            }
            .text-block {
                height: 10%;
                text-align: justify;
                font-family: $font-regular;
                color: #ea7d1e;
                padding-top: 15px;
                font-size: 20px;
            }
        }
        .not-found-block {
            height: 90%;
            width: 90%;
            .image-block {
                @include illustration-mixin;
                img {
                    width: 140px;
                }
            }
        }
    }
}
