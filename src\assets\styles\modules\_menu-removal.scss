.menu-removal-container {
  .button-margin {
    margin-right: 10px;
  }

  .nbSelect-width,
  .empty-input {
    width: 180px !important;
  }

  nb-select {
    margin-right: .5rem;
    ;
  }

  .filter-label {
    margin-right: 5px;
  }

  .tabset .tab {
    width: 20% !important;
  }

  .tabset {
    width: 100% !important;
  }

  .removal-container {

    .list-elt-header {
      @include vh-between;
      height: 60px;
      padding-top: 7px;
      padding-bottom: 7px;
      font-size: 13px;

      .col {
        height: 100%;
        display: flex;
        align-items: center;
      }

      .col-num {
        min-width: 40px;
        max-width: 40px;
      }

      .col-ref {
        min-width: 100px;
        max-width: 100px;
      }

      .col-jde {
        min-width: 140px;
        max-width: 140px;
      }

      .col-hours {
        min-width: 100px;
        max-width: 100px;
      }

      .col-date {
        min-width: 120px;
        max-width: 120px;
      }

      .col-truck {
        min-width: 140px;
        max-width: 140px;
      }

      .col-hour {
        min-width: 100px;
        max-width: 100px;
      }

      .col-name {
        max-width: 150px;
        min-width: 150px;
      }

      .col-status {
        min-width: 100px;
        max-width: 100px;
        display: flex;
        align-items: center;
        justify-content: center;

        .badge {
          font-size: 12px;
          position: relative;
          width: 103px;
        }

        span {
          background: $bg-color-primary;
          border-radius: 4px;
          color: $color-secondary;
          padding: 3px 8px;
        }
      }

      .col-truck-status {
        min-width: 130px;
        max-width: 130px;
        display: flex;
        align-items: center;
        justify-content: center;

        .badge {
          font-size: 12px;
          position: relative;
          width: 103px;
        }

        span {
          background: $bg-color-primary;
          border-radius: 4px;
          color: $color-secondary;
          padding: 3px 8px;
        }
      }

      .col-soldto {
        min-width: calc(100% - 970px) !important;
      }

      .col-details {
        min-width: 100px;
        max-width: 100px;
        overflow: hidden;
        justify-content: center;

        .product-choice {
          background-image: url("./../../images/icons/ciment.png");
          background: no-repeat center contain;
          width: 19px;
          height: 31px;
        }
      }

      .col-action {
        max-width: 90px;
        min-width: 90px;

        .action-icons {

          display: flex;
          align-items: center;
          justify-content: flex-end;
          width: 70%;
        }
      }

      button {
        text-transform: none !important;
        background-color: #edf1f7;
        padding: 5px;
        border-radius: 5px;
      }


      .col-num-rendu {
        min-width: 30px;
        max-width: 30px;
      }

      .col-ref-rendu {
        min-width: 90px;
        max-width: 90px;
      }

      .col-hours-rendu {
        min-width: 90px;
        max-width: 90px;
      }

      .col-date-rendu {
        min-width: 110px;
        max-width: 110px;
      }

      .date-size {
        max-width: 120px;
      }

      .col-truck-rendu {
        min-width: 130px;
        max-width: 130px;
      }

      .col-hour-rendu {
        min-width: 90px;
        max-width: 90px;
      }

      .col-item-product {
        min-width: 110px;
        max-width: 110px;

        span {
          align-items: center;
          gap: 5px;

          img {
            width: 12px;
            height: 12px;
          }
        }
      }

      .col-status-rendu {
        min-width: 90px;
        max-width: 90px;
        display: flex;
        align-items: center;
        justify-content: center;

        .badge {
          font-size: 12px;
          position: relative;
          width: 115px;
        }

        span {
          background: $bg-color-primary;
          border-radius: 4px;
          color: $color-secondary;
          padding: 3px 8px;
        }
      }

      .col-soldto {
        min-width: calc(100% - 970px) !important;
      }

      .col-details {
        min-width: 100px;
        max-width: 100px;
        overflow: hidden;
        justify-content: center;

        .product-choice {
          background-image: url("./../../images/icons/ciment.png");
          background: no-repeat center contain;
          width: 19px;
          height: 31px;
        }
      }

      .qty-size {
        min-width: 80px;
      }

    }

    .filter-area {

      .right-area {
        display: flex;
        align-items: unset;
        flex-direction: column;

        .reset-btn {
          background-color: #ffff;
          color: $color-primary;
        }
      }
    }

    width: 100%;

    .height {
      padding: 3px 15px !important;
    }

    .scrool-style {
      overflow-y: auto !important;
      height: 57vh;
    }

    .col-shipto {
      width: 120px;
      justify-content: flex-start !important;
    }

    .truck-quanity {
      width: 70px;
      justify-content: flex-start !important;
    }

    .badge {
      min-width: 115px !important;
    }

    .col-action {
      justify-content: flex-end !important;
    }
  }

  .nb-tab-padding {
    padding-left: 0 !important;
    padding-right: 0 !important;
  }

  .green-color {
    color: #0B305C !important;
  }

}

.her-width {
  width: 32vw;
}