import { Pipe, PipeTransform } from '@angular/core';
import * as moment from 'moment';

@Pipe({
  name: 'colorStyleWaiting'
})
export class ColorStyleWaitingPipe implements PipeTransform {

  transform(value: unknown, ...args: unknown[]): unknown {
    // if (!value) return;

    const hours = 5400001; // 5400001 equal 1h30 min
    const minutes = 1800001; // 5400001 equal 30 min
    const waitingTime = moment(moment().valueOf()).diff(moment(value));

    if (waitingTime >= hours) {
      return {
        color: '#FF4F4F'
      };
    }
    if (waitingTime >= minutes && waitingTime < hours) {
      return {
        color: '#F4D954'
      }
    }
    return {
      color: '#4DE352'
    }
  }

}
