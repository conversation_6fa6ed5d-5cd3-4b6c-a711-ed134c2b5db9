<div class="carriers-list-container" *ngIf="isdisplay">
  <div class="label-carriers-list">
    <h6>Liste des Transporteurs</h6>
  </div>
  <div class="add-carrier">
    <!-- Composant de pagination -->
    <div class="pagination-container">
      <clw-pagination [config]="paginationConfig" (pageChange)="onPageChange($event)"
        (itemsPerPageChange)="onItemsPerPageChange($event)">
      </clw-pagination>
    </div>

    <button nbButton status="success" class="search-btn" (click)="openInsertCarrier()">
      <nb-icon icon="plus-square-outline"></nb-icon>
      Ajouter
    </button>
  </div>

  <div class="menus-block">
    <div class="menu-elt" *ngFor="let carrier of carriers">
      <div class="carrier-image" [ngStyle]="carrier?.logo | getImgStyle:'assets/images/Transport_Types.png' " (click)="showdetail(carrier)">
        <div class="title-block">
          <p *ngFor="let item of carrier?.label | curveText:180 " [style]='item.style'>{{item?.letter}}</p>
        </div>
      </div>

    </div>
  </div>

  <div class="menus-block" *ngIf='carriers?.length===0 || !carriers'>
    <div class="not-found-block">
      <div class="image-block">
        <img src="../../../assets/images/account.png" alt="no search result image">
        <p>Aucun Transporteur trouvé</p>
      </div>
    </div>
  </div>
</div>

