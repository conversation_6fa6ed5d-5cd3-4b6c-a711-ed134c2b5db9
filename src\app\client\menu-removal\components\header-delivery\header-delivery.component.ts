import { Component, EventEmitter, OnInit, Output } from '@angular/core';
import { RemovalService } from '../../removal-management/removal.service';
import { OrderService } from 'src/app/client/menu-order/order.service';

@Component({
  selector: 'clw-header-delivery',
  templateUrl: './header-delivery.component.html',
  styles: [
  ]
})
export class HeaderDeliveryComponent implements OnInit {
  @Output() export = new EventEmitter<void>();
  @Output() refresh = new EventEmitter<void>();
  @Output() filter = new EventEmitter<any>();

  activeFilter: string | null = null;
  
  // Données pour les autocompletes
  orderNumbers: string[] = [];
  clients: string[] = [];
  products: any[] = [];
  deliveryPoints: string[] = [];
  
  // Stockage des données filtrées pour chaque autocomplete
  filteredOrderNumbers: string[] = [];
  filteredClients: string[] = [];
  filteredProducts: string[] = [];
  filteredDeliveryPoints: string[] = [];

  // Valeurs sélectionnées
  selectedOrderNumber: string = '';
  selectedClient: string = '';
  selectedProduct: string = '';
  selectedDeliveryPoint: string = '';

  filterForm = {
    status: '',
    LoadNumber: '',
    client: '',
    product: '',
    deliveryPoint: '',
    LoadStatus: '',
    ItemNumber: '',
    company: '',
    shipping: '',
    carrierLabel: '',
    startDate: new Date(new Date().getFullYear(), 0, 1),
    endDate: new Date(new Date().getFullYear(), 11, 31),
    FreightHandlingCode: 'A0',
    OrderType: ''
  };

  constructor(private orderSrv: OrderService) {}

  async ngOnInit(): Promise<void> {
    try {
      const data = await this.orderSrv.getFilterData({
        keyForFilters: 'erpReference,company.name,cart.items.product.label,cart.shipping.label',
      });
      
      // Initialiser les données pour les autocompletes
      this.orderNumbers = data.dataerpReference || [];
      this.clients = data['datacompany.name'] || [];
      
      // Pour les produits, il faut aplatir le tableau de tableaux
      const productsArrays = data['datacart.items.product.label'] || [];
      this.products = [...new Set(productsArrays.flat())]; // Supprime les doublons
      
      this.deliveryPoints = data['datacart.shipping.label'] || [];
      
      // Initialiser également les tableaux filtrés
      this.filteredOrderNumbers = [...this.orderNumbers];
      this.filteredClients = [...this.clients];
      this.filteredProducts = [...this.products];
      this.filteredDeliveryPoints = [...this.deliveryPoints];
    } catch (error) {
      console.error('Erreur lors du chargement des données:', error);
    }
  }

  onExport(): void {
    this.export.emit();
  }

  onRefresh(): void {
    this.refresh.emit();
  }

  // Méthodes de filtrage pour chaque autocomplete
  onOrderNumberFilterChange(value: string): void {
    if (!value) {
      this.filteredOrderNumbers = [...this.orderNumbers];
      return;
    }
    
    const lowerValue = value.toLowerCase();
    this.filteredOrderNumbers = this.orderNumbers.filter(item => 
      item.toLowerCase().includes(lowerValue)
    );
  }

  onClientFilterChange(value: string): void {
    if (!value) {
      this.filteredClients = [...this.clients];
      return;
    }
    
    const lowerValue = value.toLowerCase();
    this.filteredClients = this.clients.filter(item => 
      item.toLowerCase().includes(lowerValue)
    );
  }

  onProductFilterChange(value: string): void {
    if (!value) {
      this.filteredProducts = [...this.products];
      return;
    }
    
    const lowerValue = value.toLowerCase();
    this.filteredProducts = this.products.filter(item => 
      item.toLowerCase().includes(lowerValue)
    );
  }

  onDeliveryPointFilterChange(value: string): void {
    if (!value) {
      this.filteredDeliveryPoints = [...this.deliveryPoints];
      return;
    }
    
    const lowerValue = value.toLowerCase();
    this.filteredDeliveryPoints = this.deliveryPoints.filter(item => 
      item.toLowerCase().includes(lowerValue)
    );
  }

  // Méthodes pour gérer les sélections
  onOrderNumberSelect(value: string): void {
    if (value === 'Tous les Bons') {
      this.selectedOrderNumber = '';
      this.filterForm.LoadNumber = '';
    } else {
      this.selectedOrderNumber = value;
      this.filterForm.LoadNumber = value;
    }
    this.applyFilter('orderNumber', this.selectedOrderNumber);
  }

  onClientSelect(value: string): void {
    if (value === 'Tous les clients') {
      this.selectedClient = '';
      this.filterForm.client = '';
    } else {
      this.selectedClient = value;
      this.filterForm.client = value;
    }
    this.applyFilter('client', this.selectedClient);
  }

  onProductSelect(value: string): void {
    if (value === 'Tous les Produits') {
      this.selectedProduct = '';
      this.filterForm.product = '';
    } else {
      this.selectedProduct = value;
      this.filterForm.product = value;
    }
    this.applyFilter('product', this.selectedProduct);
  }

  onDeliveryPointSelect(value: string): void {
    if (value === 'Tous les Points de livraison') {
      this.selectedDeliveryPoint = '';
      this.filterForm.deliveryPoint = '';
    } else {
      this.selectedDeliveryPoint = value;
      this.filterForm.deliveryPoint = value;
    }
    this.applyFilter('deliveryPoint', this.selectedDeliveryPoint);
  }

  toggleFilter(filterType: string): void {
    if (this.activeFilter === filterType) {
      this.activeFilter = null;
      this.applyFilter(null, '');
    } else {
      this.activeFilter = filterType;
    }
  }

  applyFilter(type: string | null, value: string): void {
    this.filter.emit({ type, value });
  }

  clearFilter(): void {
    this.selectedOrderNumber = '';
    this.selectedClient = '';
    this.selectedProduct = '';
    this.selectedDeliveryPoint = '';
    this.filterForm.LoadNumber = '';
    this.filterForm.client = '';
    this.filterForm.product = '';
    this.filterForm.deliveryPoint = '';
    this.activeFilter = null;
    this.applyFilter(null, '');
  }
}