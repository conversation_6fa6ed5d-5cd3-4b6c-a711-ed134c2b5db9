import { SharedModule } from 'src/app/shared/shared.module';
import { NbIconModule, NbSelectModule, NbInputModule, NbButtonModule, NbListModule, NbCardModule, NbTooltipModule, NbToast, NbToastrModule, NbSpinnerModule } from '@nebular/theme';
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { PricesRoutingModule } from './prices-routing.module';
import { PricesComponent } from './prices.component';
import { FormsModule } from '@angular/forms';


@NgModule({
  declarations: [
    PricesComponent
  ],
  imports: [
    CommonModule,
    PricesRoutingModule,
    NbIconModule,
    NbSelectModule,
    NbInputModule,
    NbButtonModule,
    NbListModule,
    NbCardModule,
    NbTooltipModule,
    NbToastrModule,
    NbSpinnerModule,
    FormsModule,
    SharedModule
  ]
})
export class PricesModule { }
