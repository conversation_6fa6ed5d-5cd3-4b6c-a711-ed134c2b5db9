<div class="filter-bar">
    <div class="first-bloc">
        <label>Filtre par</label>
        <button nbButton (click)="onExport()" style="width: 90px;" status="success" fieldSize="small"
            class="export-btn">Exporter</button>
    </div>
    <div class="second-bloc">
        <div class="btn-filter">
            <!-- N° de BON -->
            <input type="text" placeholder="N° de BON" fieldSize="small" nbInput [nbAutocomplete]="bonAutoComplete"
                [(ngModel)]="filterForm.LoadNumber" (ngModelChange)="onOrderNumberFilterChange($event)" />
            <nb-autocomplete #bonAutoComplete (selectedChange)="onOrderNumberSelect($event)">
                <nb-option *ngIf="filteredOrderNumbers.length === 0"> Chargement... </nb-option>
                <nb-option value="Tous les Bons"> Tous les Bons </nb-option>
                <nb-option *ngFor="let option of filteredOrderNumbers" [value]="option">{{option}}
                </nb-option>
            </nb-autocomplete>

            <!-- Client -->
            <input type="text" placeholder="Client" fieldSize="small" nbInput [nbAutocomplete]="clientAutoComplete"
                [(ngModel)]="filterForm.client" (ngModelChange)="onClientFilterChange($event)" />
            <nb-autocomplete #clientAutoComplete (selectedChange)="onClientSelect($event)">
                <nb-option *ngIf="filteredClients.length === 0"> Chargement... </nb-option>
                <nb-option value="Tous les clients"> Tous les clients </nb-option>
                <nb-option *ngFor="let option of filteredClients" [value]="option">{{option}}
                </nb-option>
            </nb-autocomplete>

            <!-- Produit -->
            <input type="text" placeholder="Produit" fieldSize="small" nbInput [nbAutocomplete]="productAutoComplete"
                [(ngModel)]="filterForm.product" (ngModelChange)="onProductFilterChange($event)" />
            <nb-autocomplete #productAutoComplete (selectedChange)="onProductSelect($event)">
                <nb-option *ngIf="filteredProducts.length === 0"> Chargement... </nb-option>
                <nb-option value="Tous les Produits"> Tous les Produits </nb-option>
                <nb-option *ngFor="let option of filteredProducts" [value]="option">{{option}}
                </nb-option>
            </nb-autocomplete>

            <!-- Point de livraison -->
            <input type="text" placeholder="Point de livraison" fieldSize="small" nbInput
                [nbAutocomplete]="deliveryAutoComplete" [(ngModel)]="filterForm.deliveryPoint"
                (ngModelChange)="onDeliveryPointFilterChange($event)" />
            <nb-autocomplete #deliveryAutoComplete (selectedChange)="onDeliveryPointSelect($event)">
                <nb-option *ngIf="filteredDeliveryPoints.length === 0"> Chargement... </nb-option>
                <nb-option value="Tous les Points de livraison"> Tous les Points de livraison </nb-option>
                <nb-option *ngFor="let option of filteredDeliveryPoints" [value]="option">{{option}}
                </nb-option>
            </nb-autocomplete>
        </div>

        <!-- Espace à droite pour les boutons -->
        <div class="actions">

            <button nbButton status="info" fieldSize="small" class="act-btn"
                (click)="onRefresh()">Actualiser</button>
        </div>
    </div>
</div>