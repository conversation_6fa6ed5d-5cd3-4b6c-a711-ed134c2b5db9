.tile-sales-container {
  nb-card {
    height: 390px;
    width: 100%;
    max-height: 428px;
    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.09);
  }

  nb-card-header {
    padding: 8px 1rem;
  }

  nb-card-body {
    height: 280px;
    padding: 0;
  }

  .body {
    padding: 1rem;
    display: flex;
    flex-direction: row;
    gap: 1rem;
    justify-content: space-between;

    .block1 {
      display: flex;
      justify-content: space-between;
      height: 50%;

      .ae-data {
        width: 100%;
        height: 25vh;
        border-radius: 1rem;
        display: flex;
        flex-direction: column;
        justify-content: center;
        padding-inline: 1.5rem;
        box-shadow: 0px 4.6086649894714355px 13.825996398925781px 0px #0000001A;

        .ae-title {
          display: flex;
          column-gap: 0.5rem;
          font-size: 0.75rem;
          font-weight: bold;
          align-items: center;
          // justify-content: center;
          padding-block: 1rem;

          .icon {
            border-radius: 10rem;
            height: 2rem;
            width: 2rem;

            nb-icon {
              font-size: 1rem;
              margin: auto;
              // color: aquamarine;
            }
          }

          .ae-label {
            font-size: large;
          }

          .ae-value {
            font-size: 2em;
            font-weight: bold;
          }

          .info {
            background-color: #BEE0FF;
          }

          .success {
            background-color: #E8F7F0;
          }

          .danger {
            background-color: #FCDDDE;
          }

          .warning {
            background-color: #FCDDDE;
          }

          .percent {
            background-color: #E8F7F0;
            border-radius: 1rem;
            color: $color-primary;
            column-gap: 1rem;
            padding: 0.5rem;

            nb-icon {
              font-size: 2rem;
              margin: auto;
            }
          }

          .ae {
            display: flex;
            align-items: center;
          }
        }

        .ae-center {
          justify-content: center;
        }
      }

    }
    .block2 {
      display: flex;
      flex-direction: column;
      gap: 15px;
 
      .ae-data {
        width: 100%;
        border-radius: 1rem;
        display: flex;
        flex-direction: column;
        justify-content: center;
        padding-inline: 1.5rem;
        box-shadow: 0px 4.6086649894714355px 13.825996398925781px 0px #0000001A;

        .ae-title {
          display: flex;
          column-gap: 0.5rem;
          font-size: 0.75rem;
          font-weight: bold;
          align-items: center;
          // justify-content: center;
          padding-block: 1rem;

          .icon {
            border-radius: 10rem;
            height: 2rem;
            width: 2rem;

            nb-icon {
              font-size: 1rem;
              margin: auto;
              // color: aquamarine;
            }
          }

          .ae-label {
            font-size: large;
          }

          .ae-value {
            font-size: 2em;
            font-weight: bold;
          }

          .info {
            background-color: #BEE0FF;
          }

          .success {
            background-color: #E8F7F0;
          }

          .danger {
            background-color: #FCDDDE;
          }

          .warning {
            background-color: #FCDDDE;
          }

          .percent {
            background-color: #E8F7F0;
            border-radius: 1rem;
            color: $color-primary;
            column-gap: 1rem;
            padding: 0.5rem;

            nb-icon {
              font-size: 2rem;
              margin: auto;
            }
          }

          .ae {
            display: flex;
            align-items: center;
          }
        }

        .ae-center {
          justify-content: center;
        }
      }
    }

     

    
  }

  .body-inspection {
    padding: 1rem;
    display: flex;

    .inspection-nber-block {
      display: flex;
      flex-direction: column;
      width: 25%;
      gap: 1rem;

      .ae-data {
        width: 90%;
        border-radius: 1rem;
        display: flex;
        flex-direction: column;
        justify-content: center;
        padding-inline: 1.5rem;

        box-shadow: 0px 4.6086649894714355px 13.825996398925781px 0px #0000001A;

        .ae-title {
          display: flex;
          column-gap: 0.5rem;
          font-size: 0.75rem;
          font-weight: bold;
          align-items: center;
          padding-block: 1rem;

          .icon {
            border-radius: 10rem;
            height: 2rem;
            width: 2rem;

            nb-icon {
              font-size: 1rem;
              margin: auto;
            }
          }

          .ae-label {
            font-size: 13.5px;
            font-weight: 600;
          }

          .ae-value {
            font-size: 1.5em;
            font-weight: bold;
          }

          .info {
            background-color: #BEE0FF;
          }

          .success {
            background-color: #E8F7F0;
          }

          .danger {
            background-color: #FCDDDE;
          }

          .warning {
            background-color: #FCDDDE;
          }

          .percent {
            background-color: #E8F7F0;
            border-radius: 1rem;
            color: $color-primary;
            column-gap: 1rem;
            padding: 0.5rem;

            nb-icon {
              font-size: 2rem;
              margin: auto;
            }
          }

          .ae {
            display: flex;
            align-items: center;
          }
        }

        .ae-center {
          justify-content: center;
        }
      }
    }

    .medium-time-block {
      width: 50%;
      gap: 1rem;
      display: flex;
      flex-direction: column;
      padding: 0rem 0rem 0rem 1rem;
      border-left: 2px solid #D9E7F9;

      .inspection-blocks {
        display: flex;
        height: 89%;

        .inspection-nber-block {
          display: flex;
          flex-direction: column;
          width: 50%;
          gap: 1rem;

          .ae-data {
            width: 90%;
            border-radius: 1rem;
            // height: 16vh;
            display: flex;
            flex-direction: column;
            // align-items: center;
            justify-content: center;
            padding-inline: 1.5rem;
    
            box-shadow: 0px 4.6086649894714355px 13.825996398925781px 0px #0000001A;
    
            .ae-title {
              display: flex;
              column-gap: 0.5rem;
              font-size: 0.75rem;
              font-weight: bold;
              align-items: center;
              padding-block: 1rem;
    
              .icon {
                border-radius: 10rem;
                height: 2rem;
                width: 2rem;
    
                nb-icon {
                  font-size: 1rem;
                  margin: auto;
                  // color: aquamarine;
                }
              }
    
              .ae-label {
                font-size: 13.5px;
                font-weight: 600;
              }
    
              .ae-value {
                font-size: 1.5em;
                font-weight: bold;
              }
    
              .info {
                background-color: #BEE0FF;
              }
    
              .success {
                background-color: #E8F7F0;
              }
    
              .danger {
                background-color: #FCDDDE;
              }
    
              .warning {
                background-color: #FCDDDE;
              }
    
              .percent {
                background-color: #E8F7F0;
                border-radius: 1rem;
                color: $color-primary;
                column-gap: 1rem;
                padding: 0.5rem;
    
                nb-icon {
                  font-size: 2rem;
                  margin: auto;
                }
              }
    
              .ae {
                display: flex;
                align-items: center;
              }
            }
    
            .ae-center {
              justify-content: center;
            }
          }
        }
      }
    }

    .response-time-block {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      width: 25%;
      gap: 1rem;
      border-left: 2px solid #D9E7F9;


      .ae-data {
        width: 90%;
        border-radius: 1rem;
        // height: 16vh;
        display: flex;
        flex-direction: column;
        // align-items: center;
        justify-content: center;
        padding-inline: 1.5rem;

        box-shadow: 0px 4.6086649894714355px 13.825996398925781px 0px #0000001A;

        .ae-title {
          display: flex;
          column-gap: 0.5rem;
          font-size: 0.75rem;
          font-weight: bold;
          align-items: center;
          padding-block: 1rem;

          .icon {
            border-radius: 10rem;
            height: 2rem;
            width: 2rem;

            nb-icon {
              font-size: 1rem;
              margin: auto;
              // color: aquamarine;
            }
          }

          .ae-label {
            font-size: 13.5px;
            font-weight: 600;
          }

          .ae-value {
            font-size: 1.5em;
            font-weight: bold;
          }

          .info {
            background-color: #BEE0FF;
          }

          .success {
            background-color: #E8F7F0;
          }

          .danger {
            background-color: #FCDDDE;
          }

          .warning {
            background-color: #FCDDDE;
          }

          .percent {
            background-color: #E8F7F0;
            border-radius: 1rem;
            color: $color-primary;
            column-gap: 1rem;
            padding: 0.5rem;

            nb-icon {
              font-size: 2rem;
              margin: auto;
            }
          }

          .ae {
            display: flex;
            align-items: center;
          }
        }

        .ae-center {
          justify-content: center;
        }
      }
    }
  }

  .progress-info {
    padding: 0 1rem;

    .header-wrapper {
      font-weight: bolder;
      font-size: 16px;
    }

    margin-top: 20px;

    &:first-child {
      margin-top: 0px;
    }

    .subtitle {
      font-family: $font-regular;
      font-weight: 500;
      font-size: 13px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 5px;
    }

    .h4 {
      font-family: $font-regular;
      font-size: 13px;
      font-weight: 700;
    }

    nb-progress-bar {
      margin-bottom: 5px;
    }

    .caption.description {
      font-family: $font-regular;
    }
  }

  .progress {
    padding: 0 1.7rem;

  }

  .inspection-subtitle {
    height: 20px;
  }
}