import { Pipe, PipeTransform } from '@angular/core';
import { TruckStatus } from '../models/status-removal.enum';

@Pipe({
  name: 'getStatusLabelRemovals'
})
export class GetStatusLabelRemovalsPipe implements PipeTransform {

  transform(status: unknown, ...args: unknown[]): string {
    if (status === "21") { return 'Créée' }
    if (status === "29") { return 'Créée et approuvée' }
    if (status === "35") { return 'En usine' }
    if (status === "50") { return 'Transit' }
    if (status === "60") { return 'Status "60"' }
    if (status === "80") { return 'Chargée' }
    if (status === "99") { return 'Annulée' }
    return 'Non disponible';
  }

}
@Pipe({
  name: 'getStatusTrucksLabel'
})
export class GetStatusTruckInspectionsPipe implements PipeTransform {

  private statusLabels: { [key: string]: string } = {
    [TruckStatus.LOADED]: "Réceptionné",
    [TruckStatus.FACTORY]: "En chargement",
    [TruckStatus.BROKEN_DOWN]: "En panne",
  };

  transform(status: unknown, ...args: unknown[]): string {
    return this.statusLabels[status as string] || 'Non disponible';
  }
}


@Pipe({
  name: 'getNbMinRemovals'
})
export class GetNbMinRemovalsPipe implements PipeTransform {

  transform(num: unknown, ...args: unknown[]): string {
    if (num) {
      num = Math.floor(+num / 1000);
      return `${Math.floor(+num / 60)} min ${+num % 60} sec`;
    }
    return 'Non disponible';
  }

}


