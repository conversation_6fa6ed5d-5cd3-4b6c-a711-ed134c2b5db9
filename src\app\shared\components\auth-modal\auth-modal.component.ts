import { Component, Input, OnInit } from '@angular/core';
import { ViewEncapsulation } from '@angular/core';
import { Credentials } from '../../models/credentials';
import { Router } from '@angular/router';
import { CommonService } from '../../services/common.service';
import { AuthService } from '../../services/auth.service';
import { NbToastrService } from '@nebular/theme';
import { StorageService } from '../../services/storage.service';
import get from 'lodash-es/get';

@Component({
  selector: 'clw-auth-modal',
  templateUrl: './auth-modal.component.html',
  styles: [
  ],
  encapsulation: ViewEncapsulation.None
})
export class AuthModalComponent implements OnInit {

  credentials: Credentials = { login: '', password: '' };
  showPassword: boolean = false;
  formResetPassword: boolean = false;
  isLoading: boolean;
  user: any;

  constructor(
    private router: Router,
    public commonSrv: CommonService,
    private authService: AuthService,
    private toastSrv: NbToastrService,
    private storageSrv: StorageService,
  ) { }

  ngOnInit(): void { }

  async login(): Promise<any> {
    try {

      if (!this.credentials.login || !this.credentials.password) {
        return this.toastSrv.danger('Veuillez entrez des identifiants valides', 'Echec operation');
      }

      this.isLoading = true;
      this.credentials.login = this.credentials.login.toLowerCase();


      await this.authService.signin(this.credentials);
      // console.log(res)
      // if (!res || res instanceof Error) {
      //   this.isLoading = false;
      //   return this.toastSrv.danger(`Vous n'avez pas d'acces a l'application.`, 'Echec operation');
      // }

      this.user = this.storageSrv.getObject('user');
      if (this.user.enable === false) {
        this.isLoading = false
        return this.toastSrv.danger(`Vous n'avez pas d'acces a l'application.`, 'Echec operation');
      }
      console.log(this.user)
      const category = get(this.user, 'category');
      this.router.navigate(['/client/removal/historic']);

      this.toastSrv.success(`Bienvenue ${this.user.fullname}`, 'Connexion réussie');

    } catch (error) {
      this.toastSrv.danger('Une erreur est survenue lors de la connexion. Veuillez réessayer.', 'Erreur de connexion');
      console.log(error);
      this.isLoading = false;
    }

  }

  // logout user funtion
  logout(): void {
    this.authService.signout();
  }

}
