import { Component, OnInit, Pipe, PipeTransform, TemplateRef } from '@angular/core';
import { NbDialogService, NbToastrService, NbToastRef } from '@nebular/theme';
import * as moment from 'moment';
import { AuthorizationRemoval, GroupesOrders } from 'src/app/shared/models/authorization-removal';
import { RemovalService } from '../removal-management/removal.service';
import { DatePipe } from '@angular/common';
import { StatusAES, StatusAESLibrary } from 'src/app/shared/models/render-type.enum';
import { StorageService } from 'src/app/shared/services/storage.service';
import { GetStatusRemovalsPipe } from 'src/app/shared/pipes/get-status-removals.pipe';
import { StatusRemovals } from 'src/app/shared/models/status-removal.enum';
import { Order } from 'src/app/shared/models/order';
import { PaginationConfig } from 'src/app/shared/services/pagination.service';

@Component({
  selector: 'clw-historic-removal',
  templateUrl: './historic-removal.component.html',
  styles: [
  ]
})
export class HistoricRemovalComponent implements OnInit {
  connectedUser: any;
  dialogExport: any;

  removals: AuthorizationRemoval[];
  truckJourneys: any[];
  getStatusRemovalsPipe = new GetStatusRemovalsPipe();

  isLoading: boolean;
  loading = false;
  totalHoursJourney: any;

  statusFilters: any[] = [
    { code: StatusRemovals.NOT_ASSIGNED, label: 'Non assignée' },
    { code: StatusRemovals.ASSIGNED, label: 'Validée' },
    { code: StatusRemovals.WAITING_VALIDATION, label: 'Attente Validation' },
    { code: StatusRemovals.QUEUED, label: 'En file d\'attente' },
    { code: StatusRemovals.LOADED, label: 'Chargée' },
    { code: StatusRemovals.BROKEN_DOWN, label: 'En panne' },
    { code: StatusRemovals.NEW_PARKING, label: 'Parking' },
    { code: StatusRemovals.REJECTED, label: 'Rejetée' },
  ];

  statusFiltersJde: any[] = [
    { code: StatusAES.CREATED, label: 'Créer' },
    { code: StatusAES.CREATED_APPROVED, label: 'Créer et approuvée' },
    { code: StatusAES.CHECK_IN, label: 'En usine' },
    { code: StatusAES.TRANSIT, label: 'Transit' },
    { code: StatusAES.PARTIAL_LOADING, label: 'Chargement partiel' },
    { code: StatusAES.COMPLETE, label: 'Chargée' },
    { code: StatusAES.CANCEL, label: 'Annulée' },
  ];

  removal: AuthorizationRemoval & { [key: string]: any };
  dataForFilter: any;
  dataAELabels: any = [];
  dataProducts: any = [];
  dataSoldToDescLabels: any = [];
  dataShipToDescLabels: any = [];
  dataOrderTypeLabels: any[] = [];

  dataTransporters: any = [];

  filterForm = {
    status: '',
    LoadNumber: '',
    LoadStatus: '',
    ItemNumber: '',
    company: '',
    shipping: '',
    carrierLabel: '',
    startDate: new Date(new Date().getFullYear(), 0, 1),
    endDate: new Date(new Date().getFullYear(), 11, 31),
    FreightHandlingCode: 'A0',
    OrderType: ''
  };
  offset;
  limit = 20;
  endIndex = 20;
  startIndex = 1;
  total: number;
  startDate: Date;
  endDate: Date;
  min: Date

  // Configuration de la pagination
  paginationConfig: PaginationConfig = {
    currentPage: 1,
    itemsPerPage: 10,
    totalItems: 0,
    maxSize: 10
  };

  constructor(
    private datePipe: DatePipe,
    private storageSvr: StorageService,
    public dialogSvr: NbDialogService,
    private removalSvr: RemovalService,
    private toastrSvr: NbToastrService,
  ) { }

  async ngOnInit(): Promise<void> {
    this.connectedUser = this.storageSvr.getObject('user');

    await this.getAuthorization();
    await this.getDataForFilter();
  }

  async getDataForFilter() {
    try {
      this.loading = true;
      this.dataForFilter = await this.removalSvr.getFilterData({
        ...this.filterForm,
        startDate: moment(this.filterForm.startDate).format('MM-DD-YYYY'),
        endDate: moment(this.filterForm.endDate).format('MM-DD-YYYY'),
        keyForFilters: ['LoadNumber', 'ItemNumber', 'Alpha', 'ShipToDescription', 'SoldToDescription', 'OrderType']
      },);

      this.dataAELabels = this.dataForFilter?.dataLoadNumber ?? [];
      this.dataTransporters = this.dataForFilter?.dataAlpha ?? [];
      this.dataShipToDescLabels = this.dataForFilter?.dataShipToDescription ?? [];
      this.dataProducts = this.dataForFilter?.dataItemNumber ?? [];
      this.dataSoldToDescLabels = this.dataForFilter?.dataSoldToDescription ?? [];
    } catch (error) {
      return error;
    } finally { this.loading = false }
  }


  getConcatenatedLabels(groupedOrders: GroupesOrders): string {
    return groupedOrders?.map((order: Order) =>
      order?.cart?.items
        .filter(item => (item?.quantityShipped || 0) > 0)
        .map(item => `${item?.product?.label} (${item?.packaging?.label})`)
        .join(', ')
    ).join(', ') || 'N/A';
  }
  getQuantity(groupOders: GroupesOrders): number {
    const result = groupOders
      .map((order: Order) =>
        order?.cart?.items.reduce((total, cartElement) =>
          total + ((cartElement?.quantity * cartElement?.packaging.unit?.value) / 1000), 0)
      )
      .reduce((sum, quantity) => sum + quantity, 0);

    return parseFloat(result.toFixed(2));
  }

  getQuantityShipped(groupOders: GroupesOrders): number {
    const result = groupOders
      .map((order: Order) =>
        order?.cart?.items
          ?.filter(cartElement => (cartElement?.quantityShipped || 0) > 0)
          ?.reduce((total, cartElement) =>
            total + (((cartElement?.quantityShipped || 0) * (cartElement?.packaging?.unit?.value || 1)) / 1000), 0) || 0
      )
      .reduce((sum, quantity) => sum + quantity, 0);

    return parseFloat(result.toFixed(2));
  }
  async getAuthorization(): Promise<void> {
    const options = {
      limit: this.paginationConfig.itemsPerPage,
      offset: (this.paginationConfig.currentPage - 1) * this.paginationConfig.itemsPerPage,
      ...this.filterForm,
      startDate: moment(this.filterForm.startDate).format('MM-DD-YYYY'),
      endDate: moment(this.filterForm.endDate).format('MM-DD-YYYY'),
      FreightHandlingCode: 'A0',
      way: -1,
      sort: '_id'
    }
    try {
      this.isLoading = true;
      const removalsData = await this.removalSvr.getAuthorizationRemoval(options);

      this.removals = removalsData?.data || [];
      this.paginationConfig = {
        ...this.paginationConfig,
        totalItems: removalsData?.count || 0
      };

      const totalPages = Math.ceil(this.paginationConfig.totalItems / this.paginationConfig.itemsPerPage);
      if (this.paginationConfig.currentPage > totalPages && totalPages > 0) {
        this.paginationConfig.currentPage = totalPages;
        await this.getAuthorization();
      }
    }
    catch (error) {
      console.log(error);
      this.toastrSvr.danger('Impossible de récupérer la liste des AE', 'Erreur connexion !');
    } finally {
      this.isLoading = false;
    }
  }

  search(): any {
    if (this.filterForm.startDate > this.filterForm.endDate)
      return this.toastrSvr.danger('la date de fin est inférieure à la date de début', 'Erreurs Données!');
    this.getAuthorization();
    this.getDataForFilter()
  }

  onPageChange(page: number): void {
    if (this.isLoading) return;
    this.paginationConfig.currentPage = page;
    this.getAuthorization();
  }

  onItemsPerPageChange(itemsPerPage: number): void {
    this.paginationConfig.itemsPerPage = itemsPerPage;
    this.paginationConfig.currentPage = 1;
    this.getAuthorization();
  }

  async reset() {
    this.filterForm = {
      status: '',
      LoadNumber: '',
      LoadStatus: '',
      ItemNumber: '',
      company: '',
      shipping: '',
      carrierLabel: '',
      OrderType: '',
      startDate: new Date(new Date().getFullYear(), 0, 1),
      endDate: new Date(new Date().getFullYear(), 11, 31),
      FreightHandlingCode: 'A0'
    }
    await this.getAuthorization();
    await this.getDataForFilter();
  }

  async openDetailDialog(dialog: TemplateRef<any>, ae: any) {
    this.removal = ae;
    try {
      this.isLoading = true;
      this.removal = await this.removalSvr.getAuthorizationRemovalById(this.removal._id);
      const parkTimes = await this.removalSvr.getParkTimes(+this.removal.LoadNumber);
      this.removal = { ...this.removal, ...parkTimes[0] };
    } catch (error) {
      console.error(error);
      this.toastrSvr.danger('Erreur lors de la récupérations des temps d\'attentes', 'Erreur!');
    } finally {
      this.isLoading = false;
    }
    this.dialogSvr.open(dialog, {});
  }

  async openTruckJourney(dialog: TemplateRef<any>, ae: any) {
    this.removal = ae;
    try {
      this.isLoading = true;
      this.truckJourneys = await this.removalSvr.getTruckJourney(this.removal.LoadNumber);
      this.totalHoursJourney = this.truckJourneys.slice(0, -1).reduce((accumulator, currentJourney, currentIndex) => accumulator + (this.truckJourneys[++currentIndex].entry - currentJourney.entry), 0);
    } catch (error) {
      console.error(error);
      this.toastrSvr.danger('Erreur lors de la récupérations du parcours', 'Erreur!');
    } finally {
      this.isLoading = false;
    }
    this.dialogSvr.open(dialog, {})
  }

  getDurationDate(startDate: number) {
    const currentTime = Date.now();
    const diff = currentTime - startDate;
    const seconds = Math.floor(diff / 1000) % 60;
    const minutes = Math.floor(diff / (1000 * 60)) % 60;
    const hours = Math.floor(diff / (1000 * 60 * 60)) % 24;
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));

    return `${days} jours(s) ${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  }


  getColorStyle(time: any): any {
    const hours = 5400001; // 5400001 equal 1h30 min
    const minutes = 1800001;
    const waitingTime = time;
    if (waitingTime >= hours) {
      return {
        border: '2px solid #E5262E',
        'background-color': '#FCE7E8',
      };
    }
    if (waitingTime >= minutes && waitingTime < hours) {
      return {
        border: '2px solid #0F7B3F',
        'background-color': '#0F7B3F1C'
      }
    }
    return {
      border: '2px solid #0F7B3F',
      'background-color': '#0F7B3F1C'
    }
  }

  millisecondsToTime(ms: number): string {
    const days = Math.floor(ms / (1000 * 60 * 60 * 24));
    const hours = Math.floor((ms % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((ms % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((ms % (1000 * 60)) / 1000);

    const daysStr = days.toString().padStart(2, '0');
    const hoursStr = hours.toString().padStart(2, '0');
    const minutesStr = minutes.toString().padStart(2, '0');
    const secondsStr = seconds.toString().padStart(2, '0');

    return `${daysStr} jour(s) ${hoursStr}:${minutesStr}:${secondsStr}`;
  }

  onModelAEChange(value: any): void {
    if (value == null)
      this.dataAELabels = this.dataForFilter?.dataLoadNumber
    if (value) {
      this.loading = true;
      this.dataAELabels = this.dataForFilter?.dataLoadNumber?.filter((data: number) => JSON.stringify(data).includes(value)).sort((a, b) => {
        return a.toString().toLowerCase().indexOf(value?.toLowerCase()) - b.toString().toLowerCase().indexOf(value?.toLowerCase());
      });
      this.loading = false;
    }
  }

  onModelProductsChange(value: any): void {
    if (value == null)
      this.dataProducts = this.dataForFilter?.dataItemNumber;

    if (value)
      this.dataProducts = this.dataForFilter?.dataItemNumber?.filter((data: string) => data?.toLowerCase().includes(value?.toLowerCase())).sort((a, b) => {
        return a.toString().toLowerCase().indexOf(value?.toLowerCase()) - b.toString().toLowerCase().indexOf(value?.toLowerCase());
      });;
  }

  onModelCarrierChange(value: any): void {
    if (value === null)
      this.dataTransporters = this.dataForFilter?.dataAlpha;

    if (value)
      this.dataTransporters = this.dataForFilter?.dataAlpha.filter((data: string) =>
        data?.toLowerCase().includes(value?.toLowerCase())).sort((a, b) => {
          return a.toString().toLowerCase().indexOf(value?.toLowerCase()) - b.toString().toLowerCase().indexOf(value?.toLowerCase());
        });;
  }

  onModelCompanyChange(value: any): void {
    if (value == null)
      this.dataSoldToDescLabels = this.dataForFilter?.dataSoldToDescription;

    if (value)
      return this.dataSoldToDescLabels = this.dataForFilter?.dataSoldToDescription?.filter((data: string) =>
        data?.toLowerCase().includes(value?.toLowerCase())).sort((a, b) => {
          return a.toString().toLowerCase().indexOf(value?.toLowerCase()) - b.toString().toLowerCase().indexOf(value?.toLowerCase());
        });;
  }

  onModelDeliveryPointChange(value: any): void {
    if (value == null)
      this.dataShipToDescLabels = this.dataForFilter?.dataShipToDescription;

    return this.dataShipToDescLabels = this.dataForFilter?.dataShipToDescription?.filter((data: string) =>
      data?.toLowerCase().includes(value?.toLowerCase())).sort((a, b) => {
        return a.toString().toLowerCase().indexOf(value?.toLowerCase()) - b.toString().toLowerCase().indexOf(value?.toLowerCase());
      });;
  }

  onModelTypeChange(value: any): void {
    if (value == null)
      this.dataOrderTypeLabels = this.dataForFilter?.dataOrderType;

    return this.dataOrderTypeLabels = this.dataForFilter?.dataOrderType?.filter((data: string) =>
      data?.toLowerCase().includes(value?.toLowerCase())).sort((a, b) => {
        return a.toString().toLowerCase().indexOf(value.toLowerCase()) - b.toString().toLowerCase().indexOf(value.toLowerCase());
      });;
  }
  async onModelDateChange(event: any): Promise<any> {
    if (moment(this.filterForm.endDate).valueOf() < moment(this.filterForm.startDate).valueOf())
      return this.toastrSvr.danger('la date de fin est inférieure à la date de début', 'Erreurs Données!');
    await this.getAuthorization();
    await this.getDataForFilter();
  }

  async onSelectionAEChange(event: any): Promise<void> {
    if (event?.keyToReset) {
      console.log(event?.dataToReset);
      this.filterForm[event?.keyToReset] = '';
      this[event?.dataToReset](null);
    }
    if (event?.$event)
      await this.getAuthorization();
  }

  async changeStateRemoval(): Promise<any> {
    try {
      this.isLoading = true;
      if (this.connectedUser.rigths.isInspector)
        return this.toastrSvr.warning(`Vous n'avez pas le droit d'effectuer cette action`, 'Autorisations manquantes');
      await this.removalSvr.updateAe(this.removal?._id, { enable: this.removal?.enable == false ? true : false });
      await this.getAuthorization();
      return this.toastrSvr.success(`Cette AE a été mise à jour avec succès`, `Mise à jour réussi`);
    } catch (error) {
      this.toastrSvr.danger(`Une erreur est survenu lors de la mise a jour de cette AE`, 'Erreur de mise à jour')
    } finally {
      this.isLoading = false;
    }
  }

  isEnabled(removal?: AuthorizationRemoval) {
    const data = removal
    return data?.enable == false ? false : true;
  }

  openDialogExport(dailog: any): void {
    this.dialogExport = this.dialogSvr.open(dailog, {});
  }

  async exportAeToExcel(exportDialog: any): Promise<void | NbToastRef> {
    if (!this.startDate || !this.endDate) {
      return this.toastrSvr.danger('Veuillez renseigner une date de début et de fin', 'Donnés incorrectes !');
    }

    if (this.startDate > this.endDate) {
      return this.toastrSvr.danger('Veuillez renseigner une date de début inférieure à celle de la date de fin', 'Donnés incorrectes !');
    }

    const options = {
      startDate: moment(this.startDate).format('MM-DD-YYYY'),
      endDate: moment(this.endDate).format('MM-DD-YYYY'),
      ...this.filterForm,
    }
    try {

      this.isLoading = true;
      let dataAElist = (await this.removalSvr.getAuthorizationRemoval(options)).data;
      dataAElist = dataAElist.map((elt) => {
        const data = {};
        data['N° AE'] = elt?.LoadNumber;
        data['TRANSPORTEUR'] = elt?.Alpha;
        data['STATUT JDE'] = StatusAESLibrary[elt?.LoadStatus];
        data['N° COMMANDE'] = elt?.appReference;
        data['N° DE CHARGEMENT'] = elt?.ShipmentNumber;
        data['PRODUIT'] = elt?.ItemNumber;
        data['STATUT'] = this.getStatusRemovalsPipe.transform(elt?.enable == false ? 0 : elt?.status);
        data['QUANTITÉ'] = elt?.TransactionQuantity;
        data['CODE_ADRESSE'] = elt?.ShipToDescription;
        data['DATE CRÉATION'] = this.datePipe.transform(elt?.dates?.created, 'dd/MM/YYYY');
        return data;
      });

      this.removalSvr.exportAEAsExcelFile(dataAElist, 'Liste_AES_RENDU');
      this.dialogExport.close();
    } catch (error) {
      console.log(error);

      this.toastrSvr.danger(error?.error?.message, 'ERREUR!');
      return error;
    } finally { this.isLoading = false; }
  }

  async downloadAePdf(removal: any): Promise<void> {
    try {
      this.isLoading = true;
      const response = await this.removalSvr.downloadPdf(removal?.LoadNumber);
      const blob = new Blob([response], { type: 'application/pdf' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `AE_${removal.LoadNumber}.pdf`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      this.toastrSvr.success(`Le bon N°${removal.LoadNumber} a été téléchargé avec succès`, 'Téléchargement réussi');
    } catch (error) {
      console.error('Erreur lors du téléchargement du PDF:', error);
      this.toastrSvr.danger('Erreur lors du téléchargement du PDF', 'Erreur');
    } finally {
      this.isLoading = false;
    }
  }

}
