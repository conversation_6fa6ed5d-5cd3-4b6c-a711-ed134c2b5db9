import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BaseUrlService } from 'src/app/shared/services/base-url.service';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root'
})
export class MenuTrackingService {
  BASE_URL: string;


  constructor(
    private http: HttpClient,
    private baseUrlSrv: BaseUrlService,
  ) {
    this.BASE_URL = `${this.baseUrlSrv.getOrigin()}${environment.basePath}`;
  }

  async getAEs(param: any): Promise<any> {
    let params = new HttpParams();
    if (param?.status) { params = params.append('status', `${param?.status}`) }
    return await this.http.get(`${this.BASE_URL}/authorization-removal`, {params}).toPromise();
  }

  async getLocation(matricule: any): Promise<any> {
    return await this.http.get(`${this.BASE_URL}/location/${matricule}`).toPromise();

  }
}
