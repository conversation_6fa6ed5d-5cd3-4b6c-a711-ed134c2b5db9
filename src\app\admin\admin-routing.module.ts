import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

const routes: Routes = [
  { path: '', redirectTo: 'users', pathMatch: 'full' },

  {
    path: 'users',
    loadChildren: () => import('./users-list/users-list.module').then(m => m.UsersListModule)
  },

  { path: 'products', loadChildren: () => import('./menu-products/menu-products.module').then(m => m.MenuProductsModule) },
  { path: 'jde', loadChildren: () => import('./menu-jde/menu-jde.module').then(m => m.MenuJdeModule) },
  { path: 'stores', loadChildren: () => import('./menu-stores/menu-stores.module').then(m => m.MenuStoresModule) },
  { path: 'prices', loadChildren: () => import('./menu-prices/menu-prices.module').then(m => m.MenuPricesModule) },
  { path: 'prices', loadChildren: () => import('./menu-prices/menu-prices.module').then(m => m.MenuPricesModule) },

  {
    path: 'carrier', loadChildren: () => import('./carrier/carrier.module').then(m => m.CarrierModule)
  },
  {
    path: 'inspection', loadChildren: () => import('./menu-inspection/menu-inspection.module').then(m => m.MenuInspectionModule)
  },
  { path: 'tracking', loadChildren: () => import('./menu-tracking/menu-tracking.module').then(m=>m.MenuTrackingModule)},
  { path: 'reporting', loadChildren:() => import ('./menu-reporting/menu-reporting.module').then(m=>m.MenuReportingModule)},
  { path: 'reporting-render', loadChildren:() => import ('./menu-reporting-render/menu-reporting-render.module').then(m=>m.MenuReportingRenderModule)},
  { path: 'orders', loadChildren: () => import('./menu-orders/menu-orders.module').then(m => m.MenuOrdersModule)}
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class AdminRoutingModule { }
