import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { LoadingRemovalRoutingModule } from './loading-removal-routing.module';
import { LoadingRemovalComponent } from './loading-removal.component';
import { NbAutocompleteModule, NbBadgeModule, NbCardModule, NbDatepickerModule, NbIconModule, NbInputModule, NbListModule, NbSelectModule, NbSpinnerModule, NbTabsetModule, NbTooltipModule } from '@nebular/theme';
import { SharedModule } from 'src/app/shared/shared.module';
import { FormsModule } from '@angular/forms';


@NgModule({
  declarations: [LoadingRemovalComponent],
  imports: [
    FormsModule,
    CommonModule,
    NbIconModule,
    NbListModule,
    SharedModule,
    NbCardModule,
    NbInputModule,
    NbBadgeModule,
    NbTooltipModule,
    NbTabsetModule,
    NbSelectModule,
    NbSpinnerModule,
    NbDatepickerModule,
    NbAutocompleteModule,
    LoadingRemovalRoutingModule
  ]
})
export class LoadingRemovalModule { }
