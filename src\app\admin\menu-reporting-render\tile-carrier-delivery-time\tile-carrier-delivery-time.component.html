<div class="tile-sales-container">
    <nb-card accent="success" [nbSpinner]="isLoading" nbSpinnerStatus="success"
        nbSpinnerMessage="Chargement des données">
        <nb-card-header class="card-header-wrapper">
            <div class="card-header">
                {{' Temps de Livraison ' | truncateString:28}}
            </div>
        </nb-card-header>
        <nb-card-body>
            <div class="progress-info" *ngFor="let elt of deliveryTimedata">
                <div class="subtitle">{{elt?.label || 'NULL'}} :
                    <span class="h4">{{ millisecondsToTime(elt?.timeTaken ?? 0) }}</span>
                </div>
                <hr color="#f7f9fc" />
            </div>
        </nb-card-body>
    </nb-card>
</div>