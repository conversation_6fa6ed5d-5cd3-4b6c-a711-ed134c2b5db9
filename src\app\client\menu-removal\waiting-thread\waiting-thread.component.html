<div class="common-form-container menu-waiting-thread-container">
  <div class="header">
    <nb-icon icon="undo-outline" (click)="goBack()" [options]="{ animation: { type: 'zoom' } }" class="remove-icon"
      status="primary">
    </nb-icon>
    <h1 class="title">File d'attente</h1>
  </div>

  <div class="removal-container">
    <div class="filter-label">Filtrer par</div>
    <div class="filter-area">
      <div class="left-area">
        <input type="text" placeholder="N° Bon" fieldSize="small" nbInput class="empty-input height filter-input"
          [nbAutocomplete]="autoComplete0" [(ngModel)]="loadNumberFilter"
          (ngModelChange)="onModelAEChange($event)" />
        <nb-autocomplete #autoComplete0 (selectedChange)="onSelectionAEChange($event)">
          <nb-option *ngFor="let option of dataAELabels" [value]="option">{{option}}</nb-option>
        </nb-autocomplete>

        <input fieldSize="small" id="typeInput" [(ngModel)]="productFilter" class="empty-input height filter-input"
          (ngModelChange)="onModelProductChange($event)" nbInput fullWidth status="primary" type="text"
          placeholder="Rechercher le produit" [nbAutocomplete]="autoComplete1" />
        <nb-autocomplete #autoComplete1 (selectedChange)="onSelectionProductChange($event)">
          <nb-option *ngFor="let option of filteredProduct$ | async" [value]="option">{{option}}</nb-option>
        </nb-autocomplete>

        <input fieldSize="small" [(ngModel)]="orderTypeFilter" class="empty-input height filter-input"
          (ngModelChange)="onModelTypeChange($event)" nbInput fullWidth status="primary" type="text"
          placeholder="Rechercher le type" [nbAutocomplete]="autoComplete2" />
        <nb-autocomplete #autoComplete2 (selectedChange)="onSelectionOrderTypeChange($event)">
          <nb-option *ngFor="let option of dataOrderTypeLabels" [value]="option">{{option}}</nb-option>
        </nb-autocomplete>
      </div>
      <div class="right-area">
        <button nbButton status="basic" fieldSize="small" class="search-btn  reset-btn" (click)="reset()">
          <nb-icon icon="refresh-outline"></nb-icon>
          REINITIALISER
        </button>

        <button nbButton status="success" class="search-btn" (click)="getWaitingThread()">
          <nb-icon icon="search-outline"></nb-icon>
          RECHERCHER
        </button>

        <button nbButton status="success" class="search-btn" routerLink="/client/removal/new-removal">
          <nb-icon icon="plus-outline"></nb-icon>
          NOUVEAU BON
        </button>
      </div>
    </div>
    <nb-card>
      <!-- Container pour aligner tabsets et pagination horizontalement -->
      <div class="tabset-pagination-container">
        <nb-tabset (changeTab)="onTabChange($event)">
          <nb-tab tabTitle="Chargement usine">
            <span></span> <!-- Contenu vide pour l'onglet -->
          </nb-tab>
          <nb-tab tabTitle="Parking">
            <span></span> <!-- Contenu vide pour l'onglet -->
          </nb-tab>
        </nb-tabset>

        <!-- Pagination alignée à droite -->
        <div class="pagination-wrapper">
          <clw-pagination [config]="currentPaginationConfig" (pageChange)="onCurrentPageChange($event)"
            (itemsPerPageChange)="onCurrentItemsPerPageChange($event)">
          </clw-pagination>
        </div>
      </div>

      <!-- Contenu des onglets -->
      <div class="tab-content">
        <!-- Contenu pour "Chargement usine" -->
        <div *ngIf="activeTab === 'Chargement usine'">
          <nb-card class="no-border">

            <nb-list class="element">
              <nb-list-item class="list-elt-header paginator">
                <div class="col col-num">N°</div>
                <div class="col col-name">Conducteur</div>
                <div class="col col-name">N° AES</div>
                <div class="col col-name">N° X3</div>
                <div class="col col-name">Compagnies</div>
                <div class="col col-name">Produits</div>
                <div class="col col-item">Quai</div>
                <!-- <div class="col col-ae-ref">N° quais</div> -->
                <div class="col col-qty">Quantité</div>
                <div class="col col-time">Temps d'attente</div>
                <div class="col col-immatriculation">Immatriculation</div>
                <div class="col col-entry-weight">Pesée en entrer</div>
                <div class="col col-status center">Statut</div>
                <div class="col col-action"></div>
              </nb-list-item>
            </nb-list>
            <nb-list class="element scrool-style" [nbSpinner]="displayLoading" nbSpinnerStatus="primary">
              <nb-list-item class="list-elt-header" *ngFor="let waiting of waitingThreadTabDock; index as i">
                <div class="col col-num">{{ (factoryPaginationConfig.currentPage - 1) * factoryPaginationConfig.itemsPerPage + i + 1 }}</div>
                <div class="col col-name">
                  {{ waiting?.carrier?.driver?.fullname | truncateString : 15 }}
                </div>
                <div class="col col-name">{{ waiting?.LoadNumber }}</div>
                <div class="col col-name" nbTooltip="{{waiting?.erpReference?.join(',')}}">{{
                  waiting?.erpReference?.join(' ,') | truncateString:15}}
                </div>

                <div class="col col-name" nbTooltip="{{waiting?.SoldToDescription?.join(', ')}}">
                  {{waiting?.SoldToDescription.join(', ') | truncateString : 15}}
                </div>
                <div class="col col-name" nbTooltip="{{ getConcatenatedLabels(waiting?.groupedOrders) }}">
                  {{ getConcatenatedLabels(waiting?.groupedOrders) | truncateString:15 }}
                </div>
                <div class="col col-item">
                  {{ getQuaiName(waiting?.dockCode) || 'N/A' }}
                </div>
                <!-- <div class="col col-item">{{waiting?.carrier?.truck?.type |truncateString:15}}</div> -->
                <!-- <div class="col col-ae-ref">{{waiting?.carrier?.truck?.dock?.name}}</div> -->
                <div class="col col-qty">
                  {{getQuantityDelivery(waiting?.groupedOrders)}}T
                </div>
                <div class="col col-time">
                  <p [ngStyle]="waiting?.dockTimeStart | colorStyleWaiting">
                    {{ commonSrv.getDurationDate(waiting?.dockTimeStart) ?? 'N/A' }}
                  </p>
                </div>
                <div class="col col-immatriculation">
                  {{
                  waiting?.carrier?.truck?.immatriculation ||
                  waiting?.carrier?.truckImmatriculation ||
                  "Non renseigner"
                  }}
                </div>
                <div class="col col-entry-weight">
                  {{ waiting?.initialTruckTonnage ? waiting.initialTruckTonnage + 'KG' : 'Non renseigner' }}
                </div>
                <div class="col col-status" *ngIf="!waiting.dockCode || waiting.dockCode === 0">
                  <!-- <nb-badge text="OK" class="badge" position="top start" status="success"></nb-badge> -->
                  <nb-badge status="warning" text="EN ATTENTE" class="badge" position="top start"></nb-badge>
                </div>
                <div class="col col-status" *ngIf="waiting.dockCode && waiting.dockCode !== 0">
                  <!-- <nb-badge text="OK" class="badge" position="top start" status="success"></nb-badge> -->
                  <nb-badge status="info" text="EN CHARGEMENT" class="badge" position="top start"></nb-badge>
                </div>

                <div class="col col-action">
                  <button nbTooltip="Envoyer au parking" nbButton outline nbTooltipPlacement="top" nbTooltipStatus
                    (click)="openUpdateAE(updateAeDialog, 700, waiting)">
                    <nb-icon icon="arrow-circle-right-outline" status="primary"
                      [options]="{ animation: { type: 'zoom' } }">
                    </nb-icon>
                  </button>

                  <!-- <button
                      nbTooltip="Attribuer un quai"
                      nbButton
                      outline
                      nbTooltipPlacement="top"
                      nbTooltipStatus
                      (click)="openUpdateAE(dockDialog, 500, waiting)"
                    >
                      <nb-icon
                        icon="inbox-outline"
                        status="primary"
                        [options]="{ animation: { type: 'zoom' } }"
                      >
                      </nb-icon>
                    </button> -->

                  <button nbTooltip="En panne" nbButton outline nbTooltipPlacement="top" nbTooltipStatus
                    (click)="openUpdateAE(updateAeDialog, 600, waiting)">
                    <nb-icon icon="alert-circle-outline" status="primary" [options]="{ animation: { type: 'zoom' } }">
                    </nb-icon>
                  </button>

                  <button nbTooltip="Sortie d'usine" nbButton outline nbTooltipPlacement="top" nbTooltipStatus
                    (click)="openSendToQueueDialog(queueDialog,waiting)">
                    <nb-icon icon="log-out" status="primary" [options]="{ animation: { type: 'zoom' } }">
                    </nb-icon>
                  </button>

                  <button nbTooltip="Modifier Aes" nbButton outline nbTooltipPlacement="top" nbTooltipStatus
                    (click)="UpdateNewAE(addStatusAes, waiting)">
                    <nb-icon icon="edit-2-outline" status="primary" [options]="{ animation: { type: 'zoom' } }">
                    </nb-icon>
                  </button>

                  <!-- <button
                      nbTooltip="Reinitialiser"
                      nbButton
                      outline
                      nbTooltipPlacement="top"
                      nbTooltipStatus
                      (click)="resetAes(resetAeDialog, waiting)"
                    >
                      <nb-icon
                        icon="close-circle-outline"
                        status="danger"
                        [options]="{ animation: { type: 'zoom' } }"
                      >
                      </nb-icon>
                    </button> -->
                </div>
              </nb-list-item>
              <nb-list-item class="list-elt-header" *ngIf="
                    waitingThreadTabDock?.length == 0 || !waitingThreadTabDock
                  ">
                <div class="not-found">
                  <img src="../../../../assets/images/icons/shipped.png" alt="" />
                  <h6>Aucun camion trouvé</h6>
                </div>
              </nb-list-item>
            </nb-list>
          </nb-card>
        </div>

        <!-- Contenu pour "Parking" -->
        <div *ngIf="activeTab === 'Parking'">
          <nb-card class="no-border">

            <nb-list class="element">
              <nb-list-item class="list-elt-header paginator">
                <div class="col col-num">N°</div>
                <div class="col col-name">Conducteur</div>
                <div class="col col-name">N° AES</div>
                <div class="col col-name">N° x3</div>
                <div class="col col-name">Compagnies</div>
                <!-- <div class="col col-ae-ref">N° quais</div> -->
                <div class="col col-name">Produit</div>
                <div class="col col-qty">Quantité</div>
                <div class="col col-time">Temps d'attente</div>
                <div class="col col-immatriculation">immatriculation</div>
                <!-- <div class="col col-entry-weight">Pesé en entrer</div> -->
                <div class="col col-status">Statut</div>
                <div class="col col-action">
                  <div class="action-icons"></div>
                  <div class="col col-action">
                    <div class="action-icons"></div>
                  </div>
                </div>
                <!-- <div class="col col-action">Actions</div> -->
              </nb-list-item>
            </nb-list>
            <nb-list class="element scrool-style" [nbSpinner]="displayLoading" nbSpinnerStatus="primary">
              <nb-list-item class="list-elt-header" *ngFor="let waiting of waitingTheadTabNew; index as i"
                [ngClass]="{ 'blink': waiting.moved && waiting.moved.from === 700 }">
                <div class="col col-num">{{ (parkingPaginationConfig.currentPage - 1) * parkingPaginationConfig.itemsPerPage + i + 1 }}</div>
                <div class="col col-name" title="{{ waiting?.carrier?.driver?.fullname }}">
                  {{ waiting?.carrier?.driver?.fullname | truncateString : 15 }}
                </div>
                <div class="col col-name">{{ waiting?.LoadNumber }}</div>
                <div class="col col-name" nbTooltip="{{waiting?.erpReference?.join(',')}}">{{
                  waiting?.erpReference?.join(' ,') | truncateString:15}}
                </div>
                <div class="col col-name" nbTooltip="{{waiting?.SoldToDescription}}">
                  {{waiting?.SoldToDescription.join(' ,') | truncateString : 15 }}
                </div>
                <!-- <div class="col col-ae-ref">{{waiting?.carrier?.truck?.dock?.name}}</div> -->
                <div class="col col-name" nbTooltip="{{ getConcatenatedLabels(waiting?.groupedOrders)}}">
                  {{ getConcatenatedLabels(waiting?.groupedOrders) | truncateString:15}}
                </div>
                <!-- <div class="col col-item">{{waiting?.carrier?.truck?.type |truncateString:15}}</div> -->
                <div class="col col-qty">
                  {{getQuantityDelivery(waiting?.groupedOrders)}}T
                </div>
                <div class="col col-time">
                  <p [ngStyle]="waiting?.parkTime | colorStyleWaiting">
                    {{ commonSrv.getDurationDate(waiting?.parkTime) ?? 'N/A' }}
                  </p>
                </div>
                <div class="col col-immatriculation">
                  {{ waiting?.carrier?.truck?.immatriculation ||'N/A' }}
                </div>
                <!-- <div class="col col-entry-weight">80T</div> -->
                <div class="col col-status" *ngIf="!waiting.dockCode || waiting.dockCode === 0">
                  <!-- <nb-badge text="OK" class="badge" position="top start" status="success"></nb-badge> -->
                  <nb-badge status="warning" text="EN ATTENTE" class="badge" position="top start"></nb-badge>
                </div>
                <div class="col col-status" *ngIf="waiting.dockCode && waiting.dockCode !== 0">
                  <!-- <nb-badge text="OK" class="badge" position="top start" status="success"></nb-badge> -->
                  <nb-badge status="info" text="EN CHARGEMENT" class="badge" position="top start"></nb-badge>
                </div>
                <div class="col col-action">
                  <div class="col col-action" *ngIf="!waiting?.moved || waiting?.moved.from != 700">
                    <!-- <button nbTooltip="Envoyer au parking usine" nbButton outline nbTooltipPlacement="top"
                      nbTooltipStatus (click)="openUpdateAE(updateAeDialog, 400, waiting)">
                      <nb-icon icon="arrow-circle-right-outline" status="primary"
                        [options]="{ animation: { type: 'zoom' } }">
                      </nb-icon>
                    </button>-->

                    <button nbTooltip="Attribuer un quai" nbButton outline nbTooltipPlacement="top" nbTooltipStatus
                      (click)="openUpdateAE(dockDialog, 450, waiting)">
                      <nb-icon icon="inbox-outline" status="primary" [options]="{ animation: { type: 'zoom' } }">
                      </nb-icon>
                    </button>

                    <button nbTooltip= "Renseigner le poids initial" nbButton outline nbTooltipPlacement="top" nbTooltipStatus
                      (click)="openUpdateAE(weightDialog, 450, waiting)">
                      <nb-icon icon="bar-chart-outline" status="primary" [options]="{ animation: { type: 'zoom' } }">
                      </nb-icon>
                    </button>

                    <button nbTooltip="En panne" nbButton outline nbTooltipPlacement="top" nbTooltipStatus
                      (click)="openUpdateAE(updateAeDialog, 600, waiting)">
                      <nb-icon icon="alert-circle-outline" status="primary" [options]="{ animation: { type: 'zoom' } }">
                      </nb-icon>
                    </button>

                    <button nbTooltip="Modifier Bons" nbButton outline nbTooltipPlacement="top" nbTooltipStatus
                      (click)="UpdateNewAE(addStatusAes, waiting)">
                      <nb-icon icon="edit-2-outline" status="primary" [options]="{ animation: { type: 'zoom' } }">
                      </nb-icon>
                    </button>

                    <button nbTooltip="Reinitialiser" nbButton outline nbTooltipPlacement="top" nbTooltipStatus
                      (click)="resetAes(resetAeDialog, waiting)">
                      <nb-icon icon="close-circle-outline" status="danger" [options]="{ animation: { type: 'zoom' } }">
                      </nb-icon>
                    </button>
                  </div>
                </div>
              </nb-list-item>
              <nb-list-item class="list-elt-header" *ngIf="waitingTheadTabNew?.length == 0 || !waitingTheadTabNew">
                <div class="not-found">
                  <img src="../../../../assets/images/icons/shipped.png" alt="" />
                  <h6>Aucun camion trouvé</h6>
                </div>
              </nb-list-item>
            </nb-list>
          </nb-card>
        </div>
      </div>
    </nb-card>
  </div>
</div>

<ng-template #queueDialog let-data let-ref="dialogRef">
  <nb-card [nbSpinner]="isLoading" nbSpinnerStatus="primary">
    <nb-card-header class="form--header">Confirmation</nb-card-header>
    <nb-card-body>
      <div class="dialog-assign-content">
        Vous êtes sur le point de sortir le camion immatriculé <strong class="important">{{selectedTruck}} </strong>de
        l'usine. Veuillez confirmer cette action.
      </div>
    </nb-card-body>
    <nb-card-footer class="form--footer">
      <button nbButton (click)="ref.close(false)" outline ghost status="basic" [disabled]="isLoading">
        <nb-icon icon="close-square-outline" [options]="{ animation: { type: 'zoom ' } }" class="remove-icon">
        </nb-icon>
        Annuler
      </button>
      <button nbButton (click)="removeToDuck()" outline status="primary" [disabled]="isLoading">
        <nb-icon icon="done-all-outline" [options]="{ animation: { type: 'zoom ' } }" class="remove-icon">
        </nb-icon>
        Confirmer
      </button>
    </nb-card-footer>
  </nb-card>
</ng-template>

<ng-template #updateAeDialog let-data let-ref="dialogRef">
  <nb-card>
    <nb-card-header>Confirmation</nb-card-header>
    <nb-card-body>
      <p *ngIf="actionStatus === 600">
        Vous essayez d'envoyer ce camion dans la liste des camions en panne.
      </p>
      <p *ngIf="actionStatus === 700">
        Vous essayez d'envoyer ce camion dans le parking.
      </p>
      <p *ngIf="actionStatus === 400">
        Vous essayez d'envoyer ce camion dans au parking usine.
      </p>
      <p>Voulez vous poursuivre?</p>
    </nb-card-body>
    <nb-card-footer>
      <button nbButton ghost status="basic" (click)="ref.close()">
        <nb-icon icon="close-square-outline"></nb-icon>
        Annuler
      </button>
      <button nbButton outline status="success" (click)="ref.close(true)" [disabled]="isLoading" class="btn-border">
        <nb-icon icon="done-all-outline"></nb-icon>
        Confirmer
      </button>
    </nb-card-footer>
  </nb-card>
</ng-template>

<ng-template #truckReceived let-data let-ref="dialogRef">
  <nb-card>
    <nb-card-header>Confirmation</nb-card-header>
    <nb-card-body>
      <p>
        Vous êtes sur le point de marquer ce camion comme réceptionné.
      </p>
      <p>Voulez vous poursuivre?</p>
    </nb-card-body>
    <nb-card-footer>
      <button nbButton ghost status="basic" (click)="ref.close()">
        <nb-icon icon="close-square-outline"></nb-icon>
        Annuler
      </button>
      <button nbButton outline status="success" (click)="UpdateTruckInspection()" [disabled]="isLoading"
        class="btn-border">
        <nb-icon icon="done-all-outline"></nb-icon>
        Confirmer
      </button>
    </nb-card-footer>
  </nb-card>
</ng-template>

<ng-template #resetAeDialog let-data let-ref="dialogRef">
  <nb-card>
    <nb-card-header>Confirmation</nb-card-header>
    <nb-card-body class="reset-container">
      <p>
        Vous essayez de Reinitialiser un bon.
        <span><b>N° {{ removalCurrent?.LoadNumber }}</b></span>
      </p>
      <p>Voulez vous poursuivre?</p>
    </nb-card-body>
    <nb-card-footer>
      <button nbButton ghost status="basic" (click)="ref.close()">
        <nb-icon icon="close-square-outline"></nb-icon>
        ANNULER
      </button>
      <button nbButton outline status="success" (click)="ref.close(true)" [disabled]="displayLoading"
        class="btn-border">
        <nb-icon icon="done-all-outline"></nb-icon>
        CONFIRMER
      </button>
    </nb-card-footer>
  </nb-card>
</ng-template>

<ng-template #addStatusAes let-data let-ref="dialogRef">
  <nb-card class="update-aes-container">
    <div class="contain">
      <nb-card-body>
        <div class="order-detail-container her-width">
          <nb-card>
            <nb-card-header class="form--header">Bons courants</nb-card-header>
            <nb-card-body>
              <div class="order-infor order-pick-up">
                <table class="order-table" [nbSpinner]="isLoading" nbSpinnerStatus="primary">
                  <tr>
                    <td class="title">N° Bon:</td>
                    <td class="order-right">
                      {{ removalCurrent?.LoadNumber }}
                    </td>
                  </tr>
                  <tr>
                    <td class="title">statut:</td>
                    <td class="order-right">
                      {{ removalCurrent?.status | getStatusRemovals }}
                    </td>
                  </tr>
                  <tr>
                    <td class="title">Statut du chargement:</td>
                    <td class="order-right">
                      {{ removalCurrent?.LoadStatus | getStatusLabelRemovals }}
                    </td>
                  </tr>
                  <tr>
                    <td class="title">Numéro de la commande(S):</td>
                    <td class="order-right">
                      {{ removalCurrent?.erpReference?.join(', ') || "non disponible" }}
                    </td>
                  </tr>
                  <tr>
                    <td class="title">Produit:</td>
                    <td class="order-right" nbTooltip="{{ getConcatenatedLabels(removalCurrent?.groupedOrders) }}">
                      {{ getConcatenatedLabels(removalCurrent?.groupedOrders) | truncateString }}
                    </td>
                  </tr>
                  <tr>
                    <td class="title">Quantité de la commande:</td>
                    <td class="order-right">
                      {{getQuantity(removalCurrent?.groupedOrders)}}T
                    </td>
                  </tr>
                  <tr>
                    <td class="title">Quantité à livrer:</td>
                    <td class="order-right">
                      {{ getQuantityDelivery(removalCurrent?.groupedOrders)}}
                    </td>
                  </tr>
                  <tr>
                    <td class="title">Date de requête d'enlèvement:</td>
                    <td class="order-right">
                      {{ removalCurrent?.RequestedDate }}
                    </td>
                  </tr>
                  <tr>
                    <td class="title">Transporteur :</td>
                    <td class="order-right">
                      {{ removalCurrent?.carrier?.label || "non disponible" }}
                    </td>
                  </tr>
                  <tr>
                    <td class="title">Conducteur :</td>
                    <td class="order-right">
                      {{
                      removalCurrent?.carrier?.driver?.fullname ||
                      "non disponible"
                      }}
                    </td>
                  </tr>
                  <tr>
                    <td class="title">Immatriculation:</td>
                    <td class="order-right">
                      {{
                      removalCurrent?.carrier?.truckImmatriculation ||
                      "non disponible"
                      }}
                    </td>
                  </tr>
                  <tr>
                    <td class="title">Type d'enlèvement (Pickup/Rendu):</td>
                    <td class="order-right">
                      {{
                      FreightHandlingCode(
                      removalCurrent?.FreightHandlingCode
                      ) || "non disponible"
                      }}
                    </td>
                  </tr>
                  <tr>
                    <td class="title">Immatriculation du véhicule:</td>
                    <td class="order-right">
                      {{ removalCurrent?.PrimaryVehicleID || "non disponible" }}
                    </td>
                  </tr>
                </table>
              </div>
            </nb-card-body>
          </nb-card>
        </div>
      </nb-card-body>

      <nb-card-body>
        <div class="order-detail-container her-width">
          <nb-card>
            <nb-card-header class="form--header">Bons de remplacement
            </nb-card-header>
            <nb-card-body class="update-form-contain">
              <div class="form-group">
                <div class="attribute-block magin-update force-magin">
                  <label class="label" for="nameInput">N°BON</label>
                </div>
                <input nbInput class="user-name input-width" status="basic" type="number" nbInput
                  [(ngModel)]="loadNumber" (ngModelChange)="onAesChange($event)"
                  placeholder="{{ 'Selectionner Bons de remplacement' }}" [nbAutocomplete]="autoComplete2" />
                <nb-autocomplete #autoComplete2 (selectedChange)="onSelectionAE($event)">
                  <nb-option *ngIf="load"> Chargement... </nb-option>
                  <nb-option *ngFor="let option of DataLoadNumbers" [value]="option">{{ option }}
                  </nb-option>
                </nb-autocomplete>
              </div>
            </nb-card-body>
            <nb-card-body>
              <div class="order-infor order-pick-up update-infos-container">
                <table class="order-table" [nbSpinner]="isLoading" nbSpinnerStatus="primary">
                  <tr>
                    <td class="title">N° Bon:</td>
                    <td class="order-right">{{ changeAE?.LoadNumber }}</td>
                  </tr>
                  <tr>
                    <td class="title">statut:</td>
                    <td class="order-right">
                      {{ changeAE?.status | getStatusRemovals }}
                    </td>
                  </tr>
                  <tr>
                    <td class="title">Statut du chargement:</td>
                    <td class="order-right">
                      {{ changeAE?.LoadStatus | getStatusLabelRemovals }}
                    </td>
                  </tr>
                  <tr>
                    <td class="title">Numéro de la commande:</td>
                    <td class="order-right">
                      {{ changeAE?.SalesOrderNumber || "non disponible" }}
                    </td>
                  </tr>
                  <tr>
                    <td class="title">Produit:</td>
                    <td class="order-right">{{ getConcatenatedLabels(changeAE?.ItemNumber) }}</td>
                  </tr>
                  <tr>
                    <td class="title">Quantité de la commande:</td>
                    <td class="order-right">
                      {{ changeAE?.TransactionQuantity || "non disponible" }}
                    </td>
                  </tr>
                  <tr>
                    <td class="title">Quantité à livrer:</td>
                    <td class="order-right">{{ changeAE?.QuantityShipped }}</td>
                  </tr>
                  <tr>
                    <td class="title">Date de requête d'enlèvement:</td>
                    <td class="order-right">{{ changeAE?.RequestedDate }}</td>
                  </tr>
                  <tr>
                    <td class="title">Transporteur :</td>
                    <td class="order-right">
                      {{ changeAE?.carrier?.label || "non disponible" }}
                    </td>
                  </tr>
                  <tr>
                    <td class="title">Type d'enlèvement (Pickup/Rendu):</td>
                    <td class="order-right">
                      {{
                      FreightHandlingCode(changeAE?.FreightHandlingCode) ||
                      "non disponible"
                      }}
                    </td>
                  </tr>
                </table>
              </div>
            </nb-card-body>
          </nb-card>
        </div>
      </nb-card-body>
    </div>
    <nb-card-footer>
      <button nbButton ghost status="basic" (click)="ref.close()">
        <nb-icon icon="close-square-outline"></nb-icon>
        ANNULER
      </button>
      <button nbButton outline status="success" (click)="ref.close(true)" [disabled]="isLoading" class="btn-border">
        <nb-icon icon="done-all-outline"></nb-icon>
        CONFIRMER
      </button>
    </nb-card-footer>
  </nb-card>
</ng-template>

<ng-template #dockDialog let-data let-ref="dialogRef">
  <nb-card>
    <nb-card-header>Attribution de quais</nb-card-header>
    <nb-card-body>
      <div class="edit-container">
        <div class="input">
          <label for="otherTrainingType">Choississez un quais</label>
          <nb-select fullWidth placeholder="" size="medium" [(selected)]="selectedDock">
            <nb-option [value]="dock" *ngFor="let dock of docks">{{ dock?.label }}
            </nb-option>
          </nb-select>
        </div>
      </div>
    </nb-card-body>
    <nb-card-footer>
      <button nbButton ghost status="basic" (click)="ref.close()">
        <nb-icon icon="close-square-outline"></nb-icon>
        Annuler
      </button>
      <button nbButton outline status="success" (click)="sendToDock()" [disabled]="isLoading" class="btn-border">
        <nb-icon icon="done-all-outline"></nb-icon>
        Confirmer
      </button>
    </nb-card-footer>
  </nb-card>
</ng-template>

<ng-template #weightDialog let-data let-ref="dialogRef">
  <nb-card>
    <nb-card-header>Saisie du poids initial</nb-card-header>
    <nb-card-body>
      <div class="edit-container">
        <div class="input">
          <label for="otherTrainingType">Poids initial du camion (KG)</label>
          <input id="typeInput" nbInput status="basic" [min]="0" type="number" [(ngModel)]="initialTruckTonnage"
            style="margin-top: 0.2em;" placeholder="Saisissez le poids initial du camion" fullWidth />
        </div>
      </div>
    </nb-card-body>
    <nb-card-footer>
      <button nbButton ghost status="basic" (click)="ref.close()">
        <nb-icon icon="close-square-outline"></nb-icon>
        Annuler
      </button>
      <button nbButton outline status="success" (click)="saveInitialWeight()" [disabled]="isLoading" class="btn-border">
        <nb-icon icon="done-all-outline"></nb-icon>
        Confirmer
      </button>
    </nb-card-footer>
  </nb-card>
</ng-template>

<ng-template #newAesDialog let-data let-ref="dialogRef">
  <nb-card class="newManualAe-container" [nbSpinner]="isLoading" nbSpinnerStatus="primary">
    <nb-card-header> Nouveau BON</nb-card-header>
    <nb-card-body class="contain">

      <div class="group">
        <label class="label" for="nameInput">N° Bon</label>
        <input nbInput class="input-width" id="nameInput" status="basic" type="number" [maxlength]="7"
          placeholder="Veuillez entrer le numero du BON " [(ngModel)]="newManualAe.LoadNumber">
      </div>


      <div class="group">
        <label class="label" for="nameInput"> ID de l'adresse de livraison</label>
        <input nbInput class="input-width" status="basic" type="text"
          placeholder="Veuillez entrer l'adresse de livraison " [(ngModel)]="newManualAe.ShipTo">
      </div>

      <div class="group">
        <label class="label" for="nameInput"> Adresse de livraison</label>
        <input nbInput class="input-width" status="basic" type="text"
          placeholder="Veuillez entrer l'adresse de livraison" [(ngModel)]="newManualAe.ShipToDescription">
      </div>


      <div class="group">
        <label class="label" for="nameInput">ID de la compagnies</label>
        <input nbInput class=" input-width" status="basic" type="text" placeholder="Veuillez entrer le Code_Client"
          [(ngModel)]="newManualAe.SoldTo">
      </div>

      <div class="group">
        <label class="label" for="clientname">Nom du client</label>
        <input nbInput class=" input-width" id="clientname" status="basic" type="text"
          placeholder="Veuillez entrer le nom du client" [(ngModel)]="newManualAe.SoldToDescription">
      </div>


      <div class="group">
        <label class="label" for="nameInput">Quantité</label>
        <input nbInput class="u input-width" status="basic" type="text" placeholder="Veuillez entrer la quantité"
          [(ngModel)]="newManualAe.TransactionQuantity ">
      </div>

      <div class="group">
        <label class="label" for="driver">Nom du transporteur</label>
        <input nbInput class="input-width" id="driver" status="basic" type="text"
          placeholder="Entrer le Nom du transporteur" [(ngModel)]="newManualAe.Alpha">
      </div>

      <div class="group">
        <label class="label" for="jdest"> Statut X3 </label>
        <nb-select placeholder="Selectionner le statut X3" id="jdest" type="text" [(selected)]="newManualAe.LoadStatus">
          <nb-option *ngFor="let option of jdeStatus" value="{{option.code}}">{{option.name}}</nb-option>
        </nb-select>
      </div>


      <div class="group">
        <label class="label" for="nameInput">Produit</label>
        <nb-select placeholder="Selectionner le produit" [(selected)]="newManualAe.ItemNumber">
          <nb-option *ngFor="let option of productsLabels" value="{{option}}">{{option}}</nb-option>
        </nb-select>
      </div>

      <div class="group">
        <label class="label" for="nameInput"> Type de BON </label>
        <nb-select placeholder="Selectionner le type de Bon " type="text"
          [(selected)]="newManualAe.FreightHandlingCode">
          <nb-option *ngFor="let option of aesType" value="{{option.code}}">{{option.name}}</nb-option>
        </nb-select>

      </div>

      <div class="group">
        <label class="label" for="nameInput">Type de commande</label>
        <nb-select placeholder="Selectionner le type de commande  " type="text" [(selected)]="newManualAe.OrderType">
          <nb-option *ngFor="let option of orderType" value="{{option.code}}">{{option.name}}</nb-option>
        </nb-select>
      </div>
    </nb-card-body>
    <nb-card-footer>
      <button nbButton ghost status="basic" (click)="ref.close()">
        <nb-icon icon="close-square-outline"></nb-icon>
        ANNULER
      </button>
      <button nbButton outline status="success" (click)="newAes()" [disabled]="isLoading" class="btn-border">
        <nb-icon icon="done-all-outline"></nb-icon>
        ENREGISTRER
      </button>
    </nb-card-footer>
  </nb-card>
</ng-template>

<ng-template #updateTruckDialog let-data let-ref="dialogRef" class="dialogRef">
  <div class="order-detail-container her-width">
    <nb-card>
      <nb-card-header class="form--header">Modifier l'inspection</nb-card-header>
      <nb-card-body>
        <nb-tabset>
          <nb-tab tabTitle="Informations Chauffeur">
            <div class="form-container">
              <div class="form-group">
                <div class="attribute-block">
                  <label class="text"> Transporteur</label>
                </div>
                <input type="text" placeholder="Transporteur" nbInput fullWidth class="empty-input height"
                  [nbAutocomplete]="autoComplete4" [(ngModel)]="currentInspections.carrier.label"
                  (ngModelChange)="onModelCarrierChange($event)" />
                <nb-autocomplete #autoComplete4>
                  <nb-option *ngFor="let option of dataCarrierLabels" [value]="option">{{option }}
                  </nb-option>
                </nb-autocomplete>
              </div>
              <div class="form-group">
                <div class="attribute-block">
                  <label class="text" for="nameInput">Nom du chauffeur</label>
                </div>
                <input nbInput class="carrier-name input-width" status="basic" type="text" fullWidth
                  placeholder="Saisissez le nom du chauffeur" [(ngModel)]='currentInspections.driver'>
              </div>
              <div class="form-group">
                <div class="attribute-block">
                  <label class="text"> Numéro de téléphone</label>
                </div>
                <input id="typeInput" nbInput status="basic" type="tel" (keyup)="verifyTel($event)"
                  [(ngModel)]="currentInspections.driverTel" fullWidth
                  placeholder="Saisissez le numero de tel du chauffeur" />
              </div>
              <div class="form-group">
                <div class="attribute-block">
                  <label class="text">Immatriculation du véhicule</label>
                </div>
                <input id="typeInput" nbInput status="basic" type="text" [(ngModel)]="currentInspections?.tractor.label"
                  fullWidth placeholder="Saisissez l'immatriculation du Véhicule" />
              </div>
              <div class="form-group">
                <div class="attribute-block">
                  <label class="text"> Date d'expiration du permis</label>
                </div>
                <input nbInput [nbDatepicker]="datePickerStart" [(ngModel)]="currentInspections.licenseExpirationDate"
                  fullWidth class="empty-input height" placeholder="Début" />
                <nb-datepicker #datePickerStart>
                </nb-datepicker>
              </div>
              <div class="form-group">
                <div class="attribute-block">
                  <label class="text">Tonnage du camion</label>
                </div>
                <input id="typeInput" nbInput status="basic" type="number" [min]="0" max="45"
                  (keyup)="commonSrv.verifyTonnage($event)" type="number" [(ngModel)]="currentInspections.truckTonnage"
                  fullWidth placeholder="Saisissez le Tonnage du camion" />
              </div>
              <div class="form-group">
                <div class="attribute-block">
                  <label class="text"> Point de depart</label>
                </div>
                <input id="typeInput" nbInput status="basic" type="tel" [(ngModel)]="currentInspections.truckOrigin"
                  fullWidth placeholder="Saisissez le point de depart du camion" />
              </div>
              <div class="form-group">
                <div class="attribute-block">
                  <label class="text"> Destination</label>
                </div>
                <input id="typeInput" nbInput status="basic" type="tel"
                  [(ngModel)]="currentInspections.truckDestination" fullWidth
                  placeholder="Saisissez la destination du camion" />
              </div>
              <div class="form-group">
                <div class="attribute-block">
                  <label class="text"> Ridelle du camion</label>
                </div>
                <nb-select class="carrier-name input-width"
                  [placeholder]="currentInspections.statusJacks | getStatusTruck" fullWidth class="empty-input"
                  [(selected)]='currentInspections.statusJacks'>
                  <nb-option [value]='status?.value' *ngFor="let status of statusChoices">{{status?.label}}</nb-option>
                </nb-select>
              </div>
              <div class="form-group">
                <div class="attribute-block">
                  <label class="text">Circuit hydraulique(Verins)</label>
                </div>
                <nb-select class="carrier-name input-width"
                  [placeholder]="currentInspections.statusSideBoard | getStatusTruck" fullWidth class="empty-input"
                  [(selected)]='currentInspections.statusSideBoard'>
                  <nb-option [value]='status?.value' *ngFor="let status of statusChoices">{{status?.label}}</nb-option>
                </nb-select>
              </div>

            </div>
          </nb-tab>

          <nb-tab tabTitle="Verifications">
            <div class="order-infor order-pick-up">
              <table class="order-table">
                <tr>
                  <td class="title">Statut global:</td>
                  <td class="order-right">
                    <nb-select class="carrier-name input-width" placeholder="Choissisez le statut" status='primary'
                      size="small" class="empty-input" [(selected)]='currentInspections.decision'>
                      <nb-option [value]='decision' *ngFor="let decision of InspectionDecisions">{{decision}}
                      </nb-option>
                    </nb-select>
                  </td>
                </tr>
                <tr *ngFor="let check of currentInspections?.checkList; index as i ">
                  <td>{{check?.label?.slice(0,35)}}</td>
                  <nb-toggle class="order-right" [(ngModel)]="check.state"></nb-toggle>
                </tr>
              </table>
            </div>
          </nb-tab>
        </nb-tabset>
      </nb-card-body>
      <nb-card-footer class="form--footer">
        <button nbButton outline status="basic" class="" (click)="ref.close()">
          <nb-icon icon="close-square-outline" [options]="{ animation: { type: 'zoom' } }" class="remove-icon">
          </nb-icon>
          fermer
        </button>
        <button nbButton outline status="primary" class="" (click)="saveInspection();ref.close()">
          <nb-icon icon="done-all-outline" [options]="{ animation: { type: 'zoom' } }" class="remove-icon">
          </nb-icon>
          Valider
        </button>
      </nb-card-footer>
    </nb-card>
  </div>
</ng-template>
