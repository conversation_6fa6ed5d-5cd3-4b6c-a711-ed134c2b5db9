import { CommonService } from 'src/app/shared/services/common.service';
import { MenuTrackingService } from './menu-tracking.service';
import { Component, OnInit } from '@angular/core';
import { NbDialogRef, NbDialogService, NbToastrService } from '@nebular/theme';
import { Loader } from '@googlemaps/js-api-loader';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'clw-menu-tracking',
  templateUrl: './menu-tracking.component.html',
  styles: [
  ]
})
export class MenuTrackingComponent implements OnInit {
  isLoading: boolean
  categories = [];
  dialogRef: any;
  selectedCategory: any;
  selectedAe: any;
  aelist: any[];
  speed: any;
  time: any

  editDalogueRef: NbDialogRef<any>;
  addDalogueRef: NbDialogRef<any>;
  deleteDalogueRef: NbDialogRef<any>;
  detailDalogueRef: NbDialogRef<any>;

  constructor(
    private dialogSvr: NbDialogService,
    private toastrSvr: NbToastrService,
    private trackingSvr: MenuTrackingService,
    public commonSvr: CommonService
  ) { }

  async ngOnInit(): Promise<void> {
    await this.getAes();
  }

  async getAes(): Promise<any> {
    this.isLoading = true;
    let options = { status: 500 }
    try {
      const { data } = await this.trackingSvr.getAEs(options)
      this.aelist = data;
      this.toastrSvr.success('Bons recuperer avec succès', 'Operation réussi');
    } catch (error) {
      this.toastrSvr.danger('Nous n\'avons pas pu recuperer les Bons', 'Echec De Recuperation')
    } finally {
      this.isLoading = false;
    }
  }

  openAddModal(dialog: any): any {
    this.addDalogueRef = this.dialogSvr.open(dialog, {});
  }

  async addCategory(): Promise<any> {
    try {
      this.isLoading = true;
      this.addDalogueRef.close(true);
      this.toastrSvr.success('Bon ajouté avec succès', 'Ajout réussi');
    } catch (error) {
      this.toastrSvr.danger('Nous n\'avons pas pu ajouter ce Bon', 'Echec d\'ajout')
    } finally {
      this.isLoading = false;
    }
  }

  openEditModal(dialog: any, category: any): any {
    this.selectedCategory = { ...category };
    this.editDalogueRef = this.dialogSvr.open(dialog, {});
  }

  async openLocation(dailog: any, aeData: any): Promise<any> {
    this.isLoading = true;
    try {

      this.dialogSvr.open(dailog, {});
      const data = await this.trackingSvr.getLocation(aeData?.carrier?.truck?.immatriculation);
      this.speed = data.resultat[0].speed;
      let loader = new Loader({
        apiKey: `${environment.mapsKey}`,
        version: "weekly",
      })

      loader.load().then(() => {
        const map = new google.maps.Map(document.getElementById("map") as HTMLElement, {
          center: { lat: data.resultat[0].longitude, lng: data.resultat[0].latitude },
          zoom: 8,
        });

        new google.maps.Marker({
          position: { lat: data.resultat[0].longitude, lng: data.resultat[0].latitude },
          map: map,
        })
      });

    } catch (error) {
      return error;
    } finally {
      this.isLoading = false;
    }
  }

  openDeleteModal(dialog: any, category: any): any {
    this.selectedCategory = { ...category };
    this.deleteDalogueRef = this.dialogSvr.open(dialog, {})
  }


  openDetailModal(dialog: any, ae: any): any {
    this.selectedAe = { ...ae };
    this.detailDalogueRef = this.dialogSvr.open(dialog, {})
  }

  async deleteCategory(): Promise<any> {
    try {
      this.isLoading = true;
      this.deleteDalogueRef.close(true);
      this.toastrSvr.success('Bon edité avec succès', 'Operation réussi');
    } catch (error) {
      this.toastrSvr.danger('Nous n\'avons pas pu editer ce Bon', 'Echec d\'Operation')
    } finally {
      this.isLoading = false;
    }
  }

}
