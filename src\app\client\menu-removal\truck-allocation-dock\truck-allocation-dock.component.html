<div class="common-form-container dock-management-container">
  <div class="header">
    <nb-icon icon="undo-outline" [options]="{ animation: { type: 'zoom' } }" class="remove-icon" status="primary"
      (click)='goTo()'>
    </nb-icon>
    <h1 class="title">Attribution des QUAIS</h1>
  </div>

  <div class="dock-truck-container" cdkDropListGroup>
    <div class="truck">
      <div class="search-bar">
        <div class="input-elmt">
          <input fieldSize="small" id="typeInput" [(ngModel)]="filter.matriculation" [nbAutocomplete]="autoComplete1"
            [placeholder]="filter?.matriculation || 'Saisissez l\'immatriculation du camion'" type="text"
            (ngModelChange)="onModelProductChange($event)" nbInput fullWidth status="primary" />
          <nb-autocomplete #autoComplete1 (selectedChange)="onSelectionProductChange($event)">
            <nb-option (click)='getAuthorizationRemoval()'>Touts les Bons</nb-option>
            <nb-option *ngFor="let option of filteredImmatriculation$ | async" [value]="option">{{option}}</nb-option>
          </nb-autocomplete>
        </div>
        <!-- <nb-form-field>
          <input fullWidth type="text" class="input-elmt" nbInput status="primary" [(ngModel)]="immatriculation"
            (ngModelChange)="onTruckChange($event)" placeholder="Saisissez l'immatriculation du camion">

          <button nbSuffix nbButton ghost>
            <nb-icon icon="search-outline" pack="eva" status="primary"> </nb-icon>
          </button>
        </nb-form-field> -->
      </div>
      <div class="truck-container" [nbSpinner]="isLoading" nbSpinnerStatus="primary" cdkDropList id="truckList"
        #truckList="cdkDropList" [cdkDropListData]="removals" (cdkDropListDropped)="moveTruck($event)">
        <div class="truck-box" [ngStyle]="getColorStylePark(removal)" *ngFor="let removal of removals" cdkDrag>
          <div class="logo">
            <nb-icon icon="car-outline" status="primary" [options]="{ animation: { type: 'zoom' } }"></nb-icon>
          </div>
          <div class="infors">
            <div class="row">
              <div class="key">Numéro AES:</div>
              <div class="value">{{removal?.LoadNumber}}</div>
            </div>
            <div class="row">
              <div class="key"nbTooltip="{{getConcatenatedLabels(removal?.groupedOrders)}}">Produit:</div>
              <div class="value" nbTooltip="{{getConcatenatedLabels(removal?.groupedOrders)}}">{{getConcatenatedLabels(removal?.groupedOrders)| truncateString:15}}</div>
            </div>
            <div class="row">
              <div class="key">Immatriculation:</div>
              <div class="value">{{removal?.carrier?.truckImmatriculation}}</div>
            </div>
            <div class="row">
              <div class="key">Attente</div>
              <div class="value">{{removal?.parkTime? (waitingTime -removal?.parkTime - 3600000 | date:'mediumTime')
                : (waitingTime - 3600000 | date:'mediumTime')}}</div>
            </div>
            <div class="dragPreview" *cdkDragPreview style="padding: 10px 25px;
             background-color: #0B305C;
             font-size: 16px;
             color: #fff;">
              {{removal?.LoadNumber}}
            </div>
          </div>
        </div>
      </div>
    </div>


    <div class="dock">
      <div class="search-bar">
        <div class="text">Liste des QUAIS</div>
        <div class="search-input">
          <div class="filter-elt select">
            <nb-select status="primary" size="small" fullWidth placeholder="Selectionez un quai"
              [(selected)]="selectedDock" (selectedChange)='onDOckChange($event)'>
              <nb-option fieldSize="small">Tous les Quais</nb-option>
              <nb-option fieldSize="small" [value]="dock" *ngFor="let dock of docks">{{ dock?.label}}</nb-option>
            </nb-select>
          </div>
          <!-- <nb-form-field>
            <input fullWidth type="text" class="input-elmt" nbInput status="primary" [(ngModel)]="code"
              (ngModelChange)="onDockChange($event)" placeholder="Saisissez le code du quai">
            <button nbSuffix nbButton ghost>
              <nb-icon icon="search-outline" pack="eva" status="primary"> </nb-icon>
            </button>
          </nb-form-field> -->
        </div>
      </div>
      <div class="dock-container">
        <div class="dock-box" *ngFor="let dock of docks, let i = index">
          <div class="dock-code">{{dock?.label}}</div>
          <div class="truck-box-container" cdkDropList id="dockList" dockAttributeList="cdkDropList"
            [cdkDropListConnectedTo]="[truckList]" [cdkDropListData]="dock?.trucks"
            (cdkDropListDropped)="moveTruck($event, i, dock, dockDialog)">
            <div class="truck-box" [ngStyle]="getColorStyleStatus(truck)" *ngFor="let truck of dock?.trucks" cdkDrag>
              <div class="logo">
                <nb-icon icon="car-outline" status="primary" [options]="{ animation: { type: 'zoom' } }"></nb-icon>
              </div>
              <div class="infors">
                <div class="row">
                  <div class="key" nbTooltip="{{getConcatenatedLabels(truck?.groupedOrders)}}">Produit:</div>
                  <div class="value">{{getConcatenatedLabels(truck?.groupedOrders) | truncateString:15  }}</div>
                </div>
                <div class="row">
                  <div class="key">Numero AES:</div>
                  <div class="value">{{truck?.LoadNumber}}</div>
                </div>
                <!-- <div class="row">
                  <div class="key">Tonnage:</div>
                  <div class="value">{{truck?.QuantityShipped}}</div>
                </div> -->
                <div class="row" [ngStyle]="getColorStyle()">
                  <div class="key">Temps attente:</div>
                  <div class="value">{{ getDurationDate(truck.dockTimeStart) }}</div>
                </div>
              </div>
              <div class="arrow" (click)="openEditDialog(editDialog, truck)">
                <nb-icon icon="arrow-circle-right-outline" status="primary" [options]="{ animation: { type: 'zoom' } }">
                </nb-icon>
              </div>
              <div class="dragPreview" *cdkDragPreview style="padding: 10px 25px;
              background-color: #0B305C;
              font-size: 16px;
              color: #fff;">
                {{truck.LoadNumber}}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- <div class="legend">
    <div class="content" *ngFor="let legend of legends">
      <div class="bullet" [ngStyle]="getLegendStyle(legend?.status)"></div>
      <div class="text">{{legend?.label}}</div>
    </div>
  </div> -->
</div>

<ng-template #editDialog let-data let-ref="dialogRef">
  <nb-card [nbSpinner]="isConfirmLoading" nbSpinnerStatus="primary">
    <nb-card-header class="form--header">Confirmation</nb-card-header>
    <nb-card-body>
      <div class="dialog-assign-content">
        Vous êtes sur le point de sortir le camion immatriculé <strong
          class="important">{{selectedTruck?.immatriculation}} </strong>de l'usine. Veuillez confirmer cette action.
      </div>
    </nb-card-body>
    <nb-card-footer class="form--footer">
      <button nbButton (click)="ref.close(false)" outline ghost status="basic">
        <nb-icon icon="close-square-outline" [options]="{ animation: { type: 'zoom ' } }" class="remove-icon">
        </nb-icon>
        Annuler
      </button>
      <button nbButton (click)="removeToDuck()" outline status="primary">
        <nb-icon icon="done-all-outline" [options]="{ animation: { type: 'zoom ' } }" class="remove-icon">
        </nb-icon>
        Confirmer
      </button>
    </nb-card-footer>
  </nb-card>
</ng-template>

<ng-template #dockDialog let-data let-ref="dialogRef">
  <nb-card>
    <nb-card-header>Attribution de quais</nb-card-header>
    <nb-card-body>
      <div class="edit-container">
        <div class="input">
          <label for="otherTrainingType">Poids initial du camion (KG)</label>
          <input id="typeInput" nbInput status="basic" [min]="0" type="number" [(ngModel)]="initialTruckTonnage"
            style="margin-top: 0.2em;" placeholder="Saisissez le Poids initial du camion" fullWidth />
        </div>
        <!-- <br>
        <div class="input">
          <label for="otherTrainingType">Choississez un quais</label>
          <nb-select fullWidth placeholder="" size="medium" [(selected)]="selectedDock">
            <nb-option [value]="dock" *ngFor="let dock of docks">{{ dock?.label }}
            </nb-option>
          </nb-select>
        </div> -->
      </div>
    </nb-card-body>
    <nb-card-footer>
      <button nbButton ghost status="basic" (click)="ref.close()">
        <nb-icon icon="close-square-outline"></nb-icon>
        Annuler
      </button>
      <button nbButton outline status="success" (click)="ref.close(true)" [disabled]="isLoading" class="btn-border">
        <nb-icon icon="done-all-outline"></nb-icon>
        Confirmer
      </button>
    </nb-card-footer>
  </nb-card>
</ng-template>