import { CommonService } from 'src/app/shared/services/common.service';
import { Component, OnInit, Pipe, PipeTransform, TemplateRef, ViewEncapsulation } from '@angular/core';
import { Router } from '@angular/router';
import { Subscription, interval, of } from 'rxjs';
import { NbDialogRef, NbDialogService, NbToastrService } from '@nebular/theme';
import { RemovalService } from '../removal-management/removal.service';
import { ConfirmDialogComponent } from './confirm-dialog/confirm-dialog.component';
import * as moment from 'moment';
import { StatusRemovals } from 'src/app/shared/models/status-removal.enum';
import { InspectionStatus } from 'src/app/shared/enums/inspection.enum';
import { GroupesOrders } from 'src/app/shared/models/authorization-removal';
import { Order } from 'src/app/shared/models/order';

@Pipe({
  name: 'status'
})
export class StatusRemovalPipe implements PipeTransform {

  transform(status: number, isColor: boolean): string {
    const colors = { 100: 'basic', 200: 'success', 300: 'warning', 400: 'primary' };
    const statuses = { 100: 'Non assignée', 200: 'Validée', 300: 'En attente', 400: 'Chargée' };
    if (isColor) { return colors[status]; }
    return statuses[status];
  }
}

@Component({
  selector: 'clw-truck-allocation-dock',
  templateUrl: './truck-allocation-dock.component.html',
  styles: [
  ],
  encapsulation: ViewEncapsulation.None
})
export class TruckAllocationDockComponent implements OnInit {
  showPassword = true;
  removals: any[];
  docks: any[];
  occupiedCodes: number[] = [];
  dialog: any;
  editDialog: NbDialogRef<any>
  confirmDialogRef: NbDialogRef<any>
  code: number;
  immatriculation: string;
  waitingTime: any;

  isConfirmLoading: boolean;
  isLoading: boolean;
  selectedTruck: any;
  selectedDock: any;
  selectedAe: any;
  currAuthRemoval: any;

  removalsTab: any;
  docksTab: any;

  aeData: any;
  selectedDOck: any;
  dockList: any;
  initialTruckTonnage: number;

  filter = {
    inspection: InspectionStatus.COMPLETED,
    status: StatusRemovals.NEW_PARKING,
    matriculation: ""
  };

  filteredImmatriculation$: any;
  immatriculations = [];
  subscription: Subscription;

  public secondsToDday = 0;
  public minutesToDday = 0;
  public hoursToDday = 0;
  public daysToDday = 0;



  constructor(
    private router: Router,
    protected commonSrv: CommonService,
    private dialogSrv: NbDialogService,
    private removalSrv: RemovalService,
    private toastSrv: NbToastrService,
  ) { }

  async ngOnInit(): Promise<any> {
    // this.filter.matriculation = "123456"
    this.subscription = interval(1000)
      .subscribe(x => {
        this.waitingTime = moment().valueOf();
      });

    setInterval(async () => {
      await this.getAuthorizationRemoval();
    }, 300000);

    await this.getTruckByDock();
    await this.getAvailableDock();
  }


  async getAvailableDock() {
    const query = {
      status: JSON.stringify({ $ne: StatusRemovals.LOADED }),
      dockCode: JSON.stringify({ $in: this.docks?.map(dock => dock?.code) }),
      projection: 'dockCode'
    }
    const { data } = await this.removalSrv.getAuthorizationRemoval({ ...query });

    this.occupiedCodes = data?.map(dock => dock?.dockCode);
  }

  async onTruckChange(value: any): Promise<any> {
    if (!value) { return this.removals = this.removalsTab; }
    this.removals = this.removalsTab;
    this.removals = this.removals.filter(elt => elt?.carrier?.truck?.immatriculation?.toLowerCase()?.includes(value?.toLowerCase()));
  }

  close(close: boolean) {
    this.confirmDialogRef.close(true)
    return close;
  }

  async onDockChange(value: any): Promise<any> {
    if (!value) { return this.docks = this.docksTab; }
    this.docks = this.docksTab;
    this.docks = this.docks.filter(elt => elt.dockCode.toSrting().toLowerCase().includes(value.toSrting().toLowerCase()));
  }

  openEditDialog(dialog: TemplateRef<any>, ae?: any) {
    this.selectedAe = ae;
    this.selectedTruck = ae?.carrier?.truck;
    this.editDialog = this.dialogSrv.open(dialog, {});
    this.editDialog.onClose.subscribe(async (result: boolean) => {
      if (!result) return;
      await this.getAuthorizationRemoval();
      await this.getAvailableDock();
      await this.getTruckByDock();
    })
  }

  async removeToDuck(): Promise<any> {
    this.isConfirmLoading = true;
    try {
      const departureTime = moment().valueOf();
      await this.removalSrv.updateAe(this.selectedAe?._id, { status: 500, departureTime });
      this.toastSrv.success(`Le camion a été sorti de l'usine avec succès`, 'Opération réussie');
      this.editDialog.close(true);
    } catch (error) {
      return error;
    } finally { this.isConfirmLoading = false; }
  }

  async getAuthorizationRemoval(): Promise<any> {
    this.isLoading = true;
    try {
      const [factoryParkAuthRemovals] = await Promise.all([
        await this.getFactoryParkAuthRemoval(), 
        // await this.getNewParkAuthRemoval()
      ]);


      this.removals = [...factoryParkAuthRemovals];
      this.removalsTab = this.removals;
      this.immatriculations = this.removals.map(removal => removal?.carrier?.truck?.immatriculation)
    } catch (error) {
      console.log(error);
      return this.toastSrv.danger(`Erreur de connexion internet`, `Échec operation`)
    } finally { this.isLoading = false; }
  }

  async getFactoryParkAuthRemoval() {
    const { data } = await this.removalSrv.getAuthorizationRemoval({ ...this.filter });
    return data;
  }

  async getNewParkAuthRemoval() {
    const { data } = await this.removalSrv.getAuthorizationRemoval({ ...this.filter, status: StatusRemovals.ON_QUAY });
    return data;
  }

  async getTruckByDock(): Promise<any> {

    this.isLoading = true;
    let options = { dock: this.selectedDock?.label }
    try {
      this.docks = await this.removalSrv.getTruckByDock(options);
      console.log(this.docks);
      

      this.docksTab = [...this.docks];
    } catch (error) {
      console.log(error);
      return this.toastSrv.danger(`Erreur de lors du chargement des quais`, `Echec operation`)
    } finally { this.isLoading = false; }
  }

  goTo() { this.router.navigate(['client/order/list']) }

  async moveTruck(event: any, index?: number, dock?: any, dialog?: TemplateRef<any>): Promise<any> {
    let removal = event.previousContainer.data[event.previousIndex];  
    // Handle cases where dock is occupied
    if (dock?.code && this.occupiedCodes?.includes(dock.code)) {
      return this.toastSrv.warning(`Ce Quai est déjà pris.`, `Quai déjà pris`);
    }
  
    switch (`${event.previousContainer.id}->${event.container.id}`) {
      case 'truckList->dockList':
        if (!(await this.openDialogAndValidate(dialog))) return;
  
        if (!this.initialTruckTonnage || this.initialTruckTonnage < 0) {
          this.initialTruckTonnage = null;
          return this.toastSrv.danger('Veuillez renseigner toutes les données', 'Données incorrectes');
        }
  
        this.removalSrv.selectedTruck = { isAttribution: true, dockCode: dock?.label };
        removal = this.updateRemoval(removal, 450, dock?.code, dock?.label, this.initialTruckTonnage, 'dockTimeStart');
        break;
  
      case 'dockList->truckList':
        this.removalSrv.selectedTruck = { isNotAttribution: true, dockCode: dock?.label };
        removal = this.updateRemoval(removal, 400, 0, null, null, 'parkTime');
        break;
  
      case 'dockList->dockList':
        this.removalSrv.selectedTruck = { isAttribution: true, dockCode: dock?.label };
        removal = this.updateRemoval(removal, null, dock?.code, dock?.label, null, 'parkTime');
        break;
  
      default:
        return; // No action needed for other cases
    }
  
    await this.updateDock(removal?._id, removal);
  }
  
  /** Helper function to open the dialog and validate user input */
  private async openDialogAndValidate(dialog?: TemplateRef<any>): Promise<boolean> {
    if (!dialog) return true;
    this.dialog = this.dialogSrv.open(dialog);
    return new Promise((resolve) => {
      this.dialog.onClose.subscribe((result) => resolve(!!result));
    });
  }
  
  /** Helper function to update the removal object */
  updateRemoval(
    removal: any,
    status: number,
    dockCode: any,
    dockName: any,
    initialTruckTonnage: any,
    timeField: 'dockTimeStart' | 'parkTime'
  ) {
    return {
      ...removal,
      ...(status != undefined && { status }),
      ...(dockCode != undefined && { dockCode }),
      ...(dockName != undefined && { dockName }),
      ...(initialTruckTonnage != undefined && { initialTruckTonnage }),
      ...(timeField && { [timeField]: moment().valueOf() })
    };
  }
  

  async updateDock(id: string, data: { dockCode: number }): Promise<any> {
    this.isLoading = true;
    try {
      console.log('mise a jour de la pesee');
      
      await this.removalSrv.updateAe(id, data);
      await this.getAuthorizationRemoval();
      await this.getTruckByDock();
      await this.getAvailableDock();
      this.initialTruckTonnage = null;
      this.toastSrv.success(`ae assigne a un quai`, `Assignation reussi`)
    } catch (error) {
      return this.toastSrv.danger(`Erreur de connexion internet`, `Echec operation`)
    } finally { this.isLoading = false; }
  }

  updateDockTime(previousTime: any): any {
    let realTime: number;
    setInterval(() => {
      const actualTime = moment().valueOf();
      realTime = actualTime - previousTime - 3600000;
    }, 1000)
    return realTime;
  }

  openConfirmDialog(id: string, updateData?: any, sens?: string) {
    this.dialog = this.dialogSrv.open(ConfirmDialogComponent, {})
    this.dialog.onClose.subscribe(async (result: boolean): Promise<any> => {
      if (!result) { return; }
      if (result) {
        this.isLoading = true;
        try {
          await this.removalSrv.updateAe(id, updateData);
          this.toastSrv.success(`Camion déplacée avec succès`, 'Opération réussie')
          await this.getAuthorizationRemoval();
          await this.getTruckByDock();
          if (sens === 'truckToDock') {
            // const index = this.removals.findIndex((elt: any) => elt._id === id || elt?.removalId === id);
            // this.removals.splice(index, 1);
            this.removals = this.removals.filter((elt: any) => elt?._id !== id || elt?.removalId !== id);
          }
        } catch (error) {
          return this.toastSrv.danger(`une erreur c'est produite lors de l'assignation de l'AE`, 'Echec assignation')
        } finally { this.isLoading = false; this.removalSrv.selectedTruck = null; }
      }
    })
  }

  getLegendStyle(status: number): any {
    const colors = { 100: 'rgb(151, 156, 164)', 200: '#00d68f', 300: '#ffaa00', 400: '#0B305C' };
    const color = colors[status] || '#edf1f7'
    return {
      "background-color": `${color}`,
    }
  }


  getConcatenatedLabels(groupedOrders: GroupesOrders): string {
    return groupedOrders?.map((order: Order) =>
      order?.cart?.items
        .filter(item => item?.quantityDelivery > 0)
        .map(item => `${item?.product?.label} (${item?.packaging?.label})`)
        .join(', ')
    ).join(', ') || 'N/A';
  }

  getDurationDate(startDate: number) {
    const currentTime = Date.now();
    const diff = currentTime - startDate;
    const seconds = Math.floor(diff / 1000) % 60;
    const minutes = Math.floor(diff / (1000 * 60)) % 60;
    const hours = Math.floor(diff / (1000 * 60 * 60)) % 24;
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));

    return `${days} jours(s) ${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    if (this.hoursToDday === 23 && this.minutesToDday === 59 && this.secondsToDday === 59) { this.daysToDday += 1; this.hoursToDday = 0 }
  }

  getColorStyle(): any {
    // console.log(this.minutesToDday)
    if (this.minutesToDday >= 2) {
      return {
        color: '#E83742',
      }
    }
    if (this.minutesToDday === 1 && this.minutesToDday < 3) {
      return {
        color: '#F08D18',
      }
    }
  }

  getBorderStyle(): any {
    if (this.minutesToDday >= 2) {
      return {
        border: '1px solid #E83742',
      }
    }
    if (this.minutesToDday === 1 && this.minutesToDday < 3) {
      return {
        border: '1px solid #F08D18',
      }
    }
  }

  getColorStyleStatus(waiting: any): any {
    const hours = 7200001; // 5400001 equal 1h30 min
    const minutes = 5400001;
    const waitingTime = moment(this.waitingTime).diff(moment(waiting.dockTimeStart));

    if (waitingTime >= hours) {
      return {
        color: '#ff304f',
        // 'background-color': 'rgb(58 56 56)',
        border: '1px solid #ff304f'
      };
    }
    if (waitingTime >= minutes && waitingTime < hours) {
      return {
        color: '#ff304f',
        // 'background-color': '#713045',
        border: '1px solid #ff304f'
      }
    }

    return {
      color: 'black',
      // 'background-color': 'rgb(58, 56, 56)',
      // border: '1px solid black'
    }

  }
  async onDOckChange(event) {
    this.selectedDOck = event;
    await this.getTruckByDock();
  }

  getColorStylePark(waiting: any): any {
    const hours = 5400001; // 5400001 equal 1h30 min
    const minutes = 1800001; // 5400001 equal 30 min
    const waitingTime = moment(this.waitingTime).diff(moment(waiting.parkTime));

    if (waitingTime >= hours) {
      return {
        color: '#ff304f',
        // 'background-color': 'rgb(58 56 56)',
        border: '1px solid #ff304f'
      };
    }
    if (waitingTime >= minutes && waitingTime < hours) {
      return {
        color: '#ff304f',
        // 'background-color': '#713045',
        border: '1px solid #ff304f'
      }
    }

    return {
      color: 'black',
      // 'background-color': 'rgb(58, 56, 56)',
      // border: '1px solid black'
    }

  }


  //filter by products section
  private filterProduct(value: string): string[] | void {
    if (value) {
      const filterValue = `${value?.toLowerCase()}`;
      return this.immatriculations.filter(optionValue => optionValue?.toLowerCase().includes(filterValue)).sort((a, b) => {
        return a.toString().toLowerCase().indexOf(filterValue?.toLowerCase()) - b.toString().toLowerCase().indexOf(filterValue?.toLowerCase());
      });;
    }
  }

  onModelProductChange(value: string): void { if (value) this.filteredImmatriculation$ = of(this.filterProduct(value)); }

  async onSelectionProductChange($event: any): Promise<void> {
    this.filter.matriculation = $event;
    await this.getAuthorizationRemoval();
    this.filteredImmatriculation$ = of(this.immatriculations);
  }
}
