export declare type CartItem = {
    product?: Partial<Product>;
    packaging?: Partial<Packaging>;
    quantity?: number;
    unitPrice?: number;
  };

  export class Product {
    _id?: string;
    label: string;
    image: string;
    normLabel: string;
    erpRef: string;
    description: string;
    advantages?: string[];
    cautions?: string[];
    cimencamAdvantages?: string[];
    enable?: boolean;
    create_at?: number;
    fidelityPoint?: number;
    packagings: Packaging;
  }

  export class Packaging {
    _id?: string;
    label?: string;
    unit?: Unit;
    created_at?: number;
    enable?: boolean;
  
  }

  export class Unit {
    _id?: string;
    label?: string;
    enable?: boolean;
    symbol: string;
    value: number;
    ratioToTone: number;
    created_at?: number;
    create_at?: number;
  }