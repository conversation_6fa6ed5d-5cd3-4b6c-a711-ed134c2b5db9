<nb-card [nbSpinner]="isLoading" nbSpinnerStatus="primary">
  <nb-card-header class="form--header">Confirmation</nb-card-header>
  <nb-card-body>
    <div class="dialog-assign-content" *ngIf="data?.oldStatus === 100 && data?.newStatus === 300">
      <div class="form-group group">
        Vous êtes sur le point d'attribuer le Bon N°<strong class="important">{{data?.LoadNumber}}</strong> au
        transporteur <strong class="important">{{data?.label}}</strong>.<br /> Vous avez la possibilté de modifier les
        dates extimatives d'enlèvement et de livraison avant de confirmer cette action.
      </div>
      <div class="form-group">
        <label class="label" for="nameInput">Date d'enlèvèment</label><br />
        <input nbInput [nbDatepicker]="datepicker" class="carrier-name input-width"
          placeholder="{{ data?.DateRequestedShipment || 'Veuillez choisir une date'}}" [(ngModel)]='shippmentDate'>
        <nb-datepicker #datepicker [min]="minDate"></nb-datepicker>
      </div><br />
      <div class="form-group">
        <label class="label" for="nameInput">Date de livraison</label><br />
        <input nbInput [nbDatepicker]="datepicker1" class="carrier-name input-width"
          placeholder="{{ data?.PromisedDeliveryDate  || 'Veuillez choisir une date'}}" [(ngModel)]="deliveryDate">
        <nb-datepicker #datepicker1 [min]="shippmentDate"></nb-datepicker>
      </div>
      <div class="form-group">
        <label class="label" for="nameInput">Lieu de livraison</label><br />
        <input nbInput [(ngModel)]="shippement" type="text" class="carrier-name input-width"
          placeholder="{{ data?.ShipToDescription  || 'Saisir une destination'}}">
      </div>
    </div>

    <div class="dialog-assign-content" *ngIf="data?.oldStatus === 300 && data?.newStatus === 300">
      Vous êtes sur le point de déplacer le Bon N°<strong class="important">{{data?.LoadNumber}}</strong> du
      transporteur
      <strong class="important">{{data?.label}}</strong> vers le
      transporteur <strong class="important">{{data?.newCarrierLabel}}</strong>. Veuillez confirmer cette action.
    </div>

    <div class="dialog-assign-content" *ngIf="data?.oldStatus === 300 && data?.newStatus === 100">
      Vous êtes sur le point de rétirer le Bon N°<strong class="important">{{data?.LoadNumber}}</strong> au
      transporteur <strong class="important">{{data?.label}}</strong>. Veuillez confirmer cette action.
    </div>

    <div class="dialog-assign-content" *ngIf="selectedTruck">
      Vous êtes sur le point d'attribuer un camion au Quai <strong
        class="important">{{selectedTruck?.dockCode}}</strong>. Veuillez confirmer cette action.
    </div>
  </nb-card-body>
  <nb-card-footer class="form--footer">
    <button nbButton (click)="close(false)" outline ghost status="basic">
      <nb-icon icon="close-square-outline" [options]="{ animation: { type: 'zoom ' } }" class="remove-icon">
      </nb-icon>
      Annuler
    </button>
    <button nbButton (click)="close(true)" outline status="primary">
      <nb-icon icon="done-all-outline" [options]="{ animation: { type: 'zoom ' } }" class="remove-icon">
      </nb-icon>
      Confirmer
    </button>
  </nb-card-footer>
</nb-card>