<div class="tile-sales-container">
  <!-- <nb-card accent="success" nbSpinnerStatus="success" [nbSpinner]="isLoading" nbSpinnerMessage="Chargement des données">
    <nb-card-header class="card-header-wrapper">
      <div class="card-header">
        Nombre d'inspections
      </div>
    </nb-card-header>
    <nb-card-body>
      <div class="progress-info">
        <div class="subtitle">Inpections acceptées :
          <span class="h4">{{dataInspection?.nbStatusOk}}</span>
        </div>
        <hr color="#f7f9fc" />
      </div>
      <div class="progress-info">
        <div class="subtitle">Inpections réjetées :
          <span class="h4">{{dataInspection?.nbStatusNotOk}}</span>
        </div>
        <hr color="#f7f9fc" />
      </div>

      <div class="progress-info">
        Temps moyen d'attente
      </div>
      <div class="progress-info" *ngIf="parkTimes">
        <div class="subtitle">Nouveau parking : </div>
        <div class="h4">{{ millisecondsToTime(parkTimes?.averageNewTimes ?? 0) }}</div>
        <div class="subtitle">Parking usine : </div>
        <div class="h4">{{ millisecondsToTime(parkTimes?.averageFactoryTimes ?? 0) }}</div>
        <div class="subtitle">Chargement usine : </div>
        <div class="h4">{{ millisecondsToTime(parkTimes?.averageDockTimes ?? 0) }}</div>
        <div class="subtitle">Total : </div>
        <div class="h4">{{ millisecondsToTime((parkTimes?.averageDockTimes ?? 0) + (parkTimes?.averageFactoryTimes ?? 0) + (parkTimes?.averageNewTimes ?? 0)) }}</div>
      </div>
    </nb-card-body>

  </nb-card> -->
  <nb-card accent="success" nbSpinnerStatus="success" [nbSpinner]="isLoading" nbSpinnerMessage="Chargement des données">
    <nb-card-header>
      Inspections
    </nb-card-header>
    <nb-card-body class="body-inspection">
      <div class="inspection-nber-block">
        <div class="inspection-subtitle">
          Nombre D'inspections
        </div>
        <div class="ae-data">
          <div class="ae-title">
            <div class="ae icon" [ngClass]="{'success':true }">
              <nb-icon icon='file-text-outline' status="success"></nb-icon>
            </div>
            <div class="ae-label">Inspections <br>acceptées
            </div>
          </div>
          <div class="ae-title ae-center">
            <div class="ae-label ae-value">{{dataInspection?.nbStatusOk}}</div>
          </div>
        </div>
        <div class="ae-data">
          <div class="ae-title">
            <div class="ae icon" [ngClass]="{'danger':true }">
              <nb-icon icon='file-text-outline' status="danger"></nb-icon>
            </div>
            <div class="ae-label">Inspections<br> rejetées
            </div>
          </div>
          <div class="ae-title ae-center">
            <div class="ae-label ae-value">{{dataInspection?.nbStatusNotOk}}</div>
          </div>
        </div>
      </div>

      <div class="medium-time-block">
        <div class="inspection-subtitle">
          Temps moyen d’attente
        </div>

        <div class="inspection-blocks">
          <div class="inspection-nber-block">
            <!-- <div class="ae-data">
              <div class="ae-title">
                <div class="ae icon" [ngClass]="{'danger':true }">
                  <nb-icon icon='file-text-outline' status="danger"></nb-icon>
                </div>
                <div class="ae-label">Parking Usine
                </div>
              </div>
              <div class="ae-title ae-center">
                <div class="ae-label ae-value">{{ millisecondsToTime(parkTimes?.averageFactoryTimes ?? 0) }}</div>
              </div>
            </div> -->
            <div class="ae-data">
              <div class="ae-title">
                <div class="ae icon" [ngClass]="{'danger':true }">
                  <nb-icon icon='file-text-outline' status="danger"></nb-icon>
                </div>
                <div class="ae-label">Parking
                </div>
              </div>
              <div class="ae-title ae-center">
                <div class="ae-label ae-value">{{ millisecondsToTime(parkTimes?.averageNewTimes ?? 0) }}</div>
              </div>
            </div>
            <div class="ae-data">
              <div class="ae-title">
                <div class="ae icon" [ngClass]="{'danger':true }">
                  <nb-icon icon='file-text-outline' status="danger"></nb-icon>
                </div>
                <div class="ae-label">Chargement<br> usine
                </div>
              </div>
              <div class="ae-title ae-center">
                <div class="ae-label ae-value">{{ millisecondsToTime(parkTimes?.averageDockTimes ?? 0) }}</div>
              </div>
            </div>
          </div>

          <div class="inspection-nber-block">
            <div class="ae-data">
              <div class="ae-title">
                <div class="ae icon" [ngClass]="{'danger':true }">
                  <nb-icon icon='file-text-outline' status="danger"></nb-icon>
                </div>
                <div class="ae-label">Total
                </div>
              </div>
              <div class="ae-title ae-center">
                <div class="ae-label ae-value">{{ millisecondsToTime((parkTimes?.averageDockTimes ?? 0) +
                  (parkTimes?.averageFactoryTimes ?? 0) + (parkTimes?.averageNewTimes ?? 0)) }}</div>
              </div>
            </div>
          </div>

        </div>

      </div>

      <div class="response-time-block">
        <div class="inspection-subtitle">
          Temps moyen
        </div>
        <div class="ae-data">
          <div class="ae-title">
            <div class="ae icon" [ngClass]="{'info':true }">
              <nb-icon icon='file-text-outline' status="info"></nb-icon>
            </div>
            <div class="ae-label">En <br>Panne
            </div>
          </div>
          <div class="ae-title ae-center">
            <div class="ae-label ae-value">{{millisecondsToTime(parkTimes?.averageFailureTimes?? 0)}}</div>
          </div>
        </div>
        <div class="ae-data">
          <div class="ae-title">
            <div class="ae icon" [ngClass]="{'info':true }">
              <nb-icon icon='file-text-outline' status="info"></nb-icon>
            </div>
            <div class="ae-label">Temps de <br> Réponse
            </div>
          </div>
          <div class="ae-title ae-center">
            <div class="ae-label ae-value">{{millisecondsToTime(avgResponseTime?.overallAverageTime ?? 0)}}</div>
          </div>
        </div>
      </div>

    </nb-card-body>
  </nb-card>
</div>