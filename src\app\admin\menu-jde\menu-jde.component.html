<div class="common-form-container packaging-container">
  <div class="header">
    <nb-icon icon="undo-outline" (click)="goBack()" [options]="{ animation: { type: 'zoom' } }" class="remove-icon"
      status="primary">
    </nb-icon>
    <h1 class="title">X3 ID</h1>
  </div>

  <div class="filter-container">
    <div class="left-block">
    </div>
    <div class="btn-contain">
      <button nbButton status="success" class="search-btn" size="small" (click)="openAddModal(addDialog)">
        <nb-icon icon="plus-square-outline"></nb-icon>
        Ajouter un N° X3
      </button>
    </div>
  </div>


  <nb-card class="card-container" [nbSpinner]="isLoading" nbSpinnerStatus="primary"
    nbSpinnerMessage="Chargement des données" nbSpinnerStatus="primary">
    <nb-card-header>
      <nb-list class="element-head  scrool-style">
        <nb-list-item class="list-elt-header list-elt paginator no-border">
          <div class="col col-label">Libellé Depôt</div>
          <div class="col col-base">Reférence</div>
          <div class="col col-unit">Libellé Produit</div>
          <div class="col col-code">Code Produit</div>
          <div class="col col-ton">Valeur X3</div>
          <div class="col col-actions">
            <div class="actions">
            </div>
          </div>
        </nb-list-item>
      </nb-list>

    </nb-card-header>
    <nb-card-body>
      <nb-list class="element-head  scrool-style">
        <nb-list-item class="list-elt-header list-elt" *ngFor="let erpItemId of erpItemsIds">
          <div class="col col-label">{{erpItemId?.storeLabel}}</div>
          <div class="col col-code">{{erpItemId?.productErpRef}}</div>
          <div class="col col-unit">{{erpItemId?.productLabel}}</div>
          <div class="col col-base">{{erpItemId?.storeReference}}</div>
          <div class="col col-ton">{{erpItemId?.value}}</div>

          <div class="col col-actions">
            <button nbTooltip="Editer" nbTooltipPlacement="top" status="basic"
              (click)="openEditModal(editdialog, erpItemId)">
              <nb-icon icon="edit-2-outline" status="warning" [options]="{ animation: { type: 'zoom' } }">
              </nb-icon>
            </button>
            <button nbTooltip="Supprimer" nbTooltipPlacement="top" status="basic"
              (click)="openDeleteModal(deleteDialog, erpItemId)">
              <nb-icon icon="trash-2-outline" status="danger" [options]="{ animation: { type: 'zoom' } }">
              </nb-icon>
            </button>
          </div>
        </nb-list-item>
        <nb-list-item class="not-found" *ngIf="erpItemsIds.length===0">
          <img src="../../../../assets/images/EMPTY FOLDER VECTOR.png" alt="">
          <h1>
            Aucune donnée trouvée
          </h1>
        </nb-list-item>
      </nb-list>
    </nb-card-body>
  </nb-card>
</div>

<ng-template #addDialog let-data let-ref="dialogRef" class="dialogRef">
  <div class="add-X3">
    <nb-card [nbSpinner]="isConfirmLoading" nbSpinnerStatus="primary" nbSpinnerMessage="">
      <nb-card-header>Ajouter une référence X3</nb-card-header>
      <nb-card-body>
        <div class="process-option">
          <label>Sélectionner un store</label>
          <nb-select name="id" id="id" [disabled]="stores?.length===0" [(selected)]='selectedStore'
            (selectedChange)="selectStore($event)">
            <nb-option *ngFor="let store of stores" status='primary' [value]="store">{{store?.label||'inconnu'}}
            </nb-option>
          </nb-select>
        </div>
        <div class="process-option">
          <label>Sélectionner un type de produit</label>
          <nb-select name="id" id="id" [disabled]="productCategories?.length===0" [(selected)]="selectedCategory"
            (selectedChange)="selectCategory($event)">
            <nb-option *ngFor="let category of productCategories" [value]="category">
              {{category?.label||'inconnu'}}</nb-option>
          </nb-select>
        </div>
        <div class="process-option">
          <label>Sélectionner un packaging</label>
          <nb-select name="id" id="id" [disabled]="packagings?.length===0" [(selected)]="selectedPackaging"
            (selectedChange)="selectPackaging($event)">
            <nb-option *ngFor="let packaging of packagings" [value]="packaging"> {{packaging?.label||'inconnu'}}
            </nb-option>
          </nb-select>
        </div>
        <div class="process-option">
          <label>Sélectionner un produit</label>
          <nb-select name="id" id="id" [disabled]="products?.length===0" [(selected)]="selectedProduct"
            (selectedChange)="selectProduct($event)">
            <nb-option *ngFor="let product of products" [value]="product">{{product?.label||'inconnu'}}
            </nb-option>
          </nb-select>
        </div>
        <div class="process-option">
          <label for="">X3 ID</label>
          <input nbInput type="number" placeholder="Veuillez entrer votre le N° X3" [(ngModel)]="erpItemId.value">
        </div>

      </nb-card-body>
      <nb-card-footer>
        <button nbButton status="basic" class="margin-btn" ghost (click)="ref.close()">
          <nb-icon icon="close-square-outline"></nb-icon>
          Annuler
        </button>
        <button nbButton status="primary" outline (click)="insertJdeID()">
          <nb-icon icon="save-outline"></nb-icon>
          Enregistrer
        </button>
      </nb-card-footer>
    </nb-card>
  </div>
</ng-template>



<ng-template #editdialog let-data let-ref="dialogRef">
  <div [nbSpinner]="isConfirmLoading" nbSpinnerStatus="primary" nbSpinnerMessage="" class="add-jde">
    <nb-card>
      <nb-card-header>Modifier les informations</nb-card-header>
      <nb-card-body>
        <div class="process-option">
          <label>Sélectionner un store</label>
          <nb-select name="id" id="id" [disabled]="stores?.length===0" [(selected)]='selectedStore'
            (selectedChange)="selectStore($event)" placeholder="{{erpItemId?.storeLabel|| 'Non renseigner'}}">
            <nb-option *ngFor="let store of stores" status='primary' [value]="store">{{store?.label}}
            </nb-option>
          </nb-select>
        </div>
        <div class="process-option">
          <label>Sélectionner un type de produit</label>
          <nb-select name="id" id="id" [disabled]="productCategories?.length===0" [(selected)]="selectedCategory"
            (selectedChange)="selectCategory($event)" placeholder="{{erpItemId?.categoryLabel|| 'Non renseigner'}}">
            <nb-option *ngFor="let category of productCategories" [value]="category">{{category?.label}}
            </nb-option>
          </nb-select>
        </div>
        <div class="process-option">
          <label>Sélectionner un packaging</label>
          <nb-select name="id" id="id" [disabled]="packagings?.length===0" [(selected)]="selectedPackaging"
            (selectedChange)="selectPackaging($event)" placeholder="{{erpItemId?.packagingLabel|| 'Non renseigner'}}">
            <nb-option *ngFor="let packaging of packagings" [value]="packaging"> {{packaging?.label}}
            </nb-option>
          </nb-select>
        </div>
        <div class="process-option">
          <label>Sélectionner un produit</label>
          <nb-select name="id" id="id" [disabled]="products?.length===0" [(selected)]="selectedProduct"
            (selectedChange)="selectProduct($event)" placeholder="{{erpItemId?.productLabel|| 'Non renseigner'}}">
            <nb-option *ngFor="let product of products" [value]="product">{{product?.label}}
            </nb-option>
          </nb-select>
        </div>
        <div class="process-option">
          <label for="">X3 ID</label>
          <input nbInput type="number" placeholder="Veuillez entrer le N° X3" [(ngModel)]="erpItemId.value">
        </div>

      </nb-card-body>
      <nb-card-footer>
        <button nbButton status="basic" class="margin-btn" ghost (click)="ref.close()">
          <nb-icon icon="close-square-outline"></nb-icon>
          Annuler
        </button>
        <button nbButton status="primary" outline (click)="updateJdeId()">
          <nb-icon icon="save-outline"></nb-icon>
          Enregistrer
        </button>
      </nb-card-footer>
    </nb-card>
  </div>
</ng-template>

<ng-template #deleteDialog let-data let-ref="dialogRef" class="dialogRef">

  <nb-card [nbSpinner]="isConfirmLoading" nbSpinnerStatus="primary" nbSpinnerMessage="">
    <nb-card-header class="form--header">Supprimer le X3 ID</nb-card-header>
    <nb-card-body>
      <div> Etes-vous sûr de vouloir supprimer l'ID X3 {{erpItemId?.value }}?</div>
    </nb-card-body>
    <nb-card-footer class="form--footer">
      <button nbButton outline ghost status="basic" class="" [disabled]="isLoading" (click)='ref.close()'>
        <nb-icon icon="close-square-outline" [options]="{ animation: { type: 'zoom' } }" class="remove-icon">
        </nb-icon>
        Annuler
      </button>
      <button nbButton outline status="danger" class="" [disabled]="isLoading" (click)='deleteJdeId()'>
        <nb-icon icon="trash-2-outline" [options]="{ animation: { type: 'zoom' } }" class="remove-icon">
        </nb-icon>
        Supprimer
      </button>
    </nb-card-footer>
  </nb-card>

</ng-template>