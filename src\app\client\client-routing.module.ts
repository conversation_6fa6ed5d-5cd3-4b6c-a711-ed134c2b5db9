import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

const routes: Routes = [
  { path: '', redirectTo: 'order', pathMatch: 'full' },
  {
    path: 'order',
    loadChildren: () => import('./menu-order/menu-order.module').then(m => m.MenuOrderModule)
  },
  {
    path: 'removal',
    loadChildren: () => import('./menu-removal/menu-removal.module').then(m => m.MenuRemovalModule)
  },
  {
    path: 'ressources',
    loadChildren: () => import('./menu-tracking/menu-tracking.module').then(m => m.MenuTrackingModule)
  },
  // {
  //   path: 'brokenDownTrucks',
  //   loadChildren: () => import('./broken-down-trucks/broken-down-trucks.module').then(m => m.BrokenDownTrucksModule)
  // },
  // {
  //   path: 'call-screen', loadChildren: () => import('./call-screen/call-screen.module').then(m => m.CallScreenModule)
  // },

];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class ClientRoutingModule { }
