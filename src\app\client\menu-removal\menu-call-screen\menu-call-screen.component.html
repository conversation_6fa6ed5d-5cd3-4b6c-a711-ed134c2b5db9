<div class="menu-waiting-thread-container">
  <div class="header">
    <!--   <nb-icon icon="undo-outline" [options]="{ animation: { type: 'zoom' } }" class="remove-icon" status="primary">
    </nb-icon> -->
    <h1 class="title" style="color: #fff;
    display: flex;
    justify-content: center;
    margin: 0;">Ecran d'appel - {{status=='400'?"Parking usine":status=='700'?"Nouveau parking":"Chargement usine"}}
    </h1>
  </div>

  <div class="removal-container">
    <!-- <div class="filter-area">
      <div class="left-area">
        <div class="filter-label">Filtrer par</div>
        <input type="text" placeholder="N° BC" fieldSize="small" nbInput class="empty-input height" />
        <input type="text" placeholder="Date de debut" nbInput fieldSize="small" class="empty-input height" />
        <input type="text" placeholder="Date de fin" nbInput fieldSize="small" class="empty-input height" />
      </div>
      <div class="right-area">
        <button nbButton status="success" class="search-btn">
          <nb-icon icon="search-outline"></nb-icon>
          RECHERCHER
        </button>
      </div>
    </div> -->
    <nb-card class="color-card no-radius">
      <nb-list class="element">
        <nb-list-item class="list-elt-header paginator">
          <nb-icon icon="undo-outline" (click)="goBack()" [options]="{ animation: { type: 'zoom' } }"
            nbTooltip="Page d'accueil" class="remove-icon" status="basic">
          </nb-icon>
          <div class="col col-paginator">
            {{ 1 }} - {{ 10 }} sur {{ 250 }}
            <nb-icon icon="arrow-ios-back-outline" [options]="{ animation: { type: 'zoom' } }"
              nbTooltip="Page précédente" nbTooltipPlacement="bottom" nbTooltipStatus="default" status="success">
            </nb-icon>
            <nb-icon icon="arrow-ios-forward-outline" [options]="{ animation: { type: 'zoom' } }"
              nbTooltip="Page suivante" nbTooltipPlacement="bottom" nbTooltipStatus="default" status="success">
            </nb-icon>
          </div>
        </nb-list-item>
        <nb-list-item class="list-elt-header color-text-title paginator">
          <div class="col col-num">N°</div>
          <div class="col col-name">Conducteur</div>
          <div class="col col-ae-ref">N° Bon</div>
          <div class="col col-name">Client</div>
          <div class="col col-item">Produit</div>
          <!-- <div class="col col-ae-ref">N° quais</div> -->
          <!-- <div class="col col-item">Camion</div> -->
          <div class="col col-ae-ref">Quais</div>
          <div class="col col-truck-type">Type de Camion</div>
          <div class="col col-qty">Quantité</div>
          <div class="col col-time">Temps d'attente</div>
          <div class="col col-immatriculation">Immatriculation</div>
          <div class="col col-entry-weight">Pesé en entrer</div>
          <div class="col col-status center">Statut</div>
        </nb-list-item>
      </nb-list>
      <nb-list class="element  scrool-style" [nbSpinner]="isLoading" nbSpinnerStatus="primary">
        <nb-list-item class="list-elt-header color-text-list-item " *ngFor="let waiting of waitingThreadTab; index as i"
          [ngClass]="{ 'blink': waiting.moved && waiting.moved.from == status }">
          <div class="col col-num">{{i+1}}</div>
          <div class="col col-name">{{waiting?.carrier?.driver?.fullname || 'Non renseigner' |truncateString:20 }}</div>
          <div class="col col-ae-ref">{{waiting?.LoadNumber}}</div>
          <div class="col col-name" title="{{ waiting?.soldto?.name || waiting?.SoldToDescription }}">
            {{(waiting?.soldto?.name || waiting?.SoldToDescription) |truncateString:35}}</div>
          <div class="col col-item">{{getConcatenatedLabels(waiting?.ItemNumber)}}</div>
          <!-- <div class="col col-ae-ref">{{waiting?.carrier?.truck?.dock?.label}}</div> -->
          <!-- <div class="col col-item">{{waiting?.carrier?.truck?.type |truncateString:15}}</div> -->
          <div class="col col-ae-ref">{{waiting?.dockCode | getDock: docks}}</div>
          <div class="col col-truck-type">{{commonSvr.truncateString(waiting?.carrier?.truck?.type, 19)
            || 'Non renseigner'}}</div>

          <div class="col col-qty">{{waiting?.TransactionQuantity + 'T'}}</div>
          <div class="col col-time" *ngIf="!waiting.dockCode || waiting.dockCode === 0">
            <p
              [ngStyle]="getColorStyle(waiting.moved && waiting.moved.from == status ? waiting[getParkTimeKey(+status)].at(-1)?.entry : waiting?.parkTime)">
              {{getDurationDate(waiting.moved && waiting.moved.from == status ?
              waiting[getParkTimeKey(+status)]?.at(-1)?.entry : waiting?.parkTime) }}
            </p>
          </div>
          <div class="col col-time" *ngIf="waiting.dockCode && waiting.dockCode !== 0">
            <p [ngStyle]="getColorDOckStyle(waiting?.dockTimeStart)">{{getDurationDate(waiting?.dockTimeStart)}}</p>
          </div>
          <div class="col col-immatriculation">{{waiting?.carrier?.truck?.immatriculation ||
            waiting?.carrier?.truckImmatriculation || 'Non renseigner' }}</div>
          <div class="col col-entry-weight">  {{ waiting?.initialTruckTonnage ? waiting.initialTruckTonnage + 'KG' : 'Non renseigner' }}</div>
          <div class="col col-status" *ngIf="!waiting.dockCode || waiting.dockCode === 0">
            <!-- <nb-badge text="OK" class="badge" position="top start" status="success"></nb-badge> -->
            <nb-badge
              [ngStyle]="getColorStyleStatus(waiting.moved && waiting.moved.from == status ? waiting[getParkTimeKey(+status)].at(-1)?.entry : waiting?.parkTime)"
              text="EN ATTENTE" class="badge" position="top start">
            </nb-badge>
          </div>
          <div class="col col-status" *ngIf="waiting.dockCode && waiting.dockCode !== 0">
            <!-- <nb-badge text="OK" class="badge" position="top start" status="success"></nb-badge> -->
            <nb-badge [ngStyle]="getColorStyleStatus(waiting.dockTimeStart)" text="EN CHARGEMENT" class="badge"
              position="top start">
            </nb-badge>
          </div>
        </nb-list-item>
        <nb-list-item class="list-elt-header" *ngIf="waitingThreadTab?.length == 0 || !waitingThreadTab ">
          <p class="colored">Aucune Entrée pour l'instant</p>
        </nb-list-item>
      </nb-list>
    </nb-card>
  </div>
</div>