import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { InspectionsRoutingModule } from './inspections-routing.module';
import { InspectionsComponent } from './inspections.component';
import { FormsModule } from '@angular/forms';
import { NbIconModule, NbListModule, NbCardModule, NbInputModule, NbBadgeModule, NbTooltipModule, NbTabsetModule, NbSelectModule, NbSpinnerModule, NbDatepickerModule, NbAutocompleteModule, NbButtonModule, NbToggleModule, NbLayoutModule, NbMenuModule, NbActionsModule, NbContextMenuModule } from '@nebular/theme';
import { SharedModule } from 'src/app/shared/shared.module';
import { TruckInspectionComponent } from './truck-inspection/truck-inspection.component';


@NgModule({
  declarations: [
    InspectionsComponent,
    TruckInspectionComponent
  ],
  imports: [
    FormsModule,
    CommonModule,
    NbIconModule,
    NbListModule,
    SharedModule,
    NbLayoutModule,
    NbCardModule,
    NbInputModule,
    NbBadgeModule,
    NbTabsetModule,
    NbSelectModule,
    NbButtonModule,
    NbToggleModule,
    NbSpinnerModule,
    NbTooltipModule,
    NbMenuModule.forRoot(),
    NbActionsModule,
    NbContextMenuModule,
    NbDatepickerModule,
    NbAutocompleteModule,
    InspectionsRoutingModule
  ]
})
export class InspectionsModule { }
