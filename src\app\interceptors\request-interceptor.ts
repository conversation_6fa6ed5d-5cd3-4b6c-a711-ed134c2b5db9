import { HttpInterceptor, HttpRequest, HttpHandler, HttpEvent, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { StorageService } from '../shared/services/storage.service';

@Injectable()
export class RequestInterceptor implements HttpInterceptor {

    constructor(private storageSrv: StorageService) { }

    intercept(req: HttpRequest<any>, next: <PERSON>ttpHandler): Observable<HttpEvent<any>> {
        let params = req?.params ?? new HttpParams();
        const urls = [
            'removal',
            'statistics',
            'inspections-datas'
        ]

        // if ((req?.url.includes('removal') || req?.url.includes('statistics') || req?.url.includes('inspections-datas')) && req.method == 'GET' && this.storageSrv.curentFactory != 'reset') {
        //     console.log(req.url);

        //     // urls.includes(req?.url)
        //     if (this.storageSrv.curentFactory !== 'CM00000') {

        //         params = req.params.append('BusinessUnit', this.storageSrv.curentFactory)
        //     }
        // }

        // Allow CORS for all
        let headers = req.headers.append('Access-Control-Allow-Origin', '*');

        // Add authorization token in header
        try {
            const oauth = this.storageSrv.getObject('oauth');
            if (!oauth) { throw new Error('empty oauth'); }
            headers = headers.append('Authorization', `Bearer ${oauth.access_token}`);
        } catch (error) { }

        return next.handle(req.clone({ headers, params }));
    }
}
