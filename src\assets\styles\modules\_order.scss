.order-container {
  .cards {
    display: flex;
    justify-content: space-between;
    nb-card {
      width: 48%;
      height: 550px;
      .card-container {
        padding: 3%;

        .header-title {
          display: flex;
          justify-content: space-between;
          padding: 1rem 1.5rem;

          .button {
            display: flex;
            align-items: center;

            button {
              min-width: 100px;
              display: flex;
              justify-content: space-evenly;
              font-size: 14px;
            }
          }
        }
      }
    }
  }

  .no-border {
    border: none !important;
  }
  .nothing-container {
    display: flex;
    justify-content: center;
  }
  .types-order {
    display: flex;
    justify-content: space-between;
    flex-direction: column;
  }

  .form-group {
    margin-top: 10px;
    .input {
      width: 100%;
      margin-top: 2px;
      nb-select {
        max-width: 100% !important;
        width: 100%;
      }
    }
    .input-qty {
      width: 100%;
      input {
        display: flex;
        justify-content: flex-end;
      }
    }
  }

  .order-item {
    width: 45%;
  }
  .order-qty {
    width: 15%;
  }
  label {
    margin-bottom: 2px;
    color: #222222;
    font-weight: normal;
    font-size: 14px;
  }

  h4 {
    color: #848384;
    font-size: 14px;
    margin-bottom: 10px;
    letter-spacing: 1.2px;
    font-weight: normal;
    font-family: "Lato-Regular";
  }
  .product-side {
    nb-list-item {
      padding-left: 2.5rem;
      padding-right: 2.5rem;
    }

    .margin {
      margin-right: 15px;
    }

    .button {
      display: flex;
      margin-top: 23px;
      padding: 0;

      nb-icon {
        width: 24px;
      }
    }
  }
  .list-elt {
    border-top: none !important;
    .col {
      font-size: 12px;
    }
    .col-image {
      width: 10%;
      img {
          height: 20px;
        }
    }

    .col-refe {
      width: 25%;
    }
    .col-label {
      width: 25%;
    }
    .col-qty {
      width: 6%;
    }
    .col-qty-ton {
      width: 20%;
    }
    .col-actions {
      width: 10%;
    }
  }

  .form-footer {
    border: none;
    display: flex;
    justify-content: space-between;
    padding: 1rem 5.5rem;
  }


}

.add-dialog-container {
  nb-card {
      width: 380px;
      h1 {
          font-size: 17px;
          margin-bottom: 18px;
      }
      h6 {
        font-style: normal;
        font-size: 13px;
        line-height: 16px;
        color: #000000;
        text-align: center;
        margin-bottom: 18px;

    }
    .form-group{
      nb-select{
        width: 100%;
        max-width: 100%;
      }
    }

    label {
      font-style: normal;
      font-weight: 600;
      font-size: 12px;
      line-height: 16px;
      color: #000000;
    }
  }
}

.dialog-container {
  nb-card {
    width: 380px;
    h1 {
          font-style: normal;
          font-weight: normal;
          font-size: 12px;
          line-height: 16px;
          color: #000000;
      }
      .form-group{
        nb-select{
          width: 100%;
          max-width: 100%;
        }
      }
      h6 {
        font-style: normal;
        font-size: 13px;
        line-height: 16px;
        color: #000000;
        text-align: center;
        margin-bottom: 18px;

    }

    nb-card-footer {
      display: flex;
      justify-content: space-between;
    }
  }
}
