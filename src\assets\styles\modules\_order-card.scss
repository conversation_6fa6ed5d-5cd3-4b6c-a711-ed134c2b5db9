/* order-card.component.scss */
.order-card {
  position: relative;
  margin-bottom: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  background-color: white;

  nb-card-body {
    padding: 16px 20px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    gap: 2rem;
    width: 100%;

    .left-bloc {
      width: 69%;

      // En-tête avec numéro et date
      .order-header {
        display: flex;
        align-items: flex-start;
        margin-bottom: 16px;

        .order-icon-container {
          display: flex;
          justify-content: center;
          align-items: center;
          margin-right: 12px;
          background: #F2F2F7;
          height: 30px;
          width: 30px;
          border-radius: 6px;

          .document-icon {
            height: 15px;
            width: 15px;
          }
        }

        .order-info {
          flex: 1;

          .order-title {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 4px;

            h5 {
              margin: 0;
              font-size: 17px;
              font-weight: 600;
              color: #000000;
            }
          }

          .creation-date {
            margin: 0;
            font-size: 12px;
            color: #8f9bb3;
          }
        }


      }

      // Section produits
      .products-section {
        display: flex;
        align-items: flex-start;
        margin-bottom: 16px;

        .product-icon-container {
          margin-right: 12px;
          padding-top: 3px;

          .product-icon {
            color: #8f9bb3;
            font-size: 18px;
          }
        }

        .product-info {
          flex: 1;

          .product-name {
            margin: 0;
            font-size: 14px;
            font-weight: 500;
            color: #000000;
          }
        }
      }

      // Styles communs pour les étiquettes de section
      .section-label {
        margin: 0 0 4px 0;
        font-size: 12px;
        color: #8f9bb3;
      }

      // Grille pour les informations détaillées
      .details-grid {

        .product-icon-container {
          margin-right: 12px;
          padding-top: 3px;

          .product-icon {
            color: #8f9bb3;
            font-size: 18px;
          }
        }

        display: flex;
        margin-bottom: 24px;

        .detail-column {
          flex: 1;
          padding-right: 16px;

          // Alignement spécifique pour respecter la capture d'écran
          &:nth-child(1) {
            flex: 1.5;
          }

          &:nth-child(2) {
            flex: 0.8;
          }

          &:nth-child(3) {
            flex: 0.8;
            padding-right: 0;
          }

          .detail-value {
            margin: 0;
            font-size: 14px;
            font-weight: 600;
            color: #000000;
          }

          .delivered {
            width: 90px;
            border-radius: 16px;
            font-size: 12px;
            font-weight: 600;
            background-color: #e6ffef;
            color: #007AFF;
            padding-left: 12px;
          }
        }
      }

    }




    .rigth-bloc {
      display: flex;
      flex-direction: column;
      width: 44%;
      gap: 2rem;

      .status {
        display: flex;
        gap: 1rem;
        width: 95%;
        justify-content: flex-end;

        .status-pill {
          padding: 4px 12px;
          border-radius: 16px;
          font-size: 12px;
          font-weight: 500;

          &.status-200 {
            background-color: #fff8e6;
            color: #ffaa00;
            border: 1px solid #ffaa00;
          }

          &.status-300 {
            background-color: #e6ffef;
            color: #00d68f;
            border: 1px solid #00d68f;
          }

          &.status-100 {
            background-color: #ffe6e6;
            color: #fa3654;
            border: 1px solid #fa3654;
          }
        }


        .render-pill {
          padding: 4px 12px;
          border-radius: 16px;
          font-size: 12px;
          font-weight: 500;
        
          &.render-1 {
            background-color: #0B305C;
            color: #f6f7f8; 
            font-weight: 600;
            border: 1px solid #011427; 
          }
        
          &.render-2 {
            background-color: #0f6bf5; 
            color: #ffffff;
            border: 1px solid #2572b1; 
          }
        }
        
      }

      .action-buttons {
        display: flex;
        flex-direction: column;
        gap: 1rem;


        .detail-btn {
          padding: 8px 16px;
          font-weight: 500;
          font-size: 14px;
        }

        .split-btn {
          padding: 8px 16px;
          font-weight: 500;
          font-size: 14px;
        }
      }
    }
  }
}


// Barre de statut en bas


  .status-bar {
  border-radius: 3px;
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 5px;
  
    &.status-200 {
      background-color: #ffaa00;
    }
  
    &.status-300 {
      background-color: #00d68f;
    }
  
    &.status-100 {
      background-color: #fa3654;
    }
  }



.partition-container {
  display: flex;
  flex-direction: column;
  width: 81vh;
  background-color: #fff;
  padding: 0 9px 0 25px;
  height: 78vh;
  border-radius: 10px;

  .modal-header {
    width: 101%;
    display: flex;
    flex-direction: row-reverse;

    img{
      height: 30px;
    }
  
  }

  .container{
    width: 99%;
    display: flex;
    flex-direction: column;
  

  .partition-card-info {
    position: relative;
    width: 100%;
    margin-bottom: 16px;
    overflow: hidden;

    nb-card-body {
      padding: 16px 20px;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      gap: 2rem;
      background: #FAFAFA;
      border-radius: 15px;
      width: 100%;

      .left-bloc {
        width: 69%;

        // En-tête avec numéro et date
        .order-header {
          display: flex;
          align-items: flex-start;
          margin-bottom: 16px;

          .order-icon-container {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-right: 12px;
            background: #F2F2F7;
            height: 30px;
            width: 30px;
            border-radius: 6px;

            .document-icon {
              height: 15px;
              width: 15px;
            }
          }

          .order-info {
            flex: 1;

            .order-title {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-bottom: 4px;

              h5 {
                margin: 0;
                font-size: 17px;
                font-weight: 600;
                color: #000000;
              }
            }

            .creation-date {
              margin: 0;
              font-size: 12px;
              color: #8f9bb3;
            }
          }


        }



        // Styles communs pour les étiquettes de section
        .section-label {
          margin: 0 0 4px 0;
          font-size: 12px;
          color: #8f9bb3;
        }

        // Grille pour les informations détaillées
        .details-grid {

          .product-icon-container {
            margin-right: 12px;
            padding-top: 3px;

            .product-icon {
              color: #8f9bb3;
              font-size: 18px;
            }
          }

          display: flex;
          margin-bottom: 24px;

          .detail-column {
            flex: 1;
            padding-right: 16px;

            // Alignement spécifique pour respecter la capture d'écran
            &:nth-child(1) {
              flex: 1.5;
            }

            &:nth-child(2) {
              flex: 0.8;
            }

            &:nth-child(3) {
              flex: 0.8;
              padding-right: 0;
            }

            .detail-value {
              margin: 0;
              font-size: 14px;
              font-weight: 600;
              color: #000000;
            }

            .delivered {
              width: 90px;
              border-radius: 16px;
              font-size: 12px;
              font-weight: 600;
              background-color: #e6ffef;
              color: #007AFF;
              padding-left: 12px;
            }
          }
        }

      }




      .rigth-bloc {
        display: flex;
        flex-direction: column;
        width: 30%;
        gap: 2rem;

        .status {
          display: flex;
          gap: 1rem;
          justify-content: flex-end;

          .status-pill {
            padding: 4px 12px;
            border-radius: 16px;
            font-size: 12px;
            font-weight: 500;

            &.status-200 {
              background-color: #fff8e6;
              color: #ffaa00;
              border: 1px solid #ffaa00;
            }

            &.status-300 {
              background-color: #e6ffef;
              color: #00d68f;
              border: 1px solid #00d68f;
            }

            &.status-100 {
              background-color: #ffe6e6;
              color: #fa3654;
              border: 1px solid #fa3654;
            }
          }

          .render-pill {
            padding: 4px 12px;
            border-radius: 16px;
            font-size: 12px;
            font-weight: 500;
          
            &.render-1 {
              background-color: #0B305C;
              color: #f6f7f8; 
              font-weight: 600;
              border: 1px solid #011427; 
            }
          
            &.render-2 {
              background-color: #0f6bf5; 
              color: #ffffff;
              border: 1px solid #2572b1; 
            }
          }
        }

        .action-buttons {
          display: flex;
          flex-direction: column;
          gap: 1rem;


          .detail-btn {
            padding: 8px 16px;
            font-weight: 500;
            font-size: 14px;
          }

          .split-btn {
            padding: 8px 16px;
            font-weight: 500;
            font-size: 14px;
          }
        }
      }
    }
  }


  // Section produits
  .products-section {
    padding-left: 23px;
    display: flex;
    flex-direction: row;
    justify-content: start;
    width: 100%;

    .product-icon-container {
      margin-right: 12px;
      padding-top: 3px;

      .product-icon {
        color: #8f9bb3;
        font-size: 18px;
      }
    }

    .product-info {
      flex: 1;

      .product-name {
        margin: 0;
        font-size: 14px;
        font-weight: 600;
        color: #000000;
      }
    }
  }




  .partition-card-main {
    position: relative;
    margin-bottom: 3rem;
    border: 1px solid #8E8E93;
    border-radius: 10px;
    height: 29vh;
    overflow: hidden;



    nb-card-body {
      padding: 16px 20px;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      gap: 1rem;
      height: 100%;
      width: 100%;

      .left-bloc {
        width: 49%;
        height: 97%;
        border-right: 1px solid #8E8E93;
        display: flex;
        flex-direction: column;
        gap: 0.50rem;

        // En-tête avec numéro et date
        .order-header {
          display: flex;
          align-items: flex-start;
          margin-bottom: 16px;

          .order-icon-container {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-right: 12px;
            background: #F2F2F7;
            height: 30px;
            width: 30px;
            border-radius: 6px;

            .document-icon {
              height: 15px;
              width: 15px;
            }
          }

          .order-info {
            flex: 1;

            .order-title {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-bottom: 4px;

              h5 {
                margin: 0;
                font-size: 17px;
                font-weight: 600;
                color: #000000;
              }
            }

          }




        }

        .filter-product-main {
          display: flex;
          padding-left: 34px;
          height: 49%;
          flex-direction: column;

          .filters {
            width: 80%;

            nb-select {
              width: 255px;
              height: 1rem;
              margin-bottom: 26px
            }

            input {
              width: 255px;
              height: 2.5rem;
            }

          }

        }

        .available-qdty {
          width: 85%;
          padding-top: 8px;
          display: flex;
          justify-content: flex-end;

          .qdty {
            padding: 4px 5px;
            border-radius: 16px;
            font-size: 12px;
            font-weight: 500;
            background-color: #e6ffef;
            color: #00d68f;

          }

        }

        .action-buttons {
          width: 85%;
          height: 5%;
          padding-top: 28px;
          display: flex;
          justify-content: flex-end;

          button {
            height: 1rem;

          }

          .split-btn {
            transition: all 0.3s ease;
          }

          .split-btn.disabled-btn {
            background-color: #CBCBCD !important;
            border-color: #CBCBCD !important;
            color: #ffffff;
            cursor: not-allowed;
          }
        }
      }
    }

    .rigth-bloc {
      width: 49%;
      height: 97%;
      display: flex;
      flex-direction: column;
      gap: 0.50rem;

      .order-header {
        display: flex;
        align-items: flex-start;
        padding-left: 16px;

        .order-icon-container {
          display: flex;
          justify-content: center;
          align-items: center;
          margin-right: 12px;
          background: #F2F2F7;
          height: 30px;
          width: 30px;
          border-radius: 6px;

          .document-icon {
            height: 15px;
            width: 15px;
          }
        }

        .order-info {
          flex: 1;

          .order-title {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 4px;

            h5 {
              margin: 0;
              font-size: 16px;
              font-weight: 600;
              color: #222b45;
            }
          }


        }


      }

      .ae-partition-list {
        height: 95%;
        overflow: auto;
        display: flex;
        gap: 5px;
        flex-direction: column;
        margin-bottom: 25px;

        .aes-list {
          background: #F4F4F4;
          width: 95%;
          padding: 10px 10px;
          margin: 0 0 0 16px;
          border-radius: 10px;

        }

        .quantity {
          display: flex;
          justify-content: space-between;


          .modify {
            text-decoration: underline;
            color: #222b45;
            cursor: pointer;
            transition: color 0.3s ease, transform 0.2s ease;
          }

          .modify:hover {
            color: rgb(4, 102, 69);
            transform: scale(1.05);
          }

          .modify:active {
            color: navy;
            transform: scale(0.95);
          }

        }

        .empty-list {
          display: flex;
          text-align: center;

          p {
            font-size: 14px;
            color: #8f9bb3;
          }

          ;

          nb-icon {
            color: #8f9bb3;
            font-size: 18px;
          }
        }
      }

      .action-buttons {
        height: 5%;
        margin-left: 16px;
        align-items: end;
        display: flex;
        justify-content: center;

        button {
          font-size: 13px;
          height: 1rem;

        }

        .split-btn {
          transition: all 0.3s ease;
        }

        .split-btn.disabled-btn {
          background-color: #CBCBCD !important;
          border-color: #CBCBCD !important;
          color: #ffffff;
          cursor: not-allowed;
        }
      }
    }
  }



  }



}


