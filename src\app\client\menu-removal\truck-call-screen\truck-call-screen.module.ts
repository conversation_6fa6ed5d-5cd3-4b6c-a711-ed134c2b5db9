import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { TruckCallScreenRoutingModule } from './truck-call-screen-routing.module';
import { TruckCallScreenComponent } from './truck-call-screen.component';
import { NbTabsetModule, NbCardModule, NbIconModule, NbListModule, NbBadgeModule, NbDialogModule, NbButtonModule, NbSpinnerModule, NbTooltipModule, NbSelectModule, NbInputModule } from '@nebular/theme';
import { SharedModule } from 'src/app/shared/shared.module';


@NgModule({
  declarations: [
    TruckCallScreenComponent
  ],
  imports: [
    CommonModule,
    NbTabsetModule,
    NbCardModule,
    NbTabsetModule,
    NbIconModule,
    NbListModule,
    NbBadgeModule,
    NbDialogModule,
    NbButtonModule,
    NbSpinnerModule,
    NbTooltipModule,
    NbSelectModule,
    NbInputModule,
    SharedModule,
    TruckCallScreenRoutingModule
  ]
})
export class TruckCallScreenModule { }
