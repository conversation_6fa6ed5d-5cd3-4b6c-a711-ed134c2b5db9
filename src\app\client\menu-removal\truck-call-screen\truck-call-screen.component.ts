import { Router } from '@angular/router';
import { Location } from '@angular/common';
import { NbToastrService } from '@nebular/theme';
import { Dock } from 'src/app/shared/models/dock';
import { RemovalService } from '../removal-management/removal.service';
import { CommonService } from 'src/app/shared/services/common.service';
import { TruckInspection } from 'src/app/shared/models/truck-inspection';
import { WaitingThreadService } from '../waiting-thread/waiting-thread.service';
import { Component, OnDestroy, OnInit, ViewEncapsulation } from '@angular/core';
import { parkingStatus } from 'src/app/shared/models/authorization-removal';

@Component({
  selector: 'clw-truck-call-screen',
  templateUrl: './truck-call-screen.component.html',
  styles: [
  ],
  encapsulation: ViewEncapsulation.None

})
export class TruckCallScreenComponent implements OnInit, OnDestroy {

  result: number;
  docks: Dock[];
  isLoading: boolean;
  displayLoading: boolean;
  inspections: TruckInspection[];
  updateInspectionsInterval: ReturnType<typeof setInterval>;


  constructor(
    private router: Router,
    private location: Location,
    public commonSvr: CommonService,
    private toastrSvr: NbToastrService,
    private removalSvr: RemovalService,
    private waitingThreadSrv: WaitingThreadService,


  ) { }

  async ngOnInit(): Promise<void> {
    this.docks = await this.removalSvr.getTruckByDock({});
    await this.getInspections();
    this.updateInspectionsInterval = setInterval(() => {
      this.getInspections();
    }, 30000);
  }

  ngOnDestroy(): void {
    if (this.updateInspectionsInterval) clearInterval(this.updateInspectionsInterval);
  }

  async getInspections(): Promise<void> {

    try {
      this.isLoading = true;
      const { data } = await this.removalSvr.getAllInspections({ truckSatus: parkingStatus.FACTORY });
      this.inspections = data;
    }
    catch (error) {
      this.toastrSvr.danger('Impossible de récupérer la liste des Inspections camions', 'Erreur connexion !');

    } finally { this.isLoading = false; }
  }
  goBack() {
    if (window.history.length > 1) {
      this.location.back()
    } else {
      this.router.navigate(['/removal/waiting-thread']);
    }
  }

  getParkTimeKey(status: number): string {
    const StatusParkTimeKeyMap = {
      400: 'factoryParkTimes',
      450: 'dockParkTimes',
      600: 'failureParkTimes',
      700: 'newParkTimes',
    }
    return StatusParkTimeKeyMap[status];
  }

}
