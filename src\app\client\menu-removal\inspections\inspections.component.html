<div class="common-form-container menu-removal-container">
    <div class="header">
        <h1 class="title">Liste des Inspections</h1>
    </div>

    <div class="removal-container">
        <div class="filter-area">
            <div class="left-area">
                <div class="filter-label">Filtrer par</div>
                <div class="filters" *ngIf="activeFilter?.length == 0">
                    <input type="text" placeholder="N° BON" fieldSize="small" nbInput class="empty-input height"
                        [nbAutocomplete]="autoComplete1" [(ngModel)]="filterForm.LoadNumber"
                        (ngModelChange)="onModelAeChange($event)" />
                    <nb-autocomplete #autoComplete1
                        (selectedChange)="onSelectionAEChange({$event,
                            keyToReset: $event == 'Tous les Bons'?  'LoadNumber' : '',  dataToReset: 'onModelAEChange'})">
                        <nb-option *ngIf="loading"> Chargement... </nb-option>
                        <nb-option value="Tous les Bons"> Tous les Bons</nb-option>
                        <nb-option *ngFor="let option of dataAELabels" [value]="option">{{option}}
                        </nb-option>
                    </nb-autocomplete>
                    <input type="text" placeholder="Statut X3" fieldSize="small" nbInput class="empty-input height"
                        [nbAutocomplete]="autoComplete2" [(ngModel)]="filterForm.LoadStatus" />
                    <nb-autocomplete #autoComplete2
                        (selectedChange)="onSelectionAEChange({$event,
                            keyToReset: $event == 'Tous les Statuts jde'?  'LoadStatus' : '',  dataToReset: 'onModelAEChange'})">
                        <nb-option *ngFor="let option of jdeStatus" [value]="option.code">{{option?.name}}
                        </nb-option>
                    </nb-autocomplete>
                    <input type="text" placeholder="Transporteur" fieldSize="small" nbInput class="empty-input height"
                        [nbAutocomplete]="autoComplete4" [(ngModel)]="filterForm.carrierLabel"
                        (ngModelChange)="onModelCarrierChange($event)" />
                    <nb-autocomplete #autoComplete4
                        (selectedChange)="onSelectionAEChange({$event,
                        keyToReset: $event == 'Tous les transporteurs'?  'company' :  '',  dataToReset: 'onModelCompanyChange'})">
                        <nb-option value="Tous les transporteurs"> Tous les transporteurs</nb-option>
                        <nb-option *ngFor="let option of dataCarrierLabels" [value]="option">{{option }}
                        </nb-option>
                        <nb-option *ngIf="loading"> Chargement... </nb-option>
                    </nb-autocomplete>
                    <input type="text" placeholder="Camion" fieldSize="small" nbInput class="empty-input height"
                        [nbAutocomplete]="autoComplete5" [(ngModel)]="filterForm.truck"
                        (ngModelChange)="onModelTruckImmatriculationChange($event)" />
                    <nb-autocomplete #autoComplete5
                        (selectedChange)="onSelectionAEChange({$event,
                         keyToReset: $event == 'Tous les Camion'?  'shipping' : '', dataToReset: 'onModelDeliveryPointChange'})">
                        <nb-option value="Tous les Camion"> Tous les Camions </nb-option>
                        <nb-option *ngFor="let option of dataTruckImmatriculation" [value]="option">{{option }}
                        </nb-option>
                        <nb-option *ngIf="loading && !dataTruckImmatriculation?.length"> Chargement... </nb-option>
                    </nb-autocomplete>
                    <input nbInput [nbDatepicker]="datePickerStart" [(ngModel)]="filterForm.startDate" status="success"
                        (ngModelChange)="onModelDateChange($event)" fieldSize="small" class="empty-input height"
                        placeholder="Début" />
                    <nb-datepicker #datePickerStart>
                    </nb-datepicker>
                    <input nbInput [nbDatepicker]="datepickerEnd" [(ngModel)]="filterForm.endDate" status="success"
                        (ngModelChange)="onModelDateChange($event)" fieldSize="small" class="empty-input height"
                        placeholder="Fin" />
                    <nb-datepicker #datepickerEnd></nb-datepicker>
                </div>
            </div>
            <div class="right-area">
                <button nbButton status="success" fieldSize="small" class="search-btn"
                    (click)='openExportInspection(exportDialog)'>
                    <nb-icon icon="download-outline"></nb-icon>
                    EXPORTER
                </button>
                <button nbButton status="basic" fieldSize="small" class="search-btn reset-btn" (click)="reset()">
                    <nb-icon icon="refresh-outline" [options]="{ animation: { type: 'zoom' } }"></nb-icon>
                    REINITIALISER
                </button>
            </div>
        </div>
        <nb-card [nbSpinner]="isLoading">
            <nb-list class="element-head">
                <nb-list-item class="list-elt-header paginator">
                    <div class="col col-paginator">
                        <clw-pagination [config]="paginationConfig" (pageChange)="onPageChange($event)" (itemsPerPageChange)="onItemsPerPageChange($event)"></clw-pagination>
                    </div>
                </nb-list-item>
                <nb-list-item class="list-elt-header paginator">
                    <div class="col col-num">N°</div>
                    <div class="col col-ref">N° BON</div>
                    <div class="col col-ref">Type BON</div>
                    <div class="col col-date">Date création</div>
                    <div class="col col-ref">Transporteur</div>
                    <div class="col col-shipto">Camion</div>
                    <!-- <div class="col col-shipto">Usine</div> -->
                    <div class="col col-shipto">Chauffeur</div>
                    <div class="col col-shipto">Contrôleur</div>
                    <div class="col col-status">Statut global</div>
                    <div class="col col-action"></div>

                </nb-list-item>
            </nb-list>
            <nb-list class="element-head scrool-style" nbSpinnerStatus="primary">
                <nb-list-item class="list-elt-header" *ngFor="let inspection of inspections, index as i">
                    <div class="col col-num">{{i + 1}}</div>
                    <div class="col col-ref">{{inspection?.LoadNumber || 'N/A' }}</div>
                    <div class="col col-ref">{{ inspection?.type ? (inspection.type === 'C9' ? 'PICK UP' : 'RENDU') : 'N/A' }}</div>

                    <div class="col col-date">{{inspection?.date | date:"dd/MM/YYYY"}} {{inspection?.date || inspection?.date | date:"hh:mm"}}
                    </div>
                    <div class="col col-ref">{{inspection?.carrier?.label || inspection?.carrier?.label || 'N/A'}}</div>
                    <div class="col col-shipto">
                        {{inspection?.carrier?.truckImmatriculation|| inspection?.carrier?.immatriculation || inspection?.tractor?.label || 'N/A'}}
                    </div>
                    <!-- <div class="col col-shipto">
                        {{inspection?.BusinessUnit || inspection?.authorizationRemoval[0]?.BusinessUnit || 'N/A'}}
                    </div> -->
                    <div class="col col-shipto"
                        title="{{ inspection?.driver || inspection?.carrier?.driver?.fullname || 'N/A'}} ">
                        {{inspection?.carrier?.driver?.fullname || inspection?.driver || 'N/A'}}</div>
                    <div class="col col-shipto">
                        {{ (inspection?.user?.fname || '') + ' ' + (inspection?.user?.lname || '') ||
                        inspection?.user?.email|| 'N/A'}}
                    </div>
                    <div class="col col-status">
                        <nb-badge [text]="inspection?.status == inspectionDecision.REJECTED  ? inspection?.decision : inspection?.decision" class="badge"
                            position="top start"
                            [status]=" inspection?.status | getColorStatusInspection"></nb-badge>
                    </div>
                    <div class="col col-action">
                        <nb-actions size="small">
                          <nb-action icon="more-horizontal-outline"
                                    [nbContextMenu]="actionItems"
                                    nbContextMenuTag="action-menu"
                                    (click)="setContextInspection(inspection)">
                          </nb-action>
                        </nb-actions>
                      </div>

                </nb-list-item>
                <nb-list-item class="empty-list" *ngIf="!isLoading && inspections && inspections.length === 0">
                    <img src="../../../../assets/images/empty-list.png" alt="liste vide">
                    Aucune Inspection trouvée
                </nb-list-item>
            </nb-list>
        </nb-card>
    </div>
</div>


<ng-template #detailDialog let-data let-ref="dialogRef" class="dialogRef">
    <div class="order-detail-container her-width">
        <nb-card>
            <nb-card-header class="form--header">Détail de l'inspection</nb-card-header>
            <nb-card-body>
                <div class="order-infor order-pick-up">
                    <table class="order-table">
                        <tr>
                            <td class="title">N° BON:</td>
                            <td class="order-right">{{inspection?.LoadNumber}}</td>
                        </tr>
                        <!-- <h2>VERIFICATIONS</h2> -->
                        <tr *ngFor="let check of inspection?.checkList; index as i ">
                            <td>{{check.label.slice(0,35)}}</td>
                            <div class="order-right">
                                <img [src]="imageGood" *ngIf="check.status" class="icon">
                                <img [src]="imageBad" *ngIf="!check.status" class="icon">
                            </div>
                        </tr>
                    </table>
                </div>
            </nb-card-body>
            <nb-card-footer class="form--footer">
                <button nbButton outline status="basic" class="" (click)="ref.close()">
                    <nb-icon icon="close-square-outline" [options]="{ animation: { type: 'zoom' } }"
                        class="remove-icon">
                    </nb-icon>
                    fermer
                </button>
            </nb-card-footer>
        </nb-card>
    </div>
</ng-template>

<ng-template #updateDialog let-data let-ref="dialogRef" class="dialogRef">
    <div class="order-detail-container her-width">
        <nb-card>
            <nb-card-header class="form--header">Modifier l'inspection</nb-card-header>
            <nb-card-body>
                <nb-tabset>
                    <nb-tab tabTitle="Informations Chauffeur">
                        <div class="form-container">
                            <div class="form-group">
                                <div class="attribute-block">
                                    <label class="text" for="nameInput">Nom du chauffeur</label>
                                </div>
                                <input nbInput class="carrier-name input-width" status="basic" type="text" fullWidth
                                    (keyup)="verifyname($event)" placeholder="Saisissez le nom du chauffeur"
                                    [(ngModel)]='currentInspections.carrier?.driver.fullname'>
                            </div>
                            <div class="form-group">
                                <div class="attribute-block">
                                    <label class="text">Immatriculation du véhicule</label>
                                </div>
                                <input id="typeInput" nbInput status="basic" type="text"
                                    [(ngModel)]="currentInspections.carrier.truckImmatriculation"
                                    fullWidth placeholder="Saisissez l'immatriculation du Véhicule" />
                            </div>
                            <div class="form-group">
                                <div class="attribute-block">
                                    <label class="text"> Numéro de téléphone</label>
                                </div>
                                <input id="typeInput" nbInput status="basic" type="tel"
                                    [(ngModel)]="currentInspections?.carrier?.driver.tel" fullWidth
                                    (keyup)="verifyTel($event)" placeholder="Saisissez le numero de tel du chauffeur" />
                            </div>
                            <div class="form-group">
                                <div class="attribute-block">
                                    <label class="text">N° permis de conduire</label>
                                </div>
                                <input id="typeInput" nbInput status="basic" type="text" fullWidth
                                    placeholder="Saisissez le numero du permis du chauffeur"
                                    (keyup)="verifyPermis($event)"
                                    [(ngModel)]="currentInspections?.carrier?.driver.license" />
                            </div>
                        </div>
                    </nb-tab>

                    <nb-tab tabTitle="Verifications">
                        <div class="order-infor order-pick-up">
                            <table class="order-table">
                                <tr>
                                    <td class="title">N° BON:</td>
                                    <td class="order-right">{{currentInspections?.LoadNumber}}</td>
                                </tr>
                                <tr>
                                    <td class="title">Statut global:</td>
                                    <td class="order-right">
                                        <nb-select class="carrier-name input-width" placeholder="Choissisez le statut"
                                            status='primary' size="small" class="empty-input"
                                            [(selected)]='currentInspections.decision'>
                                            <nb-option [value]='decision'
                                                *ngFor="let decision of InspectionDecisions">{{decision}}
                                            </nb-option>
                                        </nb-select>
                                    </td>
                                </tr>
                                <tr *ngFor="let check of currentInspections?.checkList; index as i ">
                                    <td>{{check.label.slice(0,35)}}</td>
                                    <nb-toggle class="order-right" [(ngModel)]="check.state"></nb-toggle>
                                </tr>
                            </table>
                        </div>
                    </nb-tab>
                </nb-tabset>
            </nb-card-body>
            <nb-card-footer class="form--footer">
                <button nbButton outline status="basic" class="" (click)="ref.close()">
                    <nb-icon icon="close-square-outline" [options]="{ animation: { type: 'zoom' } }"
                        class="remove-icon">
                    </nb-icon>
                    fermer
                </button>
                <button nbButton outline status="primary" class="" (click)="saveInspection();ref.close()">
                    <nb-icon icon="done-all-outline" [options]="{ animation: { type: 'zoom' } }" class="remove-icon">
                    </nb-icon>
                    Valider
                </button>
            </nb-card-footer>
        </nb-card>
    </div>
</ng-template>
<ng-template #imageDialog let-data let-ref="dialogRef" class="dialogRef">
    <div class="order-detail-container">
        <nb-card>
            <nb-card-header class="form--header">Detail des images de l'inspection</nb-card-header>
            <div class="gallery-container">

                <div class="displayed-img-container" *ngIf="selectedImage">
                    <img [src]="selectedImage" class="displayed-img">
                    <button nbButton status="danger" (click)="clearSelectedImage()">CLOSE</button>

                    <label *ngIf="images?.length < 0" for="">Aucune image associer</label>

                    <nb-icon icon="edit-2-outline" status="primary" class="edit" (click)="updateImage()"
                        [options]="{ animation: { type: 'zoom' } }"> </nb-icon>
                    <input type="file" id="file" style="display: none;" (change)="getFile($event, selectedImageIndex)">
                </div>

                <div class="thumb-bar">
                    <img *ngFor="let image of images let i = index" [src]="image" (click)="selectImage(image, i)"
                        class="thumb">
                </div>



            </div>


            <nb-card-footer class="form--footer">
                <button nbButton outline status="basic" class="" (click)="ref.close()">
                    <nb-icon icon="close-square-outline" [options]="{ animation: { type: 'zoom' } }"
                        class="remove-icon">
                    </nb-icon>
                    fermer
                </button>

                <button *ngIf="isImageUploaded == true" nbButton outline status="primary" class=""
                    (click)="saveInspection();ref.close()">
                    <nb-icon icon="done-all-outline" [options]="{ animation: { type: 'zoom' } }" class="remove-icon">
                    </nb-icon>
                    Valider
                </button>

            </nb-card-footer>
        </nb-card>
    </div>
</ng-template>


<ng-template #exportDialog let-data let-ref="dialogRef" class="dialogRef">
    <div class="order-detail-container-export">
        <nb-card>
            <nb-card-header class="form--header">Exporter l'inspection</nb-card-header>
            <div class="export-contenair">
                <div class="filters-export">
                    <input type="text" placeholder="N° BON" fieldSize="small" nbInput class="empty-input height"
                        [nbAutocomplete]="autoComplete1" [(ngModel)]="filterForm.LoadNumber"
                        (ngModelChange)="onModelAeChange($event)" />

                    <nb-autocomplete #autoComplete1 (selectedChange)="onSelectionAEChange({$event,
                        keyToReset: $event == 'Tous les Bons'?  'LoadNumber' : '',  dataToReset: 'onModelAEChange'})">
                        <nb-option *ngIf="loading"> Chargement... </nb-option>
                        <nb-option value="Tous les Bons"> Tous les Bons </nb-option>
                        <nb-option *ngFor="let option of dataAELabels" [value]="option">{{option}}
                        </nb-option>
                    </nb-autocomplete>

                    <input type="text" placeholder="Transporteur" fieldSize="small" nbInput class="empty-input height"
                        [nbAutocomplete]="autoComplete4" [(ngModel)]="filterForm.carrierLabel"
                        (ngModelChange)="onModelCarrierChange($event)" />
                    <nb-autocomplete #autoComplete4
                        (selectedChange)="onSelectionAEChange({$event,
                        keyToReset: $event == 'Tous les transporteurs'?  'company' :  '',  dataToReset: 'onModelCompanyChange'})">
                        <nb-option value="Tous les transporteurs"> Tous les transporteurs</nb-option>
                        <nb-option *ngFor="let option of dataCarrierLabels" [value]="option">{{option }}
                        </nb-option>
                        <nb-option *ngIf="loading"> Chargement... </nb-option>
                    </nb-autocomplete>
                    <input type="text" placeholder="Camion" fieldSize="small" nbInput class="empty-input height"
                        [nbAutocomplete]="autoComplete5" [(ngModel)]="filterForm.truck"
                        (ngModelChange)="onModelTruckImmatriculationChange($event)" />
                    <nb-autocomplete #autoComplete5
                        (selectedChange)="onSelectionAEChange({$event,
                         keyToReset: $event == 'Tous les Camion'?  'shipping' : '', dataToReset: 'onModelDeliveryPointChange'})">
                        <nb-option value="Tous les Camion"> Tous les Camions </nb-option>
                        <nb-option *ngFor="let option of dataTruckImmatriculation" [value]="option">{{option }}
                        </nb-option>
                        <nb-option *ngIf="loading && !dataTruckImmatriculation?.length"> Chargement... </nb-option>
                    </nb-autocomplete>
                    <input nbInput [nbDatepicker]="datePickerStart" [(ngModel)]="filterForm.startDate" status="success"
                        (ngModelChange)="onModelDateChange($event)" fieldSize="small" class="empty-input height"
                        placeholder="Début" />
                    <nb-datepicker #datePickerStart>
                    </nb-datepicker>
                    <input nbInput [nbDatepicker]="datepickerEnd" [(ngModel)]="filterForm.endDate" status="success"
                        (ngModelChange)="onModelDateChange($event)" fieldSize="small" class="empty-input height"
                        placeholder="Fin" />
                    <nb-datepicker #datepickerEnd></nb-datepicker>
                    <div class="filter-label">Nombre d'element a exporter. max {{total}}</div>

                    <input type="number" placeholder="Entrer" fieldSize="small" class="empty-input height"
                        [(ngModel)]="paginationConfig.itemsPerPage" nbInput [value]="paginationConfig.itemsPerPage" />
                </div>
            </div>


            <nb-card-footer class="form--footer">
                <button nbButton outline status="basic" class="" (click)="ref.close(); activeFilter.length =0; ">
                    <nb-icon icon="close-square-outline" [options]="{ animation: { type: 'zoom' } }"
                        class="remove-icon">
                    </nb-icon>
                    fermer
                </button>

                <button nbButton outline status="primary" class="" (click)="exportToExcel();ref.close()">
                    <nb-icon icon="done-all-outline" [options]="{ animation: { type: 'zoom' } }" class="remove-icon">
                    </nb-icon>
                    Exporter
                </button>

            </nb-card-footer>
        </nb-card>
    </div>
</ng-template>

<ng-template #validateDialog let-data let-ref="dialogRef" class="dialogRef">
    <div class="removal-queue-container">
        <nb-card>
            <nb-card-header class="form--header">
                Choix du Parking
            </nb-card-header>
            <nb-card-body>
                <p>Vous êtes sur le point d'envoyer le Bon<strong class="important">
                        N°{{inspection?.LoadNumber}}</strong>
                    dans une
                    file
                </p>
                <p>Veuillez choisir la file</p>
                <nb-select class="carrier-name input-width" placeholder="Veuillez choisir une file" status='primary'
                    size="small" class="empty-input" [(selected)]='thread'>
                    <nb-option [value]='waitingThread' *ngFor="let waitingThread of waitingThreads">{{waitingThread}}
                    </nb-option>
                </nb-select>
            </nb-card-body>
            <nb-card-footer class="form--footer">
                <button nbButton outline status="basic" (click)="ref.close()">
                    <nb-icon icon="close-square-outline" [options]="{ animation: { type: 'zoom' } }"
                        class="remove-icon">
                    </nb-icon>
                    ANNULER
                </button>
                <button nbButton outline status="primary" class="" (click)="sendToPark();ref.close()">
                    <nb-icon icon="done-all-outline" [options]="{ animation: { type: 'zoom' } }" class="remove-icon">
                    </nb-icon>
                    Valider
                </button>
            </nb-card-footer>
        </nb-card>
    </div>
</ng-template>

<ng-template #detailDialog></ng-template>
<ng-template #updateDialog></ng-template>
<ng-template #imageDialog></ng-template>
<ng-template #validateDialog></ng-template>
