import { BaseUrlService } from 'src/app/shared/services/base-url.service';
import { HttpClient, HttpParams } from '@angular/common/http';
import { environment } from 'src/environments/environment';
import { User, UserResponse, UserParams } from 'src/app/shared/models/user';
import { Injectable } from '@angular/core';
import isEmpty from 'lodash-es/isEmpty';
import * as moment from 'moment';
import { lastValueFrom } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class UsersListService {
  image: string
  BASE_URL: string;
  constructor(
    private http: HttpClient,
    private baseUrlSrv: BaseUrlService,
  ) {
    this.BASE_URL = `${this.baseUrlSrv.getOrigin()}${environment.basePath}`;
  }

  async getAllUsers(param: UserParams): Promise<UserResponse> {
    const params = this.generateQueryParams(param);
    const response = await lastValueFrom(this.http.get<UserResponse>(`${this.BASE_URL}/users`, { params }));
    return {
      data: response.data.filter(user => user.enable === true),
      count: response.count
    };
  }

  generateQueryParams(param: UserParams): HttpParams {
    let params = new HttpParams();

    const { userLname, userDepartment, userId, range, offset, limit, enable, name } = param;

    if (offset) { params = params.append('offset', `${offset}`); }
    if (limit) { params = params.append('limit', `${limit}`); }
    if (enable) { params = params.append('enable', `${enable}`); }
    if (userDepartment) { params = params.append('department', `${userDepartment}`); }
    if (userLname) { params = params.append('lname', `${userLname}`); }
    if (userId) { params = params.append('userId', `${userId}`); }
    if (name) { params = params.append('fullname', `${name}`); }

    if (!isEmpty(range) && range.start && range.end) {
      params = params.append('start', moment(range.start).format('YYYY-MM-DD'));
      params = params.append('end', moment(range.end).format('YYYY-MM-DD'));
    }

    return params;
  }

  async updateUser(user: User): Promise<User> {
    return await lastValueFrom(this.http.put<User>(`${this.BASE_URL}/users/${user._id}`, user));
  }

  async insertUser(user: User): Promise<User> {
    return await lastValueFrom(this.http.post<User>(`${this.BASE_URL}/users`, user));
  }

  async deleteUser(user: User): Promise<void> {
    await lastValueFrom(this.http.delete(`${this.BASE_URL}/users/${user._id}`));
  }
}
