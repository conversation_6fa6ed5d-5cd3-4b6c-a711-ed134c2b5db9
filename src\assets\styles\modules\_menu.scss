// nb-sidebar {
//  position: inherit !important;
//  z-index: auto !important;
//  height: 100vh !important;

//     .menu-container {
//         padding: 2%;
//     }

//     .sidebar-container{
//         background: #027a37 !important;

//     }

//     .back-container {
//         display: flex;
//         justify-content: flex-end;
//         width: 100%;
//         margin-top: 15px;
//         nb-icon {
//             color: #fff !important;
//         }
//     }
//     &.expanded {
//         min-width: 120px;
//         width: 15% !important;
//         background: $color-primary !important;
//         top: 0% !important;

//         .main-container {
//             width: 15%;
//         }

//         // nb-menu {
//         //     display: flex;
//         //     justify-content: center;
//         //     li {
//         //         border: none !important;
//         //         a.active,
//         //         &:hover {
//         //             border-radius: 6px;
//         //             border: solid 2px #fff;
//         //             color: #fff;

//         //             nb-icon,
//         //             span {
//         //                 color: #fff !important;
//         //             }
//         //         }

//         //         .menu-title,
//         //         nb-icon {
//         //             color: #fff !important;
//         //         }
//         //         nb-icon {
//         //             font-size: 24px !important;
//         //             width: 20% !important;
//         //         }
//         //     }
//         // }
//     }

//     &.compacted {
//         min-width: 3.5rem;
//         // min-width: 120px;
//         width: 4% !important;
//         background: #027a37 !important;
//         top: 0% !important;
//         nb-menu {
//             margin: 0%;
//             width: 4.5rem;
//             li {
//                 border: none !important;
//                 a.active,
//                 &:hover {
//                     color: #fff;
//                     nb-icon,
//                     span {
//                         color: #fff !important;
//                     }
//                 }

//                 a {
//                     padding: 0.75rem 0rem !important;
//                     &.active::before {
//                         background: #fff !important;
//                     }
//                 }

//                 nb-icon {
//                     color: #fff !important;
//                     width: 25px !important;
//                     height: 30px;
//                 }
//             }
//         }
//     }
// }

.menu-title {
    font-size: 16px;
    line-height: 16px;
}
