import { Injectable } from '@angular/core';
import * as CryptoJS from 'crypto-js';
import { environment } from 'src/environments/environment';
@Injectable({
  providedIn: 'root'
})
export class StorageService {

  curentFactory: string;

  constructor() {
    this.curentFactory = this.getString('curentFactory') ?? 'CM10000';
  }

  setString(key: string, value: string): any {
    key = this.encryptUsingAES256(key);
    value = this.encryptUsingAES256(value);
    localStorage.setItem(key, value);
  }

  getString(key: string): any {
    key = this.encryptUsingAES256(key);
    const data = localStorage.getItem(key);
    if (data) { return JSON.parse(this.decryptUsingAES256(data)); }
    return null;
  }

  setObject(key?: string, value?: any): any {
    key = this.encryptUsingAES256(key);
    value = this.encryptUsingAES256(value);
    localStorage.setItem(key, value);
  }

  getObject(key: string): any {
    key = this.encryptUsingAES256(key);
    const data = localStorage.getItem(key);
    if (data) { return JSON.parse(this.decryptUsingAES256(data)); }
    return null;
  }

  removeItem(key: string): any {
    key = key = this.encryptUsingAES256(key);
    localStorage.removeItem(key);
  }

  clear(): any { localStorage.clear(); }

  encryptUsingAES256(data: any): any {
    const KEY = CryptoJS.enc.Utf8.parse(environment.secretKey);
    const IV = CryptoJS.enc.Utf8.parse(environment.secretKey);
    const encrypted = CryptoJS.AES.encrypt(
      JSON.stringify(data), KEY, {
      keySize: 16,
      iv: IV,
      mode: CryptoJS.mode.ECB,
      padding: CryptoJS.pad.Pkcs7
    });

    return encrypted.toString();
  }

  decryptUsingAES256(encrypted: any): any {
    if (!encrypted) { return null; }
    const KEY = CryptoJS.enc.Utf8.parse(environment.secretKey);
    const IV = CryptoJS.enc.Utf8.parse(environment.secretKey);

    const decrypted = CryptoJS.AES.decrypt(
      encrypted, KEY, {
      keySize: 16,
      iv: IV,
      mode: CryptoJS.mode.ECB,
      padding: CryptoJS.pad.Pkcs7
    }).toString(CryptoJS.enc.Utf8);

    return decrypted;
  }
}
