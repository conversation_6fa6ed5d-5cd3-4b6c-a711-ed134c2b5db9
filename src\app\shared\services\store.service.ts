import { Store } from './../models/store';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from 'src/environments/environment';
import { BaseUrlService } from './base-url.service';
import { CategoryProduct } from '../models/category-product';
import { Packaging } from '../models/packaging';
import { lastValueFrom } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class StoreService {
  BASE_URL: string;
  currentStore: any;

  constructor(
    private http: HttpClient,
    private baseUrlSrv: BaseUrlService,
  ) {
    this.BASE_URL = `${this.baseUrlSrv.getOrigin()}${environment.basePath}`;
  }

  generateQueryParams(param: any): any {
    let params = new HttpParams();

    const { offset, limit, type, label } = param;

    if (offset) {
      params = params.append('offset', `${offset}`);
    }
    if (limit) {
      params = params.append('limit', `${limit}`);
    }
    if (type) {
      params = params.append('type', `${type}`);
    }
    if (label) {
      params = params.append('label', `${label}`);
    }

    return params;
  }

  async deleteStore(): Promise<{ data: any, count: number }> {
    return await lastValueFrom(this.http.delete<{ data: any, count: number }>(`${this.BASE_URL}/stores/${this.currentStore._id}`));
  }

  async editstores(store: Store): Promise<{ data: any, count: number }> {
    return await lastValueFrom(this.http.put<{ data: any, count: number }>(`${this.BASE_URL}/stores/${this.currentStore._id}`, store));
  }

  async addStore(store: Store): Promise<{ data: any, count: number }> {
    return await lastValueFrom(this.http.post<{ data: any, count: number }>(`${this.BASE_URL}/stores/`, store));
  }

  async getStores(param: any): Promise<{ data: Store[], count: number }> {
    const params = this.generateQueryParams(param);
    return await lastValueFrom(this.http.get<{ data: Store[], count: number }>(`${this.BASE_URL}/stores`, { params }));
  }

  async getProductTypes(): Promise<{ data: CategoryProduct[], count: number }> {
    return await lastValueFrom(this.http.get<{ data: CategoryProduct[], count: number }>(`${this.BASE_URL}/category-products`));
  }

  async getPackagings(): Promise<{ data: Packaging[], count: number }> {
    return await lastValueFrom(this.http.get<{ data: Packaging[], count: number }>(`${this.BASE_URL}/packagings`));
  }
}

