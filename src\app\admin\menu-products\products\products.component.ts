import { Product } from './../../../shared/models/product';
import { CommonService } from 'src/app/shared/services/common.service';
import { ProductsService } from './products.service';
import { NbDialogRef, NbDialogService, NbToastrService } from '@nebular/theme';
import { Router } from '@angular/router';
import { Component, OnInit } from '@angular/core';
import * as moment from 'moment';
import { ImageCompressor } from 'image-compressor';
import { Location } from '@angular/common';
import { Observable, of } from 'rxjs';

@Component({
  selector: 'clw-products',
  templateUrl: './products.component.html',
  styles: [
  ]
})
export class ProductsComponent implements OnInit {
  rangeFilter: { start: any; end: any } = { start: null, end: null };
  isLoading: boolean;
  min!: Date;

  flipped: boolean;

  products: Product[];

  filename: string;
  dialogRef: NbDialogRef<any>;
  isEdit: boolean;
  isDelete: boolean;
  selectedProduct: Product;
  file: any;
  fileType: string;
  imageSrc: string;
  productLabels =[];
  filteredProduct$: Observable<string[]>;
  selectedProductLabel: any;



  constructor(
    private router: Router,
    private dialogSvr: NbDialogService,
    private productSrv: ProductsService,
    private toastSrv: NbToastrService,
    private commonSrv: CommonService,
    private location: Location,
    // private imageCompress: NgxImageCompressService,
    private toastSvr: NbToastrService

  ) { }

  async ngOnInit(): Promise<void> {
    this.isLoading = true;
    try {
      this.flipped = false;
      await this.getAllProducts();
    } catch (error) {
      this.toastSrv.danger('Impossible de charger cette page', 'Erreur de connexion');
      console.log(error);
    } finally {
      this.isLoading = false;
    }
  }

  getFile(fileDataSet: any): any {

    this.file = fileDataSet.target.files[0];
    const type = this.file.type;
    if (!type.includes('image')) {
      this.file = null;
      return this.toastSrv.danger('Veuillez importer uniquement les images', 'Donnés incorrectes');

    }
    this.filename = this.file.name;
    const reader = new FileReader();

    if (fileDataSet.target.files && fileDataSet.target.files.length) {
      const [file] = fileDataSet.target.files;
      reader.readAsDataURL(file);

      reader.onload = () => {
        this.imageSrc = reader.result as string;
      };
    }
  }

  goTo() { this.location.back() }

  updateDateStart(startDate: any): void {
    this.min = moment(startDate).startOf('day').toDate();
    this.rangeFilter.start = moment(startDate).startOf('day').valueOf();
  }
  updateDateEnd(endDate: any): void {
    this.rangeFilter.end = moment(endDate).endOf('day').toDate();
  }


  openModal(dailog?: any): void {
    this.dialogRef = this.dialogSvr.open(dailog, { closeOnBackdropClick: false, autoFocus: true })
    this.dialogRef.onClose.subscribe(() => {
      this.flipped = false;
      this.isDelete = false;
      this.isEdit = false;
    });
  }

  selectProduct(dialog: any, product: Product) {
    this.selectedProduct = { ...product };
    this.productSrv.currentProduct = this.selectedProduct;
    this.openModal(dialog);
  }

  openAddModalProduct(dialog: any) {
    this.resetProduct();
    this.openModal(dialog);
  }



  editModal() {
    this.isEdit = true;
    this.flipped = true;
    this.imageSrc = this.selectedProduct.img;
  }
  isDeleteModal() {
    this.isDelete = true;
    this.flipped = true;
  }

  backFlip() {
    this.isDelete = false;
    this.isEdit = false;
    this.flipped = false;
  }


  truncate(data: string, size: number): string {
    return this.commonSrv.truncateString(data, size)
  }

  async deleteProduct(): Promise<any> {
    this.isLoading = true;
    try {
      await this.productSrv.deleteProducts();
      this.dialogRef.close();
      await this.getAllProducts();
      this.toastSrv.success('Nous avons Supprimé ce produit avec succès', 'Produit supprimé');
    } catch (error) {
      this.toastSrv.danger('Nous n\'avons pas pue supprimer ce produit', 'Erreur de connexion');
    } finally {
      this.isLoading = false;
    }
  }

  async editProduct(): Promise<any> {
    this.isLoading = true;
    try {
      if (this.selectedProduct.label == '' || this.selectedProduct.img == '') {
        this.toastSrv.danger('Veuillez renseigner tout les champs', 'Donnés incorrectes');
      }

      this.compressFile(this.imageSrc, async (compressedSrc) => {
        this.selectedProduct.img = compressedSrc;
        await this.productSrv.editProducts(this.selectedProduct);
        this.resetProduct();
        this.dialogRef.close();
        await this.getAllProducts();
        this.toastSrv.success('Nous avons Modifié ce produit avec succès', 'Produit Modifié');

      });
    } catch (error) {
      this.toastSrv.danger('Nous n\'avons pas pu supprimer ce produit', 'Erreur de connexion');
    } finally {
      this.isLoading = false;
    }
  }

  async addProduct(): Promise<any> {
    this.isLoading = true;
    try {
      if (this.selectedProduct.label == '') {
        this.toastSrv.danger('Veuillez renseigner tout les champs', 'Donnés incorrectes');
      }

      if (!this.imageSrc) {
        this.toastSrv.danger('Vous n\'avez importer aucune image', 'Donnés incorrectes');
      }

      this.compressFile(this.imageSrc, async (compressedSrc) => {
        this.selectedProduct.img = compressedSrc;
        await this.productSrv.addProducts(this.selectedProduct);
        this.resetProduct();
        this.dialogRef.close();
        await this.getAllProducts();
        this.toastSrv.success('Nous avons Ajouté  ce produit avec succès', 'Produit Ajouté');

      });
    } catch (error) {
      this.toastSrv.danger('Nous n\'avons pas pu Ajouter ce produit', 'Erreur de connexion');
    } finally {
      this.isLoading = false;
    }
  }


  resetProduct() {
    this.imageSrc = null;
    this.file = null;
    this.filename = '';
    this.selectedProduct = {
      label: '',
      normLabel: '',
      img: './../../../../assets/images/item-multix.png',
      description: '',

    }
  }

  compressFile(imageSrc: string, callback: any): any {
    const imageCompressor = new ImageCompressor;

    const compressorSettings = {
      toWidth: 150,
      toHeight: 150,
      mimeType: 'image/png',
      mode: 'strict',
      quality: 0.8,
      grayScale: false,
      sepia: false,
      threshold: false,
      vReverse: false,
      hReverse: false,
      speed: 'low'
    };

    imageCompressor.run(imageSrc, compressorSettings, callback);
  }

  search() {
    this.selectedProductLabel = null;
    this.getAllProducts()
  }

  private filterStore(value: string): string[] {
    const filterValue = value?.toLowerCase();
    return this.productLabels?.filter(optionValue => optionValue?.toLowerCase().includes(filterValue)).sort((a, b) => {
      return a.toString().toLowerCase().indexOf(filterValue?.toLowerCase()) - b.toString().toLowerCase().indexOf(filterValue?.toLowerCase());
    });
  }

  onModelStoreChange(value: string): void { this.filteredProduct$ = of(this.filterStore(value)); }

  async onSelectionStoreChange($event: any): Promise<void> {
    try {
      this.isLoading = true
      this.products = await this.productSrv.getProducts({ label: $event });
    } catch (error) {
      this.toastSrv.danger('Veuillez vérifier votre connexion internet', 'Erreur connexion internet');
      console.error(error);
    } finally {
      this.isLoading = false
      this.filteredProduct$ = of(this.productLabels);
    }
  }


  async getAllProducts(): Promise<void> {
    const options = {
      label: this.selectedProductLabel
    }
    try {
      this.isLoading = true;
      this.products = await this.productSrv.getProducts(options);
      this.products.forEach((elt: any) => { this.productLabels.push(elt.label) });
    } catch (error) {
      this.toastSrv.danger('Impossile de récupérer la liste des produits', 'Erreur connexion !');
    } finally { this.isLoading = false; }
  }
}
