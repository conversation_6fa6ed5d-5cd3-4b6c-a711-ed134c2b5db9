import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { MenuInspectionRoutingModule } from './menu-inspection-routing.module';
import { MenuInspectionComponent } from './menu-inspection.component';
import { NbBadgeModule, NbButtonModule, NbCardModule, NbIconModule, NbInputModule, NbListModule, NbSelectModule, NbSpinnerModule, NbToastrModule } from '@nebular/theme';
import { FormsModule } from '@angular/forms';


@NgModule({
  declarations: [
    MenuInspectionComponent,
  ],
  imports: [
    CommonModule,
    MenuInspectionRoutingModule,
    FormsModule,
    NbIconModule,
    NbListModule,
    NbCardModule,
    NbSpinnerModule,
    FormsModule,
    NbSelectModule,
    NbToastrModule,
    NbButtonModule,
    NbSelectModule,
    NbInputModule,
    NbBadgeModule,
  ]
})
export class MenuInspectionModule { }
