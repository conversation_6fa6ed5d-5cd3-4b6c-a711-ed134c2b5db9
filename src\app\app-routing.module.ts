import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { AdminGuard } from './shared/guards/admin.guard';
import { AuthGuard } from './shared/guards/auth.guard';

const routes: Routes = [
  /* { path: '', redirectTo: 'home', pathMatch: 'full' },
 
   {
     path: 'home',
     loadChildren: () => import('./home/<USER>').then(m => m.HomeModule)
   },*/

  { path: '', redirectTo: 'new-home', pathMatch: 'full' },

  {
    path: 'new-home',
    loadChildren: () => import('./new-home/new-home.module').then(m => m.NewHomeModule)
  },

  {
    path: 'client',
    loadChildren: () => import('./client/client.module').then(m => m.ClientModule),
    canActivate: [AuthGuard]
  },
  {
    path: 'admin',
    loadChildren: () => import('./admin/admin.module').then(m => m.AdminModule),
    canActivate: [AdminGuard]
  },
  { path: 'users', loadChildren: () => import('./admin/users-list/users-list.module').then(m => m.UsersListModule) },
  { path: 'users', loadChildren: () => import('./admin/users-list/users-list.module').then(m => m.UsersListModule) },
  { path: 'jdeID', loadChildren: () => import('./admin/menu-jde/menu-jde.module').then(m => m.MenuJdeModule) },
  { path: 'carrier', loadChildren: () => import('./admin/carrier/carrier.module').then(m => m.CarrierModule) },
  { path: 'inspection', loadChildren: () => import('./admin/menu-inspection/menu-inspection.module').then(m => m.MenuInspectionModule) },
  { path: 'brokenDown', loadChildren: () => import('./client/menu-removal/menu-broken-down-trucks/menu-broken-down-trucks.module').then(m => m.MenuBrokenDownTrucksModule) },
  { path: 'callScreen', loadChildren: () => import('./client/menu-removal/menu-call-screen/menu-call-screen.module').then(m => m.MenuCallScreenModule) },
];

@NgModule({
  imports: [RouterModule.forRoot(routes, {
    initialNavigation: 'enabledNonBlocking'
  })],
  exports: [RouterModule]
})
export class AppRoutingModule { }
