import { Component, OnInit } from '@angular/core';
import { Chart } from 'chart.js';
import { MenuReportingService } from '../../menu-reporting/menu-reporting.service';

@Component({
  selector: 'clw-tile-inspection-ratio',
  templateUrl: './tile-inspection-ratio.component.html',
  styles: [
  ]
})
export class TileInspectionRatioComponent implements OnInit {
  isLoading: boolean;
  data: any;

  store: any

  constructor(
    private reportingSrv: MenuReportingService
  ) { }

  async ngOnInit() {
    try {
      this.isLoading = true;
      this.data = await this.reportingSrv.getNumberStatusAEs();
      this.renderCarrierChart();
    } catch (error) {
      console.log(error);
    } finally { this.isLoading = false; }
  }

  renderCarrierChart() {
    this.store.destroy();
    this.store = new Chart('ratio-inspection', {
      type: 'doughnut',
      data: {
        labels: ['BONS en pickup', 'BONS en rendu en attente', 'BONS en rendu en acceptés', 'BONS en rendu non assignés', 'BONS annulés'],
        datasets: [
          {
            // label: 'My First Dataset',
            data: [
              ((this.data.nbAEsPickup * 100) / this.data.nbAEs),
              ((this.data.nbAEsInAwait * 100) / this.data.nbAEs),
              ((this.data.nbAccepted * 100) / this.data.nbAEs),
              ((this.data.nbNotAssigned * 100) / this.data.nbAEs),
              ((this.data.nbAEsRenderRejected * 100) / this.data.nbAEs),
            ],
            backgroundColor: [
              `rgb(255,132, ${(this.data.nbAEsPickup * 100) / this.data.nbAEs})`,
              'rgb(54, 162, 235)',
              `rgb(255,${(this.data.nbAccepted * 100) % this.data.nbAEs}, 145)`,
              `rgb(${(this.data.nbNotAssigned * 100) / this.data.nbAEs},159, 132)`,
              `rgb(255, 10, 45)`,
            ],
          },
        ],
      },
      options: {},
    });
  }

  async refresh() {
    this.isLoading = true;
    try {
      this.data = await this.reportingSrv.getNumberStatusAEs();
      this.renderCarrierChart();
      this.isLoading = false;
    } catch (error) {
      console.log(error);
      this.isLoading = false;
      return error;
    }
  }
}
