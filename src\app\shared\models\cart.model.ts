import { RenderType } from "../enums/renderType.enum";
import { Packaging } from "./packaging";
import { Product } from "./product";
import { Shipping } from "./shipping";
import { Store } from "./store";


export class Cart {
  store?: Partial<Store>;
  items?: CartItem[];
  renderType?: RenderType;
  shipping?: Shipping;
  amount?: OrderPrice;
}

export declare type CartItem = {
  product?: Partial<Product>;
  packaging?: Partial<Packaging>;
  quantity?: number;
  quantityShipped?: number;
  quantityDelivery?: number;
  unitPrice?: number;
};
export declare type OrderPrice = {
  HT: number;
  precompte?: number;
  VAT: number;
  shipping: number;
  TTC: number;
  discount?: {
    [key: string]: number
  };
};

export declare type CartItemReseller = {
  product: Partial<Product>;
  quantity: number;
  quantityShipped?: number;
};
