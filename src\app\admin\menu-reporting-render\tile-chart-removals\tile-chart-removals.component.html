<div class="tile-chart-container tile-chart-line-container">
  <nb-card accent="success" [nbSpinner]="isLoading" nbSpinnerMessage="Chargement des données">
    <nb-card-header class="card-header-wrapper">
      <div class="card-header">
        Évolution des bons de commande
      </div>
    </nb-card-header>
    <nb-card-body>
      <div class="graphics-contain">
        <div class="card-header">
          <div class="filter-elt select">
            <nb-select placeholder="" [(selected)]="chartType" (selectedChange)="refresh()" status="success">
              <nb-option [value]="option" *ngFor="let option of chartTypes">{{option?.label}}</nb-option>
            </nb-select>
            <div class="text"> {{currentDate}} </div>
          </div>
        </div>
        <div class="canvas-contain">
          <canvas id="canvas-global-items" style="height: 260px; max-height: 260px !important; width: 100%" height="0px"
            width="100%">{{chart}}</canvas>
        </div>
      </div>
    </nb-card-body>
  </nb-card>
</div>