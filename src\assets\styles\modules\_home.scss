.home-container {
  .banner-section {
    margin-bottom: 2rem;
    width: 100%;
    height: 471px;
    background-image: url("./../../images/central-img.png");
    background-position: 50%;
    background-repeat: no-repeat;
    background-size: cover;
    .menu-bar {
      @include vh-end;
      height: 84px;
      @include container;
      .menu-options {
        color: $color-secondary;
        font-family: $font-regular;
        margin-right: 3rem;
        font-size: 16px;
        .red-mark {
          display: none;
          margin-top: 3px;
          width: 33px;
          background-color: #e83742;
          border: 1.5px solid #e83742;
        }
      }
      .menu-options:hover {
        .red-mark {
          display: flex;
        }
      }
    }

    .btn-connect {
      color: $color-secondary;
      font-family: $font-regular;
      font-size: 16px;
      border: 2px solid $color-secondary;
      padding: 7px 21px;
      border-radius: 4px;
    }
    .connexion {
      display: flex;
      align-items: flex-end;
      justify-content: flex-end;
      height: 100%;
      padding: 10px;

      .button-connect {
        box-sizing: border-box;
        border-radius: 4px;
        padding: 7px 21px;
      }
    }
  }
  .service-container {
    padding: 0 5rem;

    .our-services {
      @include v-baseline;
      justify-content: space-between;
      h6 {
        color: $bg-color-primary;
        font-family: $font-regular;
      }
      .empty-space {
        background-color: $bg-color-primary;
        height: 2px;
        width: 90%;
      }
    }
    .services-list {
      @include vh-center;
      flex-wrap: wrap;
      margin-top: 2rem;
      .order {
        @include vh-center;
        flex-direction: column;
        margin-bottom: 4%;
        width: 22%;
        margin-right: 36px;
        .order-icon {
          @include vh-center;
          width: 59%;
          height: 160px;
          background: $color-secondary;
          box-shadow: 0px 4px 9px rgba(0, 0, 0, 0.0863);
          border-radius: 6rem;
          margin-bottom: 16px;
        }
        h6 {
          color: $bg-color-primary;
          padding: 12px 0;
          text-align: center;
        }
        .more-infos {
          font-family: $font-regular;
          font-size: 14px;
          text-align: center;
          height: 100px;
        }
      }
      .order-icon:hover {
        transform: translateY(-0.25rem);
        box-shadow: 0px 3px 15px rgb(0 0 0 / 20%);
        transition: all 0.2s ease-in-out;
        cursor: pointer;
      }
    }
  }
  .green-footer {
    @include vh-center;
    height: 60px;
    background-color: $bg-color-primary;
    .footer-container {
      @include vh-between;
      width: 40%;
      .footer-text {
        color: $color-secondary;
        font-size: inherit;
        cursor: pointer;
        font-family: $font-regular;
      }
    }
  }
}
