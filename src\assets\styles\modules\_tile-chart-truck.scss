.tile-chart-truck-container {
  height: 450px;
  nb-card {
      max-height: 450px;
  }
  nb-card-body {
      padding: 0px;
  }
  .graphics-contain {
      @include vh-center;
      flex-direction: column;
      height: 340px;
      .card-header {
          @include v-center;
          justify-content: flex-start;
          height: 40px;
          width: 100%;
          padding: 0 16px;
          nb-toggle .text {
              font-size: 16px;
              font-family: $font-bold;
          }
          nb-toggle .toggle {
              height: 23px;
          }
          nb-toggle .toggle-switcher {
              width: 21px;
              height: 21px;
          }
          nb-toggle .toggle.checked .toggle-switcher {
              left: calc(100% - 21px - 0.0625rem);
          }
          .filter-elt.select {
              height: 20px;
              width: 100px;
              nb-select {
                  height: 20px;
                  width: 100%;
              }
              nb-select .select-button {
                  @include v-center;
                  height: 100%;
                  width: 100%;
                  box-sizing: border-box;
                  padding: 0px 0px 0px 16px;
                  font-family: $font-regular;
                  font-size: .8em;
              }
              nb-toggle .toggle {
                  background-color: #e7e7e7!important;
              }
              .filter-elt .nb-theme-default nb-select.appearance-outline.size-medium .select-button {
                  padding: 2px 20px!important;
              }
          }
      }
      .canvas-contain {
          // height: 260px;
          width: 100%;
          #canvas-global-items {
              width: 100%;
              // height: 260px;
          }
      }
  }
}
