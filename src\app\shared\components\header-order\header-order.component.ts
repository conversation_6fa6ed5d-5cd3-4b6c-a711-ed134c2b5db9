import { AuthService } from './../../services/auth.service';
import { NavigationEnd, Router } from '@angular/router';
import { filter } from 'rxjs/internal/operators/filter';
import { Location } from '@angular/common';
import { Component, Inject, OnInit, ViewEncapsulation } from '@angular/core';
import { User } from '../../models/user';
import { StorageService } from '../../services/storage.service';
import { NbMenuService, NbSidebarService, NB_WINDOW } from '@nebular/theme';
import { Subject } from 'rxjs';
import { RemovalService } from 'src/app/client/menu-removal/removal-management/removal.service';

@Component({
  selector: 'clw-header-order',
  templateUrl: './header-order.component.html',
  styles: [
  ],
  encapsulation: ViewEncapsulation.None
})
export class HeaderOrderComponent implements OnInit {

  connectedUser: User;
  factories: string[];
  factoriesAuthor: string[];

  protected layoutSize$ = new Subject();

  items = [
    { title: 'Se déconnecter', icon: 'power-outline' },
  ];
  reset: 'reset';

  constructor(
    @Inject(Router) private router: Router,
    private authSvr: AuthService,
    @Inject(NB_WINDOW) private window,
    private removalSvr: RemovalService,
    protected storageSrv: StorageService,
    private location: Location,
    private nbMenuService: NbMenuService,
    private sidebarService: NbSidebarService,
  ) {
    this.router.events.pipe(filter((event: any) => event instanceof NavigationEnd)).subscribe(() => {
      this.connectedUser = this.storageSrv.getObject('user');
    });
  }

  async ngOnInit(): Promise<void> {
    this.connectedUser = this.storageSrv.getObject('user');

    this.nbMenuService.onItemClick()
      .pipe(
        filter(({ tag }) => tag === 'my-context-menu'),
      )
      .subscribe(() => { this.signout() });

    await this.getDataForFilter()
  }

  async getDataForFilter() {
    try {
      const data = await this.removalSvr.getFilterData({
        // ...this.filterForm,
        // startDate: moment(this.filterForm.startDate).format('MM-DD-YYYY'),
        // endDate: moment(this.filterForm.endDate).format('MM-DD-YYYY'),
        keyForFilters: ['BusinessUnit']
      },);

      const result = data?.dataBusinessUnit ?? [];
      // this.factories = this.removeDuplicatesAndNullsAndSpaces(result)
      // if (this.connectedUser !== null) {

      //   this.factoriesAuthor = this.filterStore(this.factories, this.connectedUser?.rigths);
      // }
      // this.factoriesAuthor.push('CM00000');
      // if (!this.factoriesAuthor.length) {
      //   this.factoriesAuthor.push('CM10000');

      //   this.storageSrv.curentFactory = 'CM10000';
      // }

    } catch (error) {
      return error;
    }
  }

  nagivate() {
    this.router.navigate(['/client/order']);
  }

  goBack() {
    this.router.navigate(['/home'])
  }

  getImgProfil(): any {
    let img = this.storageSrv.getString('img');
    img = img ? img : 'assets/images/account.png';
    return {
      "background": "no-repeat center",
      "background-image": `url(${img})`,
      "background-size": "contain",
      "height": "44px",
      "width": "44px",
      "margin-left": "8px",
    }

  }

  toggleSidebar(): boolean {
    this.sidebarService.toggle(true);
    this.layoutSize$.next(null);
    return false;
  }

  signout(): void {
    this.authSvr.signout();
  }

  removeDuplicatesAndNullsAndSpaces<T>(data: string[]): string[] {
    const trimmedData = data.map((item) => item.trim());
    const uniqueSet = new Set(trimmedData.filter((item) => item !== null && item !== ''));
    return Array.from(uniqueSet);
  }

  onFactorySelected(event) {
    this.storageSrv.setObject('curentFactory', event)
    location.reload();
  }

  filterStore(store: any, userAuthorization: any) {
    const storeView = [];

    for (const element of store) {
      const authorized = userAuthorization[element] === true;

      if (authorized) {
        storeView.push(element);
      }
    }

    return storeView;
  }
}
