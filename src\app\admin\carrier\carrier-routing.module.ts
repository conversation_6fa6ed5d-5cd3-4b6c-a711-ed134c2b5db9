import { CarrierAddComponent } from './carrier-add/carrier-add.component';
import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { CarrierDetailComponent } from './carrier-detail/carrier-detail.component';
import { CarrierListComponent } from './carrier-list/carrier-list.component';

const routes: Routes = [
  { path: '', redirectTo: 'carrier-list', pathMatch: 'full' },
  {
    path: 'carrier-list',component:CarrierListComponent
  },
  {
    path: 'carrier-detail',component:CarrierDetailComponent
  },
  {
    path: 'carrier-add',component:CarrierAddComponent
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class CarrierRoutingModule { }
