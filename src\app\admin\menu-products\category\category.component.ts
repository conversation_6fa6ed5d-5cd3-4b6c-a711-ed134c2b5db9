import { StoreService } from './../../../shared/services/store.service';
import { Component, OnInit } from '@angular/core';
import { NbDialogService, NbToastrService } from '@nebular/theme';
import { CategoryService } from './category.service';
import * as moment from 'moment';
import { Router } from '@angular/router';
import { Location } from '@angular/common';

@Component({
  selector: 'clw-category',
  templateUrl: './category.component.html',
  styles: [
  ]
})
export class CategoryComponent implements OnInit {

  isLoading: boolean
  categories = [];
  dialogRef: any;
  selectedCategory: any;

  constructor(
    private dialogSvr: NbDialogService,
    private categorySvr: CategoryService,
    private toastrSvr: NbToastrService,
    private router: Router,
    private location: Location,
  ) { }

  async ngOnInit(): Promise<void> {
    this.categories = await this.categorySvr.getCategories();
  }

  openModal(dailog?: any): void {
    this.categorySvr.currentCategory = this.selectedCategory;
    this.dialogRef = this.dialogSvr.open(dailog, { closeOnBackdropClick: false, autoFocus: true })
    this.dialogRef.onClose.subscribe(() => {
    });


  }
  openAddModal(dialog: any): any {
    this.resetFields();
    this.openModal(dialog);
  }

  async addCategory(): Promise<any> {
    try {
      this.isLoading = true;
      await this.categorySvr.addCategories(this.selectedCategory);
      this.dialogRef.close();
      this.categories = await this.categorySvr.getCategories();
      this.toastrSvr.success('Catégorie ajoutée avec succès', 'Ajout réussi');
    } catch (error) {
      this.toastrSvr.danger('Nous n\'avons pas pu ajouter cette catégorie', 'Echec d\'ajout')
    } finally {
      this.isLoading = false;
    }
  }

  openEditModal(dialog: any, category: any): any {
    this.selectedCategory = { ...category };
    this.openModal(dialog);
  }

  async editCategory(): Promise<any> {
    try {
      this.isLoading = true;
      await this.categorySvr.editCategories(this.selectedCategory);
      this.dialogRef.close();
      this.categories = await this.categorySvr.getCategories();
      this.toastrSvr.success('Catégorie editée avec succès', 'Edition réussi');
    } catch (error) {
      this.toastrSvr.danger('Nous n\'avons pas pu editer cette catégorie', 'Echec d\'edition')
    } finally {
      this.isLoading = false;
    }
  }

  openDeleteModal(dialog: any, category: any): any {
    this.selectedCategory = { ...category };
    this.openModal(dialog);
  }

  async deleteCategory(): Promise<any> {
    try {
      this.isLoading = true;
      await this.categorySvr.deleteCategorys();
      this.dialogRef.close();
      this.categories = await this.categorySvr.getCategories();
      this.toastrSvr.success('Catégorie editée avec succès', 'Edition réussi');
    } catch (error) {
      this.toastrSvr.danger('Nous n\'avons pas pu editer cette catégorie', 'Echec d\'edition')
    } finally {
      this.isLoading = false;
    }
  }

  resetFields() {
    this.selectedCategory = {
      label: '',
      code: moment().valueOf()
    }
  }
  goBack() {
    if (window.history.length > 1) {
      this.location.back()
    } else {
      this.router.navigate(['/home'])
    }
  }
}
