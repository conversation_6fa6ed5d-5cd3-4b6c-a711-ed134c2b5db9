import { Location } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { Route, Router } from '@angular/router';
import { NbDialogRef, NbDialogService, NbToastrService } from '@nebular/theme';
import * as moment from 'moment';
import { CategoryProduct } from 'src/app/shared/models/category-product';
import { ErpItemId } from 'src/app/shared/models/jdeProductId';
import { Packaging } from 'src/app/shared/models/packaging';
import { Price } from 'src/app/shared/models/price';
import { Product } from 'src/app/shared/models/product';
import { Store } from 'src/app/shared/models/store';
import { StorageService } from 'src/app/shared/services/storage.service';
import { StoreService } from 'src/app/shared/services/store.service';
import { MenuJdeService } from './menu-jde.service';

@Component({
  selector: 'clw-menu-jde',
  templateUrl: './menu-jde.component.html',
  styles: [
  ]
})
export class MenuJdeComponent implements OnInit {
  isLoading: boolean;
  isConfirmLoading: boolean;
  dialogRef: any;
  enteredJde: any;
  erpItemsIds: ErpItemId[];
  stores: Store[];
  selectedStore: Store;
  productCategories: CategoryProduct[];
  selectedCategory: CategoryProduct;
  packagings: Packaging[];
  selectedPackaging: Packaging;
  selectedProduct: Product;
  products: Product[];
  adddialogRef: NbDialogRef<any>;
  editdialogRef: NbDialogRef<any>;
  deletedialogRef: NbDialogRef<any>;
  erpItemId: ErpItemId;

  constructor(
    private menuJdeSvr: MenuJdeService,
    private dialogSvr: NbDialogService,
    private toastSrv: NbToastrService,
    private storageSvr: StorageService,
    private storeSrv: StoreService,
    private router: Router,
    private location: Location,

  ) { }

  async ngOnInit(): Promise<void> {
    try {
      this.isLoading = true;
      const response = await this.storeSrv.getStores({});
      this.stores = response.data;
      this.productCategories = await this.menuJdeSvr.getProductCategories();
      this.packagings = await this.menuJdeSvr.getPackagings();
      this.products = await this.menuJdeSvr.getProducts();
      this.erpItemsIds = await this.menuJdeSvr.getJdeItemsIds() || [];
    } catch (error) {
      this.toastSrv.warning('Impossible de récupérer les données', 'Erreur de connexion');
    } finally { this.isLoading = false; }

  }

  openAddModal(dialog: any): any {
    this.resetFields();
    this.dialogRef = this.dialogSvr.open(dialog, { closeOnBackdropClick: false, autoFocus: true })
    this.dialogRef.onClose.subscribe(async (result) => {
      if (!result) { return; }
      this.erpItemsIds = await this.menuJdeSvr.getJdeItemsIds() || [],
        this.resetFields()
    });
  }


  goBack() {
    if (window.history.length > 1) {
      this.location.back()
    } else {
      this.router.navigate(['/home'])
    }
  }
  resetFields() {
    this.selectedPackaging = null;
    this.selectedStore = null;
    this.selectedCategory = null;
    this.selectedProduct = null;
    this.erpItemId = {
      storeLabel: '',
      categoryCode: 0,
      categoryLabel: '',
      packagingLabel: '',
      productLabel: '',
    }
  }

  selectCategory(category: CategoryProduct): void {
    this.selectedCategory = category;
  }

  async selectStore(store: Store): Promise<void> {
    this.selectedStore = store;
  }

  updateProducts() {
    this.storageSvr.setObject('jde', this.products);
  }

  selectPackaging(packaging: Packaging): void {
    this.selectedPackaging = packaging;
  }

  selectProduct(product: Product): void {
    this.selectedProduct = product;

  }

  async insertJdeID(): Promise<any> {
    this.isConfirmLoading = true;
    try {
      if (!this.erpItemId.value || !this.selectedPackaging || !this.selectedProduct || !this.selectedCategory || !this.selectedStore) {
        return this.toastSrv.danger("Veuillez renseigner le(s) champ(s) vide(s)", 'Donnée(s) manquante(s)');
      }

      this.erpItemId = {
        categoryCode: this.selectedCategory.code,
        categoryLabel: this.selectedCategory.label,
        packagingCode: this.selectedPackaging.code,
        packagingLabel: this.selectedPackaging.label,
        productErpRef: this.selectedProduct.erpRef,
        productLabel: this.selectedProduct.label,
        storeLabel: this.selectedStore.label,
        storeReference: this.selectedStore.jdeReference,
        value: this.erpItemId.value,
      }

      await this.menuJdeSvr.addItemId(this.erpItemId);
      this.dialogRef.close(true);
      this.toastSrv.success('Donnée ajoutée avec succès', 'Ajout réussi');
    }
    catch (error) {
      this.toastSrv.danger('Nous n\'avons pas pu ajouter cette donnée', 'Echec de l\'ajout')
    }
    finally {
      this.isConfirmLoading = false;
    }
  }

  openEditModal(dailog: any, erpItemId: ErpItemId): any {
    this.erpItemId = erpItemId;
    this.editdialogRef = this.dialogSvr.open(dailog, {})
    this.editdialogRef.onClose.subscribe(async (result) => {
      if (!result) { return }
      this.erpItemsIds = await this.menuJdeSvr.getJdeItemsIds() || [];
    });
  }

  openDeleteModal(dailog: any, erpItemId: ErpItemId): any {
    this.erpItemId = erpItemId;
    this.deletedialogRef = this.dialogSvr.open(dailog, {})
    this.deletedialogRef.onClose.subscribe(async (result) => {
      if (!result) { return; }
      this.erpItemsIds = await this.menuJdeSvr.getJdeItemsIds() || [];
    });
  }

  async updateJdeId(): Promise<any> {
    try {
      this.isConfirmLoading = true;
      if (!this.erpItemId?.value) {
        return this.toastSrv.danger('Veuillez renseigner tous les champs', 'Données incorrectes');
      }
      if (this.selectedProduct) {
        this.erpItemId.productErpRef = this.selectedProduct.erpRef;
        this.erpItemId.productLabel = this.selectedProduct.label;

      }
      if (this.selectedPackaging) {
        this.erpItemId.packagingCode = this.selectedPackaging.code;
        this.erpItemId.packagingLabel = this.selectedPackaging.label;

      }
      if (this.selectedCategory) {
        this.erpItemId.categoryCode = this.selectedCategory.code;
        this.erpItemId.categoryLabel = this.selectedCategory.label;

      }
      if (this.selectedStore) {
        this.erpItemId.storeReference = this.selectedStore.jdeReference;
        this.erpItemId.storeLabel = this.selectedStore.label;

      }

      await this.menuJdeSvr.editItemId(this.erpItemId);
      this.editdialogRef.close();
      this.toastSrv.success('ID JDE edité avec succès', 'Edition réussie');
    }
    catch (error) {
      this.toastSrv.danger('Nous n\'avons pas pu éditer cet ID JDE', 'Echec d\'édition')
    }
    finally {
      this.isConfirmLoading = false;
    }
  }

  async deleteJdeId(): Promise<any> {
    try {
      this.isConfirmLoading = true;
      if (!this.erpItemId)
        this.toastSrv.danger('Aucune donnée trouvée', 'Echec d\'affichage')
      await this.menuJdeSvr.deleteItemId(this.erpItemId);
      this.deletedialogRef.close(true);
      this.toastSrv.success('ID JDE supprimé avec succès', 'Suppression réussie');
    } catch (error) {
      this.toastSrv.danger('Nous n\'avons pas pu supprimer cet Id_Erp', 'Echec d\'edition')
    } finally { this.isConfirmLoading = false; }
  }
}
