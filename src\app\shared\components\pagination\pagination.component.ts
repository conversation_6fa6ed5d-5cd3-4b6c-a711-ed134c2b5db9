import { Component, EventEmitter, Input, Output, OnInit, OnChanges, SimpleChanges, ChangeDetectorRef } from '@angular/core';
import { PaginationService, PaginationConfig, PaginationResult } from '../../services/pagination.service';

@Component({
    selector: 'clw-pagination',
    templateUrl: './pagination.component.html',
    styleUrls: []
})
export class PaginationComponent implements OnInit, OnChanges {
    @Input() config: PaginationConfig;
    @Output() pageChange = new EventEmitter<number>();
    @Output() itemsPerPageChange = new EventEmitter<number>();

    result: PaginationResult;

    constructor(
        public paginationService: PaginationService,
        private cdr: ChangeDetectorRef
    ) { }

    ngOnInit(): void {
        this.updatePagination();
    }

    ngOnChanges(changes: SimpleChanges): void {
        if (changes['config']) {
            this.updatePagination();
            this.cdr.detectChanges();
        }
    }

    private updatePagination(): void {
        if (!this.config) {
            return;
        }
        this.result = this.paginationService.calculatePagination(this.config);
    }

    onPageChange(page: number): void {
        if (page < 1 || page > this.result.totalPages) {
            return;
        }
        this.pageChange.emit(page);
    }

    onItemsPerPageChange(itemsPerPage: number): void {
        this.itemsPerPageChange.emit(itemsPerPage);
    }
} 