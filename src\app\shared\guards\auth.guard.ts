import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, CanActivate, RouterStateSnapshot, UrlTree } from '@angular/router';
import { NbToastrModule, NbToastrService } from '@nebular/theme';
import { Observable } from 'rxjs';
import { StorageService } from '../services/storage.service';

@Injectable({
  providedIn: 'root'
})
export class AuthGuard implements CanActivate {

  constructor(
    private storageSvr: StorageService,
    private toastSvr: NbToastrService
    ){}
  canActivate(): boolean {
    const user = this.storageSvr.getObject('user');
    let { rigths } = user;
    // if (rigths?.isInspector && rigths?.isInspector === true) {
    //   this.toastSvr.danger(`vous n'aviez pas d'access`, 'Connexion refuser')
    //   return false;
    // }

    // this.toastSvr.success('Vos identifications ont été vérifiés', 'Connexion réussi');
    return true;
  }
}
