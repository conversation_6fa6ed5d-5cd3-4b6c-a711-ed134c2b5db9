<div class="common-form-container tracking-container">
    <div class="header">
        <nb-icon icon="undo-outline" [options]="{ animation: { type: 'zoom' } }" class="remove-icon" status="primary">
        </nb-icon>
        <h1 class="title">Suiv<PERSON> de la flotte</h1>
    </div>

    <div class="filter-container">
        <div class="left-block">
            <div class="filter-elt store-filter">
            </div>
        </div>
        <div class="btn-contain">
            <!-- <button nbButton outline status="success" class="search-btn filter-btn" (click)='search()'>
              <nb-icon icon="search-outline"></nb-icon>
              Filtrer
          </button> -->
        </div>
    </div>


    <nb-card class="card-container" [nbSpinner]="isLoading" nbSpinnerMessage="Chargement des données"
        nbSpinnerStatus="primary">
        <nb-card-header>
            <nb-list class="element-head  scrool-style">
                <nb-list-item class="list-elt-header list-elt paginator no-border">
                    <div class="col col-number">N°</div>
                    <div class="col col-immatriculation">Immatriculation</div>
                    <div class="col col-driver">Nom chauffeur</div>
                    <div class="col col-ae-number">N° Bon</div>
                    <div class="col col-ae-number">Code_Client</div>
                    <div class="col col-quantity">Quantité</div>
                    <div class="col col-time">Heure depart</div>
                    <div class="col col-actions">
                        <div class="actions">
                        </div>
                    </div>
                </nb-list-item>
            </nb-list>

        </nb-card-header>
        <nb-card-body>
            <nb-list class="element-head  scrool-style">
                <nb-list-item class="list-elt-header list-elt" *ngFor="let ae of aelist;index as i">
                    <div class="col col-number">{{i + 1}}</div>
                    <div class="col col-immatriculation">{{ae?.carrier?.truck?.immatriculation || 'non asigner'}}</div>
                    <div class="col col-driver">{{ae?.carrier?.driver?.fullname || 'non asigner'}}</div>
                    <div class="col col-ae-number">{{ae?.LoadNumber}}</div>
                    <div class="col col-ae-number">{{commonSvr.truncateString( ae?.soldto?.name, 15) || ae.soldto}}
                    </div>
                    <div class="col col-quantity">{{ae?.QuantityOrdered}}</div>
                    <div class="col col-time">{{ae?.ActualShipDate}}</div>

                    <div class="col col-actions">
                        <button nbTooltip="Detail" nbTooltipPlacement="top" nbTooltipStatus status="basic"
                            (click)='openDetailModal(detailDialog, ae)'>
                            <nb-icon icon="file-text-outline" status="basic"
                                [options]="{ animation: { type: 'zoom' } }"></nb-icon>
                        </button>
                        <button nbTooltip="Sur Carte" nbTooltipPlacement="top" status="basic"
                            (click)='openLocation(locationDialog, ae)'>
                            <nb-icon icon="pin-outline" status="basic"
                                [options]="{ animation: { type: 'zoom' } }"></nb-icon>
                        </button>
                        <button nbTooltip="Supprimer" nbTooltipPlacement="top" status="basic"
                            (click)='openDeleteModal(deleteDialog, ae)'>
                            <nb-icon icon="trash-2-outline" status="danger"
                                [options]="{ animation: { type: 'zoom' } }"></nb-icon>
                        </button>
                    </div>
                </nb-list-item>
                <nb-list-item class="not-found" *ngIf='aelist?.length===0'>
                    <img src="../../../../assets/images/EMPTY FOLDER VECTOR.png" alt="">
                    <h1>
                        Aucun Bon trouvé
                    </h1>
                </nb-list-item>
            </nb-list>
        </nb-card-body>
    </nb-card>
</div>


<ng-template #deleteDialog let-data let-ref="dialogRef">
    <nb-card>
        <nb-card-header class="form--header">Supprimer le tracking</nb-card-header>
        <nb-card-body>
            <p> Etes-vous sûr de vouloir supprimer {{selectedCategory.label}} ?</p>
        </nb-card-body>
        <nb-card-footer class="form--footer">
            <!-- <button nbButton outline ghost status="basic" class="" [disabled]="isLoading" (click)='ref.close()'>
            <nb-icon icon="close-square-outline" [options]="{ animation: { type: 'zoom' } }" class="remove-icon">
            </nb-icon>
            Annuler
        </button> -->
            <button nbButton outline status="danger" class="" [disabled]="isLoading" (click)='deleteCategory()'>
                <nb-icon icon="trash-2-outline" [options]="{ animation: { type: 'zoom' } }" class="remove-icon">
                </nb-icon>
                Fermer
            </button>
        </nb-card-footer>
    </nb-card>
</ng-template>

<ng-template #detailDialog let-data let-ref="dialogRef">
    <nb-card class="detail-location-container">
        <nb-card-header class="location-header">
            <nb-icon icon="car-outline" status="primary" size="large" [options]="{ animation: { type: 'zoom' } }"
                class="remove-icon"></nb-icon>
            <div class="center">
                <hr style="border-top: dashed 1px;" />
            </div>
            <nb-icon icon="pin-outline" status="primary" [options]="{ animation: { type: 'zoom' } }"
                class="remove-icon"></nb-icon>
        </nb-card-header>
        <nb-card-body>
            <h6 class="detail-header">Parcours Camion</h6>
            <div class="detail container" *ngFor="let location of selectedAe.carrier.travelData">
                <div class="first-section">
                    <p>Position géographique</p>
                    <p>Heure précise</p>
                </div>
                <hr />
                <div class="second-section">
                    <p>{{location?.location?.village}}</p>
                    <p>{{location?.arrivedTime}}</p>
                </div>
            </div>
        </nb-card-body>
        <nb-card-footer class="form--footer">
            <button nbButton outline status="basic" class="" [disabled]="isLoading" (click)='ref.close()'>
                <nb-icon icon="trash-2-outline" [options]="{ animation: { type: 'zoom' } }" class="remove-icon">
                </nb-icon>
                Fermer
            </button>
        </nb-card-footer>
    </nb-card>
</ng-template>

<ng-template #locationDialog let-data let-ref="dialogRef">
    <nb-card class="localisation-container">
        <nb-card-header class="form--header">Localisation</nb-card-header>
        <nb-card-body class="location-container">
            <div class="map" id="map"></div>
            <div class="right-block">
                <p>vitesse {{speed}}m/s</p>
                <!-- <p>info</p>
            <p>info</p> -->
            </div>
        </nb-card-body>
        <nb-card-footer class="form--footer">
            <button nbButton outline status="basic" class="" [disabled]="isLoading" (click)='ref.close()'>
                <nb-icon icon="trash-2-outline" [options]="{ animation: { type: 'zoom' } }" class="remove-icon">
                </nb-icon>
                Fermer
            </button>
        </nb-card-footer>
    </nb-card>
</ng-template>