<div class="common-form-container menu-removal-container">
  <div class="header">
    <!-- <nb-icon icon="undo-outline" [options]="{ animation: { type: 'zoom' } }" class="remove-icon" status="primary"
      (click)='goTo()'>
    </nb-icon> -->
    <h1 class="title">Historique des bons de commande en Pick up</h1>
  </div>

  <div class="removal-container">
    <div class="filter-area">
      <div class="left-area">
        <div class="filter-label">Filtrer par</div>
        <div class="filters">
          <nb-select status="success" class="nbSelect-width" size="small" [(selected)]="filterForm.status"
            (selectedChange)="onSelectionAEChange({keyToReset: $event == 'Tous les Statuts' ? 'status': '', $event})"
            placeholder="Statut">
            <nb-option [value]="filter?.code" *ngFor="let filter of statusFilters">
              {{filter?.label}}
            </nb-option>
          </nb-select>

          <input type="text" placeholder="N° Bon" fieldSize="small" nbInput class="empty-input height"
            [nbAutocomplete]="autoComplete1" [(ngModel)]="filterForm.LoadNumber"
            (ngModelChange)="onModelAEChange($event)" />
          <nb-autocomplete #autoComplete1 (selectedChange)="onSelectionAEChange({$event,
               keyToReset: $event == 'Tous les Bons'?  'LoadNumber' : '',  dataToReset: 'onModelAEChange'})">
            <nb-option *ngIf="loading"> Chargement... </nb-option>
            <nb-option value="Tous les Bons"> Tous les Bons </nb-option>
            <nb-option *ngFor="let option of dataAELabels" [value]="option">{{option}}
            </nb-option>
          </nb-autocomplete>

          <input type="text" placeholder="Transporteur" fieldSize="small" nbInput class="empty-input height"
            [nbAutocomplete]="autoComplete2" [(ngModel)]="filterForm.carrierLabel"
            (ngModelChange)="onModelCarrierChange($event)" />
          <nb-autocomplete #autoComplete2
            (selectedChange)="onSelectionAEChange({$event,
               keyToReset: $event == 'Tous les transporteurs'?  'carrierLabel' : '', dataToReset: 'onModelCarrierChange'})">
            <nb-option value="Tous les transporteurs"> Tous les transporteurs</nb-option>
            <nb-option *ngFor="let option of dataTransporters" [value]="option">{{option}}
            </nb-option>
            <nb-option *ngIf="loading"> Chargement... </nb-option>
          </nb-autocomplete>

          <input type="text" placeholder="Produit" fieldSize="small" nbInput class="empty-input height"
            [nbAutocomplete]="autoComplete3" [(ngModel)]="filterForm.ItemNumber"
            (ngModelChange)="onModelProductsChange($event)" />
          <nb-autocomplete #autoComplete3 (selectedChange)="onSelectionAEChange({$event,
              keyToReset: $event == 'Tous les Produits'?  'ItemNumber' : '', dataToReset: 'onModelProductsChange'})">
            <nb-option value="Tous les Produits"> Tous les Produits </nb-option>
            <nb-option *ngFor="let option of dataProducts" [value]="option">{{option }}
            </nb-option>
            <nb-option *ngIf="loading"> Chargement... </nb-option>
          </nb-autocomplete>

          <input type="text" placeholder="Client" fieldSize="small" nbInput class="empty-input height"
            [nbAutocomplete]="autoComplete4" [(ngModel)]="filterForm.company"
            (ngModelChange)="onModelCompanyChange($event)" />
          <nb-autocomplete #autoComplete4 (selectedChange)="onSelectionAEChange({$event,
              keyToReset: $event == 'Tous les clients'?  'company' :  '',  dataToReset: 'onModelCompanyChange'})">
            <nb-option value="Tous les clients"> Tous les clients </nb-option>
            <nb-option *ngFor="let option of dataSoldToDescLabels" [value]="option">{{option }}
            </nb-option>
            <nb-option *ngIf="loading"> Chargement... </nb-option>
          </nb-autocomplete>

          <input type="text" placeholder="Point de livraison" fieldSize="small" nbInput class="empty-input height"
            [nbAutocomplete]="autoComplete5" [(ngModel)]="filterForm.shipping"
            (ngModelChange)="onModelDeliveryPointChange($event)" />
          <nb-autocomplete #autoComplete5 (selectedChange)="onSelectionAEChange({$event,
               keyToReset: $event == 'Tous les Points'?  'shipping' : '', dataToReset: 'onModelDeliveryPointChange'})">
            <nb-option value="Tous les Points"> Tous les Points de livraison </nb-option>
            <nb-option *ngFor="let option of dataShipToDescLabels" [value]="option">{{option }}
            </nb-option>
            <nb-option *ngIf="loading && !dataShipToDescLabels?.length"> Chargement... </nb-option>
          </nb-autocomplete>

          <nb-select status="success" class="nbSelect-width" size="small" [(selected)]="filterForm.OrderType"
            (selectedChange)=" $event == 'Tous les types'? filterForm.OrderType = '' : null  ; onSelectionAEChange({$event})"
            placeholder="Type Bons">
            <nb-option value="Tous les types"> Tous les types </nb-option>
            <nb-option value="SO"> SO </nb-option>
            <nb-option value="ST"> ST </nb-option>
          </nb-select>

          <nb-select status="success" class="nbSelect-width" size="small" [(selected)]="filterForm.LoadStatus"
            (selectedChange)="onSelectionAEChange({keyToReset: $event == 'Tous les Statuts' ? 'status': '', $event})"
            placeholder="Statut X3">
            <nb-option [value]="filter?.code" *ngFor="let filter of statusFiltersJde">
              {{filter?.label}}
            </nb-option>
          </nb-select>

          <input nbInput [nbDatepicker]="datePickerStart" [(ngModel)]="filterForm.startDate" status="success"
            (ngModelChange)="onModelDateChange($event)" fieldSize="small" class="empty-input height"
            placeholder="Début" />
          <nb-datepicker #datePickerStart>
          </nb-datepicker>

          <input nbInput [nbDatepicker]="datepickerEnd" [(ngModel)]="filterForm.endDate" status="success"
            (ngModelChange)="onModelDateChange($event)" fieldSize="small" class="empty-input height"
            placeholder="Fin" />
          <nb-datepicker #datepickerEnd></nb-datepicker>
        </div>
      </div>
      <div class="right-area">
        <button nbButton status="success" fieldSize="small" class="search-btn" (click)="openDialogExport(exportDialog)">
          <nb-icon icon="download-outline"></nb-icon>
          EXPORTER
        </button>
        <button nbButton status="basic" fieldSize="small" class="search-btn reset-btn" (click)="reset()">
          <nb-icon icon="refresh-outline" [options]="{ animation: { type: 'zoom' } }"></nb-icon>
          REINITIALISER
        </button>
      </div>
    </div>
    <nb-card [nbSpinner]="isLoading">
      <nb-list class="element-head">
        <nb-list-item class="list-elt-header paginator">
          <div class="col col-paginator">
            <clw-pagination [config]="paginationConfig" (pageChange)="onPageChange($any($event))"
              (itemsPerPageChange)="onItemsPerPageChange($any($event))">
            </clw-pagination>
          </div>
        </nb-list-item>
        <nb-list-item class="list-elt-header paginator">
          <div class="col col-num">N°</div>
          <div class="col col-ref">N° Bon</div>
          <!-- <div class="col col-jde">Numéro commande</div>
          <div class="col col-name">Nom du client</div> -->
          <div class="col col-date">Date création</div>
          <div class="col col-date">Heure</div>
          <!-- <div class="col col-date">Transporteur</div> -->
          <div class="col col-name">Produits</div>
          <div class="col col-shipto">Code_Adresse</div>
          <div class="col col-shipto">Companies</div>
          <div class="col col-details">Quantité</div>
          <div class="col col-status">Statut</div>
          <div class="col col-action"></div>
        </nb-list-item>
      </nb-list>
      <nb-list class="element-head scrool-style" nbSpinnerStatus="primary">
        <nb-list-item class="list-elt-header" *ngFor="let removal of removals, index as i">
          <div class="col col-num">{{i+1}}</div>
          <div class="col col-ref">{{removal?.LoadNumber}}</div>
          <!-- <div class="col col-jde">{{removal.numOrder}}</div>
          <div class="col col-name">{{removal.customerName | truncateString:25}}</div> -->
          <div class="col col-date">{{removal?.dates?.created || removal?.dates?.updated | date:"dd/MM/YYYY"}}</div>
          <div class="col col-date">{{removal?.dates?.created || removal?.dates?.updated | date:"hh:mm"}} </div>
          <!-- <div class="col col-date" nbTooltip="{{removal?.carrier?.label}} || 'N/A'" nbTooltipPlacement="bottom"
            nbTooltipStatus="default">{{removal?.carrier?.label | truncateString :15}}</div> -->
          <div class="col col-name">{{getConcatenatedLabels(removal?.groupedOrders) | truncateString:32}}</div>
          <div class="col col-shipto" title="{{ removal?.ShipTo || 'N?A'}} ">
            {{truncate(removal?.ShipmentNumber, 20)}}</div>
          <div class="col col-shipto" title="{{ removal?.SoldToDescription || 'N?A'}} ">
            {{truncate(removal?.SoldToDescription, 20)}}</div>
          <div class="col col-details">{{getQuantityDelivery(removal?.groupedOrders)}}T</div>
          <div class="col col-status">
            <nb-badge [text]="removal?.enable == false ? '0' : removal?.status | getStatusRemovals" class="badge"
              position="top start"
              [status]="removal?.enable == false ? '0' : removal?.status | getColorStatusRemovals"></nb-badge>
          </div>
          <div class="col col-action">
            <div class="action-icons">
              <button nbTooltip="Detail" class="button-margin" nbTooltipPlacement="top" nbTooltipStatus
                (click)='openDetailModal(detailDialog, removal)'>
                <nb-icon icon="file-text-outline" status="primary" [options]="{ animation: { type: 'zoom' } }">
                </nb-icon>
              </button>
              <button nbTooltip="Parcours" class="button-margin" nbTooltipPlacement="top" nbTooltipStatus
                (click)='openTruckJourney(JourneyDialog, removal)'>
                <nb-icon icon="map-outline" status="primary" [options]="{ animation: { type: 'zoom' } }">
                </nb-icon>
              </button>
              <button nbTooltip="Télécharger" class="button-margin" nbTooltipPlacement="top" nbTooltipStatus
                (click)='downloadAePdf(removal)'>
                <nb-icon icon="download-outline" status="primary" [options]="{ animation: { type: 'zoom' } }">
                </nb-icon>
              </button>
              <button [nbTooltip]="isEnabled(removal)?'Désactiver Bon':'Activer Bon'" nbTooltipPlacement="top"
                nbTooltipStatus (click)="openEnableRemoval(deleteDialog, removal)">
                <nb-icon [icon]="isEnabled(removal)?'eye-outline':'eye-off-outline'"
                  [status]="isEnabled(removal)?'info':'danger'" [options]="{ animation: { type: 'zoom' } }"></nb-icon>
              </button>
            </div>
          </div>
        </nb-list-item>

        <nb-list-item class="empty-list" *ngIf="!removals?.length">
          <img src="../../../../assets/images/empty-list.png" alt="liste vide">
          Aucun Bon trouvé
        </nb-list-item>
      </nb-list>
    </nb-card>
  </div>
</div>


<ng-template #detailDialog let-data let-ref="dialogRef" class="dialogRef">
  <div class="order-detail-container her-width">
    <nb-card>
      <nb-card-header class="form--header">détail de l'autorisation</nb-card-header>
      <nb-card-body>
        <div class="order-infor order-pick-up">
          <table class="order-table">
            <tr>
              <td class="title">N° Bon:</td>
              <td class="order-right">{{removal?.LoadNumber}}</td>
            </tr>
            <tr>
              <td class="title">statut:</td>
              <td class="order-right">{{removal?.status | getStatusRemovals}}</td>
            </tr>
            <tr>
              <td class="title">Statut du chargement:</td>
              <td class="order-right">{{removal?.LoadStatus | getStatusLabelRemovals}}</td>
            </tr>
            <tr>
              <td class="title"> N° de commande(s):</td>
              <td class="order-right">{{removal?.erpReference?.join(', ') || 'non disponible'}}</td>
            </tr>
            <tr>
              <td class="title">Produit:</td>
              <td class="order-right">{{getConcatenatedLabels(removal?.groupedOrders)}}</td>
            </tr>
            <tr>
              <td class="title">Quantité de la commande:</td>
              <td class="order-right">{{getQuantity(removal?.groupedOrders) || 'non disponible'}}</td>
            </tr>
            <tr>
              <td class="title">Quantité à livrer:</td>
              <td class="order-right">{{getQuantityDelivery(removal?.groupedOrders)}}</td>
            </tr>
            <tr>
              <td class="title">Date de requête d'enlèvement:</td>
              <td class="order-right">{{removal?.RequestedDate}}</td>
            </tr>
            <tr>
              <td class="title">Transporteur :</td>
              <td class="order-right">{{removal?.carrier || 'non disponible'}}</td>
            </tr>
            <tr>
              <td class="title">Conducteur :</td>
              <td class="order-right">{{removal?.carrier?.driver?.fullname || 'non disponible'}}</td>
            </tr>
            <tr>
              <td class="title">Immatriculation:</td>
              <td class="order-right">{{removal?.carrier?.truckImmatriculation || 'non disponible'}}</td>
            </tr>
            <tr>
              <td class="title">Type d'enlèvement (Pickup/Rendu):</td>
              <td class="order-right">{{removal?.FreightHandlingCode | freightHandlingCode}} </td>
            </tr>
            <tr>
              <td class="title">Immatriculation du véhicule:</td>
              <td class="order-right">{{removal?.PrimaryVehicleID || 'non disponible'}}</td>
            </tr>
          </table>
        </div>
        <h2 style="text-align: center; margin: 1em 0;">Détails des temps d'attentes</h2>
        <div class="order-infor order-pick-up">
          <table class="order-table">
            <tr>
              <td class="title">Temps mis dans parking:</td>
              <td class="order-right">{{millisecondsToTime(removal?.newParkTimeTotal)}}</td>
            </tr>
            <tr>
              <td class="title">Temps mis dans parking usine:</td>
              <td class="order-right">{{millisecondsToTime(removal?.factoryParkTimeTotal)}}</td>
            </tr>
            <tr>
              <td class="title">Temps mis en panne:</td>
              <td class="order-right">{{millisecondsToTime(removal?.failureParkTimeTotal)}}</td>
            </tr>
            <tr>
              <td class="title">Temps mis dans le quai:</td>
              <td class="order-right">{{millisecondsToTime(removal?.dockParkTimeTotal)}}</td>
            </tr>
            <tr>
              <td class="title">Temps total d'attente:</td>
              <td class="order-right">{{millisecondsToTime(removal?.dockParkTimeTotal + removal?.factoryParkTimeTotal +
                removal?.newParkTimeTotal)}}</td>
            </tr>
          </table>
        </div>
      </nb-card-body>
      <nb-card-footer class="form--footer">
        <button nbButton outline status="basic" class="" (click)="ref.close()">
          <nb-icon icon="close-square-outline" [options]="{ animation: { type: 'zoom' } }" class="remove-icon">
          </nb-icon>
          fermer
        </button>
      </nb-card-footer>
    </nb-card>
  </div>
</ng-template>

<ng-template #editDialog let-data let-ref="dialogRef" class="dialogRef">
  <div class="removal-queue-container">
    <nb-card>
      <nb-card-header>
        <h6>Voulez vous modifier le Bon {{currentRemoval.LoadNumber}} et lui assigner un autre transporteur ?</h6>
      </nb-card-header>
      <nb-card-body>
        <div class="filter-elt">
          <nb-select status="success" size="small" [(selected)]="currentRemoval.carrier" fullWidth
            placeholder="Selectionnez un transporteur">
            <nb-option [value]="carrier" *ngFor="let carrier of carriers">{{carrier?.label}}</nb-option>
          </nb-select>
        </div>
      </nb-card-body>
      <nb-card-footer class="form--footer">
        <div class="footer-button">
          <button nbButton outline status="basic" class="" (click)="ref.close()">
            ANNULER
          </button>
          <button nbButton outline status="success" class="" (click)="editRemoval()">
            <nb-icon icon="done-all-outline" [options]="{ animation: { type: 'zoom' } }" class="remove-icon">
            </nb-icon>
            Valider
          </button>
        </div>
      </nb-card-footer>
    </nb-card>
  </div>
</ng-template>

<ng-template #deleteDialog let-data let-ref="dialogRef" class="dialogRef">
  <div class="removal-queue-container">
    <nb-card [nbSpinner]="isLoading">
      <nb-card-header>
        <h6>{{isEnabled()?'Désactivation':'Activation'}} dU Bon {{currentRemoval.LoadNumber}}</h6>
      </nb-card-header>
      <nb-card-body>
        <p>vous êtes sur le point {{isEnabled()?'de désactiver':'d\'activer'}}le Bon {{currentRemoval.LoadNumber}}</p>
      </nb-card-body>
      <nb-card-footer class="form--footer">
        <div class="footer-button">
          <button nbButton outline status="basic" class="" (click)="ref.close()">
            ANNULER
          </button>
          <button nbButton outline status="success" class="" (click)="ref.close(true)">
            <nb-icon icon="done-all-outline" [options]="{ animation: { type: 'zoom' } }" class="remove-icon">
            </nb-icon>
            Valider
          </button>
        </div>
      </nb-card-footer>
    </nb-card>
  </div>
</ng-template>

<ng-template #exportDialog let-data let-ref="dialogRef" class="dialogRef">
  <div class="export-container">
    <nb-card [nbSpinner]="isLoading">
      <nb-card-header>
        <h6>Exporter Bons </h6>
      </nb-card-header>
      <nb-card-body>
        <p>Sélectionnez un intervalle pour Exporter la liste</p>
        <div class="filter">
          <div class="filter-elt input date">
            <input nbInput [nbDatepicker]="datePickerStart" [(ngModel)]="startDate" status="success" size="small"
              placeholder="Début">
            <nb-datepicker #datePickerStart>
            </nb-datepicker>
          </div>

          <div class="filter-elt input date">
            <input nbInput id="test" [nbDatepicker]="datepickerEnd" [(ngModel)]="endDate" status="success" size="small"
              placeholder="Fin">
            <nb-datepicker #datepickerEnd></nb-datepicker>
          </div>
        </div>
      </nb-card-body>
      <nb-card-footer class="form--footer">
        <div class="footer-button">
          <button nbButton outline status="basic" class="" (click)="ref.close()">
            ANNULER
          </button>
          <button nbButton outline status="success" class="" (click)="exportAeToExcel()">
            <nb-icon icon="done-all-outline" [options]="{ animation: { type: 'zoom' } }" class="remove-icon">
            </nb-icon>
            Valider
          </button>
        </div>
      </nb-card-footer>
    </nb-card>
  </div>
</ng-template>

<ng-template #JourneyDialog let-data let-ref="dialogRef" class="dialogRef">
  <div class="order-detail-container">
    <nb-card>
      <nb-card-header class="form--header">Parcours Bon{{ removal?.LoadNumber}}</nb-card-header>
      <nb-card-body>
        <nb-list class="element-head timeline-header">
          <nb-list-item class="list-elt-header paginator">
            <div class="col col-status-rendu col-header">Déplacements</div>
            <div class="col col-details col-header">Horaires</div>
            <div class="col col-shipto col-header">Recap</div>
          </nb-list-item>
        </nb-list>
        <div class="timelines" *ngIf="truckJourneys?.length >= 2">
          <div class="journees">
            <div class="truck-journey" *ngFor="let truckJourney of truckJourneys,index as i">
              <div class="col">
                <ul class="{{ i === (truckJourneys.length - 1) ? 'timeline-end': 'timeline'}}">
                  <li class="timeline-event">
                    <label class="timeline-event-icon"></label>
                    <div class="timeline-event-copy">
                      <p class="timeline-event-thumbnail">{{truckJourney.name}}</p>
                    </div>
                  </li>
                </ul>
              </div>
              <div class="col col-container-timeline">
                <p class="text1">{{truckJourney.entry | date : 'short'}}</p>
                <div class="{{ i === (truckJourneys.length - 1) ? 'div-transparent': 'div'}}"></div>
                <div class="div-global-text">
                  <p class="text2 total-hours-value" *ngIf=" i === (truckJourneys.length - 1) ? false : true"
                    [ngStyle]="getColorStyle((truckJourneys[i + 1]).entry - truckJourney.entry)">{{
                    millisecondsToTime((truckJourneys[i + 1]).entry - truckJourney.entry) }}</p>
                </div>
              </div>
            </div>
          </div>
          <div class="col col-total-hours">
            <p class="total-hours-text">Durée moyenne</p>
            <p class="total-hours-value" [ngStyle]="getColorStyle(totalHoursJourney)">
              {{millisecondsToTime(totalHoursJourney)}}</p>
          </div>
        </div>
        <div class="empty-list flex" *ngIf="!truckJourneys?.length || truckJourneys?.length < 2">
          <img src="../../../../assets/images/empty-list.png" alt="liste vide">
          Aucun parcour trouvé
        </div>
      </nb-card-body>
      <nb-card-footer class="form--footer">
        <button nbButton outline status="basic" class="" (click)="ref.close()">
          <nb-icon icon="close-square-outline" [options]="{ animation: { type: 'zoom' } }" class="remove-icon">
          </nb-icon>
          fermer
        </button>
      </nb-card-footer>
    </nb-card>
  </div>
</ng-template>