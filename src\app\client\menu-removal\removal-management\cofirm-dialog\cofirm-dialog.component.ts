import { Component, OnInit, ViewEncapsulation } from '@angular/core';
import { NbDialogRef } from '@nebular/theme';
import { RemovalService } from '../removal.service';
import * as moment from 'moment';

@Component({
  selector: 'clw-cofirm-dialog',
  templateUrl: './cofirm-dialog.component.html',
  styles: [
  ],
  encapsulation: ViewEncapsulation.None
})
export class CofirmDialogComponent implements OnInit {
  isLoading: boolean;
  selectedAE: any;
  shippmentDate:any;
  deliveryDate:any;
  selectedCarrier: any;
  selectedTruck: any;
  shippement: string;
  data: any;
  minDate = new Date();

  constructor(
    private dialogRef: NbDialogRef<CofirmDialogComponent>,
    private removalSrv: RemovalService
  ) { }

  ngOnInit(): void {
    this.data = this.removalSrv.data;
    
    
  }

  close(close: boolean) { 
   const date = {
      shippmentDate:moment(this.shippmentDate).format("YYYY/MM/DD"),
      deliveryDate: moment(this.deliveryDate).format("YYYY/MM/DD"),
    }
    const shippement = this.shippement; 
    this.removalSrv.date = date;
    this.removalSrv.shippement = this.shippement;
    this.dialogRef.close(close) }

}
