<div class="common-form-container broken-down-truck-container">
  <div class="label-historic-order">
    <button nbButton>
      <nb-icon icon="undo-outline" (click)="goBack()" status="success"></nb-icon>
    </button>
    <h1 class="title">Liste des camions en panne</h1>
  </div>
  <div class="filter-area">
    <div class="left-area">
      <div class="filter-label">Filtrer par</div>
      <div class="filters">
        <!-- <input type="text" (ngModelChange)="onModelImmatriculationChange($event)"  placeholder="Immatriculation"
        fieldSize="small" nbInput class="empty-input" [nbAutocomplete]="autoComplete1" [(ngModel)]= 'currimmatriculation'
        [nbTooltip]="currimmatriculation || 'Rechercher'"/>
        <nb-autocomplete #autoComplete1 (selectedChange)="onSelectionImmatriculationChange($event)">
          <nb-option *ngFor="let option of filteredImmatriculation$ | async" [value]="option">{{option}}
          </nb-option>
      </nb-autocomplete> -->
        <input type="text" placeholder="Date d'entree" nbInput fieldSize="small" class="empty-input"
          [nbDatepicker]="datePickerStart" [disabled]="isLoading" [(ngModel)]="rangeFilter.start" />
        <nb-datepicker #datePickerStart>
        </nb-datepicker>
      </div>

      <!-- <input type="text" placeholder="Date de sortie" nbInput fieldSize="small" class="empty-input">
      <nb-datepicker>
      </nb-datepicker> -->
    </div>
    <div class="right-area">
      <button nbButton status="success" class="search-btn" (click)="getAllBrokenDown()">
        <nb-icon icon="search-outline"></nb-icon>
        RECHERCHER
      </button>
      <button nbButton status="primary" outline class="search-btn" (click)="resetFilters()">
        <nb-icon icon="refresh-outline"></nb-icon>
        RÉINITIALISER
      </button>
    </div>
  </div>
  <nb-card class="heigth-card">
    <!-- Container pour aligner tabsets et pagination horizontalement -->
    <div class="tabset-pagination-container">
      <nb-tabset>
        <nb-tab tabTitle="Camion en panne">
          <span></span> <!-- Contenu vide pour l'onglet -->
        </nb-tab>
      </nb-tabset>

      <!-- Pagination alignée à droite -->
      <div class="pagination-wrapper" *ngIf="total > 0">
        <clw-pagination [config]="paginationConfig" (pageChange)="onPageChange($event)"
          (itemsPerPageChange)="onItemsPerPageChange($event)">
        </clw-pagination>
      </div>
    </div>

    <!-- Contenu de l'onglet -->
    <div class="tab-content">
      <nb-card-body class="element-head" [nbSpinner]="isLoading" nbSpinnerStatus="primary">
          <div class="list-elt-header">
            <div class="col col-num title-wrong">N°</div>
            <div class="col col-ref title-wrong">N°Bon</div>
            <div class="col col-ref title-wrong">Code_Client</div>
            <div class="col col-ref title-wrong">Immatriculation</div>
            <div class="col col-date title-wrong">Transporteur</div>
            <div class="col col-name title-wrong">Nom conducteur</div>
            <div class="col col-ref title-wrong">Téléphone</div>
            <div class="col col-hour title-wrong">Attente</div>
            <div class="col col-action">
              <div class="action-icons"></div>
            </div>
          </div>
          <div class="list-elt-header" *ngFor="let removal of removals; index as i">
            <div class="col col-num">{{i+1}}</div>
            <div class="col col-ref">{{removal?.LoadNumber}}</div>
            <div class="col col-ref" title="{{ removal?.SoldToDescription ?? '' }}">{{removal?.SoldToDescription ??
              removal?.SoldTo | truncateString:15}}</div>
            <div class="col col-ref">{{
            removal?.carrier?.truck?.immatriculation ||
            removal?.carrier?.truckImmatriculation ||
            "Non renseigner"
            }}</div>
            <div class="col col-date ">{{removal?.carrier?.label ||
              removal?.carrier?.driver?.fullname || removal?.Alpha || 'N/A'}}</div>
            <div class="col col-name">{{removal?.carrier?.driver?.fullname |truncateString:15}}</div>
            <div class="col col-ref">{{removal?.carrier?.driver?.tel}}</div>
            <div class="col col-hour"> {{((brokenDownTime-removal?.carrier?.dateStartBrokenDown)-hourInMillisecond) |
              date:
              "mediumTime" }}</div>
            <div class="col col-action">
              <div class="action-icons">
                <button nbButton class="btn-margin btn-raduis" nbTooltip="Detail" outline nbTooltipPlacement="top"
                  nbTooltipStatus (click)="getDetailsTruck(detailBrokenDownTruckDialog,removal)">
                  <nb-icon icon="file-text-outline" status="basic"
                    [options]="{ animation: { type: 'zoom' } }"></nb-icon>
                </button>
                <button nbButton nbTooltip="file d'attente" class="btn-raduis" outline nbTooltipPlacement="top"
                  nbTooltipStatus (click)="setTruckWaitingTime(waitingTimeTruckDialog, removal)">
                  <nb-icon icon="arrow-circle-right-outline" status="primary"
                    [options]="{ animation: { type: 'zoom' } }"></nb-icon>
                </button>
              </div>
            </div>
          </div>
          <div *ngIf='removals?.length === 0 && !isLoading'>
            <div class="not-found">

              <img src="../../../../assets/images/icons/shipped.png" alt="">
              <h6>
                Aucun camion trouvé
              </h6>
            </div>
          </div>
        </nb-card-body>
    </div>
  </nb-card>
</div>
<ng-template #detailBrokenDownTruckDialog let-data let-ref="dialogRef">
  <div class="truckDetail">
    <nb-card>

      <nb-card-header class="form--header">Détails du camion {{removal?.carrier?.truck?.Immatriculation ||
        removal?.tractor?.label}}</nb-card-header>

      <nb-card-body class="truck-container">
        <div class="truck-infor">
          <div class="truck-label wrong">Conducteur</div>
          <div class="truck-label wrong">Immatriculation</div>
          <div class="truck-label wrong">Heure d'entrée</div>
          <div class="truck-label wrong">Date d'entrée</div>
        </div>
        <div class="truck-infor">
          <div class="truck-label">{{removal?.carrier?.driver?.fullname || removal?.driver || 'N/A'}}</div>
          <div class="truck-label">{{removal?.carrier?.truck?.immatriculation || removal?.tractor?.label}}</div>
          <div class="truck-label">{{(removal?.carrier?.truck?.dateStartBrokenDown || removal?.dateTruckStartBrokenDown
            | date: "mediumTime") || 'N/A' }}</div>
          <div class="truck-label">{{(removal?.carrier?.truck?.dateStartBrokenDown || removal?.dateTruckStartBrokenDown|
            date: "dd/MM/yyyy") || 'N/A' }}</div>
        </div>
      </nb-card-body>
      <nb-card-footer>
        <button nbButton outline status="primary" class="btn-raduis" (click)="ref.close()">
          <nb-icon icon="close-square-outline" status="primary"></nb-icon>
          Fermer
        </button>
      </nb-card-footer>
    </nb-card>
  </div>
</ng-template>

<ng-template #waitingTimeTruckDialog let-data let-ref="dialogRef">
  <div class="assign-truck">
    <nb-card>
      <nb-card-header>
        Confirmation
      </nb-card-header>
      <nb-card-body class="truck-assign">
        <div class="group-select">
          <div class="input">
            <p>Veuillez choisir la file dans la quelle vous voulez envoyer le camion
              <span>{{removal?.carrier?.truck?.immatriculation || removal?.carrier?.truckImmatriculation}}</span>
            </p>
            <nb-select [(selected)]="selectedQueue" [fullWidth]="true" status="success" size="medium"
              placeholder="Veuillez choisir une file">
              <nb-option [value]="queue" *ngFor="let queue of queues">{{queue}} </nb-option>
            </nb-select>
          </div>
          <div class="input">
            <p></p>
            <nb-select *ngIf='selectedQueue === "Chargement Usine"' status="success" size="medium"
              [(selected)]="selectedDock" placeholder="Veuillez choisir un Quais" [fullWidth]="true">
              <nb-option [value]="dock" *ngFor="let dock of docks"> {{dock.label}}</nb-option>
            </nb-select>
          </div>
        </div>
        <!-- <p>Veuillez choisir la file dans la quelle vous voulez envoyer le camion
          <span>{{removal?.carrier?.truck?.immatriculation || removal?.carrier?.truckImmatriculation}}</span></p>
        <div class="group-select">
          <nb-select status="success" class="nbSelect-width" size="small" [(selected)]="selectedQueue"
            placeholder="Veuillez choisir une file">
            <nb-option [value]="queue" *ngFor="let queue of queues">{{queue}} </nb-option>
          </nb-select>
          <nb-select *ngIf='selectedQueue === "Chargement Usine"' status="success" class="nbSelect-width" size="small"
            [(selected)]="selectedDock" placeholder="Veuillez choisir un Quais">
            <nb-option [value]="dock" *ngFor="let dock of docks"> {{dock.label}}</nb-option>
          </nb-select>
        </div> -->
      </nb-card-body>
      <nb-card-footer class="form--footer">
        <button nbButton ghost status="basic" (click)="ref.close()">
          <nb-icon icon="close-square-outline" status="basic"></nb-icon>
          Annuler
        </button>
        <button nbButton outline status="success" class="btn-border" (click)="setDateEndBrokenDown()">
          <nb-icon icon="save-outline" status="primary"></nb-icon>
          Enregistrer
        </button>
      </nb-card-footer>
    </nb-card>
  </div>
</ng-template>
<ng-template #moveTruckToDockDialog let-data let-ref="dialogRef">

  <div class="assign-truck">
    <nb-card>
      <nb-card-header>
        Confirmation
      </nb-card-header>
      <nb-card-body class="truck-assign">
        <div class="group-select">
          <div class="input">
            <p>Vous êtes sur le point d'envoyer le camion en Chargement usine
              <span>{{removal?.carrier?.truck?.immatriculation || removal?.carrier?.truckImmatriculation}}</span>
            </p>
          </div>
          <div class="input">
            <p></p>
            <nb-select status="success" size="medium" [(selected)]="selectedDock"
              placeholder="Veuillez choisir un Quais" [fullWidth]="true">
              <nb-option [value]="dock" *ngFor="let dock of docks"> {{dock?.label}}</nb-option>
            </nb-select>
          </div>
        </div>
      </nb-card-body>
      <nb-card-footer class="form--footer">
        <button nbButton ghost status="basic" (click)="ref.close()">
          <nb-icon icon="close-square-outline" status="basic"></nb-icon>
          Annuler
        </button>
        <button nbButton outline status="success" class="btn-border" (click)="setTruckBrokenDownEndDate()">
          <nb-icon icon="save-outline" status="primary"></nb-icon>
          Enregistrer
        </button>
      </nb-card-footer>
    </nb-card>
  </div>

</ng-template>
