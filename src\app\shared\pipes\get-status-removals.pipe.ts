import { Pipe, PipeTransform } from '@angular/core';
import { RenderTypeValueLibrary } from '../models/render-type.enum';
import { StatusRemovals } from '../models/status-removal.enum';
import { StoreListeLibrairie } from '../models/store';

@Pipe({ name: 'getStatusRemovals' })
export class GetStatusRemovalsPipe implements PipeTransform {

  transform(value: unknown, ...args: unknown[]): string {
    if (value === StatusRemovals.NOT_ASSIGNED) {
      return "Non assignée"
    }
    if (value === StatusRemovals.ASSIGNED) {
      return "Validée"
    }
    if (value === StatusRemovals.WAITING_VALIDATION) {
      return "Attente Validation"
    }
    if (value === StatusRemovals.QUEUED) {
      return 'En file d\'attente'
    }
    if (value === StatusRemovals.LOADED) {
      return "Chargée"
    }
    if (value === StatusRemovals.BROKEN_DOWN) {
      return "En panne"
    }
    if (value === StatusRemovals.NEW_PARKING) {
      return "Parking"
    }
    if (value == StatusRemovals.REJECTED) {
      return "Rejetée"
    }
    if (value == 0) {
      return "Désactivée"
    }

    return "Non assignée"
  }

}

@Pipe({ name: 'freightHandlingCode' })
export class GetFreightHandlingCodePipe implements PipeTransform {

  transform(value: any, ...args: unknown[]): string {
    return RenderTypeValueLibrary[value];
  }

}

@Pipe({ name: 'getStoreLabel' })
export class GetStoreLabelPipe implements PipeTransform {
  transform(value: any, ...args: unknown[]): string {
    return StoreListeLibrairie[value] ?? value;
  }

}

@Pipe({ name: 'getStatusTruck' })
export class GetStatusTruckPipe implements PipeTransform {
  transform(value: boolean): string {
    if (!value) return 'N/A';
    return value ? 'OPERATIONAL' : 'NOT OPERATIONAL';
  }

}
