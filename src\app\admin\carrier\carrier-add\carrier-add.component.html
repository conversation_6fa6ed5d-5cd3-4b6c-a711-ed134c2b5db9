<div class="common-form-container add-carrier-container">
  <div class="header">
    <button nbButton ghost (click)="cancel()">
      <nb-icon icon="undo-outline" (click)="goBack()" [options]="{ animation: { type: 'zoom' } }" class="remove-icon"
        status="primary">
      </nb-icon>

    </button>
    <h1 class="title">Ajouter un transporteur</h1>
  </div>
  <nb-card>
    <nb-card-body class="rm-padding" [nbSpinner]="isLoading">

      <nb-tabset>
        <nb-tab tabTitle="Informations Générales">


          <nb-card-body class="overflow">

            <div class="form-container">
              <div class="form-group">
                <div class="attribute-block">
                  <label class="label" for="nameInput">Raison sociale</label>
                </div>
                <input nbInput class="carrier-name input-width" status="basic" type="text"
                  placeholder="Veuillez entrer Votre Raison sociale" [(ngModel)]='carrier.label'>
              </div>
              <div class="form-group">
                <div class="attribute-block">
                  <label class="label" for="nameInput">NIU</label>
                </div>
                <input nbInput class="carrier-name input-width" status="basic" type="text"
                  placeholder="Veuillez entrer Votre NIU" [(ngModel)]='carrier.niu'>
              </div>
              <div class="form-group">
                <div class="attribute-block">
                  <label class="label" for="nameInput">Nº X3</label>
                </div>
                <input nbInput class="carrier-name input-width" status="basic" type="number"
                  placeholder="Veuillez entrer Votre Numéro X3" [(ngModel)]='carrier.njde'>
              </div>
              <div class="form-group">
                <div class="attribute-block">
                  <label class="label" for="nameInput">N° Tel</label>
                </div>
                <input nbInput class="carrier-name input-width" status="basic" type="number"
                  placeholder="Veuillez entrer Votre Contact" [(ngModel)]='carrier.tel'>
              </div>
              <div class="form-group">
                <div class="attribute-block">
                  <label class="label" for="nameInput">Email</label>
                </div>
                <input nbInput class="carrier-name input-width" status="basic" type="text"
                  placeholder="Veuillez entrer Votre Email" [(ngModel)]='carrier.email'>
              </div>
              <div class="form-group">
                <div class="attribute-block">
                  <label class="label" for="nameInput">Registre de commerce</label>
                </div>
                <input nbInput class="carrier-name input-width" status="basic" type="text"
                  placeholder="Veuillez entrer Votre Registre de commerce" [(ngModel)]='carrier.rccm'>
              </div>
            </div>

            <div class="carrier-card-header remove-padding">
              <label for="file" class="image-container">
                <div class="carrier-image width-img"
                  nbTooltip="{{(imgResultAfterCompress) ? 'Modifier votre photo de profil' : 'Ajouter une photo de profil'}}"
                  [ngStyle]="getImgStyle(imageSrc)">
                </div>
              </label>
              <input type="file" id="file" style="display: none;" (change)='getFile($event)'>
            </div>
          </nb-card-body>
        </nb-tab>
        <nb-tab tabTitle="Camions">
          <div class="truck-list">
            <nb-list class="element-head">
              <nb-list-item class="list-elt-header paginator">
                <div class="col col-paginator">
                  <!-- {{ startIndex }} - {{ endIndex }} sur {{ total }} -->
                  <!-- <nb-icon icon="arrow-ios-back-outline" [options]="{ animation: { type: 'zoom' } }"
                    nbTooltip="Page précédente" nbTooltipPlacement="bottom" nbTooltipStatus="default" status="success">
                  </nb-icon> -->
                  <div class="btn-contain">
                    <button nbButton status="success" class="search-btn" size="small"
                      (click)='openAddTrucsModal(addTruckDialog)'>
                      <nb-icon icon="plus-square-outline"></nb-icon>
                      Ajouter un Camion
                    </button>
                  </div>
                </div>
              </nb-list-item>
              <nb-list-item class="list-elt-header paginator">
                <div class="col col-num">N°</div>
                <div class="col col-truc-type">Type de camion</div>
                <div class="col col-matriculation">Immatriculation</div>
                <div class="col col-weight">Poids</div>
                <div class="col col-vol">Volume</div>
                <div class="col col-description">Description</div>

                <div class="col col-action"></div>
              </nb-list-item>
            </nb-list>
            <nb-list class="element-head scrool-style" [nbSpinner]="isLoading" nbSpinnerStatus="primary">
              <nb-list-item class="list-elt" *ngFor="let truck of trucks; index as i">
                <div class="col col-num">{{i+1}}</div>
                <div class="col col-truc-type">{{getTrucksTypes(truck?.type) || 'N/A' }}</div>
                <div class="col col-matriculation">{{truck?.immatriculation || 'N/A'}}</div>
                <div class="col col-weight">{{getTrucksCapacity(selectedTruck?.capacity) || 'N/A' }}</div>
                <div class="col col-vol">{{truck?.volume || 'N/A'}}</div>
                <div class="col col-description">{{truncate(truck?.desc, 30)|| 'N/A'}}</div>

                <div class="col col-action">
                  <div class="action-icons">
                    <!--    <button nbTooltip="Detail" nbTooltipPlacement="top" nbTooltipStatus>
                      <nb-icon icon="file-text-outline" status="primary" [options]="{ animation: { type: 'zoom' } }">
                      </nb-icon>
                    </button> -->
                    <!--
                                <button nbTooltip="{{truck?.enable? 'Desactiver' : 'Activer'}}" nbTooltipPlacement="top">
                                  <nb-icon icon="{{truck?.enable? 'eye-outline' : 'eye-off-outline'}}"
                                    status="{{truck?.enable?  'primary' : 'danger'}}" [options]="{ animation: { type: 'zoom' } }">
                                  </nb-icon>
                                </button> -->
                  </div>
                </div>
              </nb-list-item>
              <nb-list-item *ngIf='trucks?.length === 0'>
                <div class="not-found">

                  <img src="../../../../assets/images/icons/shipped.png" alt="">
                  <h6>
                    Aucun camion trouvé
                  </h6>
                </div>
              </nb-list-item>
            </nb-list>
          </div>
        </nb-tab>

        <nb-tab tabTitle="Chauffeurs">
          <div class="drivers-list">
            <nb-list class="element-head">
              <nb-list-item class="list-elt-header paginator">
                <div class="col col-paginator">
                  <!-- {{ startIndex }} - {{ endIndex }} sur {{ total }} -->
                  <div class="btn-contain">
                    <button nbButton status="success" class="search-btn" size="small"
                      (click)='openAddDriverModal(addDriverDialog)'>
                      <nb-icon icon="plus-square-outline"></nb-icon>
                      Ajouter un Chauffeur
                    </button>
                  </div>
                </div>
              </nb-list-item>
              <nb-list-item class="list-elt paginator">
                <div class="col col-num">N°</div>
                <div class="col col-driver-code">Code Chauffeur</div>
                <div class="col col-permit">N° Permis</div>
                <div class="col col-fullname">Nom Complet</div>
                <div class="col col-phone">Téléphone</div>
                <div class="col col-phone"> Deuxieme Téléphone</div>
                <div class="col col-cni">CNI</div>

                <div class="col col-action"></div>
              </nb-list-item>
            </nb-list>
            <nb-list class="element-head scrool-style" [nbSpinner]="isLoading" nbSpinnerStatus="primary">
              <nb-list-item class="list-elt" *ngFor="let driver of drivers; index as i">
                <div class="col col-num">{{i+1}}</div>
                <div class="col col-driver-code">{{driver?.driverCode}}</div>
                <div class="col col-permit">{{driver?.driverLicense}}</div>
                <div class="col col-fullname">{{truncate(driver?.fullname, 25)}}</div>
                <div class="col col-phone">{{driver?.tel1}}</div>
                <div class="col col-phone">{{driver?.tel2}}</div>
                <div class="col col-cni">{{driver?.cni}}</div>

                <div class="col col-action">
                  <div class="action-icons">
                    <!--    <button nbTooltip="Detail" nbTooltipPlacement="top" nbTooltipStatus>
                      <nb-icon icon="file-text-outline" status="primary" [options]="{ animation: { type: 'zoom' } }">
                      </nb-icon>
                    </button>
                    <button nbTooltip="Désactiver" nbTooltipPlacement="top">
                      <nb-icon icon="eye-off-outline" status="danger" [options]="{ animation: { type: 'zoom' } }">
                      </nb-icon>
                    </button> -->
                  </div>
                </div>
              </nb-list-item>
              <nb-list-item *ngIf='drivers?.length === 0'>
                <div class="not-found">

                  <img src="../../../../assets/images/icons/people.png" alt="">
                  <h6>
                    Aucun Chauffeur trouvé
                  </h6>
                </div>
              </nb-list-item>
            </nb-list>
          </div>

        </nb-tab>
        <!--     <nb-tab tabTitle="Utilisateurs">
          <nb-card [nbSpinner]='isLoading' nbSpinnerStatus='primary' nbSpinnerMessage='chargement' class="block">
            <nb-list class="element-head">
              <nb-list-item class="list-elt-header paginator">
                <div class="col col-paginator">
                  <div class="btn-contain">
                    <button nbButton status="success" class="search-btn" size="small"
                      (click)='openAddDriverModal(addUserDialog)'>
                      <nb-icon icon="plus-square-outline"></nb-icon>
                      Ajouter un Utilisateur
                    </button>
                  </div>
                </div>
              </nb-list-item>
            </nb-list>
            <div class="menus-block">

              <div class="menu-elt">
                <div class="user-image" [ngStyle]="getImgStyle()">
                   <div class="title-block">
                      <p *ngFor="let item of curveText('jude banseka', 180 ) " [style]='item.style'>{{item?.letter}}</p>
                    </div>
                </div>
                <div class="text-block">
                  jude banseka
                </div>
              </div>
            </div>
          </nb-card>
        </nb-tab> -->
      </nb-tabset>

    </nb-card-body>
    <nb-card-footer class="form--footer">
      <button nbButton ghost status="basic" (click)="cancel()">
        <nb-icon icon="close-square-outline"></nb-icon>
        Annuler
      </button>
      <button nbButton outline status="success" [disabled]="isLoading" class="btn-border" (click)="addCarrier()">
        <nb-icon icon="save-outline"></nb-icon>
        Enregistrer
      </button>
    </nb-card-footer>

  </nb-card>


</div>

<ng-template #addTruckDialog let-data let-ref="dialogRef">
  <div class="add-carrier-container">
    <nb-card class="add-truck">
      <nb-card-header class="form--header">Nouveau Camion</nb-card-header>


      <nb-card-body>
        <div class="add-container">
          <div class="form-container">
            <!-- <div class="form-group">
              <div class="attribute-block">
                <label class="label" for="nameInput">Type de camion</label>
              </div>
              <input nbInput class="carrier-name input-width" status="basic" type="text"
                placeholder="Veuillez entrer le Type de camion" [(ngModel)]='selectedTruck.type'>
            </div> -->
            <div class="form-group">
              <div class="attribute-block">
                <label class="label" for="nameInput">Type de camion</label>
              </div>
              <nb-select class="carrier-name input-width" fullWidth placeholder="Veuillez entrer le type de camion"
                status='primary' size="small" class="empty-input" [(selected)]='selectedTruck.type'>
                <nb-option [value]='type.value' *ngFor="let type of trucksTypes">{{type?.label}}
                </nb-option>
              </nb-select>
            </div>
            <div class="form-group">
              <div class="attribute-block">
                <label class="label" for="nameInput">Immatriculation</label>
              </div>
              <input nbInput class="carrier-name input-width" status="basic" type="text"
                placeholder="Veuillez entrer l'immatriculation" [(ngModel)]='selectedTruck.immatriculation'>
            </div>
            <div class="form-group">
              <div class="attribute-block">
                <label class="label" for="nameInput">Poids</label>
              </div>
              <nb-select class="carrier-name input-width" fullWidth placeholder="Veuillez entrer le poids du camion"
                status='primary' size="small" class="empty-input" [(selected)]='selectedTruck.capacity'>
                <nb-option [value]='capacity.value' *ngFor="let capacity of trucksCapacity">{{capacity?.label}}
                </nb-option>
              </nb-select>
              <!-- <input nbInput class="carrier-name input-width" status="basic" type="number"
                placeholder="Veuillez entrer Le Poids" [(ngModel)]='selectedTruck.capacity'> -->
            </div>
            <!-- <div class="form-group">
              <div class="attribute-block">
                <label class="label" for="nameInput">Volume</label>
              </div>
              <input nbInput class="carrier-name input-width" status="basic" type="text"
                placeholder="Veuillez entrer Le Volume" [(ngModel)]='selectedTruck.volume'>
            </div>
            <div class="form-group">
              <div class="attribute-block">
                <label class="label" for="nameInput">Description</label>
              </div>
              <textarea nbInput class="carrier-name input-width" status="basic" type="text"
                placeholder="Veuillez entrer Votre Description" [(ngModel)]='selectedTruck.desc'></textarea>
            </div> -->
          </div>
        </div>
      </nb-card-body>
      <nb-card-footer>
        <button nbButton ghost status="basic" (click)="ref.close()">
          <nb-icon icon="close-square-outline"></nb-icon>
          Annuler
        </button>
        <button nbButton outline status="success" [disabled]="isLoading" class="btn-border" (click)="addTruck()">
          <nb-icon icon="save-outline"></nb-icon>
          Enregistrer
        </button>
      </nb-card-footer>
    </nb-card>
  </div>
</ng-template>

<!-- <ng-template #addTruckDialog let-data let-ref="dialogRef">
  <div class="add-carrier-container">
    <nb-card class="add-truck">
      <nb-card-header class="form--header">Ajouter les informations</nb-card-header>


      <nb-card-body>
        <div class="add-container">
          <div class="form-container">
            <div class="form-group">
              <div class="attribute-block">
                <label class="label" for="nameInput">Type de camion</label>
              </div>
              <input nbInput class="carrier-name input-width" status="basic" type="text"
                placeholder="Veuillez entrer le Type de camion" [(ngModel)]='selectedTruck.type'>
            </div>
            <div class="form-group">
              <div class="attribute-block">
                <label class="label" for="nameInput">Immatriculation</label>
              </div>
              <input nbInput class="carrier-name input-width" status="basic" type="text"
                placeholder="Veuillez entrer l'Immatriculation" [(ngModel)]='selectedTruck.immatriculation'>
            </div>

            <div class="form-group">
              <div class="attribute-block">
                <label class="label" for="nameInput">Poids</label>
              </div>
              <input nbInput class="carrier-name input-width" status="basic" type="number"
                placeholder="Veuillez entrer Le Poids" [(ngModel)]='selectedTruck.capacity'>
            </div>
            <div class="form-group">
              <div class="attribute-block">
                <label class="label" for="nameInput">Volume</label>
              </div>
              <input nbInput class="carrier-name input-width" status="basic" type="text"
                placeholder="Veuillez entrer Le Volume" [(ngModel)]='selectedTruck.volume'>
            </div>
            <div class="form-group">
              <div class="attribute-block">
                <label class="label" for="nameInput">Description</label>
              </div>
              <textarea nbInput class="carrier-name input-width" status="basic" type="text"
                placeholder="Veuillez entrer Votre Description" [(ngModel)]='selectedTruck.desc'></textarea>
            </div>
          </div>
        </div>
      </nb-card-body>
      <nb-card-footer>
        <button nbButton ghost status="basic" (click)="ref.close()">
          <nb-icon icon="close-square-outline"></nb-icon>
          Annuler
        </button>
        <button nbButton outline status="success" [disabled]="isLoading" class="btn-border" (click)="addTruck()">
          <nb-icon icon="save-outline"></nb-icon>
          Enregistrer
        </button>
      </nb-card-footer>
    </nb-card>
  </div>
</ng-template> -->


<ng-template #addDriverDialog let-data let-ref="dialogRef">
  <div class="add-carrier-container">
    <nb-flip-card [showToggleButton]="false" [flipped]="flipped">
      <nb-card-front>
        <nb-card class="add-truck">
          <nb-card-header class="form--header">Ajouter les informations</nb-card-header>
          <nb-card-body>
            <nb-tabset>
              <nb-tab tabTitle="Informations Générales">
                <div class="add-container">
                  <div class="form-container">
                    <div class="form-group">
                      <div class="attribute-block">
                        <label class="label" for="nameInput">Code Chauffeur</label>
                      </div>
                      <input nbInput class="carrier-name input-width" status="basic" type="text"
                        placeholder="Veuillez entrer Le Code Chauffeur" [(ngModel)]='selectedDriver.driverCode'>
                    </div>
                    <div class="form-group">
                      <div class="attribute-block">
                        <label class="label" for="nameInput">Permis</label>
                      </div>
                      <input nbInput class="carrier-name input-width" status="basic" type="text"
                        placeholder="Veuillez entrer le Permis" [(ngModel)]='selectedDriver.driverLicense'>
                    </div>
                    <div class="form-group">
                      <div class="attribute-block">
                        <label class="label" for="nameInput">Nom</label>
                      </div>
                      <input nbInput class="carrier-name input-width" status="basic" type="text"
                        placeholder="Veuillez entrer Le Nom " [(ngModel)]='selectedDriver.lname'>
                    </div>
                    <div class="form-group">
                      <div class="attribute-block">
                        <label class="label" for="nameInput">Prenom</label>
                      </div>
                      <input nbInput class="carrier-name input-width" status="basic" type="text"
                        placeholder="Veuillez entrer Le Prenom" [(ngModel)]='selectedDriver.fname'>
                    </div>

                    <div class="form-group">
                      <div class="attribute-block">
                        <label class="label" for="nameInput">Téléphone</label>
                      </div>
                      <input nbInput class="carrier-name input-width" status="basic" type="number"
                        placeholder="Veuillez entrer Votre Contact" [(ngModel)]='selectedDriver.tel1'>
                    </div>
                    <div class="form-group">
                      <div class="attribute-block">
                        <label class="label" for="nameInput">Deuxieme téléphone</label>
                      </div>
                      <input nbInput class="carrier-name input-width" status="basic" type="number"
                        placeholder="Veuillez entrer Votre Contact" [(ngModel)]='selectedDriver.tel2'>
                    </div>
                    <div class="form-group">
                      <div class="attribute-block">
                        <label class="label" for="nameInput">CNI</label>
                      </div>
                      <input nbInput class="carrier-name input-width" status="basic" type="text"
                        placeholder="Veuillez entrer Le numero de CNI" [(ngModel)]='selectedDriver.cni'>
                    </div>
                    <div class="form-group">
                      <div class="attribute-block">
                        <label class="label" for="nameInput">Date d'Expiration du permis</label>
                      </div>
                      <input nbInput [nbDatepicker]="datepicker" class="carrier-name input-width"
                        placeholder="Veuillez choisir une date" [(ngModel)]='selectedDriver.licenseExpireDate'>
                      <nb-datepicker #datepicker></nb-datepicker>
                    </div>
                  </div>
                </div>
              </nb-tab>
              <nb-tab tabTitle="Formations" class="tab-padding">
                <nb-list class="element-head">
                  <nb-list-item class="list-elt-header list-head">
                    <div class="col col-paginator">
                      <div class="btn-contain">
                        <button nbButton status="success" class="search-btn" size="small" (click)='addTraining()'>
                          <nb-icon icon="plus-square-outline"></nb-icon>
                          Ajouter une formation
                        </button>
                      </div>
                    </div>
                  </nb-list-item>
                  <nb-list-item class="list-elt-header">
                    <div class="col col-tain-num">N°</div>
                    <div class="col col-tain-label">Libelé</div>
                    <div class="col col-tain-trainer">Formateur</div>
                    <div class="col col-tain-date">Date de début</div>
                    <div class="col col-tain-date">Date de fin</div>
                    <div class="col col-tain-date">Date d'expiration</div>

                    <div class="col col-tain-action"></div>
                  </nb-list-item>
                </nb-list>
                <nb-list class="element-head scrool-style" [nbSpinner]="isLoading" nbSpinnerStatus="primary">
                  <nb-list-item class="list-elt" *ngFor="let training of selectedDriver.trainings; index as i">
                    <div class="col col-tain-train">{{i+1}}</div>
                    <div class="col col-tain-label">{{truncate(training?.trainingType, 10)|| 'N/A'}}</div>
                    <div class="col col-tain-trainer">{{truncate(training?.trainerName, 10)|| 'N/A'}}</div>
                    <div class="col col-tain-date">{{training?.date?.start | date:'dd/MM/YYYY' || 'N/A'}}</div>
                    <div class="col col-tain-date">{{training?.date?.end | date:'dd/MM/YYYY'|| 'N/A'}}</div>
                    <div class="col col-tain-date">{{training?.date?.expiredDate | date:'dd/MM/YYYY'|| 'N/A'}}</div>
                    <div class="col col-tain-action">
                      <button nbTooltip="Modifier" nbButton ghost nbTooltipPlacement="top" (click)="editTraining(i)">
                        <nb-icon icon="edit-2-outline" status="warning" [options]="{ animation: { type: 'zoom' } }">
                        </nb-icon>
                      </button>
                    </div>

                  </nb-list-item>
                  <nb-list-item *ngIf='selectedDriver.trainings?.length === 0 || !selectedDriver.trainings'>
                    <div class="not-found">

                      <img src="../../../../assets/images/icons/npm.png" alt="">
                      <h6>
                        Aucune formation trouvé
                      </h6>
                    </div>
                  </nb-list-item>
                </nb-list>
              </nb-tab>
            </nb-tabset>
          </nb-card-body>
          <nb-card-footer>
            <button nbButton ghost status="basic" (click)="ref.close()">
              <nb-icon icon="close-square-outline"></nb-icon>
              Annuler
            </button>
            <button nbButton outline status="success" [disabled]="isLoading" class="btn-border" (click)="addDriver()">
              <nb-icon icon="save-outline"></nb-icon>
              Enregistrer
            </button>
          </nb-card-footer>
        </nb-card>
      </nb-card-front>
      <nb-card-back>
        <nb-card class="add-truck" *ngIf="flipped&&isNewTraining">
          <nb-card-header class="form--header">Ajouter une formation</nb-card-header>
          <nb-card-body>
            <div class="add-container">
              <div class="form-container">
                <div class="form-group">
                  <div class="attribute-block">
                    <label class="label" for="nameInput">Libelé</label>
                  </div>
                  <input nbInput class="carrier-name input-width" status="basic" type="text"
                    placeholder="Entrez un Libelé" [(ngModel)]='selectedTraining.trainingType'>
                </div>
                <div class="form-group">
                  <div class="attribute-block">
                    <label class="label" for="nameInput">Nom du Formateur</label>
                  </div>
                  <input nbInput class="carrier-name input-width" status="basic" type="text" placeholder="Entrez un nom"
                    [(ngModel)]='selectedTraining.trainerName'>
                </div>
                <div class="form-group">
                  <div class="attribute-block">
                    <label class="label" for="nameInput">Date début</label>
                  </div>
                  <input nbInput class="carrier-name input-width" status="basic" [nbDatepicker]="trainDatePickerStart"
                    [(ngModel)]='trainDateStart' (ngModelChange)="updateTrainDateStart()">
                  <nb-datepicker #trainDatePickerStart></nb-datepicker>
                </div>
                <div class="form-group">
                  <div class="attribute-block">
                    <label class="label" for="nameInput">Date de fin</label>
                  </div>
                  <input nbInput class="carrier-name input-width" status="basic" [nbDatepicker]="trainDatePickerEnd"
                    [(ngModel)]='trainDateEnd' (ngModelChange)="updateTrainDateEnd()">
                  <nb-datepicker #trainDatePickerEnd></nb-datepicker>
                </div>
                <div class="form-group">
                  <div class="attribute-block">
                    <label class="label" for="nameInput">Date d'expiration</label>
                  </div>
                  <input nbInput class="carrier-name input-width" status="basic" [nbDatepicker]="trainDatePickerexpired"
                    [(ngModel)]='trainDateExpired' (ngModelChange)="updateTrainDateExpired()">
                  <nb-datepicker #trainDatePickerexpired></nb-datepicker>
                </div>
              </div>
            </div>
          </nb-card-body>
          <nb-card-footer>
            <button nbButton ghost status="basic" (click)="toggle()">
              <nb-icon icon="close-square-outline"></nb-icon>
              Annuler
            </button>
            <button nbButton outline status="success" [disabled]="isLoading" class="btn-border"
              (click)="pushTraining()">
              <nb-icon icon="save-outline"></nb-icon>
              Enregistrer
            </button>
          </nb-card-footer>
        </nb-card>
        <nb-card class="add-truck" *ngIf="flipped&&isEditTraining">
          <nb-card-header class="form--header">Modifier Formation</nb-card-header>
          <nb-card-body>
            <div class="add-container">
              <div class="form-container">
                <div class="form-group">
                  <div class="attribute-block">
                    <label class="label" for="nameInput">Libelé</label>
                  </div>
                  <input nbInput class="carrier-name input-width" status="basic" type="text"
                    placeholder="Libelé inconnu"
                    [(ngModel)]='selectedDriver.trainings[selectedTrainingIndex].trainingType' readonly>
                </div>
                <div class="form-group">
                  <div class="attribute-block">
                    <label class="label" for="nameInput">Nom du Formateur</label>
                  </div>
                  <input nbInput class="carrier-name input-width" status="basic" type="text" placeholder="Nom inconnu"
                    [(ngModel)]='selectedDriver.trainings[selectedTrainingIndex].trainerName' readonly>
                </div>
                <div class="form-group">
                  <div class="attribute-block">
                    <label class="label" for="nameInput">Date début</label>
                  </div>
                  <input nbInput class="carrier-name input-width" status="basic" [nbDatepicker]="trainDatePickerStart"
                    [(ngModel)]='trainDateStart' (ngModelChange)="updateTrainDateStart()">
                  <nb-datepicker #trainDatePickerStart></nb-datepicker>
                </div>
                <div class="form-group">
                  <div class="attribute-block">
                    <label class="label" for="nameInput">Date de fin</label>
                  </div>
                  <input nbInput class="carrier-name input-width" status="basic" [nbDatepicker]="trainDatePickerEnd"
                    [(ngModel)]='trainDateEnd' (ngModelChange)="updateTrainDateEnd()">
                  <nb-datepicker #trainDatePickerEnd></nb-datepicker>
                </div>
                <div class="form-group">
                  <div class="attribute-block">
                    <label class="label" for="nameInput">Date d'expiration</label>
                  </div>
                  <input nbInput class="carrier-name input-width" status="basic" [nbDatepicker]="trainDatePickerexpired"
                    [(ngModel)]='trainDateExpired' (ngModelChange)="updateTrainDateExpired()">
                  <nb-datepicker #trainDatePickerexpired></nb-datepicker>
                </div>
              </div>
            </div>
          </nb-card-body>
          <nb-card-footer>
            <button nbButton ghost status="basic" (click)="toggle()">
              <nb-icon icon="close-square-outline"></nb-icon>
              Annuler
            </button>
            <button nbButton outline status="success" [disabled]="isLoading" class="btn-border"
              (click)="pushEditTraining()">
              <nb-icon icon="save-outline"></nb-icon>
              Enregistrer
            </button>
          </nb-card-footer>
        </nb-card>
      </nb-card-back>
    </nb-flip-card>
  </div>
</ng-template>

<ng-template #addUserDialog let-data let-ref="dialogRef">
  <div class="add-carrier-container">
    <nb-card class="add-user">
      <nb-card-header class="form--header">Ajouter les informations</nb-card-header>

      <nb-card-header class="user-card-header remove-padding">
        <label for="file">
          <div class="user-image width-img"
            nbTooltip="{{(imgResultAfterCompress) ? 'Modifier votre photo de profil' : 'Ajouter une photo de profil'}}"
            [ngStyle]="getImgStyle(imageSrc)">
          </div>
        </label>
        <input type="file" id="file" style="display: none;" (change)='getFile($event)'>
      </nb-card-header>
      <nb-card-body>
        <div class="form-group">
          <div class="attribute-block">
            <div class="icon-btn">
              <nb-icon icon="person-outline"></nb-icon>
            </div>
            <label class="label" for="nameInput">Nom</label>
          </div>
          <input nbInput class="user-name input-width" status="basic" type="text" placeholder="Veuillez entrer le nom">
        </div>
        <div class="form-group">
          <div class="attribute-block">
            <div class="icon-btn">
              <nb-icon icon="person-outline"></nb-icon>
            </div>
            <label class="label" for="nameInput">Prénom</label>
          </div>
          <input nbInput class="user-name input-width" status="basic" type="text"
            placeholder="Veuillez entrer le prénom">
        </div>
        <div class="form-group">
          <div class="attribute-block">
            <div class="icon-btn">
              <nb-icon icon="home-outline"></nb-icon>
            </div>
            <label class="label" for="nameInput">Matricule</label>
          </div>
          <input nbInput class="user-name input-width" status="basic" type="text"
            placeholder="Veuillez entrer le matricule">
        </div>
        <div class="form-group">
          <div class="attribute-block">
            <div class="icon-btn">
              <nb-icon icon="phone-outline"></nb-icon>
            </div>
            <label class="label" for="nameInput">N° tél</label>
          </div>
          <input nbInput class="user-name input-width" status="basic" type="number"
            placeholder="Veuillez entrer le N° tél" min="10" max="10">
        </div>
        <div class="form-group">
          <div class="attribute-block">
            <div class="icon-btn">
              <nb-icon icon="email-outline"></nb-icon>
            </div>
            <label class="label" for="nameInput">Email</label>
          </div>
          <input nbInput class="user-name input-width" status="basic" type="email" placeholder="Veuillez entrer l'email"
            pattern=".+@globex\.com" size="30" required>
        </div>
      </nb-card-body>
      <nb-card-footer>
        <button nbButton ghost status="basic">
          <nb-icon icon="close-square-outline"></nb-icon>
          Annuler
        </button>
        <button nbButton outline status="success" [disabled]="isLoading" class="btn-border">
          <nb-icon icon="save-outline"></nb-icon>
          Enregistrer
        </button>
      </nb-card-footer>
    </nb-card>
  </div>
</ng-template>