import { Component, Inject, OnInit, ViewEncapsulation } from '@angular/core';
import { Router } from '@angular/router';
import { AuthService } from '../../services/auth.service';

@Component({
  selector: 'clw-header-home',
  templateUrl: './header-home.component.html',
  styles: [
  ],
  encapsulation: ViewEncapsulation.None
})
export class HeaderHomeComponent implements OnInit {
  isLoading: boolean;



  constructor(
    @Inject(Router) private router: Router,
    private authSvr: AuthService,

  ) { }

  ngOnInit(): void {
    this.isLoading = false;
  }

  openLoginForm() {
    this.authSvr.formStatus.isActive = !this.authSvr.formStatus.isActive
  }


  openLogoutModal() {
    this.isLoading = true;
    this.authSvr.signout();
    this.isLoading = false;
  }

  isUserConnected() {
    return this.authSvr.isUserConnected();
  }

}
