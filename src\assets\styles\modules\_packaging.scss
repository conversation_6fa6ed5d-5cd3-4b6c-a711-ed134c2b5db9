.packaging-container {

    filter-container {
        .left-block {
            .filter-elt {
                width: 14%;
            }
            .store-filter {
                width: 23%;
            }
            .product-filter {
                width: 29%;
            }
        }
        .btn-contain {
            @include v-center;
            .space {
                margin-right: 15px;
            }
            .search-btn {
                width: 50%;
                margin-right: 5px;
            }
            .color-text {
                color: #fff;
            }
        }
    }

    nb-card-body {
        max-height: 65vh;
    }

    .not-found {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        min-height: 200px;
        img {
            height: 140px;
        }
        h1 {
            font-size: 18px;
        }
    }
    .list-elt {
        .col-label {
            width: 15%;
        }
        .col-code {
            width: 15%;
        }
        .col-unit {
            width: 15%;
        }
        .col-ton {
            width: 20%;
        }
        .col-base {
            width: 15%;
        }

        .col-actions {
            width: 5%;
            button {
                background: none;
            }
        }
    }
}


.packaging-dialog-container {

    nb-card {
        max-width: 35vw;
        .row {
            display: flex;
            justify-content: space-between;
            .input {
                width: 45%;
            }
        }
    }
}
