import * as moment from 'moment';
import { Router } from '@angular/router';
import { Location } from '@angular/common';
import cloneDeep from 'lodash-es/cloneDeep';
import { Component, OnInit, TemplateRef } from '@angular/core';
import { RemovalService } from '../removal-management/removal.service';
import { CommonService } from 'src/app/shared/services/common.service';
import { TruckInspection } from 'src/app/shared/models/truck-inspection';
import { NbDialogRef, NbDialogService, NbToastrService } from '@nebular/theme';
import { StatusRemovals } from 'src/app/shared/models/status-removal.enum';
import { AuthorizationRemoval } from 'src/app/shared/models/authorization-removal';
import { Dock } from 'src/app/shared/models/dock';


@Component({
  selector: 'clw-material-inspections',
  templateUrl: './material-inspections.component.html',
  styles: [
  ]
})
export class MaterialInspectionsComponent implements OnInit {


  dialogRef: NbDialogRef<any>;
  actionStatus: number;
  isLoading: boolean;
  displayLoading: boolean;
  dataCarrierLabels: string[];

  inspections: TruckInspection[];
  loadedInspections: TruckInspection[];
  currentInspections: TruckInspection;
  statusChoices: { label: string, value: boolean }[] = [{ label: 'OK', value: true }, { label: 'PAS OK', value: false }];
  InspectionDecisions = ['ACCEPTEE', 'REJETEE'];
  docks: Dock[];
  dataOrderTypeLabels: any[] = [];

  dataForFilter: any;

  orderTypeFilter: any;
  filteredOptions$: any;
  loadNumber = '';

  filterForm = {

    LoadNumber: 0,
    ItemNumber: '',
    SoldTo: '',
    FreightHandlingCode: '',
    OrderType: '',
  };

  constructor(
    private router: Router,
    private dialogSrv: NbDialogService,
    private removalSrv: RemovalService,
    public commonSrv: CommonService,
    private toastSrv: NbToastrService,
    private location: Location,
  ) { }

  async ngOnInit() {
    await this.getDataForFilter();
    await this.getInspections();
    await this.getloadedInspections();
  }

  async getDataForFilter() {
    try {
      this.isLoading = true;
      this.dataForFilter = await this.removalSrv.getFilterData({
        ...this.filterForm,
        keyForFilters: ['OrderType', 'carrier.label']
      },);
      this.dataOrderTypeLabels = this.dataForFilter?.dataOrderType ?? [];
      this.dataCarrierLabels = this.dataForFilter['datacarrierlabel'] ?? [];

    } catch (error) {
      console.error(error);
      return error;
    } finally { this.isLoading = false }
  }

  async getInspections(): Promise<void> {

    try {
      this.isLoading = true;
      const { data } = await this.removalSrv.getTruckInspections({ truckStatus: 450 });
      this.inspections = data;
    }
    catch (error) {
      this.toastSrv.danger('Impossible de récupérer la liste des BONS', 'Erreur connexion !');

    } finally { this.isLoading = false; }
  }
  async getloadedInspections(): Promise<void> {

    try {
      this.isLoading = true;
      const { data } = await this.removalSrv.getTruckInspections({ truckStatus: 500 });
      this.loadedInspections = data;
    }
    catch (error) {
      this.toastSrv.danger('Impossible de récupérer la liste des BONS', 'Erreur connexion !');

    } finally { this.isLoading = false; }
  }

  verifyTel(event: any): any {
    const phoneNumber = event?.target?.value || '';
    const sanitizedPhoneNumber = this.commonSrv.sanitizePhoneNumber(phoneNumber);

    if (this.commonSrv.isValidPhoneNumber(sanitizedPhoneNumber)) {
      this.currentInspections.driverTel = sanitizedPhoneNumber;
      return this.toastSrv.danger('Renseignez le bon format du numéro de téléphone, ex: 6xXxXxXxX');
    }

    this.currentInspections.driverTel = sanitizedPhoneNumber;
  }

  async openUpdateTruckModal(dialog: TemplateRef<any>, status: number, inspection: TruckInspection): Promise<any> {

    this.actionStatus = status;
    this.dialogRef = this.dialogSrv.open(dialog);

    this.dialogRef.onClose.subscribe(async (result) => {
      try {
        this.isLoading = true;
        if (!result) { return }
        inspection.dateTruckStartBrokenDown = moment().valueOf();
        inspection.truckStatus = status;

        await this.removalSrv.updateInspection(inspection?._id, inspection);
        await this.getInspections();
        this.toastSrv.success('Cette inspection a été mise à jour avec succès', 'Mise à jour réussi')
      } catch (error) {
        this.toastSrv.danger('Une Erreur est survenue lors de la mise à jour de cette inspection', 'Échec de mise à jour')
      } finally {
        this.isLoading = false
      }
    })
  }

  onModelCarrierChange(value: any): void {
    this.onModelChange(value, 'datacarrierlabel', 'dataCarrierLabels');
  }

  onModelChange(value: any, dataKey: string, targetArray: string): void {
    if (value === null) {
      this[targetArray] = this.dataForFilter[dataKey];
      return;
    }
    const normalizedValue = value?.toLowerCase() || '';

    const filteredData = this.dataForFilter[dataKey]?.filter((data: string) =>
      data?.toLowerCase().includes(normalizedValue)
    );
    const sortedData = filteredData?.sort((a: string, b: string) => {
      const indexA = a?.toLowerCase().indexOf(normalizedValue);
      const indexB = b?.toLowerCase().indexOf(normalizedValue);
      return indexA - indexB;
    });

    this[targetArray] = sortedData;
  }

  async UpdateTruckInspection(): Promise<void> {
    try {
      this.isLoading = true;

      this.currentInspections.truckStatus = StatusRemovals.LOADED;

      await this.removalSrv.updateInspection(this.currentInspections?._id, this.currentInspections);

      this.toastSrv.success('Cette inspection a été mise à jour avec succès', 'Mise à jour réussi')
    } catch (error) {
      this.toastSrv.danger('Une Erreur est survenue lors de la mise à jour de cette inspection', 'Échec de mise à jour');
      return;
    } finally {
      this.isLoading = false;
    }
    this.dialogRef.close();
    await this.getInspections();
    await this.getloadedInspections();
  }

  async saveInspection(): Promise<void> {
    try {
      this.isLoading = true;
      await this.removalSrv.updateInspection(this.currentInspections?._id, { ...this.currentInspections });

      await this.getInspections();

      this.toastSrv.success(`Cette inspection a été mise à jour avec succès`, `Mise à jour réussi`);
    } catch (error) {
      this.toastSrv.danger(`Une erreur est survenue lors de la mise à jour de cette inspection.`, 'Erreur de mise à jour');
    } finally {
      this.isLoading = false;
    }
  }

  openUpdateInspection(dialog?: any, inspection?: TruckInspection) {
    this.currentInspections = cloneDeep(inspection);
    this.dialogRef = this.dialogSrv.open(dialog);
  }

  async opentruckReceivedModal(dialog: TemplateRef<any>, inspection: TruckInspection) {
    this.dialogRef = this.dialogSrv.open(dialog);
    this.currentInspections = inspection;
  }

  goBack() {
    if (window.history.length > 1) {
      this.location.back()
    } else {
      this.router.navigate(['/home'])
    }
  }

}


