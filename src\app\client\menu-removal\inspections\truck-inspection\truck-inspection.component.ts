import * as moment from 'moment';
import cloneDeep from 'lodash-es/cloneDeep';
import { Component, OnInit } from '@angular/core';
import { CommonService } from 'src/app/shared/services/common.service';
import { TruckInspection } from 'src/app/shared/models/truck-inspection';
import { RemovalService } from '../../removal-management/removal.service';
import { NbDialogRef, NbDialogService, NbToastrService } from '@nebular/theme';
import { AuthorizationRemoval } from 'src/app/shared/models/authorization-removal';
import { InspectionDecision, WaitingThread } from 'src/app/shared/models/inspectionData-enum';

@Component({
  selector: 'clw-truck-inspection',
  templateUrl: './truck-inspection.component.html',
  styles: [
  ]
})
export class TruckInspectionComponent implements OnInit {

  imageGood = '../../assets/images/icons/checkmark-green.svg';
  imageBad = '../../assets/images/icons/close-default.svg';
  loading: any;
  offset ;
  limit = 20;
  startDate: Date;
  endDate: Date;
  index: number
  endIndex = 20;
  startIndex = 1;
  total: number;
  dialogRef: any;
  images = [];
  inspection: any;
  inspections: TruckInspection[];
  isLoading: boolean;
  dataForFilter: any = {};
  dataDrivers: any;
  activeFilter = [];
  dataAELabels: any;
  dataAELabelsExport: any;
  dataCarrierLabels: any;
  currentInspections: TruckInspection;
  dataDecisionsLabels: any;
  dataTruckImmatriculation: any;
  validateRef: NbDialogRef<any>;
  detailDetailRef: NbDialogRef<any>;
  modifyInspectionRef: NbDialogRef<any>;
  inspectionDecision = InspectionDecision;

  statusChoices = [
    { label: 'OK', value: true },
    { label: 'PAS OK', value: false }
  ]

  filterForm = {
    status: '',
    driver: '',
    carrierLabel: '',
    truck: '',
    startDate: new Date(new Date().getFullYear(), 0, 1),
    endDate: new Date(new Date().getFullYear(), 11, 31),
  }

  InspectionDecisions = ['ACCEPTEE', 'REJETEE'];
  waitingThreads = ['PARKING USINE', 'PARKING']
  thread: any;
  selectedImage: any;
  file: any;
  filename: any;
  imageSrc: string;
  isImageUploaded: boolean;
  dataInpections: any;

  constructor(
    private dialogSvr: NbDialogService,
    private removalSvr: RemovalService,
    protected commonService: CommonService,
    private toastrSvr: NbToastrService,

  ) { }

  async ngOnInit() {
    await this.getInspections();
    await this.getDataForFilter();
    this.activeFilter.length = 0;
  }

  async getInspections(): Promise<void> {
    const options = {
      limit: this.limit,
      offset: this.offset,
      ...this.filterForm,
      startDate: moment(this.filterForm?.startDate).format('MM-DD-YYYY'),
      endDate: moment(this.filterForm?.endDate).format('MM-DD-YYYY'),
    }
    try {
      this.isLoading = true;
      const { data, total } = await this.removalSvr.getTruckInspections(options);
      this.inspections = data;
      this.total = total;
      this.endIndex = (this.offset * this.limit < this.total) ? this.offset * this.limit : this.total;
      this.startIndex = (this.total === 0) ? this.total : ((this.offset - 1) * this.limit) + 1 || this.startIndex;

    }
    catch (error) {
      this.toastrSvr.danger('Impossible de récupérer la liste des inspections', 'Erreur connexion !');

    } finally { this.isLoading = false; }
  }


  async getDataForFilter() {
    try {
      this.loading = true;
      this.dataForFilter = await this.removalSvr.getFilterData({
        ...this.filterForm,
        startDate: moment(this.filterForm?.startDate).format('MM-DD-YYYY'),
        endDate: moment(this.filterForm?.endDate).format('MM-DD-YYYY'),
        keyForFilters: ['decision', 'carrier.label', 'driver', 'tractor.label',]
      },);
      this.dataDrivers = this.dataForFilter['datadriver'] ?? [];
      this.dataCarrierLabels = this.dataForFilter['datacarrierlabel'] ?? [];
      this.dataDecisionsLabels = this.dataForFilter?.datadecision;
      this.dataTruckImmatriculation = this.dataForFilter['datatractorlabel'];

    } catch (error) {
      return error;
    } finally { this.loading = false }
  }

  async sendToPark(): Promise<any> {
    try {
      this.isLoading = true;
      const currentAE = await this.removalSvr.getAuthorizationRemoval({ LoadNumber: this.inspection?.LoadNumber }) as unknown as AuthorizationRemoval;
      currentAE.inspection = {
        id: this.inspection._id,
        status: this.inspection.status
      }
      const updateAe = {
        ...currentAE,
        parkTime: moment().valueOf(),
        status: this.thread === WaitingThread.PARKING_USINE ? 400 : 700,
      };
      await this.removalSvr.updateAe(currentAE?._id, updateAe);

      await this.getInspections();
      return this.toastrSvr.success(`Cette inspection a été mise à jour avec succès`, `Mise à jour réussi`);
    } catch (error) {
      this.toastrSvr.danger(`Une erreur est survenue lors de la mise à jour de cette inspection.`, 'Erreur de mise à jour');
    } finally {
      this.isLoading = false;
    }
  }

  async saveInspection(): Promise<void> {
    try {
      this.isLoading = true;
      await this.removalSvr.updateInspection(this.currentInspections?._id, { ...this.currentInspections });

      await this.getInspections();

      this.toastrSvr.success(`Cette inspection a été mise à jour avec succès`, `Mise à jour réussi`);
    } catch (error) {
      this.toastrSvr.danger(`Une erreur est survenue lors de la mise à jour de cette inspection.`, 'Erreur de mise à jour');
    } finally {
      this.isLoading = false;
      this.isImageUploaded = false;
    }
  }

  async openDetailModal(dialog?: any, inspection?: any): Promise<void> {
    this.inspection = { ...inspection };
    this.dialogRef = this.openModal(dialog);
  }

  openUpdateInspection(dialog?: any, inspection?: TruckInspection) {
    this.currentInspections = cloneDeep(inspection);
    this.dialogRef = this.dialogSvr.open(dialog, {});
  }

  openImageInspection(dialog?: any, inspection?: any) {
    this.currentInspections = cloneDeep(inspection);

    this.images = inspection?.image;

    this.dialogRef = this.openModal(dialog);
  }

  openExportInspection(dialog?: any) {
    this.activeFilter.push(0);
    this.dialogRef = this.openModal(dialog);
  }

  selectImage(image: string, index: number) {

    this.index = index;
    this.selectedImage = image;
  }

  clearSelectedImage() {
    this.selectedImage = null;
  }

  getFile(fileDataSet: any): any {

    this.file = fileDataSet.target.files[0];
    const type = this.file.type;
    if (!type.includes('image')) {
      this.file = null;
      return this.toastrSvr.danger('Veuillez importer uniquement les images', 'Donnés incorrectes');

    }
    this.filename = this.file.name;

    const reader = new FileReader();
    if (fileDataSet.target.files && fileDataSet.target.files.length) {
      const [file] = fileDataSet.target.files;
      reader.readAsDataURL(file);

      reader.onload = () => {
        this.imageSrc = reader.result as string;
        this.images[this.index] = this.imageSrc;
        this.currentInspections.images = this.images;
        this.isImageUploaded = true;

      };

    }
  }

  reset() {
    this.filterForm = {
      status: '',
      driver: '',
      carrierLabel: '',
      truck: '',
      startDate: new Date(new Date().getFullYear(), 0, 1),
      endDate: new Date(new Date().getFullYear(), 11, 31),
    }
    this.activeFilter.length = 0;
    this.getInspections();
    this.getDataForFilter();
  }


  async exportToExcel() {
    this.isLoading = true;
    await this.getInspections();
    this.dataInpections = this.inspections?.map(elt => {
      const data = {};
      data['Date création'] = moment(elt?.date).format('YYYY-MM-DD');
      data['Transporteur'] = elt?.carrier?.label || 'N/A';
      data['Immatriculation Camion'] = elt?.tractor?.label || 'N/A';
      data['Chauffeur'] = elt?.driver || 'N/A';
      data['Nom du contrôleur'] = elt?.user?.fullname || 'N/A';
      data['Decision'] = elt?.decision || 'N/A';
      if (elt?.decision && elt?.decision.includes('REJETEE')) {
        const notValidCheckList = elt?.checkList?.filter((check: { state: boolean; }) => !check?.state);
        data['Raison du rejet'] = notValidCheckList?.map((elt: { label: string; }) => `Non Conformité ${elt?.label}`).join(', ');
      }
      return data;
    });
    this.commonService.exportRetriveExcelFile(this.dataInpections, 'Liste des inspections camions');
    this.isLoading = false;
  }

  updateImage() { document.getElementById('file').click() }

  async openValidateInspection(dialog?: any, inspection?: any): Promise<void> {
    this.inspection = { ...inspection };

    this.dialogRef = this.openModal(dialog);
  }

  onModelChange(value: any, dataKey: string, targetArray: string): void {
    if (value === null) {
      this[targetArray] = this.dataForFilter[dataKey];
    } else {
      this[targetArray] = this.dataForFilter[dataKey]?.filter((data: string) =>
        data?.toLowerCase().includes(value?.toLowerCase()))?.sort((a, b) => {
          return a?.toString().toLowerCase().indexOf(value?.toLowerCase()) - b?.toString().toLowerCase().indexOf(value?.toLowerCase());
        });
    }
  }

  onModelCarrierChange(value: any): void {
    this.onModelChange(value, 'datacarrierlabel', 'dataCarrierLabels');
  }

  onModelTruckImmatriculationChange(value: any): void {
    this.onModelChange(value, 'datatractorlabel', 'dataTruckImmatriculation');
  }

  onModelTruckDriverChange(value: any): void {
    this.onModelChange(value, 'datadriver', 'dataDrivers');
  }


  async onModelDateChange(event: any): Promise<any> {
    if (moment(this.filterForm.endDate).valueOf() < moment(this.filterForm.startDate).valueOf())
      return this.toastrSvr.danger('la date de fin est inférieure à la date de début', 'Erreurs Données!');
    await this.getInspections();
    await this.getDataForFilter();
  }

  openModal(dailog: any): void {
    this.dialogRef = this.dialogSvr.open(dailog, {});
  }

  async previousPage(): Promise<any> {
    this.offset -= 1;
    if (this.offset <= 0) { this.offset ; }
    this.startIndex = (this.offset - 1) * this.limit;
    if (this.startIndex === 0) { this.startIndex = 1; }
    this.endIndex = this.offset * this.limit;
    await this.getInspections();
  }

  async nextPage(): Promise<any> {
    if (this.endIndex >= this.total) {
      this.endIndex = this.total;
      return;
    }
    this.offset += 1;
    this.startIndex = (this.offset - 1) * this.limit;
    this.endIndex = this.offset * this.limit;
    if (this.startIndex <= 0) { this.startIndex = 1; }
    await this.getInspections();
  }

  async onSelectionAEChange(event: any): Promise<void> {
    if (event?.keyToReset) {
      this.filterForm[event?.keyToReset] = '';
      this[event?.dataToReset](null);
    }
    if (event?.$event)
      await this.getInspections();
  }

  validatePhoneNumber(event: Event): void {
    this.commonService.verifyTel(event, this.currentInspections);
  }


}


