import { Packaging } from 'src/app/shared/models/packaging';
export interface Price {
    _id?: string;
    amount?: number
    storeRef?: string;
    store?: {
        _id?: string;
        label?: string;
    }
    productRef?: string;
    product?: {
        _id?: string;
        label?: string;
        normLabel?: string;
        img?: string; 
    };
    packagingCode:number;
    packaging?: Packaging;
    categoryCode?: number;
    productCategory?:{
        _id: string; 
        label?: string;  
    }
}