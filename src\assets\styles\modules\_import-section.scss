/* order-card.component.scss */
.order-card {
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
  margin-bottom: 16px;
  height: 16rem;
  overflow: hidden;
}

.order-item {
  .cart-recap {
    height: auto;
  }

  .empty-list-partition {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    text-align: center;
    color: #6c757d;

    p {
      color: #8E8E93;
      font-weight: 500;
      font-size: 15px;

    }

    nb-icon {
      width: 50px;
      height: 50px;
    }
  }
}

.order-content {
  padding: 16px;
}

.order-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;

  .status {
    display: flex;
    gap:1rem;

    .render-pill {
      padding: 4px 12px;
      border-radius: 9px;
      font-size: 12px;
      height: 31px;
      font-weight: 500;
  
      &.render-1 {
        background-color: #0B305C;
        color: #f6f7f8;
        font-weight: 600;
        border: 1px solid #011427;
      }
  
      &.render-2 {
        background-color: #0f6bf5;
        color: #ffffff;
        border: 1px solid #2572b1;
      }
    }
  }



}




.bon-number {
  color: #8a95a5;
  font-size: 18px;
  font-weight: 500;
  margin: 0;
}

.section-label {
  color: #8a95a5;
  font-size: 14px;
  margin-bottom: 12px;
}

.products-container {
  display: flex;
  justify-content: flex-start;
  margin-bottom: 20px;
}

.product-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 22%;
}

.product-image {
  width: 100%;
  height: 100px;
  object-fit: contain;
  margin-bottom: 8px;
}

.product-name {
  font-size: 14px;
  color: #000000;
  text-align: center;
  font-weight: 600;
}

.product-weight {
  font-size: 12px;
  color: #8a95a5;
}

.client-section {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;

  .modify-button {
    background-color: #E6E7ED;
    color: #010D49;
    border-radius: 20px;
    padding: 6px 16px;
    font-weight: 900;
    box-shadow: none;
  }
}

.info-row {
  display: flex;
  flex-direction: column;
}

.info-label {
  color: #8a95a5;
  font-size: 14px;
  margin-bottom: 4px;
}

.info-value {
  font-size: 15px;
  color: #000000;
  font-weight: 600;
}

.client-code {
  min-width: 120px;
}