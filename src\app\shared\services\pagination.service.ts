import { Injectable } from '@angular/core';

export interface PaginationConfig {
  currentPage: number;
  itemsPerPage: number;
  totalItems: number;
  maxSize?: number;
}

export interface PaginationResult {
  startIndex: number;
  endIndex: number;
  totalPages: number;
}

@Injectable({
  providedIn: 'root'
})
export class PaginationService {
  constructor() { }

  calculatePagination(config: PaginationConfig): PaginationResult {
    if (!config || config.totalItems === undefined || config.totalItems === 0) {
      return { startIndex: 0, endIndex: 0, totalPages: 0 };
    }

    const totalPages = Math.max(1, Math.ceil(config.totalItems / config.itemsPerPage));
    const currentPage = Math.min(Math.max(1, config.currentPage), totalPages);
    
    // Calculer l'index de base (commence à 0)
    const baseIndex = (currentPage - 1) * config.itemsPerPage;
    
    // Calculer les indices de début et de fin (commencent à 1)
    const startIndex = baseIndex + 1;
    const endIndex = Math.min(baseIndex + config.itemsPerPage, config.totalItems);

    console.log('Pagination calculation:', {
      totalItems: config.totalItems,
      itemsPerPage: config.itemsPerPage, 
      currentPage,
      baseIndex,
      startIndex,
      endIndex,
      totalPages
    });

    return {
      startIndex,
      endIndex,
      totalPages
    };
  }

  getPageNumbers(config: PaginationConfig): number[] {
    const totalPages = Math.ceil(config.totalItems / config.itemsPerPage);
    const maxSize = config.maxSize || 5;
    const currentPage = config.currentPage;
    
    let startPage = Math.max(1, currentPage - Math.floor(maxSize / 2));
    let endPage = Math.min(totalPages, startPage + maxSize - 1);

    if (endPage - startPage + 1 < maxSize) {
      startPage = Math.max(1, endPage - maxSize + 1);
    }

    return Array.from({ length: endPage - startPage + 1 }, (_, i) => startPage + i);
  }

  isFirstPage(currentPage: number): boolean {
    return currentPage === 1;
  }

  isLastPage(currentPage: number, totalPages: number): boolean {
    return currentPage === totalPages;
  }

  getDisplayedItems<T>(items: T[], config: PaginationConfig): T[] {
    const { startIndex, endIndex } = this.calculatePagination(config);
    return items.slice(startIndex - 1, endIndex);
  }
} 