# Documentation: Gestion des Commandes - Cadyst Logistique Web

## Vue d'ensemble

Le module de Gestion des Commandes du composant Web de Cadyst Logistique fournit un ensemble complet de fonctionnalités permettant la planification, le suivi, et la gestion des camions et livraisons. Cette documentation technique est conçue pour les développeurs travaillant sur la refonte de l'application, en mettant l'accent sur quatre sous-modules principaux:

1. **Planification**: Gestion des planifications de chargement et d'enlèvement
2. **Suivi des camions et livraisons**: Surveillance en temps réel des véhicules et livraisons
3. **Gestion des camions en panne**: Traitement des incidents et réaffectations
4. **Écran d'appel**: Interface d'appel des camions vers les quais

## Architecture et Modèles de données

### Modèles principaux

#### 1. Commande (Order)

```typescript
export interface Order {
    _id?: string;
    userId?: string;
    user?: {
        _id: string;
        fullname?: string;
        tel?: string;
        email?: string;
    }
    jdeReference?: string;
    appReference?: string;
    items?: Item[];
    price?: number;
    type?: orderType;
    storesRef?: {
        origin: any;
        arrival: any;
    }
    status?: orderStatus;
    dates?: {
        created?: number;
        paid?: number;
        canceled?: number;
    }
}

export enum orderType {
    SO = 100,
    ST = 200,
}

export enum orderStatus {
    INITIATED = 100,
    CONFIRMED = 200,
    ERROR = 300,
    CANCELLED = 500
}
```

#### 2. Camion (Truck)

```typescript
export interface Truck {
  _id?: string;
  immatriculation?: string;
  desc?: string;
  code?: number;
  type?: string;
  capacity?: number;
  volume?: number;
  enable?: boolean;
  status?: number;
  carrierRef?: number;
  dateStartBrokenDown?: number;
  dateEndBrokenDown?: number;
  dock?: any;
}
```

#### 3. Statut d'Enlèvement (StatusRemovals)

```typescript
export enum StatusRemovals {
  NOT_ASSIGNED = 100,        // Non assigné
  ASSIGNED = 200,            // Assigné
  WAITING_VALIDATION = 300,  // En attente de validation
  QUEUED = 400,              // En file d'attente
  LOADED = 500,              // Chargé
  BROKEN_DOWN = 600,         // En panne
  NEW_PARKING = 700,         // Nouveau parking
  REJECTED = 800,            // Rejeté
}

export enum TruckStatus {
  FACTORY = 450,             // Usine
  LOADED = 500,              // Chargé
  BROKEN_DOWN = 600,         // En panne
}
```

## 1. Module de Planification

Le module de planification permet de gérer la programmation des enlèvements et des livraisons.

### Composants principaux

- **PlanificationListComponent**: Affiche la liste des planifications
- **PlanificationFormComponent**: Formulaire de création/modification de planification

### Fonctionnalités clés

#### 1.1 Gestion des planifications

```typescript
// Création d'une nouvelle planification
async createPlanification(planification: any): Promise<any> {
  try {
    const result = await this.planificationService.create(planification);
    this.toastrService.success('Planification créée avec succès', 'Succès');
    return result;
  } catch (error) {
    this.toastrService.danger('Erreur lors de la création', 'Erreur');
    throw error;
  }
}

// Récupération des planifications avec filtres
async getPlanifications(filters: any): Promise<any> {
  const { start, end, status, carrier } = filters;
  const options = { 
    start, 
    end, 
    status, 
    carrier,
    limit: this.limit,
    offset: this.offset
  };
  
  return await this.planificationService.getPlanifications(options);
}
```

#### 1.2 Attribution de transporteurs

```typescript
// Assigner un transporteur à une planification
async assignCarrier(planificationId: string, carrierId: string): Promise<any> {
  try {
    await this.planificationService.assignCarrier(planificationId, carrierId);
    this.toastrService.success('Transporteur assigné avec succès', 'Succès');
  } catch (error) {
    this.toastrService.danger('Erreur lors de l\'assignation', 'Erreur');
    throw error;
  }
}
```

## 2. Module de Suivi des Camions et Livraisons

Le module de suivi permet la visualisation en temps réel des camions et des livraisons.

### Composants principaux

- **TruckAllocationDockComponent**: Gestion de l'allocation des camions aux quais
- **WaitingThreadComponent**: Gestion de la file d'attente des camions

### Fonctionnalités clés

#### 2.1 Allocation des camions aux quais

```typescript
// Fonction principale pour déplacer un camion (depuis TruckAllocationDockComponent)
async moveTruck(event: any, index?: number, dock?: any, dialog?: TemplateRef<any>): Promise<any> {
  let removal = event.previousContainer.data[event.previousIndex];  
  
  // Vérifier si le quai est occupé
  if (dock?.code && this.occupiedCodes?.includes(dock.code)) {
    return this.toastSrv.warning(`Ce Quai est déjà pris.`, `Quai déjà pris`);
  }

  switch (`${event.previousContainer.id}->${event.container.id}`) {
    case 'truckList->dockList':
      // Déplacer le camion de la liste d'attente au quai
      if (!(await this.openDialogAndValidate(dialog))) return;
      
      removal = this.updateRemoval(removal, 450, dock?.code, dock?.label, this.initialTruckTonnage, 'dockTimeStart');
      break;
      
    case 'dockList->truckList':
      // Retirer le camion du quai et le remettre en attente
      removal = this.updateRemoval(removal, 400, 0, null, null, 'parkTime');
      break;
      
    case 'dockList->dockList':
      // Déplacer le camion d'un quai à un autre
      removal = this.updateRemoval(removal, null, dock?.code, dock?.label, null, 'parkTime');
      break;
  }

  await this.updateDock(removal?._id, removal);
}
```

#### 2.2 Gestion des files d'attente

```typescript
// Récupérer les camions en file d'attente (utilisé dans WaitingThreadComponent)
async getWaitingThread(): Promise<any> {
  this.isLoading = true;
  try {
    // Récupérer les camions dans différentes files
    await this.getWaithingThreadFirst();   // File d'attente principale
    await this.getWaithingThreadDock();    // Sur les quais
    await this.getWaithingThreadNew();     // Nouveau parking
    
    // Récupérer la liste des quais
    this.docks = await this.waitingThreadSrv.getDocs({});
  } catch (error) {
    return this.toastSrv.danger(`Erreur de connexion internet`, `Échec opération`);
  } finally { 
    this.isLoading = false; 
  }
}

// Récupérer les camions dans la file d'attente principale
async getWaithingThreadFirst() {
  this.displayLoading = true;
  
  const options = { 
    status: 400,    // Statut QUEUED 
    moved: 400,     // Récemment déplacés
    product: this.productFilter, 
    sort: 'parkTime', 
    way: 1 
  };
  
  const { data, total } = await this.removalSrv.getAuthorizationRemoval(options);
  this.waitingThreadTab = data;
  this.displayLoading = false;
}
```

#### 2.3 Fonctionnalités de suivi en temps réel

Le système implémente un mécanisme de rafraîchissement automatique des données :

```typescript
// Initialisation du timer de rafraîchissement (utilisé dans TruckAllocationDockComponent)
ngOnInit(): Promise<any> {
  this.subscription = interval(1000)
    .subscribe(x => {
      this.waitingTime = moment().valueOf();
    });

  // Rafraîchir les données toutes les 5 minutes
  setInterval(async () => {
    await this.getAuthorizationRemoval();
  }, 300000);
  
  await this.getTruckByDock();
  await this.getAvailableDock();
}
```

## 3. Module de Gestion des Camions en Panne

Ce module permet de gérer les incidents liés aux camions en panne et leur réintégration dans le flux normal.

### Composants principaux

- **MenuBrokenDownTrucksComponent**: Gestion des camions en panne

### Fonctionnalités clés

#### 3.1 Liste et filtrage des camions en panne

```typescript
// Récupérer la liste des camions en panne
async getAllBrokenDown(): Promise<void> {
  this.isLoading = true;
  const options = {
    limit: this.limit, 
    offset: this.offset, 
    status: 600,                       // Statut BROKEN_DOWN
    immatriculation: this.currimmatriculation, 
    dateStart: this.rangeFilter.start
  };
  
  try {
    const { data, total } = await this.downtruck.getAuthorizationRemoval(options);
    this.removals = data;
    this.total = total;
    this.endIndex = (this.offset * this.limit < total) ? this.offset * this.limit : total;
    this.startIndex = (total === 0) ? total : this.startIndex || 1;
  } catch (error) {
    this.toastrSrv.danger('Impossible de récupérer la liste des camions en panne', 'Erreur connexion !');
  } finally { 
    this.isLoading = false; 
  }
}
```

#### 3.2 Gestion de la réintégration d'un camion en panne

```typescript
// Réintégrer un camion en panne dans la file d'attente normale
async setDateEndBrokenDown(): Promise<any> {
  this.isLoading = true;
  try {
    // Mettre à jour l'horodatage approprié
    if (this.removal?.dockTimeStart) {
      this.removal.dockTimeStart = moment().valueOf();
    } else {
      this.removal.parkTime = moment().valueOf();
    }
    
    // Envoyer au quai si un quai est sélectionné
    if (this.selectedDock) {
      await this.sendToDock();
    } else {
      // Sinon, mettre à jour le statut selon la file sélectionnée
      this.removal.status = QueueStatus[this.selectedQueue];
      await this.downtruck.updateAuthRemoval(this.removal, this.removal._id);
      this.toastSrv.success('Cette mise à jour a été effectuée avec succès', 'Mise à jour réussie');
    }

    // Fermer le dialogue et rediriger
    this.removedialogRef.close();
    this.router.navigate(["/client/removal/waiting-thread"]);
  } catch (error) {
    this.toastSrv.danger('Une erreur est survenue lors de cette mise à jour', 'Échec de mise à jour');
  } finally {
    this.isLoading = false;
  }
}
```

#### 3.3 Envoi d'un camion réparé vers un quai

```typescript
// Envoyer un camion réparé directement à un quai
async sendToDock(): Promise<any> {
  try {
    this.isLoading = true;
    
    if (!this.selectedDock) {
      return this.toastSrv.danger('Veuillez choisir un quai', 'Données incorrectes');
    }
    
    // Si le camion a déjà un quai assigné
    if (this.removal?.dockCode !== 0 && this.removal?.dockCode) {
      this.removal.status = 450;  // ON_QUAY
      this.removal.dockCode = this.removal?.code;
      await this.removalSrv.updateAe(this.removal?._id, this.removal);
      this.toastSrv.success('Cette mise à jour a été effectuée avec succès', 'Mise à jour réussie');
      return this.dialogRef?.close();
    }

    // Assigner un nouveau quai
    this.removal.status = 450;  // ON_QUAY
    this.removal.dockCode = this.selectedDock?.code;
    this.removal.dockTimeStart = moment().valueOf();
    await this.removalSrv.updateAe(this.removal?._id, this.removal);
    this.toastSrv?.success('Cette mise à jour a été effectuée avec succès', 'Mise à jour réussie');
    this.dialogRef?.close();
  } catch (error) {
    this.toastSrv.danger('Une erreur est survenue lors de cette mise à jour', 'Échec de mise à jour');
  } finally {
    this.isLoading = false;
    this.selectedDock = null;
  }
}
```

## 4. Module d'Écran d'Appel

Ce module fournit une interface d'écran d'appel pour gérer les camions qui doivent se présenter aux quais.

### Composants principaux

- **TruckCallScreenComponent**: Écran d'appel des camions
- **MenuCallScreenComponent**: Gestion des écrans d'appel

### Fonctionnalités clés

#### 4.1 Récupération et mise à jour des inspections de camions

```typescript
// Initialisation de l'écran d'appel
async ngOnInit(): Promise<void> {
  // Récupérer les quais
  this.docks = await this.waitingThreadSrv.getDocs({});
  
  // Récupérer les inspections
  await this.getInspections();
  
  // Mettre à jour automatiquement toutes les 30 secondes
  this.updateInspectionsInterval = setInterval(() => {
    this.getInspections();
  }, 30000);
}

// Récupérer les inspections pour l'écran d'appel
async getInspections(): Promise<void> {
  try {
    this.isLoading = true;
    const { data } = await this.removalSvr.getTruckInspections({ truckSatus: parkingStatus.FACTORY });
    this.inspections = data;
  }
  catch (error) {
    this.toastrSvr.danger('Impossible de récupérer la liste des Inspections camions', 'Erreur connexion !');
  } finally { 
    this.isLoading = false; 
  }
}
```

#### 4.2 Gestion des quais et des statuts de parking

```typescript
// Récupérer la clé de stockage des temps de parking selon le statut
getParkTimeKey(status: number): string {
  const StatusParkTimeKeyMap = {
    400: 'factoryParkTimes',    // QUEUED
    450: 'dockParkTimes',       // ON_QUAY
    600: 'failureParkTimes',    // BROKEN_DOWN
    700: 'newParkTimes',        // NEW_PARKING
  }
  return StatusParkTimeKeyMap[status];
}
```

## Services transversaux

### 1. RemovalService

Le service central pour la gestion des autorisations d'enlèvement et des camions.

```typescript
// Services offerts par RemovalService
export class RemovalService {
  // Récupérer des autorisations d'enlèvement avec filtrage
  async getAuthorizationRemoval(options: any): Promise<{ data: any[], total: number }> {
    // Implémentation...
  }
  
  // Mettre à jour une autorisation d'enlèvement
  async updateAe(id: string, data: any): Promise<any> {
    // Implémentation...
  }
  
  // Mettre à jour l'attribution d'un quai
  async updateDock(id: string, data: { dockCode: number }): Promise<any> {
    // Implémentation...
  }
  
  // Récupérer les camions par quai
  async getTruckByDock(options: any): Promise<any[]> {
    // Implémentation...
  }
  
  // Récupérer les inspections de camions
  async getTruckInspections(options: any): Promise<{ data: TruckInspection[], total: number }> {
    // Implémentation...
  }
}
```

### 2. WaitingThreadService

Service spécialisé pour la gestion des files d'attente.

```typescript
export class WaitingThreadService {
  // Récupérer les quais
  async getDocs(options: any): Promise<dock[]> {
    // Implémentation...
  }
  
  // Récupérer les camions en file d'attente
  async getWaitingThread(options: any): Promise<any[]> {
    // Implémentation...
  }
}
```

### 3. MenuBrokenDownTrucksService

Service spécialisé pour la gestion des camions en panne.

```typescript
export class MenuBrokenDownTrucksService {
  // Récupérer les camions en panne
  async getAuthorizationRemoval(options: any): Promise<{ data: any[], total: number }> {
    // Implémentation...
  }
  
  // Mettre à jour une autorisation d'enlèvement (pour un camion en panne)
  async updateAuthRemoval(removal: any, id: string): Promise<any> {
    // Implémentation...
  }
}
```

## Gestion des statuts et transitions

### 1. Changements de statut

Le système gère les transitions entre différents états des camions :

```typescript
// Exemple de transition d'état dans TruckAllocationDockComponent
updateRemoval(
  removal: any,
  status: number,
  dockCode: any,
  dockName: any,
  initialTruckTonnage: any,
  timeField: 'dockTimeStart' | 'parkTime'
) {
  return {
    ...removal,
    ...(status != undefined && { status }),               // Mettre à jour le statut
    ...(dockCode != undefined && { dockCode }),           // Mettre à jour le code du quai
    ...(dockName != undefined && { dockName }),           // Mettre à jour le nom du quai
    ...(initialTruckTonnage != undefined && { initialTruckTonnage }), // Mettre à jour le tonnage
    ...(timeField && { [timeField]: moment().valueOf() }) // Enregistrer l'horodatage
  };
}
```

### 2. Suivi des temps et métriques

Le système maintient des métriques détaillées sur les temps passés à chaque état :

```typescript
// Calcul de la durée dans TruckAllocationDockComponent
getDurationDate(startDate: number) {
  const currentTime = Date.now();
  const diff = currentTime - startDate;
  const seconds = Math.floor(diff / 1000) % 60;
  const minutes = Math.floor(diff / (1000 * 60)) % 60;
  const hours = Math.floor(diff / (1000 * 60 * 60)) % 24;
  const days = Math.floor(diff / (1000 * 60 * 60 * 24));

  return `${days} jours(s) ${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
}

// Stylisation basée sur le temps d'attente
getColorStylePark(waiting: any): any {
  const hours = 5400001;    // 1h30 en millisecondes
  const minutes = 1800001;  // 30 minutes en millisecondes
  const waitingTime = moment(this.waitingTime).diff(moment(waiting.parkTime));

  if (waitingTime >= hours) {
    return {
      color: '#ff304f',
      border: '1px solid #ff304f'
    };
  }
  if (waitingTime >= minutes && waitingTime < hours) {
    return {
      color: '#ff304f',
      border: '1px solid #ff304f'
    }
  }

  return {
    color: 'black',
  }
}
```

## Interfaces utilisateur

### 1. Écran d'allocation des quais

L'interface de TruckAllocationDockComponent permet de visualiser et gérer l'allocation des camions aux quais via une interface de type drag-and-drop :

- Une liste des camions en attente
- Une visualisation des quais disponibles et occupés
- Des indicateurs visuels du temps passé dans chaque état
- Des fonctionnalités de filtrage par immatriculation

### 2. Écran de file d'attente

L'interface WaitingThreadComponent gère différentes files d'attente :

- File d'attente principale (QUEUED, status 400)
- Camions aux quais (ON_QUAY, status 450)
- Nouveau parking (NEW_PARKING, status 700)

### 3. Écran des camions en panne

L'interface MenuBrokenDownTrucksComponent permet :

- Visualiser les camions en panne (BROKEN_DOWN, status 600)
- Filtrer par immatriculation et par date
- Réintégrer un camion réparé dans le flux normal

### 4. Écran d'appel

L'interface TruckCallScreenComponent fournit :

- Une liste des camions à appeler aux quais
- Des mécanismes de rafraîchissement automatique
- Une association avec les quais disponibles

## Recommandations pour la refonte

### 1. Architecture de code

#### 1.1 Restructuration des composants

- **Séparation des responsabilités** : Diviser les composants volumineux comme `TruckAllocationDockComponent` en sous-composants plus spécialisés
- **Composants réutilisables** : Extraire les éléments communs (listes de camions, sélecteurs de quais) en composants réutilisables
- **Services spécialisés** : Renforcer la séparation entre la logique de présentation et la logique métier

#### 1.2 Gestion d'état

- **Utilisation de NgRx/Redux** : Implémenter une gestion d'état centralisée pour un suivi plus cohérent des statuts
- **BehaviorSubject** : Utiliser RxJS BehaviorSubject pour les données partagées entre composants
- **Immutabilité** : Appliquer des principes d'immutabilité pour éviter les mutations accidentelles d'état

### 2. Expérience utilisateur

#### 2.1 Interface drag-and-drop

- **Modernisation** : Remplacer le système actuel par des bibliothèques plus robustes comme Angular CDK Drag and Drop
- **Retour visuel amélioré** : Ajouter des animations et effets de transition lors des déplacements
- **Compatibilité mobile** : Assurer la compatibilité avec les appareils tactiles

#### 2.2 Métriques et visualisations

- **Tableaux de bord** : Ajouter des tableaux de bord visuels pour les métriques clés (temps d'attente, efficacité des quais)
- **Visualisations en temps réel** : Implémenter des graphiques dynamiques pour suivre les métriques
- **Alertes proactives** : Système d'alerte pour les camions en attente prolongée

### 3. Performance et fiabilité

#### 3.1 Optimisation des requêtes API

- **Mise en cache** : Implémenter un système de mise en cache intelligente pour réduire les appels réseau
- **Pagination optimisée** : Améliorer la gestion de grandes quantités de données
- **Gestion des erreurs** : Renforcer la détection et la récupération après erreurs

#### 3.2 Rafraîchissement des données

- **WebSockets** : Remplacer les intervalles par une communication en temps réel via WebSockets
- **Stratégie de rafraîchissement adaptative** : Ajuster dynamiquement la fréquence de rafraîchissement selon l'activité
- **Mode hors ligne** : Ajouter une prise en charge des opérations hors ligne avec synchronisation ultérieure

### 4. Nouvelles fonctionnalités

#### 4.1 Analytique avancée

- **Prédictions** : Implémenter des modèles prédictifs pour estimer les temps d'attente
- **Détection d'anomalies** : Alertes sur les schémas inhabituels dans les mouvements de camions
- **Rapports configurables** : Permettre aux utilisateurs de créer leurs propres tableaux de bord

#### 4.2 Intégration externe

- **APIs exportables** : Exposer les données pour intégration avec d'autres systèmes
- **Intégration GPS** : Suivre les camions en dehors du site
- **Notifications push** : Alertes sur mobile pour les chauffeurs et gestionnaires

## Conclusion

Le module de Gestion des Commandes du composant Web de Cadyst Logistique offre un ensemble complet de fonctionnalités pour gérer la planification, le suivi des camions et livraisons, la gestion des camions en panne et les écrans d'appel. La refonte proposée vise à améliorer l'architecture, l'expérience utilisateur, les performances et ajouter de nouvelles fonctionnalités pour répondre aux besoins croissants de la logistique moderne.