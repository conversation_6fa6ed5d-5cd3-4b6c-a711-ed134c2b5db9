<nb-card [nbSpinner]="isLoading" nbSpinnerStatus="primary">
  <nb-card-header class="form--header">Confirmation</nb-card-header>
  <nb-card-body>
    <div class="dialog-assign-content" *ngIf="selectedTruck?.isAttribution || selectedTruck?.isAttributionDock">
      Vous êtes sur le point d'attribuer un camion au Quai <strong class="important">{{selectedTruck?.dockCode}}</strong>. Veuillez confirmer cette action.
    </div>

      <div class="dialog-assign-content"  *ngIf="selectedTruck?.isNotAttribution">
      Vous êtes sur le point de rétirer le camion au Quai <strong class="important">{{selectedTruck?.dockCode}}</strong>. Veuillez confirmer cette action.
    </div>

    <!-- <div class="dialog-assign-content"  *ngIf="selectedTruck?.isNotAttribution">
      Vous êtes sur le point de rétirer le camion au Quai <strong class="important">{{selectedTruck?.dockCode}}</strong>. Veuillez confirmer cette action.
    </div> -->

  </nb-card-body>
  <nb-card-footer class="form--footer">
    <button nbButton (click)="close(false)" outline ghost status="basic">
      <nb-icon icon="close-square-outline" [options]="{ animation: { type: 'zoom ' } }" class="remove-icon">
      </nb-icon>
      Annuler
    </button>
    <button nbButton (click)="close(true)" outline status="primary">
      <nb-icon icon="done-all-outline" [options]="{ animation: { type: 'zoom ' } }" class="remove-icon">
      </nb-icon>
      Confirmer
    </button>
  </nb-card-footer>
</nb-card>
