.removals-detail-container {
  @include vh-center;
  padding-top: 25px;
  nb-card {
    max-height: 95vh;
    width: 450px;
  }
  .form--header {
    display: flex;
    justify-content: flex-start;
    font-weight: bold;
    font-family: $font-regular;
  }

  .text {
    font-family: $font-regular !important;
  }

  .footer {
    display: flex;
    justify-content: center;
    width: 100%;
    // height: auto;
    img {
      width: 100%;
      background-color: white;
    }
  }
}

.removal-assign-container {
  @include vh-center;
  h6 {
    font-family: $font-bold;
  }
  p {
    font-size: 16px;
    font-family: $font-bold;
    padding: 10px 0;
  }
  .select-width{
    max-width: 28rem !important;
    width: 98% !important;
  }
  nb-select {
    margin-bottom: 10px;
    max-width: 20rem;
  }
}
.footer-button {
  @include vh-between;
}
.removal-queue-container {
  span {
    font-family: $font-bold;
  }
  p h6 {
    font-size: 15px;
    font-family: $font-regular;
    height: 60px;
  }
  nb-card {
    .important {
      font-size: 16px;
      font-weight: bold;
    }
  }
}
.removal-quai-container {
  font-family: $font-regular;
  .quai-infor {
    font-size: 16px;
    font-family: $font-regular;
  }
  .button-list {
    @include vh-between;
    margin: 30px 0;
  }
  .quai-position {
    text-align: center;
    font-size: 15px;
    font-family: $font-regular;
    line-height: 30px;
    padding: 0 22px;
  }
  .quai-detail {
    @include vh-between;
    margin: 10px 0;
  }
  .left-block {
    .car {
      font-family: $font-bold;
      font-size: 16px;
      padding: 15px 0;
    }
  }
  .right-block {
    .car {
      font-family: $font-regular;
      font-size: 16px;
      padding: 15px 0;
      text-align: end;
    }
  }
}
.export-container {
  .filter {
    @include vh-center;
    gap: 1em;
    margin-bottom: 1em;
  }
}
