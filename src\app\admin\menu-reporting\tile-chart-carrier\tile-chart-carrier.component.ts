import { Component, OnInit, ViewEncapsulation } from '@angular/core';
import { Chart, registerables } from 'chart.js'

Chart.register(...registerables)

@Component({
  selector: 'clw-tile-chart-carrier',
  templateUrl: './tile-chart-carrier.component.html',
  styles: [],
  encapsulation: ViewEncapsulation.None,
})
export class TileChartCarrierComponent implements OnInit {
  carrier: any;
  isLoading: boolean;

  constructor() {}

  ngOnInit(): void {
    this.renderCarrierChart();
  }

  renderCarrierChart() {
    this.carrier = new Chart('canvas-carrier', {
      type: 'doughnut',
      data: {
        labels: ['CCMM', 'TRANSAFRIQUE', 'AFRICA LOGISTIQUE'],
        datasets: [
          {
            label: 'My First Dataset',
            data: [300, 50, 100],
            backgroundColor: [
              'rgb(255, 99, 132)',
              'rgb(54, 162, 235)',
              'rgb(255, 205, 86)',
            ],
          },
        ],
      },
      options: {},
    });
  }
}
