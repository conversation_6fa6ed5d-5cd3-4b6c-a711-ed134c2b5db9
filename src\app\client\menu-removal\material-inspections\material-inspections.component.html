<div class="common-form-container menu-waiting-thread-container">
  <div class="header">
    <nb-icon icon="undo-outline" (click)="goBack()" [options]="{ animation: { type: 'zoom' } }" class="remove-icon"
      status="primary">
    </nb-icon>
    <h1 class="title">Ecran d'appel - Parking</h1>
  </div>

  <div class="removal-container">

    <nb-card>

      <nb-tabset>

        <nb-tab tabTitle="Chargement usine matières">
          <nb-card class="no-border">
            <nb-list class="element">

              <nb-list-item class="list-elt-header paginator">
                <div class="col col-num">N°</div>
                <div class="col col-name">Transporteur</div>
                <div class="col col-name">Conducteur</div>
                <div class="col col-qty">Tonnage</div>
                <div class="col col-qty">Quai</div>
                <div class="col col-time">Temps d'attente</div>
                <div class="col col-immatriculation">Immatriculation</div>
                <div class="col col-status center">Statut</div>
                <div class="col col-action">
                  <div class="action-icons"></div>
                  <div class="col col-action">
                    <div class="action-icons"></div>
                  </div>
                </div>
              </nb-list-item>
            </nb-list>
            <nb-list class="element scrool-style" [nbSpinner]="displayLoading" nbSpinnerStatus="primary">
              <nb-list-item class="list-elt-header" *ngFor="let inspection of inspections | filterDockCode; index as i">
                <div class="col col-num">{{ i + 1 }}</div>
                <div class="col col-name" title="{{ inspection?.carrier?.label }}">{{ inspection?.carrier?.label || "Non
                  renseigner" | truncateString : 20}}</div>
                <div class="col col-name" title="{{ inspection?.driver }}">{{ inspection?.driver || "Non renseigner" |
                  truncateString : 20 }}</div>
                <div class="col col-qty">{{ inspection?.truckTonnage }}</div>
                <div class="col col-qty">{{ inspection?.dockCode | getDock: docks }}</div>
                <div class="col col-time">
                  <p [ngStyle]="inspection?.dockTimeStart | colorStyleWaiting">
                    {{commonSrv.getDurationDate(inspection?.dockTimeStart)}}
                  </p>
                </div>
                <div class="col col-immatriculation">
                  {{ inspection?.tractor?.label || "Non renseigner"}}
                </div>
                <div class="col col-status" *ngIf="!inspection?.dockCode || inspection?.dockCode === 0">
                  <nb-badge status="warning" text="EN ATTENTE" class="badge" position="top start"></nb-badge>
                </div>
                <div class="col col-status" *ngIf="inspection?.dockCode && inspection?.dockCode !== 0">
                  <nb-badge status="info" text="EN CHARGEMENT" class="badge" position="top start"></nb-badge>
                </div>
                <div class="col col-action">

                  <div class="col col-action">
                    <button nbTooltip="Modifier" class="button-margin" nbTooltipPlacement="top" nbTooltipStatus
                      (click)='openUpdateInspection(updateTruckDialog, inspection)'>
                      <nb-icon icon="edit-2-outline" status="primary" [options]="{ animation: { type: 'zoom' } }">
                      </nb-icon>
                    </button>
                    <button nbTooltip="En panne" nbButton outline nbTooltipPlacement="top" nbTooltipStatus
                      (click)="openUpdateTruckModal(updateAeDialog,600, inspection)">
                      <nb-icon icon="alert-circle-outline" status="primary" [options]="{ animation: { type: 'zoom' } }">
                      </nb-icon>
                    </button>
                    <button nbTooltip="Marquer ce camion réceptionné" class="button-margin" nbButton outline
                      nbTooltipPlacement="top" nbTooltipStatus
                      (click)="opentruckReceivedModal(truckReceived,inspection)">
                      <nb-icon icon="arrow-circle-right-outline" status="primary"
                        [options]="{ animation: { type: 'zoom' } }">
                      </nb-icon>
                    </button>
                  </div>

                </div>
              </nb-list-item>
              <nb-list-item class="list-elt-header" *ngIf="!inspections?.length">
                <div class="not-found">
                  <img src="../../../../assets/images/icons/shipped.png" alt="" />
                  <h6>Aucun camion trouvé</h6>
                </div>
              </nb-list-item>
            </nb-list>
          </nb-card>
        </nb-tab>

        <nb-tab tabTitle="Camion réceptionné">
          <nb-card class="no-border">
            <nb-list class="element">

              <nb-list-item class="list-elt-header paginator">
                <div class="col col-num">N°</div>
                <div class="col col-name">Transporteur</div>
                <div class="col col-name">Conducteur</div>
                <div class="col col-qty">Tonnage</div>
                <div class="col col-qty">Quai</div>
                <div class="col col-time">Temps d'attente</div>
                <div class="col col-immatriculation">Immatriculation</div>
                <div class="col col-status center">Statut</div>
                <div class="col col-action">
                  <div class="action-icons"></div>
                  <div class="col col-action">
                    <div class="action-icons"></div>
                  </div>
                </div>
              </nb-list-item>
            </nb-list>
            <nb-list class="element scrool-style" [nbSpinner]="displayLoading" nbSpinnerStatus="primary">
              <nb-list-item class="list-elt-header" *ngFor="let inspection of loadedInspections; index as i">
                <div class="col col-num">{{ i + 1 }}</div>
                <div class="col col-name" title="{{ inspection?.carrier?.label }}">{{ inspection?.carrier?.label || "Non
                  renseigner" | truncateString : 20}}</div>
                <div class="col col-name" title="{{ inspection?.driver }}">{{ inspection?.driver || "Non renseigner" |
                  truncateString : 20 }}</div>
                <div class="col col-qty">{{ inspection?.truckTonnage }}</div>
                <div class="col col-qty">{{ inspection?.dockCode | getDock: docks }}</div>
                <div class="col col-time">
                  <p [ngStyle]="inspection?.dockTimeStart | colorStyleWaiting">
                    {{commonSrv.getDurationDate(inspection?.dockTimeStart)}}
                  </p>
                </div>
                <div class="col col-immatriculation">
                  {{ inspection?.tractor?.label || "Non renseigner"}}
                </div>
                <div class="col col-status" *ngIf="!inspection?.dockCode || inspection?.dockCode === 0">
                  <nb-badge status="warning" text="EN ATTENTE" class="badge" position="top start"></nb-badge>
                </div>
                <div class="col col-status" *ngIf="inspection?.dockCode && inspection?.dockCode !== 0">
                  <nb-badge [text]="inspection?.enable == false ? '0' : inspection?.truckStatus | getStatusTrucksLabel"
                    class="badge" position="top start"
                    [status]="inspection?.enable == false ? '0' : inspection?.truckStatus | getColorStatusRemovals"></nb-badge>
                </div>
                <div class="col col-action">

                  <div class="col col-action">
                    <button nbTooltip="Modifier" class="button-margin" nbTooltipPlacement="top" nbTooltipStatus
                      (click)='openUpdateInspection(updateTruckDialog, inspection)'>
                      <nb-icon icon="edit-2-outline" status="primary" [options]="{ animation: { type: 'zoom' } }">
                      </nb-icon>
                    </button>
                    <button nbTooltip="En panne" nbButton outline nbTooltipPlacement="top" nbTooltipStatus
                      (click)="openUpdateTruckModal(updateAeDialog,600, inspection)">
                      <nb-icon icon="alert-circle-outline" status="primary" [options]="{ animation: { type: 'zoom' } }">
                      </nb-icon>
                    </button>
                  </div>

                </div>
              </nb-list-item>
              <nb-list-item class="list-elt-header" *ngIf="!loadedInspections?.length">
                <div class="not-found">
                  <img src="../../../../assets/images/icons/shipped.png" alt="" />
                  <h6>Aucun camion trouvé</h6>
                </div>
              </nb-list-item>
            </nb-list>
          </nb-card>
        </nb-tab>

      </nb-tabset>

    </nb-card>
  </div>
</div>

<ng-template #updateAeDialog let-data let-ref="dialogRef">
  <nb-card>
    <nb-card-header>Confirmation</nb-card-header>
    <nb-card-body>
      <p *ngIf="actionStatus === 600">
        Vous essayez d'envoyer ce camion dans la liste des camions en panne.
      </p>
      <p *ngIf="actionStatus === 700">
        Vous essayez d'envoyer ce camion dans le parking.
      </p>
      <p *ngIf="actionStatus === 400">
        Vous essayez d'envoyer ce camion dans au parking usine.
      </p>
      <p>Voulez vous poursuivre?</p>
    </nb-card-body>
    <nb-card-footer>
      <button nbButton ghost status="basic" (click)="ref.close()">
        <nb-icon icon="close-square-outline"></nb-icon>
        Annuler
      </button>
      <button nbButton outline status="success" (click)="ref.close(true)" [disabled]="isLoading" class="btn-border">
        <nb-icon icon="done-all-outline"></nb-icon>
        Confirmer
      </button>
    </nb-card-footer>
  </nb-card>
</ng-template>

<ng-template #truckReceived let-data let-ref="dialogRef">
  <nb-card>
    <nb-card-header>Confirmation</nb-card-header>
    <nb-card-body>
      <p>
        Vous êtes sur le point de marquer ce camion comme réceptionné.
      </p>
      <p>Voulez vous poursuivre?</p>
    </nb-card-body>
    <nb-card-footer>
      <button nbButton ghost status="basic" (click)="ref.close()">
        <nb-icon icon="close-square-outline"></nb-icon>
        Annuler
      </button>
      <button nbButton outline status="success" (click)="UpdateTruckInspection()" [disabled]="isLoading"
        class="btn-border">
        <nb-icon icon="done-all-outline"></nb-icon>
        Confirmer
      </button>
    </nb-card-footer>
  </nb-card>
</ng-template>

<ng-template #updateTruckDialog let-data let-ref="dialogRef" class="dialogRef">
  <div class="order-detail-container her-width">
    <nb-card>
      <nb-card-header class="form--header">Modifier l'inspection</nb-card-header>
      <nb-card-body>
        <nb-tabset>
          <nb-tab tabTitle="Informations Chauffeur">
            <div class="form-container">
              <div class="form-group">
                <div class="attribute-block">
                  <label class="text"> Transporteur</label>
                </div>
                <input type="text" placeholder="Transporteur" nbInput fullWidth class="empty-input height"
                  [nbAutocomplete]="autoComplete4" [(ngModel)]="currentInspections.carrier.label"
                  (ngModelChange)="onModelCarrierChange($event)" />
                <nb-autocomplete #autoComplete4>
                  <nb-option *ngFor="let option of dataCarrierLabels" [value]="option">{{option }}
                  </nb-option>
                </nb-autocomplete>
              </div>
              <div class="form-group">
                <div class="attribute-block">
                  <label class="text" for="nameInput">Nom du chauffeur</label>
                </div>
                <input nbInput class="carrier-name input-width" status="basic" type="text" fullWidth
                  placeholder="Saisissez le nom du chauffeur" [(ngModel)]='currentInspections.driver'>
              </div>
              <div class="form-group">
                <div class="attribute-block">
                  <label class="text"> Numéro de téléphone</label>
                </div>
                <input id="typeInput" nbInput status="basic" type="tel" (keyup)="verifyTel($event)"
                  [(ngModel)]="currentInspections.driverTel" fullWidth
                  placeholder="Saisissez le numero de tel du chauffeur" />
              </div>
              <div class="form-group">
                <div class="attribute-block">
                  <label class="text">Immatriculation du véhicule</label>
                </div>
                <input id="typeInput" nbInput status="basic" type="text" [(ngModel)]="currentInspections?.tractor.label"
                  fullWidth placeholder="Saisissez l'immatriculation du Véhicule" />
              </div>
              <div class="form-group">
                <div class="attribute-block">
                  <label class="text"> Date d'expiration du permis</label>
                </div>
                <input nbInput [nbDatepicker]="datePickerStart" [(ngModel)]="currentInspections.licenseExpirationDate"
                  fullWidth class="empty-input height" placeholder="Début" />
                <nb-datepicker #datePickerStart>
                </nb-datepicker>
              </div>
              <div class="form-group">
                <div class="attribute-block">
                  <label class="text">Tonnage du camion</label>
                </div>
                <input id="typeInput" nbInput status="basic" type="number" [min]="0" max="45"
                  (keyup)="commonSrv.verifyTonnage($event)" type="number" [(ngModel)]="currentInspections.truckTonnage"
                  fullWidth placeholder="Saisissez le Tonnage du camion" />
              </div>
              <div class="form-group">
                <div class="attribute-block">
                  <label class="text"> Point de depart</label>
                </div>
                <input id="typeInput" nbInput status="basic" type="tel" [(ngModel)]="currentInspections.truckOrigin"
                  fullWidth placeholder="Saisissez le point de depart du camion" />
              </div>
              <div class="form-group">
                <div class="attribute-block">
                  <label class="text"> Destination</label>
                </div>
                <input id="typeInput" nbInput status="basic" type="tel"
                  [(ngModel)]="currentInspections.truckDestination" fullWidth
                  placeholder="Saisissez la destination du camion" />
              </div>
              <div class="form-group">
                <div class="attribute-block">
                  <label class="text"> Ridelle du camion</label>
                </div>
                <nb-select class="carrier-name input-width"
                  [placeholder]="currentInspections.statusJacks | getStatusTruck" fullWidth class="empty-input"
                  [(selected)]='currentInspections.statusJacks'>
                  <nb-option [value]='status?.value' *ngFor="let status of statusChoices">{{status?.label}}</nb-option>
                </nb-select>
              </div>
              <div class="form-group">
                <div class="attribute-block">
                  <label class="text">Circuit hydraulique(Verins)</label>
                </div>
                <nb-select class="carrier-name input-width"
                  [placeholder]="currentInspections.statusSideBoard | getStatusTruck" fullWidth class="empty-input"
                  [(selected)]='currentInspections.statusSideBoard'>
                  <nb-option [value]='status?.value' *ngFor="let status of statusChoices">{{status?.label}}</nb-option>
                </nb-select>
              </div>

            </div>
          </nb-tab>

          <nb-tab tabTitle="Verifications">
            <div class="order-infor order-pick-up">
              <table class="order-table">
                <tr>
                  <td class="title">Statut global:</td>
                  <td class="order-right">
                    <nb-select class="carrier-name input-width" placeholder="Choissisez le statut" status='primary'
                      size="small" class="empty-input" [(selected)]='currentInspections.decision'>
                      <nb-option [value]='decision' *ngFor="let decision of InspectionDecisions">{{decision}}
                      </nb-option>
                    </nb-select>
                  </td>
                </tr>
                <tr *ngFor="let check of currentInspections?.checkList; index as i ">
                  <td>{{check?.label?.slice(0,35)}}</td>
                  <nb-toggle class="order-right" [(ngModel)]="check.state"></nb-toggle>
                </tr>
              </table>
            </div>
          </nb-tab>
        </nb-tabset>
      </nb-card-body>
      <nb-card-footer class="form--footer">
        <button nbButton outline status="basic" class="" (click)="ref.close()">
          <nb-icon icon="close-square-outline" [options]="{ animation: { type: 'zoom' } }" class="remove-icon">
          </nb-icon>
          fermer
        </button>
        <button nbButton outline status="primary" class="" (click)="saveInspection();ref.close()">
          <nb-icon icon="done-all-outline" [options]="{ animation: { type: 'zoom' } }" class="remove-icon">
          </nb-icon>
          Valider
        </button>
      </nb-card-footer>
    </nb-card>
  </div>
</ng-template>