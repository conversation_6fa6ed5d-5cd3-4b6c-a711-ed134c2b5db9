import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { PackagingRoutingModule } from './packaging-routing.module';
import { PackagingComponent } from './packaging.component';
import { FormsModule } from '@angular/forms';
import { NbIconModule, NbButtonModule, NbSelectModule, NbCardModule, NbListModule, NbDialogModule, NbToastrModule, NbSpinnerModule, NbInputModule, NbTooltipModule } from '@nebular/theme';


@NgModule({
  declarations: [
    PackagingComponent
  ],
  imports: [
    CommonModule,
    PackagingRoutingModule,
    NbIconModule,
    NbButtonModule,
    NbButtonModule,
    FormsModule,
    NbSelectModule,
    NbCardModule,
    NbListModule,
    NbDialogModule,
    NbToastrModule,
    NbSpinnerModule,
    NbInputModule,
    NbTooltipModule
  ]
})
export class PackagingModule { }
