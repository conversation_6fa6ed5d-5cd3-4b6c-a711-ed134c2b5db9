import { FormsModule } from '@angular/forms';
import { NbCardModule,
  NbInputModule,
  NbButtonModule,
  NbIconModule,
  NbSpinnerModule,
  NbListModule,
  NbTooltipModule,
  NbFormFieldModule,
  NbDatepickerModule,
  NbProgressBarModule,
  NbToggleModule,
  NbSelectModule, 
  NbTabsetModule,
  NbAutocompleteModule} from '@nebular/theme';
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MenuReportingComponent } from './menu-reporting.component';
import { TileTimesComponent } from './tile-times/tile-times.component';
import { MenuReportingRoutingModule } from './menu-reporting-routing.module';
import { TileNumbersComponent } from './tile-numbers/tile-numbers.component';
import { TileCarriersComponent } from './tile-carriers/tile-carriers.component';
import { TileInspectionComponent } from './tile-inspection/tile-inspection.component';
import { TileChartStoreComponent } from './tile-chart-store/tile-chart-store.component';
import { TileChartTruckComponent } from './tile-chart-truck/tile-chart-truck.component';
import { TileChartProductComponent } from './tile-chart-product/tile-chart-product.component';
import { TileChartCarrierComponent } from './tile-chart-carrier/tile-chart-carrier.component';
import { TileChartRemovalsComponent } from './tile-chart-removals/tile-chart-removals.component';
import { TileAesRenderComponent } from './tile-aes-render/tile-aes-render.component';
import { TileAesPickupComponent } from './tile-aes-pickup/tile-aes-pickup.component';
import { TileInspectionRatioComponent } from './tile-inspection-ratio/tile-inspection-ratio.component';
import { SharedModule } from "../../shared/shared.module";
import { TileCarrierResponseTimeComponent } from './tile-carrier-response-time/tile-carrier-response-time.component';
import { TileCarrierDeliveryTimeComponent } from './tile-carrier-delivery-time/tile-carrier-delivery-time.component';
// import { Chart } from './tile-chart-truck/tile-chart-truck.component';


@NgModule({
    declarations: [
        TileTimesComponent,
        TileNumbersComponent,
        TileCarriersComponent,
        MenuReportingComponent,
        TileChartStoreComponent,
        TileChartTruckComponent,
        TileInspectionComponent,
        TileChartProductComponent,
        TileChartCarrierComponent,
        TileChartRemovalsComponent,
        TileAesRenderComponent,
        TileAesPickupComponent,
        TileInspectionRatioComponent,
        TileCarrierResponseTimeComponent,
        TileCarrierDeliveryTimeComponent,
    ],
    imports: [
        CommonModule,
        NbCardModule,
        FormsModule,
        NbIconModule,
        NbListModule,
        NbInputModule,
        NbTabsetModule,
        NbButtonModule,
        NbToggleModule,
        NbSelectModule,
        NbSpinnerModule,
        NbTooltipModule,
        NbFormFieldModule,
        NbDatepickerModule,
        NbProgressBarModule,
        NbAutocompleteModule,
        MenuReportingRoutingModule,
        SharedModule
    ]
})
export class MenuReportingModule { }
