import {animate, state, style, transition, trigger } from '@angular/animations';
import { StorageService } from '../shared/services/storage.service';
import { AuthService } from '../shared/services/auth.service';
import { Credentials } from '../shared/models/credentials';
import { Component, OnInit } from '@angular/core';
import { NbToastrService } from '@nebular/theme';
import { Router } from '@angular/router';
import get from 'lodash-es/get';

@Component({
  selector: 'clw-home',
  templateUrl: './home.component.html',
  styles: [
  ],
  animations: [
    trigger('openLoginForm', [

      state('open', style({
        height: '400px',
        opacity: 1,
        backgroundColor: '#f8f8f8',
      })),

      state('closed', style({
        height: '0px',
        opacity: 0,
        backgroundColor: '#ffffff'
      })),

      transition('closed => open', [
        animate('350ms')
      ]),

      transition('open => closed', [
        animate('500ms')
      ])
    ])
  ]
})
export class HomeComponent implements OnInit {

  options = {
    label: 'Sliding Entrances',
    animations: ['slideInUp', 'slideInDown', 'slideInLeft', 'slideInRight']
  }
  credentials: Credentials;
  isLoading: boolean;
  user: any;

  constructor(
    private authSvr: AuthService,
    private toastSrv: NbToastrService,
    private storageSrv: StorageService,
    private router : Router
  ) { }

  ngOnInit(): void {
    this.credentials = { login: null, password: null };

  }

  openLoginForm() {
    this.authSvr.formStatus.isActive = !this.authSvr.formStatus.isActive
  }

  isFormActive(): Boolean{
    return this.authSvr.formStatus.isActive;
  }


  async login(): Promise<any> {
    try {
      if (!this.credentials.login || !this.credentials.password) {
        // this.isLoading = false;
        return this.toastSrv.danger('Veuillez entrez des identifiants valides', 'Echec operation');
      }
      // if (!this.authSrv.validateEmail(this.credentials.email)) {
      //   return this.toastSrv.danger('Entrez une adresse email correcte', 'Adresse email incorrecte');
      // }


      this.isLoading = true;

      this.credentials.login = this.credentials.login.toLowerCase();
      await this.authSvr.signin(this.credentials);

      this.user = this.storageSrv.getObject('user');
      if (this.user.enable === false) {
        this.isLoading = false
        return this.toastSrv.danger(`Vous n'avez pas d'acces a l'application.`, 'Echec operation');
      }
      const category = get(this.user, 'category');
      this.router.navigate(['/client/removal/historic'])


/*       if (category === 300) { this.router.navigate(['/admin']); }
      if (category === 100) { this.router.navigate(['/user']); } */
    } catch (error) {
      console.log(error);
    } finally { this.isLoading = false; }
  }

  // logout user funtion
  logOut(): void {
    this.authSvr.signout();
  }
}
