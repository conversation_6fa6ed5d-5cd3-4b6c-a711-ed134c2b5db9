<form class="auth-modal-container modal-container animate" id="auth-modal" [nbSpinner]="isLoading">
  <!-- *ngIf="commonSrv.showAuthModal" -->

  <div class="header-modal-auth">
    <span class="close" title="Close Modal" (click)="commonSrv.showAuthModal = false">&times;</span>
    <div class="h2-title">Authentification</div>
    <div class="small-text ">
      <div>
        Bien vouloir renseigner vos identifiants pour accéder à la plateforme d'administration
      </div>
    </div>
    <!-- <div class="h4-title" >Se connecter avec</div>
    <div class="social-connexion container">
      <div class="social-element">
        <img src="assets/icons/google-logo.png" alt="">
        <div class="bloc-center">
          <div class="small-title" >Se connecter via Google</div>
        </div>
      </div>

      <div class="social-element" (click)="authByOtp()">
        <img src="assets/icons/otp-logo.png" alt="">
        <div class="bloc-center">
          <div class="small-title" >Se connecter via OTP</div>
        </div>
      </div>
    </div>

    <div class="h4-title">
      <div class="border-line">
      </div>
      <p >OU</p>
      <div class="border-line">
      </div>
    </div> -->
  </div>
  <div class="container">
    <div>
      <!-- *ngIf="!formResetPassword" -->
      <input class="input" [(ngModel)]="credentials.login" type="email" placeholder="Email" name="email" required
        title="Entrez un email vailde" data-cy="login-email" />
      <span class="input-icon-right">
        <i [class]="showPassword ? 'pi pi-eye-slash' :'pi pi-eye'" (click)="showPassword = !showPassword"></i>
        <input class="input" [(ngModel)]="credentials.password" [type]="showPassword? 'test' : 'password'"
          placeholder="Mot de passe" name="psw" required data-cy="login-password" />
      </span>
      <div class="text-left" (click)="formResetPassword = true"> Mot de passe oublié ? </div>
      <button type="submit" class="btn btn-primary" title="CONNEXION" (click)="login()"
       data-cy="login-btn" [disabled]="isLoading"> CONNEXION </button>

    </div>
    <!-- <form *ngIf="formResetPassword">
      <input class="input" [(ngModel)]="credentials.email" type="email" placeholder="Email" name="email" required
        title="Entrez un email vailde" />
      <div class="text-left" (click)="formResetPassword = false" > Se connecter </div>

      <button type="submit" class="btn btn-primary" title="RÉINITIALISER"
        (click)="resetPassword()">
        RÉINITIALISER
      </button>
    </form> -->
  </div>

</form>

<!-- <p-toast position="top-right"></p-toast> -->
