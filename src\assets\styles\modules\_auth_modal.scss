.auth-modal-container {
  box-shadow: -6px 14px 20px 9px rgba(53, 54, 54, 0.2);
  background-color: #fefefe;
  border: 1px solid #888;
  width: 30%;
  border-radius: 10px;
  position: relative;
  overflow: hidden;

  .close {
    position: absolute;
    right: 15px;
    top: 5px;
    color: #000;
    font-size: 35px;
    font-weight: bold;
  }

  .close:hover,
  .close:focus {
    color: var(--clr-secondary-400);
    cursor: pointer;
  }

  .error-message {
    padding: 5px 0;
    font-size: 13px;
    font-weight: bold;
    line-height: 1.3em;
    color: var(--clr-danger-400);
  }

  .text-center {
    text-align: center !important;
    margin-top: 1.5rem;
    padding: 0 !important;
  }

  .text-left {
    padding: 0 0 15px 0;
    font-size: 13px;
    text-align: right;
    line-height: 1.3em;
    font-family: $font-mont-bold;

    &:hover {
      text-decoration: underline;
      cursor: pointer;
      color: $bg-color-primary;
    }
  }

  .header-modal-auth {
    padding: 30px 0 0 0;
    position: relative;

    .h2-title {
      font-size: 24px;
      font-weight: bold;
      font-family: $font-mont-bold;
      margin-bottom: 10px;
      color: $color-primary;
      text-align: center;
    }

    .small-text {
      text-align: center;
      font-weight: 500;
      font-size: 15px;
      line-height: 20px;
      padding: 15px 10%;
      margin-bottom: 3px;

      div {
        font-family: $font-mont-bold;
      }
    }

    .h4-title {
      font-size: &_px;
      font-weight: bold;
      padding: 0px 10%;
      color: $color-primary;
      font-family: $font-mont-bold;
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: center;

      p {
        margin: 0 5px;
        font-size: 18px;
      }
    }

    .border-line {
      border: 1px solid #ccc;
      width: 55px;
    }

    .social-connexion {
      display: flex;
      justify-content: space-around;
      gap: 1rem;

      .social-element {
        cursor: pointer;
        padding: 5px;
        display: flex;
        background: rgba(237, 242, 249, 0.85);
        border: 1px solid #dedede;
        box-sizing: border-box;
        border-radius: 8px;

        img {
          max-width: 25px;
          max-height: 25px;
          margin-right: 4px;
        }

        .bloc-center {
          display: flex;
          flex-direction: column;
          justify-content: center;

          .small-title {
            font-weight: 600;
            font-size: 12px;
          }

        }

        &:hover {
          opacity: 0.9;
          background-color: $color-third;
          color: $color-primary;
        }
      }
    }
  }

  .header-modal-otp {
    padding: 30px 0 0 0;
    position: relative;
    width: 100%;

    .h2-title {
      font-size: 24px;
      font-weight: bold;
      font-family: $font-mont-bold;
      margin-bottom: 10px;
      color: $color-primary;
      text-align: center;
      padding-bottom: 8px;
      //border-bottom: 2px solid var(--clr-primary-400);
      margin-inline: 10%;
      margin-bottom: 1.5rem;
      ;
    }

    .head-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 1.5rem;
      margin-bottom: 0.5rem;
      width: 100%;

      .img-otp {
        max-width: 100%;
        padding: 0 10%;

        img {
          width: 100%;
        }
      }

      .h3-title {
        text-align: center;
        font-weight: 600;
        font-size: 16px;
        padding: 0px 10%;
      }
    }

    .small-text {
      text-align: center;
      font-weight: 500;
      font-size: 15px;
      line-height: 20px;
      padding: 0px 10%;
      margin-bottom: 3px;
    }

    .h4-text {
      font-size: &_px;
      font-weight: 600;
      padding: 0px 10%;
      color: $color-muted;
      font-family: $font-mont-bold;
    }

    .input-otp {
      display: flex;
      flex-direction: row;
      gap: 4px;
      justify-content: space-between;
      padding: 0px 10%;
    }

  }

  .container {
    padding: 20px 10%;

    .account-input {
      height: 35vh;
      overflow: auto;
      margin-bottom: 1rem;
    }

    .input-icon-right {
      position: relative;

      i {
        position: absolute;
        bottom: 0;
        left: -25px;
        z-index: 10;
        color: $color-primary;
        height: 25px;
        width: 25px;
        color: black;
        bottom: -65px;
      }
    }

    .input {
      width: 98%;
      padding: 12px 20px;
      margin-bottom: 15px;
      border: 1px solid #ccc;
      box-sizing: border-box;
      border-radius: 0.5em;
      background-color: hsla(215, 50%, 95%, 0.85);
      font-family: $font-mont-bold;

      &::placeholder {
        font-family: $font-mont-bold;
      }

      &:not(:focus):invalid {
        color: $color-muted
      }
    }

    .input-dropdown {
      width: 98%;
      margin-bottom: 15px;
      padding-right: 20px;
      border: 1px solid #ccc;
      box-sizing: border-box;
      border-radius: 0.5em;
      background-color: rgba(237, 242, 249, 0.85);

      .p-dropdown.p-dropdown-clearable .p-dropdown-label {
        font-family: $font-mont-bold;
        font-size: 14px;
        padding: 0 0 0 20px;
      }

      .p-dropdown-clear-icon {
        right: 0.5rem;
      }

      &:not(:focus):invalid {
        color: $color-muted;
      }
    }

    button {
      border: none;
      font-size: 1rem;
      cursor: pointer;
      letter-spacing: 1px;
      padding: 0.5rem;
      width: 7rem;
      margin: 0 auto;
      border-radius: 2em;
      position: relative;
      font-family: $font-mont-bold !important;
      transition: background 0.3s ease;
      height: auto;

      &:hover {
        background: darken($bg-color-primary, 10%);
      }

      @media only screen and (max-width: 520px) {
        margin: 0 25%;
      }

    }

    .block-icon {
      display: flex;
      justify-content: space-between;
      margin: 10px 35% 10px 35%;

      img {
        max-width: 35px;
        cursor: pointer;
      }
    }

    .text-underline {
      color: $bg-color-primary;
      margin-top: 15px;
      text-decoration: underline;
      font-size: 15px;
      line-height: 15px;
      text-align: center;

      &:hover {
        cursor: pointer;
      }
    }

    .cancelbtn {
      width: auto;
      margin: 0;
      padding: 10px 18px;
      background-color: var(--clr-normal-100);
    }

    .p-dropdown {
      width: 98%;
      margin-bottom: 1rem;
    }

    @media only screen and (max-width: 520px) {
      padding: 10px 6%;
    }
  }

  .footer-modal {
    padding: 15px 10%;
    border-radius: 0 0 0.5em 0.5em;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #f1f1f1;

    .psw {
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      font-weight: bold;

      input {
        margin-right: 5px;
      }
    }

    @media only screen and (max-width: 520px) {
      padding: 10px 6%;
    }
  }


  @media only screen and (max-width: 920px) {
    width: 48%;

    .header-modal-auth {
      .social-element {
        padding: 4px 10px 4px 1px !important;

        img {
          max-width: 20px !important;
          max-height: 20px !important;
        }
      }

      .small-title {
        font-size: 10px !important;
        margin-bottom: 2px !important;
      }

      .small-text {
        padding: 0 6% 10px 6% !important;
        font-size: 13px !important;

        div {
          font-family: $font-mont-bold !important;
        }
      }

      .text {
        font-size: 8px !important;
      }
    }
  }

  @media only screen and (max-width: 520px) {
    width: 85%;

    .avatar {
      max-width: 22%;
    }

    h2,
    .container {
      padding: 10px 6%;
    }

  }
}