.stores-container {
    .cards {
        display: flex;
        justify-content: space-between;
        margin-top: 25px;
        .card-container {
            width: 100%;
            .filter-elt{
                width: 15%;
            }
            .store-side {
                display: flex;
                justify-content: flex-start;
                flex-wrap: wrap;
                row-gap: 16px;
                column-gap: 4.4%;
                .no-shadow {
                    box-shadow: none !important;
                    width: 13%;
                    margin: 0 0 2%;
                    align-items: center;
                    justify-content: center;
                    display: flex;
                    padding: 20px 5px;

                    nb-icon {
                        height: 83px;
                        width: fit-content;
                        display: flex;
                        justify-content: center;
                        margin-bottom: 5px;
                    }
                    &:hover {
                        @include scale-effect;
                    }
                }
                .store {
                    background: #fff;
                    margin: 0 0 2%;
                    align-items: center;
                    justify-content: center;
                    display: flex;
                    flex-direction: column;
                    width: 13%;
                    box-shadow: 0px 4px 10px rgb(0 0 0 / 9%);
                    border-radius: 13px;
                    padding: 20px 5px;
                    img {
                        margin: 20px 0;
                        height: 60px;
                        display: flex;
                        justify-content: center;
                    }

                    label {
                        font-weight: bolder;
                        font-size: 10px;
                        line-height: 18px;
                        text-align: center;
                        color: #353535;
                    }

                    &:hover {
                        @include scale-effect;
                    }
                }
            }
        }
    }

    label{
        text-transform: uppercase
    }
}

.detail-store-container {
    @include vh-center;

    .flipcard-body {
        width: 30vw;
        display: flex;
        align-items: center;

        span {
            font-weight: bold;
        }
    }
    nb-card {
        .image {
            display: flex;
            justify-content: center;
            img {
                width: 100px;
            }
        }
        border-radius: 9px;
        .form--header {
            display: flex;
            justify-content: space-between;
            .action-icons {
                display: flex;
                justify-content: flex-end;
            }
        }

        .edit-container {
            .row {
                display: flex;
                justify-content: space-between;
                .input {
                    width: 45%;
                }
            }
        }

        .detail-container {
            display: flex;
            flex-direction: column;

            span {
                font-weight: bold;
            }

            .image {
                margin-bottom: 25px;

                img {
                    width: 100px;
                }
            }

            .row {
                justify-content: space-between;
                display: flex;
                margin-bottom: 15px;

                .title {
                    font-size: 15px;
                    font-weight: 600;
                }

                .value {
                    font-size: 14px;
                    text-transform: uppercase;

                }
            }
        }
    }
}

.add-store-container {
    nb-card {
        .row {
            display: flex;
            justify-content: space-between;
            .input {
                width: 45%;
            }
        }
    }
}
