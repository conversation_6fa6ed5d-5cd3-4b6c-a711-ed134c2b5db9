import { Product } from 'src/app/shared/models/product';
import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { BaseUrlService } from 'src/app/shared/services/base-url.service';
import { StorageService } from 'src/app/shared/services/storage.service';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root'
})
export class ProductsService {
  

  BASE_URL: string;
  currentProduct: Product;

  constructor(
    private http: HttpClient,
    private baseUrlSrv: BaseUrlService,
    private storageSrv: StorageService,

  ) {
    this.BASE_URL = `${this.baseUrlSrv.getOrigin()}${environment.basePath}`;

  }

  generateQueryParams(param: any): any {   
    let params = new HttpParams();

    const { offset, limit, type, label } = param;

    if (offset) {
      params = params.append('offset', `${offset}`);
    }
    if (limit) {
      params = params.append('limit', `${limit}`);
    }

    if (label) {
      params = params.append('label', `${label}`);
    }
    return params;
  }

  async getProducts(param:any): Promise<Product[]> {
    const params = this.generateQueryParams(param);
    return await this.http.get<Product[]>(`${this.BASE_URL}/products`,{params}).toPromise();

  }

  async deleteProducts(): Promise<any> {
    return await this.http.delete(`${this.BASE_URL}/products/${this.currentProduct._id}`).toPromise();
  }

  async editProducts(product: Product ): Promise<any> {
    return await this.http.put(`${this.BASE_URL}/products/${this.currentProduct._id}`, product).toPromise();
  }

  async addProducts(product: Product ): Promise<any> {
    return await this.http.post(`${this.BASE_URL}/products/`, product).toPromise();
  }

}
