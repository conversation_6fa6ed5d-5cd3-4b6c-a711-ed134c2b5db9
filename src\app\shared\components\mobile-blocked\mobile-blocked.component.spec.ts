import { ComponentFixture, TestBed } from '@angular/core/testing';

import { MobileBlockedComponent } from './mobile-blocked.component';

describe('MobileBlockedComponent', () => {
  let component: MobileBlockedComponent;
  let fixture: ComponentFixture<MobileBlockedComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ MobileBlockedComponent ]
    })
    .compileComponents();

    fixture = TestBed.createComponent(MobileBlockedComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
