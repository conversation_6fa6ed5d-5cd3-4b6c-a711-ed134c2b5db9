import { Observable, of } from 'rxjs';
import { Location } from '@angular/common';
import { Component, OnInit, TemplateRef, ChangeDetectorRef } from '@angular/core';
import { Router } from '@angular/router';
import { NbDialogRef, NbDialogService, NbToastrService } from '@nebular/theme';
import * as moment from 'moment';
import { AuthorizationRemoval, parkingStatus } from 'src/app/shared/models/authorization-removal';
import { MenuBrokenDownTrucksService } from './menu-broken-down-trucks.service';
import { QueueStatus } from 'src/app/shared/models/render-type.enum';
import { RemovalService } from '../removal-management/removal.service';
import { WaitingThreadService } from '../waiting-thread/waiting-thread.service';
import { TruckInspection } from 'src/app/shared/models/truck-inspection';
import { CommonService } from 'src/app/shared/services/common.service';
import { StatusRemovals } from 'src/app/shared/models/status-removal.enum';
import { PaginationConfig, PaginationService } from 'src/app/shared/services/pagination.service';

@Component({
  selector: 'clw-menu-broken-down-trucks',
  templateUrl: './menu-broken-down-trucks.component.html'
})
export class MenuBrokenDownTrucksComponent implements OnInit {
  isLoading: boolean;
  dialogRef: NbDialogRef<any>;
  removedialogRef: NbDialogRef<any>;
  removals: AuthorizationRemoval[];
  removal: any;
  dateEnd: number;
  DateEndBrokenDown: any;
  DateStartBrokenDown: number;
  currDate: number;
  offset: number = 0;
  limit: number = 20;
  startIndex: number = 1;
  endIndex: number = 20;
  offsetInsp: number = 1;
  startIndexInsp: number = 1;
  endIndexInsp: number = 20;
  total: number;
  limitInsp: number = 20;
  totalInsp: number;
  currimmatriculation: any;
  rangeFilter: { start: any; end: any; };
  productLabels: any;
  filteredImmatriculation$: Observable<string[]>;
  immatriculaion: any;
  immatrulationsLabels = [];
  brokenDownTime: number;
  docks: any;
  queues = ['Parking Usine', 'Chargement Usine'];
  selectedQueue: any;
  selectedDock: any;
  selectedWaiting: any
  dataForFilter: any;
  productFilter: any;
  waitingThread: any;
  waitingThreadTab: any[];
  waitingThreadTabDock: any[];
  waitingTheadTabNew: any[];
  displayLoading: boolean;
  inspections: TruckInspection[];
  hourInMillisecond: number = 3600000;

  // Configuration de la pagination
  paginationConfig: PaginationConfig = {
    currentPage: 1,
    itemsPerPage: 20,
    totalItems: 0,
    maxSize: 10
  };

  constructor(
    private downtruck: MenuBrokenDownTrucksService,
    private toastrSrv: NbToastrService,
    private dialogSrv: NbDialogService,
    private removalSrv: RemovalService,
    private toastSrv: NbToastrService,
    private waitingThreadSrv: WaitingThreadService,
    private router: Router,
    public commonSrv: CommonService,
    private location: Location,
    private toastrSvr: NbToastrService,
    private paginationService: PaginationService,
    private cdr: ChangeDetectorRef,
  ) { }

  async ngOnInit(): Promise<void> {
    this.rangeFilter = { start: null, end: null };

    // Initialiser la pagination avec des valeurs par défaut
    this.paginationConfig = {
      currentPage: 1,
      itemsPerPage: 20,
      totalItems: 0,
      maxSize: 10
    };

    setInterval(() => {
      this.brokenDownTime = moment().valueOf();
    }, 1000);

    setInterval(async () => {
      const options = {
        limit: this.paginationConfig.itemsPerPage,
        offset: (this.paginationConfig.currentPage - 1) * this.paginationConfig.itemsPerPage,
        status: 600,
        immatriculation: this.currimmatriculation,
        dateStart: this.rangeFilter.start
      };
      const { data, total } = await this.downtruck.getAuthorizationRemoval(options);
      this.removals = data;
      this.total = total;

      // Mettre à jour la pagination
      this.paginationConfig = {
        ...this.paginationConfig,
        totalItems: total
      };
    }, 300000);

    await this.getAllBrokenDown();
    await this.getInspections();

    let options = { dock: '' };
    this.docks = await this.removalSrv.getTruckByDock(options);
  }

  /**
   * Méthode appelée lorsque l'utilisateur change de page
   * @param page Numéro de la page sélectionnée
   */
  onPageChange(page: number): void {
    if (this.isLoading) return; // Éviter les requêtes multiples pendant le chargement

    // Créer une nouvelle instance de PaginationConfig pour forcer la détection des changements
    this.paginationConfig = {
      currentPage: page,
      itemsPerPage: this.paginationConfig.itemsPerPage,
      totalItems: this.total,
      maxSize: 10
    };

    // Forcer la détection des changements
    this.cdr.detectChanges();

    this.getAllBrokenDown();
  }

  /**
   * Méthode appelée lorsque l'utilisateur change le nombre d'éléments par page
   * @param itemsPerPage Nombre d'éléments par page sélectionné
   */
  onItemsPerPageChange(itemsPerPage: number): void {
    // Mettre à jour la limite pour la compatibilité avec le code existant
    this.limit = itemsPerPage;

    // Créer une nouvelle instance de PaginationConfig pour forcer la détection des changements
    this.paginationConfig = {
      currentPage: 1, // Revenir à la première page
      itemsPerPage: itemsPerPage,
      totalItems: this.total,
      maxSize: 10
    };

    // Forcer la détection des changements
    this.cdr.detectChanges();

    this.getAllBrokenDown();
  }

  async updateDock(id: string, data: { dockCode: number }): Promise<any> {
    this.isLoading = true;
    try {
      await this.removalSrv.updateDock(id, data);
      this.toastSrv.success(`ae assigne a un quai`, `Assignation reussi`)
    } catch (error) {
      return this.toastSrv.danger(`Erreur de connexion internet`, `Echec operation`)
    } finally { this.isLoading = false; }
  }

  updatePage(increment: number): void {
    const pagination = this.updatePagination(this.offset, this.limit, this.total, increment);

    this.offset = pagination?.updatedOffset;
    this.startIndex = pagination?.startIndex;
    this.endIndex = pagination?.endIndex;
  }

  updatePageTruckInsp(increment: number): void {
    const pagination = this.updatePagination(this.offsetInsp, this.limitInsp, this.totalInsp, increment);

    this.offsetInsp = pagination?.updatedOffset;
    this.startIndexInsp = pagination?.startIndex;
    this.endIndexInsp = pagination?.endIndex;
  }


  updatePagination(offset: number, limit: number, total: number, increment: number): { updatedOffset: number, startIndex: number, endIndex: number } {

    let updatedOffset = offset + increment;
    if (updatedOffset <= 0) {
      updatedOffset = 0;
    }

    let startIndex = (updatedOffset - 1) * limit;
    if (startIndex <= 0) {
      startIndex = 1;
    }

    let endIndex = updatedOffset * limit;
    if (endIndex >= total) {
      endIndex = total;
    }

    return { updatedOffset, startIndex, endIndex };
  }

  async nextPage(updatePageFunction: () => void, fetchFunction: () => Promise<void>): Promise<void> {
    updatePageFunction();
    await fetchFunction();
  }

  async nextPageGetAllBrokenDown(): Promise<void> {
    await this.nextPage(() => this.updatePage(1), () => this.getAllBrokenDown());
  }

  async nextPageGetInspections(): Promise<void> {
    await this.nextPage(() => this.updatePageTruckInsp(1), () => this.getInspections());
  }

  async previousPageGetAllBrokenDown(): Promise<void> {
    await this.nextPage(() => this.updatePage(-1), () => this.getAllBrokenDown());
  }

  async previousPageGetInspections(): Promise<void> {
    await this.nextPage(() => this.updatePageTruckInsp(-1), () => this.getInspections());
  }

  // Méthode pour réinitialiser les filtres et la pagination
  resetFilters(): void {
    this.rangeFilter = { start: null, end: null };
    this.currimmatriculation = null;

    // Réinitialiser la pagination à la première page
    this.paginationConfig = {
      currentPage: 1,
      itemsPerPage: this.paginationConfig.itemsPerPage,
      totalItems: 0,
      maxSize: 10
    };

    this.getAllBrokenDown();
  }


  async getAllBrokenDown(): Promise<void> {
    try {
      this.isLoading = true;

      const options = {
        limit: this.paginationConfig.itemsPerPage,
        offset: (this.paginationConfig.currentPage - 1) * this.paginationConfig.itemsPerPage,
        status: 600,
        immatriculation: this.currimmatriculation,
        dateStart: this.rangeFilter.start
      };

      const { data, total } = await this.downtruck.getAuthorizationRemoval(options);
      this.removals = data;
      this.total = total;

      // Créer une nouvelle instance de PaginationConfig pour forcer la détection des changements
      this.paginationConfig = {
        currentPage: this.paginationConfig.currentPage,
        itemsPerPage: this.paginationConfig.itemsPerPage,
        totalItems: total,
        maxSize: 10
      };

      // Pour la compatibilité avec le code existant
      this.endIndex = Math.min((this.paginationConfig.currentPage * this.paginationConfig.itemsPerPage), total);
      this.startIndex = total === 0 ? 0 : (this.paginationConfig.currentPage - 1) * this.paginationConfig.itemsPerPage + 1;

      // Vérification si la page actuelle est valide
      const totalPages = Math.ceil(this.paginationConfig.totalItems / this.paginationConfig.itemsPerPage);
      if (this.paginationConfig.currentPage > totalPages && totalPages > 0) {
        this.paginationConfig = {
          currentPage: totalPages,
          itemsPerPage: this.paginationConfig.itemsPerPage,
          totalItems: this.paginationConfig.totalItems,
          maxSize: 10
        };
        this.cdr.detectChanges();
        await this.getAllBrokenDown();
        return;
      }

    } catch (error) {
      this.toastrSrv.danger('Impossible de récupérer la liste des camions en panne', 'Erreur connexion !');
    } finally {
      this.isLoading = false;
    }
  }

  async setTruckWaitingTime(dialog: TemplateRef<any>, authorization: any) {
    this.removal = authorization;

    this.removedialogRef = this.dialogSrv.open(dialog, { context: '' });
    this.removedialogRef.onClose.subscribe(async (result): Promise<any> => {
      // this.setDateEndBrokenDown();
    });
  }

  async setDateEndBrokenDown(): Promise<any> {
    this.isLoading = true;
    try {

      if (this.removal?.dockTimeStart) {
        this.removal.dockTimeStart = moment().valueOf();
      } else {
        this.removal.parkTime = moment().valueOf();
      }
      if (this.selectedDock) {
        await this.sendToDock();
      }
      else {
        this.removal.status = QueueStatus[this.selectedQueue];
        await this.downtruck.updateAuthRemoval(this.removal, this.removal._id);
        this.toastSrv.success('Cette mise à jour a été effectuée avec succès ', 'Mise à jour réussi');
      }

      this.removedialogRef.close();
      this.router.navigate(["/client/removal/waiting-thread"]);
      this.isLoading = false;
    } catch (error) {
      this.toastSrv.danger('Une erreur est survenu lors de cette mise à jour', 'Échec de mise à jour')
    }
  }

  async setTruckBrokenDownEndDate(): Promise<any> {
    this.isLoading = true;
    try {

      if (!this.selectedDock) {
        return this.toastSrv.danger('Veuillez choisir un quai', 'Données incorrectes')
      }

      this.removal.dockTimeStart = moment().valueOf();
      this.removal.truckStatus = parkingStatus.FACTORY;
      this.removal.dockCode = this.selectedDock?.code;
      await this.removalSrv.updateInspection(this.removal?._id, this.removal);

      this.toastSrv.success('Cette mise à jour a été effectuée avec succès ', 'Mise à jour réussi');
      this.closeDialogAndNavigate();
      this.isLoading = false;
    } catch (error) {
      this.toastSrv.danger('Une erreur est survenue lors de cette mise à jour', 'Échec de mise à jour')
    }
  }

  private closeDialogAndNavigate(): void {
    this.removedialogRef.close();
    this.router.navigate(['/client/removal/material-inspections']);
  }

  async sendToDock(): Promise<any> {
    try {
      this.isLoading = true
      if (!this.selectedDock) {
        return this.toastSrv.danger('Veillez choisir un quais', 'Donnés incorrectes')
      }
      if (this.removal?.dockCode !== 0 && this.removal?.dockCode) {
        this.removal.status = 450;
        this.removal.dockCode = this.removal?.code;
        await this.removalSrv.updateAe(this.removal?._id, this.removal);
        this.toastSrv.success('Cette mise à jour a été effectuée avec succès ', 'Mise à jour réussi');
        return this.dialogRef?.close();
      }

      this.removal.status = 450;
      this.removal.dockCode = this.selectedDock?.code;
      this.removal.dockTimeStart = moment().valueOf();
      await this.removalSrv.updateAe(this.removal?._id, this.removal);
      this.toastSrv?.success('Cette mise à jour a été effectuée avec succès ', 'Mise à jour réussi');
      this.dialogRef?.close();

    } catch (error) {
      this.toastSrv.danger('Une erreur est survenu lors de cette mise à jour', 'Échec de mise à jour')
    } finally {
      this.isLoading = false;
      this.selectedDock = null;
    }

  }

  async getWaitingThread(): Promise<any> {
    this.isLoading = true;
    try {
      await this.getWaithingThreadFirst();
      await this.getWaithingThreadDock();
      await this.getWaithingThreadNew();
      this.docks = await this.removalSrv.getTruckByDock({});
      this.productFilter = null;
    } catch (error) {
      console.log(error);
      return this.toastSrv.danger(`Erreur de connexion internet`, `Echec operation`)
    } finally { this.isLoading = false; }
  }

  async getWaithingThreadFirst() {
    this.displayLoading = true;
    if (this.productFilter === 'MULTIX') {
      this.productFilter = 'MIX'
    }
    const options = { status: 400, moved: 400, product: this.productFilter, sort: 'parkTime', way: 1 }
    const { data } = await this.removalSrv.getAuthorizationRemoval(options);
    this.waitingThreadTab = data;
    this.displayLoading = false;
  }

  async getWaithingThreadDock() {
    this.displayLoading = true;
    if (this.productFilter === 'MULTIX') {
      this.productFilter = 'MIX'
    }
    const options = { status: 450, product: this.productFilter, sort: 'dockTimeStart', way: 1 }
    const { data } = await this.removalSrv.getAuthorizationRemoval(options);
    this.waitingThreadTabDock = data;
    this.displayLoading = false;
  }

  async getWaithingThreadNew() {
    this.displayLoading = true;

    if (this.productFilter === 'MULTIX') {
      this.productFilter = 'MIX'
    }
    const options = { status: 700, moved: 700, product: this.productFilter, sort: 'parkTime', way: 1 }
    const { data } = await this.removalSrv.getAuthorizationRemoval(options);
    this.waitingTheadTabNew = data
    this.displayLoading = false;


  }

  async getDetailsTruck(dialog: TemplateRef<any>, removal: any) {
    this.removal = removal;

    this.dialogRef = this.dialogSrv.open(dialog);
    this.dialogRef.onClose.subscribe(async (result): Promise<any> => {
    });
  }

  showTimeBrokenDown(waiting: any): any {
    const hours = 7200001; // 5400001 equal 1h30 min
    const minutes = 5400001;
  }

  selectedChange($event: any) {
    this.removals = this.removals.filter((elt: any) => elt.carrier.truck.immatriculation === $event.carrier.truck.immatriculation);
  }

  private filterImmatriculation(value: string): string[] {
    const filterValue = value.toLowerCase();
    return this.immatrulationsLabels.filter(optionValue => optionValue.toLowerCase().includes(filterValue));
  }

  onModelImmatriculationChange(value: string): void {
    this.filteredImmatriculation$ = of(this.filterImmatriculation(value));
  }

  async onSelectionImmatriculationChange($event: any): Promise<void> {
    try {
      this.isLoading = true
      this.immatriculaion = await this.downtruck.getAuthorizationRemoval({ immatriculation: $event });
    } catch (error) {
      this.toastrSrv.danger('Veuillez vérifier votre connexion internet', 'Erreur connexion internet');
      console.error(error);
    } finally {
      this.isLoading = false
      this.filteredImmatriculation$ = of(this.immatrulationsLabels);
    }
  }

  goBack() {
    if (window.history.length > 1) {
      this.location.back()
    } else {
      this.router.navigate(['/home'])
    }
  }

  async getInspections(): Promise<void> {
    try {
      this.isLoading = true;
      const options = {
        limit: this.limitInsp, offset: this.offsetInsp, truckStatus: StatusRemovals.BROKEN_DOWN
      }
      const { data, total } = await this.removalSrv.getTruckInspections(options);
      [this.inspections, this.totalInsp] = [data, total];
      this.endIndexInsp = (this.offsetInsp * this.limitInsp < total) ? this.offsetInsp * this.limitInsp : total;
      this.startIndexInsp = (total === 0) ? total : this.startIndexInsp || 1;
    }
    catch (error) {
      this.toastrSvr.danger('Impossible de récupérer la liste des Camions', 'Erreur connexion !');

    } finally { this.isLoading = false; }
  }
}
