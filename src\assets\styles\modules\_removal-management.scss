.removal-management-container {
  // background-color: #f3fbff;
  height: calc(100vh - 76px);

  .removals-container {
    // height: calc(100% - 76px);

    height: calc(100% - 136px);
    display: flex;
    justify-content: space-between;
  }

  .header {
    display: flex;
    justify-content: space-between;
    width: 67.6%;

    .col-paginator {
      position: relative;
      bottom: -17px;
    }
  }

  .removals-container .removals {
    width: 80%;
    height: 100%;
    background-color: #fff;
    // margin-right: 15px;
    border-radius: 5px;

    .search-bar {
      height: 70px;
      border-bottom: 2px solid #e9e9e9;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 30px;

      nb-form-field {
        width: 50%;
      }

      .select-filters {
        width: 49.5%;
        display: flex;
        justify-content: space-between;

        .filter-elt {
          width: 49.5%;
          margin: 0px !important;
        }

        .filter-elts {
          display: flex;
        }
      }

      .left-area {
        display: flex;
        margin: 0px !important;
        gap: 1em;

        .width {
          display: flex;
          justify-content: center;
          width: 20%;

          .width-input {
            width: 100%;
          }
        }
      }

      .right-area {
        display: flex;
        gap: 0.5em;
        align-items: center;

        .reset-btn {
          background-color: #ffff;
          color: $color-primary;
        }
      }
    }

    .ae-container {
      // height: calc(100% - 150px);
      padding: 15px 30px;
      gap: 15px;
      overflow: auto;
      width: 100%;
      margin: 10px 10px 10px 0;
      display: grid;
      grid-template-columns: 1fr 1fr;

      .ae {
        box-shadow: 0px 4px 17px rgba(0, 0, 0, 0.06);
        border-radius: 7px;
        border: 1px solid #efefef;
        padding: 1rem;
        width: 100%;
        cursor: move;
        font-size: 12px;

        .row {
          @include v-center;
          margin-bottom: 10px;
          font-size: 14px;

          .infors {
            @include v-center;
            width: 50%;

            .key {
              margin-right: 5px;
            }

            .color {
              border-radius: 5px;
              color: #fff;
              font-size: 14px;
              padding: 0 0.3rem;
            }
          }

          .infors-long {
            @include v-center;
            width: 100%;

            .key {
              margin-right: 5px;
            }

            .color {
              border-radius: 5px;
              color: #fff;
              font-size: 14px;
              padding: 0 0.3rem;
            }
          }
        }
        .ae-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 0.5rem;

          .ae-num {
            margin: 0;
            font-weight: bold;
          }

          button {
            padding: 0.25rem;
            min-width: auto;
          }
        }
      }

      .ae-num {
        text-transform: capitalize;
        color: $color-primary;
        font-size: 16px;
        font-family: $font-bold;
        letter-spacing: 2px;
      }
    }
  }

  .removals-container .carrier {
    height: 100%;
    width: 30%;
    overflow: auto;

    // background-color: #f3fbff;
    .search-bar {
      height: 70px;
      @include vh-center;
      padding: 0 16px;
      margin-bottom: 15px;
      justify-content: space-between;

      .text {
        width: 50%;
        @include v-center;
        height: 100%;
        font-size: 1.2em;
      }

      .search-input {
        width: 50%;
        @include v-center;
        height: 100%;

        nb-form-field {
          width: 100%;
        }
      }
    }

    .carrier-container {
      height: calc(100% - 85px);
      padding: 0px 16px;
      overflow: auto;

      .carrier-AE {
        border: 2px solid $color-primary;
        border-radius: 7px;
        border-style: dashed;
        margin-bottom: 16px;
        height: 150px;
        padding: 16px;

        .carrier-name {
          border-radius: 7px;
          background-color: $color-primary;
          color: #fff;
          font-weight: 700;
          font-family: $font-regular;
          text-transform: capitalize;
          // width: 40%;
          // height: 30px;
          padding: 7px;
          @include vh-center;
          margin-bottom: 16px;
          font-size: 12px;
        }

        .container-AE {
          width: 100%;
          height: calc(100% - 40px);
          overflow: auto;
          flex-wrap: wrap;
          @include v-center;
        }

        .container-AE .attribute-AE {
          cursor: pointer;
          border-radius: 7px;
          background-color: #fff;
          @include vh-center;
          color: #fff;
          font-weight: 600;
          width: 135px;
          height: 35px;
          margin-right: 5px;
          margin-bottom: 10px;
          font-size: 12px;
        }
      }

      .ae-num {
        text-transform: capitalize;
        color: $color-primary;
        font-size: 16px;
        font-family: $font-bold;
      }
    }
  }

  .input-elmt {
    width: 100%;
    font-family: $font-regular;
    font-size: 13px;
    color: #000;
    margin-right: 5px;
  }

  .input-elmt-all {
    width: 50%;
    font-family: $font-regular;
    font-size: 13px;
    color: #000;
    margin-right: 5px;
  }

  .filter-elt select {
    width: 60% !important;
    margin-right: 5px !important;
  }

  nb-form-field .input-elmt::placeholder {
    font-family: $font-regular;
    font-size: 13px;
    color: #000;
  }

  .legend {
    width: 50%;
    padding: 0 15px;
    margin-top: 10px;
    @include v-center;

    .content {
      width: 155px;
      @include v-center;

      .bullet {
        height: 15px;
        width: 15px;
        margin-right: 5px;
        border-radius: 50%;
      }

      .text {
        width: calc(100% - 20px);
        font-family: $font-regular;
        font-size: 11px;
      }
    }
  }
}

.cdk-drag-preview {
  box-sizing: border-box;
  border-radius: 4px;
  box-shadow:
    0 5px 5px -3px rgba(0, 0, 0, 0.2),
    0 8px 10px 1px rgba(0, 0, 0, 0.14),
    0 3px 14px 2px rgba(0, 0, 0, 0.12);
}

.cdk-drag-animating {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

.ae-container.cdk-drop-list-dragging .ae:not(.cdk-drag-placeholder) {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

.example-custom-placeholder {
  background: #ccc;
  border: dotted 3px #999;
  min-height: 60px;
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

@media screen and (max-width: 1500px) {
  .removal-management-container {
    height: calc(100vh - 100px);
  }

  .removal-management-container .removals-container {
    height: calc(100% - 80px);
  }

  .removal-management-container .removals-container .carrier {
    width: calc(100% - 450px);
  }

  .removal-management-container .removals-container .carrier .carrier-container .carrier-AE .carrier-name {
    font-size: 11px;
    width: 30%;
    height: 30px;
  }

  .removal-management-container
    .removals-container
    .carrier
    .carrier-container
    .carrier-AE
    .container-AE
    .attribute-AE {
    width: 100px;
    height: 30px;
    font-size: 12px;
  }
}
