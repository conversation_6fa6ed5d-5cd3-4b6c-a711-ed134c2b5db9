<div class="tile-sales-container" style="width: 90em;">
  <nb-card accent="success" [nbSpinner]="isLoading" nbSpinnerStatus="success" nbSpinnerMessage="Chargement des données">
    <nb-card-header class="card-header-wapper">
      <div class="card-header-contain">
        <div>
          Repartition des Bon en Rendu
        </div>
      </div>
    </nb-card-header>
    <nb-card-body class="body">
      <div class="block1">
        <div class="ae-data">
          <div class="ae-title">
            <div class="ae icon" [ngClass]="{'info':true }">
              <nb-icon icon='file-text-outline' status="info"></nb-icon>
            </div>
            <div class="ae-label">Bons Chargée
            </div>
          </div>
          <div class="ae-title ae-center">
            <div class="ae-label ae-value">{{data?.Chargee}}</div>
          </div>
        </div>
      </div>

      <div class="block2">

        <div class="ae-data">
          <div class="ae-title">
            <div class="ae icon" [ngClass]="{'info':true }">
              <nb-icon icon='file-remove-outline' status="info"></nb-icon>
            </div>
            <div class=" ae-label">Bons Créer
            </div>
          </div>
          <div class="ae-title ae-center">
            <div class="ae-label ae-value">{{data?.Creer}}</div>
          </div>
        </div>
        <div class="ae-data">
          <div class="ae-title">
            <div class="ae icon" [ngClass]="{'success':true }">
              <nb-icon icon='checkmark-circle-2-outline' status="success"></nb-icon>
            </div>
            <div class="ae-label">Bons
              Créer et approuvés
            </div>
          </div>
          <div class="ae-title ae-center">
            <div class="ae-label ae-value">{{data?.Creeretapprouvee}}</div>
          </div>
        </div>
      </div>

      <div class="block2">
        <div class="ae-data">
          <div class="ae-title">
            <div class="ae icon" [ngClass]="{'danger':true }">
              <nb-icon icon='close-circle-outline' status="danger"></nb-icon>
            </div>
            <div class="ae-label">Bons
              En usine
            </div>
          </div>
          <div class="ae-title ae-center">
            <div class="ae-label ae-value">{{data?.Enusine}}</div>
          </div>
        </div>
        <div class="ae-data">
          <div class="ae-title">
            <div class="ae icon" [ngClass]="{'warning':true }">
              <nb-icon icon='file-text-outline' status="warning"></nb-icon>
            </div>
            <div class="ae-label">Bons Annulés
            </div>
          </div>
          <div class="ae-title ae-center">
            <div class="ae-label ae-value">{{data?.Annulee}}</div>
          </div>
        </div>

        <!-- <div class="ae-data">
          <div class="ae-title">
            <div class="ae icon" [ngClass]="{'info':true }">
              <nb-icon icon='shopping-cart-outline' status="info"></nb-icon>
            </div>
            <div class="ae-label">Bons en Chargement partiel
            </div>
          </div>
          <div class="ae-title ae-center">
            <div class="ae-label ae-value">{{data?.Chargementpartiel}}</div>
          </div>
        </div> -->
      </div>
      <div class="block2">

       
        <!-- <div class="ae-data">
          <div class="ae-title">
            <div class="ae icon" [ngClass]="{'info':true }">
              <nb-icon icon='file-text-outline' status="info"></nb-icon>
            </div>
            <div class="ae-label">Bons en Transit
            </div>
          </div>
          <div class="ae-title ae-center">
            <div class="ae-label ae-value">{{data?.Transit}}</div>
          </div>
        </div> -->

      </div>

    </nb-card-body>
  </nb-card>
</div>