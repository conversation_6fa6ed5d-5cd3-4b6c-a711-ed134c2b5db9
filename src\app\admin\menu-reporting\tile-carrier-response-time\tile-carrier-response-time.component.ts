import { Component, OnInit } from '@angular/core';
import { MenuReportingService } from '../menu-reporting.service';

@Component({
  selector: 'clw-tile-carrier-response-time',
  templateUrl: './tile-carrier-response-time.component.html',
  styles: [
  ]
})
export class TileCarrierResponseTimeComponent implements OnInit {

  isLoading: boolean;
  reponseTimedata: any;

  constructor(
    private reportingSrv: MenuReportingService

  ) { }

  async ngOnInit() {
    await this.refresh();
  }

  async refresh() {
    this.isLoading = true;
    try {
      this.reponseTimedata = await this.reportingSrv.getAverageResponseTimePerCarrier();
      this.isLoading = false;
    } catch (error) {
      console.log(error);
      this.isLoading = false;
    }
  }

  millisecondsToTime(ms: number): string {
    const days = Math.floor(ms / (1000 * 60 * 60 * 24));
    const hours = Math.floor((ms % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((ms % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((ms % (1000 * 60)) / 1000);

    const daysStr = days.toString().padStart(2, '0');
    const hoursStr = hours.toString().padStart(2, '0');
    const minutesStr = minutes.toString().padStart(2, '0');
    const secondsStr = seconds.toString().padStart(2, '0');

    return `${daysStr} jour(s) ${hoursStr}:${minutesStr}:${secondsStr}`;
  }

}
