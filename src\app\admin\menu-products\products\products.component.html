<div class="common-form-container products-container">
  <div class="header">
    <nb-icon icon="undo-outline" [options]="{ animation: { type: 'zoom' } }" class="remove-icon" status="primary"
      (click)='goTo()'>
    </nb-icon>
    <h1 class="title">Gestion des produits</h1>
  </div>

  <div class="filter-container">
    <div class="left-block">
      <div class="filter-label">Filtrer par</div>
      <div class="filter-elt">
        <div class="filter-elt">
          <input  nbInput status='primary' fieldSize="small" [(ngModel)]="selectedProductLabel"
              (ngModelChange)="onModelStoreChange($event)" fullWidth type="text" placeholder="Libelé du produit"
              [nbAutocomplete]="autoComplete1" [nbTooltip]="selectedProductLabel || 'Rechercher'"
              nbTooltipPlacement="bottom" />
          <nb-autocomplete #autoComplete1 (selectedChange)="onSelectionStoreChange($event)">
              <nb-option *ngFor="let option of filteredProduct$ | async" [value]="option">{{option}}
              </nb-option>
          </nb-autocomplete>
      </div>
      </div>

    </div>
    <div class="btn-contain">
      <button nbButton outline status="success" size="small" class="search-btn filter-btn" (click)='search()'>
        <nb-icon icon="flip-2-outline"></nb-icon>
        Réinitialiser
    </button>
      <button nbButton status="success" class="search-btn" size="small" (click)='openAddModalProduct(addDialog)'>
        <nb-icon icon="plus-square-outline"></nb-icon>
        Ajouter un produit
      </button>
    </div>
  </div>

  <div class="cards">


    <nb-card [nbSpinner]="isLoading" nbSpinnerStatus="primary">
      <nb-card-body>
        <div class="card-container">
          <div class="product-side">
            <div class="product" *ngFor='let product of products' (click)='selectProduct(detailDialog, product)'>
              <img [src]="product.img" alt="">
              <label>{{product?.label}} ({{product?.normLabel}})</label>
            </div>
            <div class="product" (click)='openAddModalProduct(addDialog)'>
              <nb-icon icon='plus-circle-outline' status='primary'></nb-icon>
              <label>Ajouter un produit</label>
            </div>
          </div>
        </div>
      </nb-card-body>
    </nb-card>

  </div>



  <ng-template #detailDialog let-data let-ref="dialogRef" class="dialogRef">
    <div class="detail-product-container">
      <nb-flip-card [flipped]='flipped' [showToggleButton]='false'>
        <nb-card-front>
          <nb-card>
            <nb-card-header class="form--header">Détail du produit
              <div class="action-icons">
                <button nbTooltip="Editer" nbTooltipPlacement="top" status="basic" (click)='editModal()'>
                  <nb-icon icon="edit-2-outline" status="warning" [options]="{ animation: { type: 'zoom' } }"></nb-icon>
                </button>
                <button nbTooltip="Supprimer" nbTooltipPlacement="top" status="basic" (click)='isDeleteModal()'>
                  <nb-icon icon="trash-2-outline" status="danger" [options]="{ animation: { type: 'zoom' } }"></nb-icon>
                </button>
              </div>
            </nb-card-header>
            <nb-card-body>
              <div class="detail-container">
                <div class="image">
                  <img [src]="selectedProduct.img"  alt="">
                </div>

                <div class="row">
                  <div class="title">Nom du produit</div>
                  <div class="value">{{selectedProduct?.label}}</div>
                </div>
                <div class="row">
                  <div class="title">Référence</div>
                  <div class="value">{{selectedProduct?.normLabel}}</div>
                </div>
                <div class="row">
                  <div class="title">Description</div>
                  <div class="value"> {{selectedProduct?.description || 'Aucune description !!! '}} </div>
                </div>




              </div>

            </nb-card-body>
            <nb-card-footer class="form--footer">
              <button nbButton outline status="basic" ghost class="btn-border border" (click)="ref.close()" [disabled]="isLoading">
                <nb-icon icon="close-square-outline" [options]="{ animation: { type: 'zoom' } }" class="remove-icon">
                </nb-icon>
                Fermer
              </button>


            </nb-card-footer>
          </nb-card>
        </nb-card-front>
        <nb-card-back>
          <nb-card *ngIf='isDelete'>
            <nb-card-header class="form--header">Supprimer le produit</nb-card-header>
            <nb-card-body>
              <p> Etes-Vous sûr de vouloir supprimer le produit <span>{{selectedProduct.label}}
                  ({{selectedProduct.normLabel}})
                  ?</span></p>
            </nb-card-body>
            <nb-card-footer class="form--footer">
              <button nbButton outline status="basic" ghost class="btn-border border" (click)="backFlip()" [disabled]="isLoading">
                <nb-icon icon="close-square-outline" [options]="{ animation: { type: 'zoom' } }" class="remove-icon">
                </nb-icon>
                Annuler
              </button>
              <button nbButton outline status="danger" class="" (click)="deleteProduct()" [disabled]="isLoading">
                <nb-icon icon="trash-2-outline" [options]="{ animation: { type: 'zoom' } }" class="remove-icon">
                </nb-icon>
                Supprimer
              </button>
            </nb-card-footer>
          </nb-card>


          <nb-card *ngIf='isEdit' [nbSpinner]="isLoading" nbSpinnerMessage="Chargement des données"
            nbSpinnerStatus="primary" class="edit-order">
            <nb-card-header class="form--header">Modifier les informations</nb-card-header>


            <nb-card-body>
              <div class="input">
                <label for="labelTrainingType">Nom du produit</label>
                <input nbInput fullWidth size="medium" type="text" class="form-input"
                  [(ngModel)]='selectedProduct.label'>
              </div>
              <div class="input">
                <label for="otherTrainingType">Description</label>
                <textarea nbInput fullWidth [(ngModel)]='selectedProduct.description'></textarea>
              </div>
              <div class="input">
                <label for="otherTrainingType">Reference</label>
                <input nbInput fullWidth size="medium" type="text" class="form-input"
                  [(ngModel)]='selectedProduct.normLabel'>
              </div>
              <div class="input">
                <label for="file"> <button nbTooltip="Télécharger" nbTooltipPlacement="top" status="basic">
                    <nb-icon icon="download-outline" status="primary" [options]="{ animation: { type: 'zoom' } }">
                    </nb-icon>
                  </button>Importer une image </label>
                <input type="file" id="file" style="display: none;" (change)=" getFile($event)" />

              </div>
              <div class="input">
                <label>{{truncate(filename, 30)||''}}</label>

              </div>
              <div class="image">
                <img [src]="imageSrc" *ngIf="imageSrc">
              </div>
            </nb-card-body>

            <nb-card-footer class="footer">
              <button nbButton outline status="basic" ghost class="btn-border border" (click)="backFlip()"
                [disabled]="isLoading">
                <nb-icon icon="close-square-outline" [options]="{ animation: { type: 'zoom' } }" class="remove-icon">
                </nb-icon>
                Annuler
              </button>
              <button nbButton outline status="success" class="btn-contain" [disabled]="isLoading"
                (click)='editProduct()'>
                <nb-icon icon="save-outline" [options]="{ animation: { type: 'zoom' } }" class="remove-icon">
                </nb-icon>
                Enregistrer
              </button>
            </nb-card-footer>
          </nb-card>
        </nb-card-back>
      </nb-flip-card>
    </div>

  </ng-template>


  <ng-template #addDialog let-data let-ref="dialogRef" class="dialogRef">
    <div class="add-product-container">
      <nb-card [nbSpinner]="isLoading" nbSpinnerMessage="Chargement des données" nbSpinnerStatus="primary"
        class="edit-order">
        <nb-card-header class="form--header">Ajouter les informations</nb-card-header>

        <nb-card-body>
          <div class="input">
            <label for="labelTrainingType">Nom du produit</label>
            <input nbInput fullWidth size="medium" type="text" class="form-input" [(ngModel)]='selectedProduct.label'>
          </div>
          <div class="input">
            <label for="otherTrainingType">Description</label>
            <textarea nbInput fullWidth [(ngModel)]='selectedProduct.description'></textarea>
          </div>
          <div class="input">
            <label for="otherTrainingType">Reference</label>
            <input nbInput fullWidth size="medium" type="text" class="form-input"
              [(ngModel)]='selectedProduct.normLabel'>
          </div>
          <div class="input">
            <label for="file"> <button nbTooltip="Télécharger" nbTooltipPlacement="top" status="basic">
                <nb-icon icon="download-outline" status="primary" [options]="{ animation: { type: 'zoom' } }"></nb-icon>
              </button>Importer une image </label>
            <input type="file" id="file" style="display: none;" (change)=" getFile($event)" />

          </div>
          <div class="input">
            <label>{{truncate(filename, 30)||''}}</label>

          </div>

          <div class="image">
            <img [src]="imageSrc" *ngIf="imageSrc">
          </div>
        </nb-card-body>

        <nb-card-footer class="footer">
          <button nbButton outline status="basic" ghost class="btn-border border" (click)="ref.close()"
            [disabled]="isLoading">
            <nb-icon icon="close-square-outline" [options]="{ animation: { type: 'zoom' } }" class="remove-icon">
            </nb-icon>
            Annuler
          </button>
          <button nbButton outline status="success" class="btn-contain" [disabled]="isLoading" (click)='addProduct()'>
            <nb-icon icon="save-outline" [options]="{ animation: { type: 'zoom' } }" class="remove-icon">
            </nb-icon>
            Enregistrer
          </button>
        </nb-card-footer>
      </nb-card>

    </div>

  </ng-template>