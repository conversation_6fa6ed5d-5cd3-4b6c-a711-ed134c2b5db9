# Documentation du Module : Autorisations d'Enlèvement (AE) - Composant Web

## Vue d'ensemble

Le composant Web des Autorisations d'Enlèvement (AE) fournit une interface utilisateur pour gérer les autorisations d'enlèvement dans le système Cadyst Logistique. L'interface web est conçue pour les utilisateurs administratifs qui doivent gérer, attribuer et suivre le statut des autorisations tout au long de leur cycle de vie.

Remarque : D'après notre exploration, nous avons identifié que la fonctionnalité AE est davantage axée sur les composants API et mobiles, avec des implémentations spécifiques au web limitées. Cette documentation se concentre sur les aspects disponibles du composant web, bien qu'ils puissent être moins étendus que ceux de l'application mobile.

## Fonctionnalités principales

Le composant Web fournit des interfaces pour :

1. **Gestion des AE** : Visualiser, filtrer et gérer les autorisations d'enlèvement.
2. **Attribution des transporteurs** : Attribuer des transporteurs à des autorisations spécifiques.
3. **Suivi des statuts** : Suivre le statut des AE tout au long du processus logistique.
4. **Rapports et statistiques** : Générer des rapports sur les indicateurs de performance des AE.

## Composants de l'interface utilisateur

L'interface web comprend les sections principales suivantes :

### Tableau de bord
- Affiche les statistiques des AE par statut.
- Montre les attributions en attente.
- Met en évidence les problèmes critiques nécessitant une attention.

### Vue liste des AE
- Liste filtrable de toutes les autorisations.
- Indicateurs de statut avec code couleur.
- Capacités de tri et de recherche.
- Pagination pour les grands ensembles de données.

### Vue détaillée des AE
- Informations complètes sur une AE spécifique.
- Historique des statuts et horodatages.
- Informations sur le transporteur associé.
- Résultats des inspections, si disponibles.
- Informations sur l'attribution des quais.

### Interface d'attribution
- Outil pour attribuer des transporteurs aux AE.
- Fonctionnalité de sélection des transporteurs.
- Planification des dates et heures.

### Interface de rapports
- Outils de rapport basés sur des filtres.
- Capacités d'exportation.
- Vues d'analyse statistique.

## Flux de données

1. **Récupération des données** :
   - Les données des AE sont récupérées depuis les points de terminaison API.
   - Les données sont traitées et formatées pour l'affichage.
   - Divers filtres et requêtes sont appliqués en fonction des sélections de l'utilisateur.

2. **Actions utilisateur** :
   - Les changements de statut sont envoyés à l'API.
   - Les attributions sont traitées via des appels API.
   - Les filtres sont gérés par la construction de paramètres de requête.

3. **Mises à jour en temps réel** :
   - Les changements de statut des AE peuvent être reflétés en temps réel.
   - Des notifications apparaissent pour les changements de statut importants.

## Points d'intégration

Le composant web s'intègre avec :

1. **Backend API** :
   - Toutes les données sont récupérées et mises à jour via des appels API.
   - Les points de terminaison RESTful gèrent la logique métier.

2. **Authentification utilisateur** :
   - Les permissions de sécurité contrôlent l'accès aux fonctionnalités de gestion des AE.
   - L'accès basé sur les rôles détermine les actions disponibles.

3. **Système de notifications** :
   - Les notifications par e-mail peuvent être déclenchées pour les changements de statut.
   - Des alertes système apparaissent pour les problèmes critiques.

## Gestion des statuts

L'interface web gère les statuts suivants des AE :

```typescript
enum StatusRemovals {
    NOT_ASSIGNED = 100,
    ASSIGNED = 200,
    WAITING_VALIDATION = 300,
    QUEUED = 400,
    ON_QUAY = 450,
    LOADED = 500,
    BROKEN_DOWN = 600,
    NEW_PARKING = 700,
    REJECTED = 800
}