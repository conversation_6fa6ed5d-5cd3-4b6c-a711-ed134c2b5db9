.users-list-container {
  margin-left: 2%;
  margin-right: 6%;
  margin-top: 2%;

  .label-users-list,
  .right-area {
    display: flex;
    align-items: center;
    h6 {
      margin-left: 10px;
      font-family: $font-regular;
    }
  }
  .empty-input {
    padding: 4px;
  }
  .right-area {
    justify-content: space-between;
    .search-btn{
      margin-left: 10px;
     }
  }

  .block{
    background: none;
    border: none;
    min-height: 50px;
  }

  .menus-block {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    margin-top: 15px;
    .menu-elt {
      border-radius: 15px;
      height: 160px;
      width: 12%;
      display: flex;
      flex-direction: column;
      margin-bottom: 22px;
      margin-right: 24px;
      cursor: pointer;
      transition: all 0.2s ease-in-out;
      outline: none;
      &:hover {
        transform: scale(1.1);
      }
      align-items: flex-start;
      padding-top: 20px;
      .user-image {
        height: 120px;
        width: 120px;
        background-color: #bbb;
        border-radius: 50%;
        display: inline-block;
        // height: 80%;
        position: relative;
        .small-img {
          height: 125px;
          width: 125px;
        }
        .title-block {
          font-family: $font-regular;
          font-weight: bold;
          font-size: 18px;
          position: absolute;
          left: 54px;
          top: -16%;
          text-transform: uppercase;
        }
      }
      .text-block {
        height: 10%;
        text-align: justify;
        font-family: $font-regular;
        color: #ea7d1e;
        padding-top: 15px;
        font-size: 20px;
      }
    }
    .not-found-block {
      height: 90%;
      width: 90%;
      .image-block {
        @include illustration-mixin;
        img{
          width: 140px;
        }
      }

    }
  }
}

.details-user-container {
  border: transparent !important;
  width: 37vw !important;

  .remove-padding {
    padding: 10px 0 !important;
  }
  .user-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 45px;

    .user-image {
      height: 125px;
      width: 125px;
      background-color: #bbb;
      border-radius: 50%;
      display: inline-block;
    }

    .user-profile {
      flex: 0 0 57%;
      .user-name {
        font-size: 22px !important;
        text-align: justify;
        font-family: $font-bold !important;
      }
      .user-function {
        font-size: 16px !important;
        text-align: justify;
        font-family: $font-regular !important;
      }

      .icon-button {
        width: 0;
        height: 0;
      }
    }
  }
  .overflow {
    overflow-y: auto !important;
    padding: 0 !important;
  }
  .form-group {
    padding: 8px 0;
    @include vh-between;
    .attribute-block {
      display: flex;
      align-items: center;
      flex: 0 0 35%;
      .label {
        margin-left: 5px;
        font-family: $font-bold;
        font-size: 14px;
        color: black;
      }
      .icon-btn {
        background: #f5f5f5;
        height: 30px;
        width: 30px;
        border-radius: 50%;
        display: inline-block;
        color: $color-primary;
        padding: 5px;
      }
    }
    .input-width {
      width: 16rem !important;
      border-right: transparent !important;
      border-top: transparent !important;
      border-left: transparent !important;
      background: white !important;
      font-family: $font-regular;
      font-size: 13px;
    }
    .text {
      display: flex;
      align-items: justify;
      flex: 0 0 48%;
      font-family: $font-regular;
      font-size: 13px;
      color: grey;
    }
  }

  .btn-actions {
    display: flex;
    flex-direction: column;

    button{
      margin-bottom: 1em;
    }
    // &:first-child {
    //   margin-bottom: 1em;
    // }
  }
  .name-container {
    display: flex;
    flex-direction: column;
  }
  .form--footer {
    display: flex;
    justify-content: justify;
  }

  .btn-border {
    border-radius: 5px !important;
    padding: 7px !important;
  }
  .add-title {
    font-size: 16px;
    font-family: $font-bold;
  }
  .insert-border {
    border-bottom: 1px solid #e4e9f2;
  }
  .acces-rigth {
    margin-bottom: 11px;
    padding-bottom: 11px;
    @include vh-between;
    .rigth-label {
      font-family: $font-regular;
      font-size: 14px;
    }
  }
  .label-title {
    margin-bottom: 20px;
    font-size: 16px;
    font-family: $font-bold;
  }

  .container-privilege{
    max-height:30em;
    overflow-x: scroll !important;
  }
}
.rm-padding{
  padding: 0px !important;
}
.rm-width{
  // width: 34vw !important;
  border-radius: 5px!important;

}

.card-container {
  .flipcard-body {
    width: 37vw;
    justify-content: center;
    align-items: center;
    display: flex;
  }

}
