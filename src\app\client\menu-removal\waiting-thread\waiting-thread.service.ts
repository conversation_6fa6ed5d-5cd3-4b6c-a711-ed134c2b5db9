import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BaseUrlService } from 'src/app/shared/services/base-url.service';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root'
})
export class WaitingThreadService {
  BASE_URL: string;


  constructor(
    private http: HttpClient,
    private baseUrlSrv: BaseUrlService,
  ) {
    this.BASE_URL = `${this.baseUrlSrv.getOrigin()}${environment.basePath}`;

  }

  generateQueryParams(param: any): any {
    let params = new HttpParams();

    const { offset, limit, type, label } = param;

    if (offset) {
      params = params.append('offset', `${offset}`);
    }
    if (limit) {
      params = params.append('limit', `${limit}`);
    }
    if (type) {
      params = params.append('type', `${type}`);
    }
    if (label) {
      params = params.append('label', `${label}`);
    }
    return params;
  }


}
