$bg-color-primary: #0B305C;
$bg-color-card: #fffbfa;
$color-primary: #0B305C;
$color-secondary: #ffffff;
$color-third: #9e9e9e;
$color-fourth: #419CFB;
$fz-primary: 14px;
$fz-small: 10px;
$fz-medium: 12px;
$fz-h1: 34.544px;
$fz-h2: 22px;
$fz-h3: 20px;
$fw-primary: 400;
$fw-middle: 600;
$fw-bold: 700;
$ls-primary: 0.6px;
$ls-secondary: 0.25px;
$ls-h1: 0.25px;

$color-accent: #141315;
$color-muted: #4C4A51;
$color-limit: #0B305C;
$color-green: green;
$color-circle: #67B7A2;
//fonts-variables
$font-regular: "Metropolis-Regular";
$font-medium: "Metropolis-Medium";
$font-demibold: "Lato-Regular";
$font-bold: "Metropolis-Bold";
$font-black: "Metropolis-Black";

$font-mont-regular: "Mont";
$font-mont-bold: "Mont-Bold";

// Mixins
@mixin shadow {
    box-shadow: -6px 14px 20px 9px rgba(53, 54, 54, 0.2);
    -webkit-box-shadow: -6px 14px 20px 9px rgba(53, 54, 54, 0.2);
}

@mixin v-center {
    display: flex;
    align-items: center;
}

@mixin v-baseline {
    display: flex;
    align-items: baseline;
}

@mixin vh-center {
    display: flex;
    align-items: center;
    justify-content: center;
}

@mixin h-center {
    display: flex;
    justify-content: center;
}

@mixin vh-end {
    display: flex;
    align-items: center;
    justify-content: flex-end;
}

@mixin vh-between {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

@mixin h-end {
    margin-left: auto;
    margin-right: auto;
}

@mixin appearance($value: none) {
    -webkit-appearance: $value;
    -moz-appearance: $value;
    -ms-appearance: $value;
    -o-appearance: $value;
    appearance: $value;
}

@mixin rotate-180-left {
    -webkit-transform: scale(-1, 1);
    -moz-transform: scale(-1, 1);
    -ms-transform: scale(-1, 1);
    -o-transform: scale(-1, 1);
    transform: scale(-1, 1);
}

@mixin scale-effect {
    transition: all 0.2s ease-in-out 0.1s !important;
    transform: scale(0.9);
    transition-duration: 0.15s;
    transition-property: background-color, border-color, box-shadow, color;
}

@mixin container {
    width: 89%;
    margin-left: auto;
    margin-right: auto;

    @media (max-width: 1080px) {
        width: 90%;
    }
}

@mixin container-page {
    @include container;

}

@mixin illustration-mixin {
    height: 100%;
    width: 100%;
    flex-direction: column;
    display: flex;
    justify-content: center;
    align-items: center;
}