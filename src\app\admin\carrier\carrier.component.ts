import { Component, OnInit, Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'status'
})
export class OrderStatusPipe implements PipeTransform {

  transform(value: number, isColor: boolean): string {
    const statuses = {100: 'initiée',200: 'confirmée', 300: 'echec', 500: 'annulée'};
    const colors = {100: 'basic',200: 'primary', 300: 'danger', 500: 'danger'};

    if(isColor){ return colors[value];}
     return statuses[value];
  }

}
@Component({
  selector: 'clw-carrier',
  templateUrl: './carrier.component.html',
  styles: [
  ]
})
export class CarrierComponent implements OnInit {
  constructor() { }

  ngOnInit(): void {

  }

}
