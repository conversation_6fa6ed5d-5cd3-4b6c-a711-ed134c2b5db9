import * as moment from 'moment';
import { DatePipe } from '@angular/common';
import { UntypedFormControl } from '@angular/forms';
import { Component, OnInit, TemplateRef } from '@angular/core';
import { CommonService } from 'src/app/shared/services/common.service';
import { RemovalService } from '../removal-management/removal.service';
import { StatusRemovals } from 'src/app/shared/models/status-removal.enum';
import { AuthorizationRemoval } from 'src/app/shared/models/authorization-removal';
import { GetStatusRemovalsPipe } from 'src/app/shared/pipes/get-status-removals.pipe';
import { NbDialogRef, NbDialogService, NbToastRef, NbToastrService } from '@nebular/theme';
import { RenderTypeValueLibrary, StatusAESLibrary } from 'src/app/shared/models/render-type.enum';

@Component({
  selector: 'clw-loading-removal',
  templateUrl: './loading-removal.component.html',
  styles: [
  ]
})
export class LoadingRemovalComponent implements OnInit {

  formControl = new UntypedFormControl(new Date());
  dialogRef: NbDialogRef<any>;
  detailDetailRef: NbDialogRef<any>;
  dialogExport: NbDialogRef<any>;

  removals: any[];

  loading = false;

  getStatusRemovalsPipe = new GetStatusRemovalsPipe();


  statusFilter: 300;
  transporteur: any;
  produit: any;
  AE: any;
  Product: any;
  carrierLabel: any;
  removal: AuthorizationRemoval & { [key: string]: any };

  aeCarriers: any;
  code = false;
  selectBtn: any;
  datePlusthree: any;
  datePlusfive: any;
  Orderstatus: any;
  isLoading: boolean;

  offset ;
  limit = 20;
  startDate: Date;
  endDate: Date;
  endIndex = 20;
  startIndex = 1;
  total: number;
  rangeFilter: any;
  statusLabels: any = [];
  filteredStatus$: any;
  AELabels: any = [];
  filteredAE$: any;
  filteredProducts$: any;
  filteredCarrier$: any;
  filterProductLabels: any[] = [];
  ProductLabels: any[] = [];
  CarrierLabels: any[] = [];
  dataOrderTypeLabels: any[] = [];
  carriers: any;

  filterForm = {
    status: '',
    LoadNumber: '',
    ItemNumber: '',
    company: '',
    shipping: '',
    carrierLabel: '',
    startDate: new Date(new Date().getFullYear(), 0, 1),
    endDate: new Date(new Date().getFullYear(), 11, 31),
    OrderType: '',
    ShipTo: '103995',
    ShipTo1: '103994',
  }

  statusFilters: any[] = [
    { code: StatusRemovals.NOT_ASSIGNED, label: 'Non assignée' },
    { code: StatusRemovals.ASSIGNED, label: 'Validée' },
    { code: StatusRemovals.WAITING_VALIDATION, label: 'Attente Validation' },
    { code: StatusRemovals.QUEUED, label: 'En file d\'attente' },
    { code: StatusRemovals.LOADED, label: 'Chargée' },
    { code: StatusRemovals.BROKEN_DOWN, label: 'En panne' },
    { code: StatusRemovals.NEW_PARKING, label: 'Parking' },
    { code: StatusRemovals.REJECTED, label: 'Rejetée' },
  ];

  currentRemoval: AuthorizationRemoval;
  dataForFilter: any;
  dataAELabels: any = [];
  dataProducts: any = [];
  dataSoldToDescLabels: any = [];
  dataShipToDescLabels: any = [];
  dataTransporters: any = [];


  constructor(
    private datePipe: DatePipe,
    private commonSrv: CommonService,
    private dialogSvr: NbDialogService,
    private removalSvr: RemovalService,
    private toastrSvr: NbToastrService,
  ) { }

  async ngOnInit(): Promise<void> {
    await this.getAuthorization();
    await this.getDataForFilter();
  }

  FreightHandlingCode(HandlingCode: any) {
    return RenderTypeValueLibrary[HandlingCode];
  }

  async getDataForFilter() {
    try {
      this.loading = true;
      this.dataForFilter = await this.removalSvr.getFilterData({
        ...this.filterForm,
        startDate: moment(this.filterForm.startDate).format('MM-DD-YYYY'),
        endDate: moment(this.filterForm.endDate).format('MM-DD-YYYY'),
        keyForFilters: ['LoadNumber', 'ItemNumber', 'Alpha', 'ShipToDescription', 'SoldToDescription', 'OrderType']
      },);
      this.dataAELabels = this.dataForFilter?.dataLoadNumber ?? [];
      this.dataTransporters = this.dataForFilter?.dataAlpha ?? [];
      this.dataShipToDescLabels = this.dataForFilter?.dataShipToDescription ?? [];
      this.dataProducts = this.dataForFilter?.dataItemNumber ?? [];
      this.dataSoldToDescLabels = this.dataForFilter?.dataSoldToDescription ?? [];

    } catch (error) {
      return error;
    } finally { this.loading = false }
  }

  async getAuthorization(): Promise<void> {
    const options = {
      limit: this.limit,
      offset: this.offset,
      ...this.filterForm,
      startDate: moment(this.filterForm.startDate).format('MM-DD-YYYY'),
      endDate: moment(this.filterForm.endDate).format('MM-DD-YYYY'),
    }
    try {
      this.isLoading = true;
      const { count, data } = await this.removalSvr.getAuthorizationRemoval(options);
      this.removals = data;
      this.total = count;
      this.endIndex = (this.offset * this.limit < this.total) ? this.offset * this.limit : this.total;
      this.startIndex = (this.total === 0) ? this.total : ((this.offset - 1) * this.limit) + 1 || this.startIndex;

    }
    catch (error) {
      console.log(error);
      this.toastrSvr.danger('Impossible de récupérer la liste des BONS', 'Erreur connexion !');

    } finally { this.isLoading = false; }
  }

  search(): any {
    if (this.filterForm.startDate > this.filterForm.endDate)
      return this.toastrSvr.danger('la date de fin est inférieure à la date de début', 'Erreurs Données!');
    this.getAuthorization();
    this.getDataForFilter()
  }

  async reset() {
    this.filterForm = {
      status: '',
      OrderType: '',
      LoadNumber: '',
      ItemNumber: '',
      company: '',
      shipping: '',
      carrierLabel: '',
      startDate: new Date(new Date().getFullYear(), 0, 1),
      endDate: new Date(new Date().getFullYear(), 11, 31),
      // FreightHandlingCode: 'A0',
      ShipTo: '103995',
      ShipTo1: '103994'
    }

    await this.getAuthorization();
    await this.getDataForFilter();
  }

  onModelAEChange(value: any): void {
    if (value == null)
      this.dataAELabels = this.dataForFilter?.dataLoadNumber
    if (value) {
      this.loading = true;
      this.dataAELabels = this.dataForFilter?.dataLoadNumber.filter((data: number) => JSON.stringify(data).includes(value)).sort((a, b) => {
        return a.toString().toLowerCase().indexOf(value.toLowerCase()) - b.toString().toLowerCase().indexOf(value.toLowerCase());
      });
      this.loading = false;
    }
  }

  onModelCompanyChange(value: any): void {
    if (value == null)
      this.dataSoldToDescLabels = this.dataForFilter?.dataSoldToDescription;

    if (value)
      return this.dataSoldToDescLabels = this.dataForFilter?.dataSoldToDescription.filter((data: string) =>
        data.toLowerCase().includes(value.toLowerCase())).sort((a, b) => {
          return a.toString().toLowerCase().indexOf(value.toLowerCase()) - b.toString().toLowerCase().indexOf(value.toLowerCase());
        });
  }

  onModelDeliveryPointChange(value: any): void {
    if (value == null)
      this.dataShipToDescLabels = this.dataForFilter?.dataShipToDescription;

    return this.dataShipToDescLabels = this.dataForFilter?.dataShipToDescription.filter((data: string) =>
      data.toLowerCase().includes(value.toLowerCase())).sort((a, b) => {
        return a.toString().toLowerCase().indexOf(value.toLowerCase()) - b.toString().toLowerCase().indexOf(value.toLowerCase());
      });
  }

  async onSelectionAEChange(event: any): Promise<void> {
    if (event?.keyToReset) {
      console.log(event?.dataToReset);
      this.filterForm[event?.keyToReset] = '';
      this[event?.dataToReset](null);
    }
    if (event?.$event)
      await this.getAuthorization();
  }

  onModelProductsChange(value: any): void {
    if (value == null)
      this.dataProducts = this.dataForFilter?.dataItemNumber;

    if (value)
      this.dataProducts = this.dataForFilter?.dataItemNumber?.filter((data: string) => data.toLowerCase().includes(value.toLowerCase())).sort((a, b) => {
        return a.toString().toLowerCase().indexOf(value.toLowerCase()) - b.toString().toLowerCase().indexOf(value.toLowerCase());
      });
  }

  onModelCarrierChange(value: any): void {
    if (value === null)
      this.dataTransporters = this.dataForFilter?.dataAlpha;

    if (value)
      this.dataTransporters = this.dataForFilter?.dataAlpha?.filter((data: string) =>
        data.toLowerCase().includes(value?.toLowerCase())).sort((a, b) => {
          return a.toString().toLowerCase().indexOf(value?.toLowerCase()) - b.toString().toLowerCase().indexOf(value?.toLowerCase());
        });;
  }

  onModelTypeChange(value: any): void {
    if (value == null)
      this.dataOrderTypeLabels = this.dataForFilter?.dataOrderType;

    return this.dataOrderTypeLabels = this.dataForFilter?.dataOrderType?.filter((data: string) =>
      data?.toLowerCase().includes(value?.toLowerCase())).sort((a, b) => {
        return a.toString().toLowerCase().indexOf(value.toLowerCase()) - b.toString().toLowerCase().indexOf(value.toLowerCase());
      });
  }

  async onModelDateChange(event: any): Promise<any> {
    if (moment(this.filterForm.endDate).valueOf() < moment(this.filterForm.startDate).valueOf())
      return this.toastrSvr.danger('la date de fin est inférieure à la date de début', 'Erreurs Données!');
    await this.getAuthorization();
    await this.getDataForFilter();
  }

  truncate(desc: string, length) {
    if (!desc) { return; }
    return this.commonSrv.truncateString(desc, length);
  }

  async previousPage(): Promise<any> {
    this.offset -= 1;
    if (this.offset <= 0) { this.offset ; }
    this.startIndex = (this.offset - 1) * this.limit;
    if (this.startIndex === 0) { this.startIndex = 1; }
    this.endIndex = this.offset * this.limit;
    await this.getAuthorization();
  }

  async nextPage(): Promise<any> {
    if (this.endIndex >= this.total) {
      this.endIndex = this.total;
      return;
    }
    this.offset += 1;
    this.startIndex = (this.offset - 1) * this.limit;
    this.endIndex = this.offset * this.limit;
    if (this.startIndex <= 0) { this.startIndex = 1; }
    await this.getAuthorization();
  }

  isEnabled(removal?: AuthorizationRemoval) {
    const data = removal || this.currentRemoval;
    return data?.enable == false ? false : true;
  }

  async openDetailModal(dailog?: any, removal?: any): Promise<void> {
    this.removal = removal;
    try {
      this.isLoading = true;
      this.removal = await this.removalSvr.getAuthorizationRemovalById(this.removal._id);
      const parkTimes = await this.removalSvr.getParkTimes(+this.removal.LoadNumber);
      this.removal = { ...this.removal, ...parkTimes[0] };
    } catch (error) {
      console.error(error);
      this.toastrSvr.danger('Erreur lors de la récupérations des temps d\'attentes', 'Erreur!');
    } finally {
      this.isLoading = false;
    }
    this.detailDetailRef = this.dialogSvr.open(dailog, {});
  }

  millisecondsToTime(ms: number): string {
    const days = Math.floor(ms / (1000 * 60 * 60 * 24));
    const hours = Math.floor((ms % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((ms % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((ms % (1000 * 60)) / 1000);

    const daysStr = days.toString().padStart(2, '0');
    const hoursStr = hours.toString().padStart(2, '0');
    const minutesStr = minutes.toString().padStart(2, '0');
    const secondsStr = seconds.toString().padStart(2, '0');

    return `${daysStr} jour(s) ${hoursStr}:${minutesStr}:${secondsStr}`;
  }

  openEnableRemoval(dialog: TemplateRef<any>, removal: AuthorizationRemoval) {
    this.currentRemoval = removal;
    this.dialogRef = this.dialogSvr.open(dialog);
    this.dialogRef.onClose.subscribe(async (result) => {
      if (result) {
        try {
          this.isLoading = true;
          await this.removalSvr.updateAe(removal._id, { enable: !this.isEnabled(removal) });
          await this.getAuthorization();
          this.toastrSvr.success(`Ce BON a été mise à jour avec succès`, `Mise à jour réussi`);
        } catch (error) {
          this.toastrSvr.danger(`Une erreur est survenu lors de la mise a jour de ce BON`, 'Erreur de mise à jour')
        } finally {
          this.isLoading = false;
        }
      }
    })
  }

  openDialogExport(dailog: any): void {
    this.dialogExport = this.dialogSvr.open(dailog, {});
  }

  async exportAeToExcel(): Promise<void | NbToastRef> {
    if (!this.startDate || !this.endDate) {
      return this.toastrSvr.danger('Veuillez renseigner une date de début et de fin', 'Donnés incorrectes !');
    }

    if (this.startDate > this.endDate) {
      return this.toastrSvr.danger('Veuillez renseigner une date de début inférieure à celle de la date de fin', 'Donnés incorrectes !');
    }

    const options = {
      startDate: moment(this.startDate).format('MM-DD-YYYY'),
      endDate: moment(this.endDate).format('MM-DD-YYYY'),
      LoadNumber: this.AE,
      ItemNumber: this.Product,
      carrierLabel: this.carrierLabel,
      status: 200,
    }
    try {

      this.isLoading = true;
      let dataAElist = (await this.removalSvr.getAuthorizationRemoval(options)).data;
      dataAElist = dataAElist.map((elt) => {
        const data = {};
        data['N° AE'] = elt?.LoadNumber;
        data['PRODUIT'] = elt?.ItemNumber;
        data['STATUT'] = this.getStatusRemovalsPipe.transform(elt?.enable == false ? 0 : elt?.status);
        data['STATUT JDE'] = StatusAESLibrary[elt?.LoadStatus];
        data['N° COMMANDE'] = elt?.appReference;
        data['N° DE CHARGEMENT'] = elt?.ShipmentNumber;
        data['QUANTITÉ'] = elt?.TransactionQuantity;
        data['CODE_ADRESSE'] = elt?.ShipToDescription;
        data['CODE_CLIENT'] = elt?.SoldToDescription;
        data['DATE CRÉATION'] = this.datePipe.transform(elt?.dates?.created, 'dd/MM/YYYY');
        return data;
      });

      this.removalSvr.exportAEAsExcelFile(dataAElist, 'Liste_AES_PRECHARGEMENT');
      this.dialogExport.close();
    } catch (error) {
      this.toastrSvr.danger(error.error.message, 'ERREUR!');
      return error;
    } finally { this.isLoading = false; }
  }
  getConcatenatedLabels(itemNumber): string {
    return itemNumber?.map(item => `${item?.product?.label} (${item?.packaging?.label})`).join(', ') || '';
  }
}
