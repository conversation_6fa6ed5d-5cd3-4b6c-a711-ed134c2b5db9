import { Component, OnInit, ViewChild } from '@angular/core';
import { MenuReportingService } from '../menu-reporting/menu-reporting.service';
import { NbDialogService, NbToastRef, NbToastrService } from '@nebular/theme';
import * as moment from 'moment';
import { TileInspectionComponent } from './tile-inspection/tile-inspection.component';
import { TileNumbersComponent } from './tile-numbers/tile-numbers.component';
import { TileAesRenderComponent } from './tile-aes-render/tile-aes-render.component';
import { TileCarriersComponent } from './tile-carriers/tile-carriers.component';
import { TileCarrierResponseTimeComponent } from './tile-carrier-response-time/tile-carrier-response-time.component';
import { TileCarrierDeliveryTimeComponent } from './tile-carrier-delivery-time/tile-carrier-delivery-time.component';
import { TileChartStoreComponent } from './tile-chart-store/tile-chart-store.component';
import { TileTimesComponent } from './tile-times/tile-times.component';
import { TileChartRemovalsComponent } from './tile-chart-removals/tile-chart-removals.component';
import { TileChartProductComponent } from './tile-chart-product/tile-chart-product.component';
import { TileInspectionRatioComponent } from './tile-inspection-ratio/tile-inspection-ratio.component';
import { CommonService } from 'src/app/shared/services/common.service';

@Component({
  selector: 'clw-menu-reporting-render',
  templateUrl: './menu-reporting-render.component.html',
  styles: [
  ]
})
export class MenuReportingRenderComponent implements OnInit {


  @ViewChild(TileInspectionComponent)
  private tileInspections: TileInspectionComponent;
  @ViewChild(TileNumbersComponent)
  private tileNumbers: TileNumbersComponent;
  @ViewChild(TileCarriersComponent)
  private tileCarriers: TileCarriersComponent;
  @ViewChild(TileCarrierResponseTimeComponent)
  private tileCarriersResponseTime: TileCarrierResponseTimeComponent;
  @ViewChild(TileCarrierDeliveryTimeComponent)
  private tileCarriersDeliveryTime: TileCarrierDeliveryTimeComponent;
  @ViewChild(TileChartStoreComponent)
  private tileChartStore: TileChartStoreComponent;
  @ViewChild(TileTimesComponent)
  private tileTimes: TileTimesComponent
  @ViewChild(TileChartRemovalsComponent)
  private tileChartRemovals: TileChartRemovalsComponent;
  @ViewChild(TileChartProductComponent)
  private tileChartProduct: TileChartProductComponent;

  @ViewChild(TileAesRenderComponent)
  private tileAesPRender: TileAesRenderComponent;
  @ViewChild(TileInspectionRatioComponent)
  private tileInspectionRatio: TileInspectionRatioComponent;


  startDate: any;
  endDate: any;
  orderType: string;
  orderTypes: ['SO', 'ST'];
  total: number;
  limit = 20;
  activeFilter = [];
  isLoading: boolean;
  aeToExport: any[];
  dialogRef: any;

  constructor(private reportingSrv: MenuReportingService,
    private dialogSvr: NbDialogService,
    private toastrSvr: NbToastrService,
    private commonSrv: CommonService

  ) { }

  async ngOnInit() { }

  async filterStat(): Promise<NbToastRef | void> {
    if ((!this.startDate && this.endDate) || (this.startDate && !this.endDate)) {
      return this.toastrSvr.danger('Veuillez renseigner une date de début et de fin', 'Donnés incorrectes !');
    }

    if ((this.startDate > this.endDate) && this.startDate) {
      return this.toastrSvr.danger('Veuillez renseigner une date de début inférieure à celle de la date de fin', 'Donnés incorrectes !');
    }
    await this.updateData();
  }

  selectOrderType(event: any) {
    this.orderType = event;
  }

  async updateData() {

    if (this.startDate && this.endDate) {
      this.reportingSrv.startDate = moment(this.startDate).format('MM-DD-YYYY');
      this.reportingSrv.endDate = moment(this.endDate).format('MM-DD-YYYY');
    }
    this.reportingSrv.orderType = this.orderType;
    await this.tileInspections.refresh();
    await this.tileNumbers.refresh();
    await this.tileCarriers.refresh();
    await this.tileCarriersResponseTime.refresh();
    await this.tileCarriersDeliveryTime.refresh();
    await this.tileTimes.getData();
    await this.tileAesPRender.refresh();
    await this.tileInspectionRatio?.refresh();
    await this.tileChartRemovals.refresh();
    await this.tileChartProduct.refresh();
    await this.tileChartStore.refresh();
  }

  async refresh(): Promise<void> {
    this.startDate = this.endDate = null;
    this.orderType = null;
    delete this.reportingSrv.endDate;
    delete this.reportingSrv.startDate;
    delete this.reportingSrv.orderType;
    await this.tileInspections.refresh();
    await this.tileNumbers.refresh();
    await this.tileCarriers.refresh();
    await this.tileCarriersResponseTime.refresh();
    await this.tileCarriersDeliveryTime.refresh();
    await this.tileTimes.getData();
    await this.tileAesPRender.refresh();
    await this.tileInspectionRatio.refresh();
    await this.tileChartRemovals.refresh();
    await this.tileChartProduct.refresh();
    await this.tileChartStore.refresh();
  }

  async onModelDateChange(event: any): Promise<any> {
    if (moment(this.endDate).valueOf() < moment(this.startDate).valueOf())
      return this.toastrSvr.danger('la date de fin est inférieure à la date de début', 'Erreurs Données!');
    return this.updateData();
  }

  openExportInspection(dialog?: any) {
    this.activeFilter.push(0);
    this.dialogRef = this.openModal(dialog);
  }

  openModal(dailog: any): void {
    this.dialogRef = this.dialogSvr.open(dailog, {});
  }

  millisecondsToTime(ms: number): string {
    const days = Math.floor(ms / (1000 * 60 * 60 * 24));
    const hours = Math.floor((ms % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((ms % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((ms % (1000 * 60)) / 1000);

    const daysStr = days.toString().padStart(2, '0');
    const hoursStr = hours.toString().padStart(2, '0');
    const minutesStr = minutes.toString().padStart(2, '0');
    const secondsStr = seconds.toString().padStart(2, '0');

    return `${daysStr} jour(s) ${hoursStr}:${minutesStr}:${secondsStr}`;
  }

  async exportToExcel(): Promise<void> {
    try {
      this.isLoading = true;

      this.updateData();

      this.exportAEsPickupData();

      this.exportEffectifFluidData();

      this.exportAEPickupDettails();

      this.exportAvgTimeData();

      this.exportCombinedCarrierData();

    } catch (error) {
      console.error('Error exporting to Excel:', error);
    } finally {
      this.isLoading = false;
    }
  }

  private exportAEsPickupData() {
    const arrayData = [{
      'AE Totales': this.tileInspections.data?.nbAEs ?? 0,
      'AEs en attente': this.tileInspections.data?.nbAEsInAwait ?? 0,
      'AEs acceptées': this.tileInspections.data?.nbAccepted ?? 0,
      'AEs non acceptées': this.tileInspections.data?.nbNotAssigned ?? 0,
      'AEs non préchargées': this.tileInspections.data?.nbAEsPickup ?? 0,
      'AEs Annulées': this.tileInspections.data?.nbAEsRenderRejected ?? 0,
      'AEs en préchargées': this.tileInspections.data?.nbAEsInPreLoading ?? 0
    }];
    this.commonSrv.exportRetriveExcelFile(arrayData, 'Totaux des AES en Rendu');
  }

  private exportEffectifFluidData() {
    const arrayEffectifFluid = [{
      'Autorisations': this.tileNumbers?.data?.nbAEs ?? 0,
      'Utilisateurs': this.tileNumbers?.data?.nbUsers ?? 0,
      'Transporteurs': this.tileNumbers?.data?.nbCarriers ?? 0,
      'Camions': this.tileNumbers?.data?.nbTrucks ?? 0,
      'Chauffeurs': this.tileNumbers?.data?.nbDrivers ?? 0
    }];
    this.commonSrv.exportRetriveExcelFile(arrayEffectifFluid, 'Effectif Fluid');
  }

  private exportAvgTimeData() {
    const arrayDataAvgTime = [{
      'Inspections acceptées': this.tileTimes?.dataInspection?.nbStatusOk ?? 0,
      'Inspections rejetées': this.tileTimes?.dataInspection?.nbStatusNotOk ?? 0,
      'parking': this.millisecondsToTime(this.tileTimes?.parkTimes?.averageNewTimes ?? 0),
      'Parking Usine': this.millisecondsToTime(this.tileTimes?.parkTimes?.averageFactoryTimes ?? 0),
      'Chargement usine': this.millisecondsToTime(this.tileTimes?.parkTimes?.averageDockTimes ?? 0),
      'Total': this.millisecondsToTime(
        (this.tileTimes?.parkTimes?.averageDockTimes ?? 0) +
        (this.tileTimes?.parkTimes?.averageFactoryTimes ?? 0) +
        (this.tileTimes?.parkTimes?.averageNewTimes ?? 0)
      ),
      'En Panne': this.millisecondsToTime(this.tileTimes?.parkTimes?.averageFailureTimes ?? 0),
      'Temps de Réponse': this.millisecondsToTime(this.tileTimes?.avgResponseTime?.overallAverageTime ?? 0)
    }];
    this.commonSrv.exportRetriveExcelFile(arrayDataAvgTime, 'Nombre d\'inspections et temps moyen d\'attente en Rendu');
  }

  private exportAEPickupDettails() {
    const arrayDataAvgTime = [{
      'Bons Chargés': this.tileAesPRender?.data?.Chargee ?? 0,
      'Bons Créer': this.tileAesPRender?.data?.Creer ?? 0,
      'Bons Créer et approuvés': this.tileAesPRender?.data?.Creeretapprouvee ?? 0,
      'Bons En usine': this.tileAesPRender?.data?.Enusine ?? 0,
      'Bons en  Chargement partiel': this.tileAesPRender?.data?.Chargementpartiel ?? 0,
      'Bons Annulés': this.tileAesPRender?.data?.Annulee ?? 0,
      'Bons en Transit': this.tileAesPRender?.data?.Transit ?? 0,
    }];
    this.commonSrv.exportRetriveExcelFile(arrayDataAvgTime, 'Informations des AES en Rendu');
  }

  private exportCombinedCarrierData() {

    const combinedDataMap = new Map<string, any>();

    this.tileCarriers?.data?.forEach(elt => {
      const label = elt?.label || 'N/A';
      combinedDataMap.set(label, {
        'Transporteurs': label,
        'Nombre d\'AEs': elt?.numberElt ?? 0
      });
    });

    this.tileCarriersResponseTime?.reponseTimedata?.forEach(elt => {
      const label = elt?.label || 'N/A';
      const entry = combinedDataMap.get(label) || { 'Carrier': label };
      entry['Temps de reponse'] = this.millisecondsToTime(elt?.timeTaken || 0);
      combinedDataMap.set(label, entry);
    });

    this.tileCarriersDeliveryTime?.deliveryTimedata?.forEach(elt => {
      const label = elt?.label || 'N/A';
      const entry = combinedDataMap.get(label) || { 'Carrier': label };
      entry['Temps de livraison'] = this.millisecondsToTime(elt?.timeTaken ?? 0) || 'N/A';
      combinedDataMap.set(label, entry);
    });

    const responseTimeLabels = new Set(this.tileCarriersResponseTime?.reponseTimedata?.map(elt => elt?.label || 'N/A'));
    const deliveryTimeLabels = new Set(this.tileCarriersDeliveryTime?.deliveryTimedata?.map(elt => elt?.label || 'N/A'));

    combinedDataMap.forEach((entry, label) => {
      if (!responseTimeLabels.has(label)) {
        entry['Temps de reponse'] = 'N/A';
      }
      if (!deliveryTimeLabels.has(label)) {
        entry['Temps de livraison'] = 'N/A';
      }
    });

    const combinedArrayData = Array.from(combinedDataMap.values());

    this.commonSrv.exportRetriveExcelFile(combinedArrayData, 'Classement des Transporteurs');
  }


}
