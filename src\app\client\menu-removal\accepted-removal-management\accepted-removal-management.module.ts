import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { AcceptedRemovalManagementRoutingModule } from './accepted-removal-management-routing.module';
import { AcceptedRemovalManagementComponent } from './accepted-removal-management.component';
import {
   NbAutocompleteModule,
   NbBadgeModule, 
   NbButtonModule, 
   NbCardModule, 
   NbDatepickerModule, 
   NbDialogModule, 
   NbIconModule, 
   NbInputModule, 
   NbListModule, 
   NbSelectModule, 
   NbSpinnerModule, 
   NbTabsetModule, 
   NbTooltipModule } from '@nebular/theme';
import { SharedModule } from 'src/app/shared/shared.module';
import { FormsModule } from '@angular/forms';


@NgModule({
  declarations: [
    AcceptedRemovalManagementComponent
  ],
  imports: [
    CommonModule,
    FormsModule,
    NbTabsetModule,
    NbCardModule,
    NbTabsetModule,
    NbAutocompleteModule,
    NbIconModule,
    NbListModule,
    NbBadgeModule,
    NbDialogModule,
    NbButtonModule,
    NbSpinnerModule,
    NbTooltipModule,
    NbSelectModule,
    NbInputModule,
    SharedModule,
    NbDatepickerModule,
    AcceptedRemovalManagementRoutingModule
  ]
})
export class AcceptedRemovalManagementModule { }
