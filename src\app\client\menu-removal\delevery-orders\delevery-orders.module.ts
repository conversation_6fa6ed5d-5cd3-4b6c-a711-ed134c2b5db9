import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  NbAutocompleteModule,
  NbBadgeModule, NbButtonModule, NbCardModule, NbDatepickerModule,
  NbDialogModule, NbIconModule, NbInputModule, NbListModule, NbSelectModule, NbSpinnerModule,
  NbTabsetModule, NbToastrModule, NbToggleModule, NbTooltipModule
} from '@nebular/theme';
import { DeleveryOrdersRoutingModule } from './delevery-orders-routing.module';
import { DeleveryOrdersComponent } from './delevery-orders.component';
import { HeaderDeliveryComponent } from '../components/header-delivery/header-delivery.component';
import { StatusFilterComponent } from '../components/status-filter/status-filter.component';
import { OrderCartComponent } from '../components/order-cart/order-cart.component';
import { ImportSectionComponent } from '../components/import-section/import-section.component';
import { FormsModule } from '@angular/forms';
import { SharedModule } from 'src/app/shared/shared.module';


@NgModule({
  declarations: [
    DeleveryOrdersComponent,
    StatusFilterComponent,
    ImportSectionComponent,
    OrderCartComponent,
    HeaderDeliveryComponent
  ],
  imports: [
    CommonModule,
    FormsModule,
    SharedModule,
    NbAutocompleteModule,
    NbBadgeModule, NbButtonModule, NbCardModule, NbDatepickerModule,
    NbDialogModule, NbIconModule, NbInputModule, NbListModule, NbSelectModule, NbSpinnerModule,
    NbTabsetModule, NbToastrModule, NbToggleModule, NbTooltipModule, 

    DeleveryOrdersRoutingModule
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class DeleveryOrdersModule { }
