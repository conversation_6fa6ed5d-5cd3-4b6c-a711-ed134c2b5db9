import { User } from '../../models/user';
import { NbMenuItem } from '@nebular/theme';
import { Component, Inject, OnInit } from '@angular/core';
import { Router, NavigationEnd } from '@angular/router';
import { StorageService } from '../../services/storage.service';

@Component({
  selector: 'clw-menu',
  templateUrl: './menu.component.html',
  styles: [
  ]
})
export class MenuComponent implements OnInit {

  url: string = '';
  user: User;
  removalMenu = {};
  removalMenuInsp = {};
  adminMenu = {};
  reportingMenu = {};
  callScreenMenu = {};
  trackingMenu = {};
  inspectionHistoryMenu = {};
  items: NbMenuItem[] = [];

  ngOnInit(): void {
    this.user = this.storageSrv.getObject('user');

    // Initialiser l'URL avec l'URL actuelle
    this.url = this.router.url || '';

    this.setMenuItems();
    this.items = this.generateMenuItems();

    // Réinitialiser tous les états "selected" des menus
    this.resetAllMenuSelections();

    // Mettre à jour les menus actifs après leur génération
    setTimeout(() => {
      this.updateActiveMenus();
    }, 0);
  }

  setMenuItems() {

    // this.inspectionHistoryMenu = {
    //   title: 'Historique Insp',
    //   icon: 'list-outline',
    //   link: 'client/removal/inspections',
    //   hidden: this.user?.rigths?.canViewInspections !== true && this.user?.rigths?.isFullRigth !== true,
    //   /*  children: [
    //       {
    //         title: 'Insp matières',
    //         icon: 'car-outline',
    //         link: 'client/removal/inspections/truck-inspection',
    //       },
    //       {
    //         title: 'Rendu/Pickup',
    //         icon: 'copy-outline',
    //         link: 'client/removal/inspections',
    //       },
    //     ]*/
    // };

    this.removalMenu = {
      title: 'Gestion Enlevements',
      icon: 'clipboard-outline',
      children: [
        {
          title: 'Historique Bon',
          icon: 'file-text-outline',
          children: [
            /*  {

                title: 'Préchargement',
                icon: 'copy-outline',
                link: 'client/removal/ae-loading',
              },*/
            {
              title: 'Bon en Rendu',
              icon: 'copy-outline',
              link: 'client/removal/historic',
              selected: this.isSelected('client/removal/historic'),
              hidden: this.user?.rigths?.canViewHistory !== true && this.user?.rigths?.isFullRigth !== true,
            },
            {
              title: 'Bon en Pick up',
              icon: 'copy-outline',
              link: 'client/removal/historic/history-puck-up',
              selected: this.isSelected('client/removal/historic/history-puck-up'),
              hidden: this.user?.rigths?.canViewHistory !== true && this.user?.rigths?.isFullRigth !== true,
            },
          ],
          // link: 'client/removal/historic',
        },
        {
          title: 'Attribution Bon',
          icon: 'copy-outline',
          link: 'client/removal/ae-allocation',
          selected: this.isSelected('client/removal/ae-allocation'),
          hidden: this.user?.rigths?.canAllocate !== true && this.user?.rigths?.isFullRigth !== true,
        },
        {
          title: 'Transport Validée',
          icon: 'car-outline',
          link: 'client/removal/ae-accepted',
          selected: this.isSelected('client/removal/ae-accepted'),
          hidden: this.user?.rigths?.canViewTransports !== true && this.user?.rigths?.isFullRigth !== true,
        },
        {
          icon: 'car-outline',
          title: 'Camions chargée',
          link: 'client/loaded-trucks',
          selected: this.isSelected('client/loaded-trucks'),
          hidden: this.user?.rigths?.canViewLoadedTrucks !== true && this.user?.rigths?.isFullRigth !== true,
        },
        {
          title: 'File d\'attente',
          icon: 'list-outline',
          link: 'client/removal/waiting-thread',
          selected: this.isSelected('client/removal/waiting-thread'),
          hidden: this.user?.rigths?.canViewQueue !== true && this.user?.rigths?.isFullRigth !== true,
        },
        {
          title: 'Créer un enlèvement',
          icon: 'plus-square-outline',
          link: 'client/removal/new-removal',
          selected: this.isSelected('client/removal/new-removal'),
          hidden: this.user?.rigths?.canRemove !== true && this.user?.rigths?.isFullRigth !== true,
        },
        /*   {
             title: 'File d\'attente Matières',
             icon: 'list-outline',
             link: 'client/removal/material-inspections',
           },*/
        {
          title: 'Attribution Quai',
          icon: 'copy-outline',
          link: 'client/removal/dock-allocation',
          selected: this.isSelected('client/removal/dock-allocation'),
          hidden: this.user?.rigths?.canAllocateDock !== true && this.user?.rigths?.isFullRigth !== true,
        },
        {
          icon: 'car-outline',
          title: 'Camion en panne',
          link: 'client/brokenDownTrucks',
          selected: this.isSelected('client/brokenDownTrucks'),
          hidden: this.user?.rigths?.canViewBrokenTrucks !== true && this.user?.rigths?.isFullRigth !== true,
        },
        {
          title: 'Historique Insp',
          icon: 'list-outline',
          link: 'client/removal/inspections',
          selected: this.isSelected('client/removal/inspections'),
          hidden: this.user?.rigths?.canViewInspections !== true && this.user?.rigths?.isFullRigth !== true,
          /* children: [
             {
               title: 'Insp matières',
               icon: 'car-outline',
               link: 'client/removal/inspections/truck-inspection',
             },
             {
               title: 'Rendu/Pickup',
               icon: 'copy-outline',
               link: 'client/removal/inspections',
             },
           ]*/
        }
      ]
    };

    this.removalMenuInsp = {
      title: 'Gestion Enlevements',
      icon: 'clipboard-outline',
      hidden: this.user?.rigths?.canManageRemovals !== true && this.user?.rigths?.isFullRigth !== true,
      children: [
        {
          title: 'Historique Bon',
          icon: 'file-text-outline',
          children: [
            {
              title: 'Bon en Rendu',
              icon: 'copy-outline',
              link: 'client/removal/historic',
              selected: this.isSelected('client/removal/historic'),
              hidden: this.user?.rigths?.canViewHistory !== true && this.user?.rigths?.isFullRigth !== true,
            },
            {
              title: 'Bon en Pick up',
              icon: 'copy-outline',
              link: 'client/removal/historic/history-puck-up',
              selected: this.isSelected('client/removal/historic/history-puck-up'),
              hidden: this.user?.rigths?.canViewHistory !== true && this.user?.rigths?.isFullRigth !== true,
            },
          ],
          // link: 'client/removal/historic',
        },
        {
          title: 'File d\'attente',
          icon: 'list-outline',
          link: 'client/removal/waiting-thread',
          selected: this.isSelected('client/removal/waiting-thread'),
          hidden: this.user?.rigths?.canViewQueue !== true && this.user?.rigths?.isFullRigth !== true,
        },
        /* {
           title: 'File d\'attente matières',
           icon: 'car-outline',
           link: 'client/removal/material-inspections',
         },*/
        {
          icon: 'car-outline',
          title: 'Camion en panne',
          link: 'client/brokenDownTrucks',
          selected: this.isSelected('client/brokenDownTrucks'),
          hidden: this.user?.rigths?.canViewBrokenTrucks !== true && this.user?.rigths?.isFullRigth !== true,
        },
        {
          title: 'Historique Inspection',
          icon: 'list-outline',
          link: 'client/removal/inspections',
          selected: this.isSelected('client/removal/inspections'),
          hidden: this.user?.rigths?.canViewInspections !== true && this.user?.rigths?.isFullRigth !== true,
          /* children: [
             {
               title: 'Insp matières',
               icon: 'copy-outline',
               link: 'client/removal/inspections/truck-inspection',
             },
             {
               icon: 'copy-outline',
               title: 'Insp Rendu/Pickup',
               link: 'client/removal/inspections',
             }
           ]*/
        }
      ]
    };

    this.adminMenu = {
      title: 'Administration',
      icon: 'settings-outline',
      hidden: this.user?.rigths?.isAdmin !== true && this.user?.rigths?.isFullRigth !== true,
      children: [
        {
          icon: 'person-outline',
          title: 'Utilisateurs',
          link: '/admin/users',
          selected: this.isSelected('/admin/users'),
          hidden: this.user?.rigths?.canManageUsers !== true && this.user?.rigths?.isFullRigth !== true,
        },
        /* {
           icon: 'clipboard-outline',
           title: 'Produits',
           selected: this.isSelected('/admin/products'),
           children: [
             {
               icon: 'clipboard-outline',
               title: 'Liste produits',
               link: '/admin/products',
               selected: this.isSelected('/admin/products'),
             },

             {
               icon: 'grid-outline',
               title: 'Categories',
               link: '/admin/products/category',
             },
             {
               icon: 'shopping-bag-outline',
               title: 'Packagings',
               link: '/admin/products/packaging',
             },
           ]
         },
         {
           icon: 'info-outline',
           title: 'JDE ID',
           link: '/admin/jde',
         },*/
        {
          icon: 'home-outline',
          title: 'Points d\'enlèvement',
          link: '/admin/stores',
          selected: this.isSelected('/admin/stores'),
          hidden: this.user?.rigths?.canManageStores !== true && this.user?.rigths?.isFullRigth !== true,
        },
        {
          icon: 'car-outline',
          title: 'Transporteurs',
          link: '/admin/carrier',
          selected: this.isSelected('/admin/carriers'),
          hidden: this.user?.rigths?.canManageCarriers !== true && this.user?.rigths?.isFullRigth !== true,
        },
        {
          icon: 'done-all-outline',
          title: 'Inspection',
          link: '/admin/inspection',
          selected: this.isSelected('/admin/inspections'),
          hidden: this.user?.rigths?.canManageInspections !== true && this.user?.rigths?.isFullRigth !== true,
        },
        {
          icon: 'car-outline',
          title: 'Gestion des commandes',
          link: '/admin/orders',
          selected: this.isSelected('/admin/orders'),
          hidden: this.user?.rigths?.canManageOrders !== true && this.user?.rigths?.isFullRigth !== true,
        }
      ]
    };

    this.reportingMenu = {
      title: 'Reporting',
      icon: 'trending-up-outline',
      hidden: this.user?.rigths?.canViewReporting !== true && this.user?.rigths?.isFullRigth !== true,
      children: [
        {
          icon: 'trending-up-outline',
          title: ' Bons rendu',
          link: 'admin/reporting-render',
          selected: this.isSelected('admin/reporting-render'),
          hidden: this.user?.rigths?.canViewRenderReporting !== true && this.user?.rigths?.isFullRigth !== true,
        },
        {
          icon: 'trending-up-outline',
          title: 'Bons pickup',
          link: 'admin/reporting',
          selected: this.isSelected('admin/reporting'),
          hidden: this.user?.rigths?.canViewPickupReporting !== true && this.user?.rigths?.isFullRigth !== true,
        },
      ]
    };

    /* this.trackingMenu = {
       title: 'Tracking',
       icon: 'globe-outline',
       link: 'admin/tracking',
     };*/

    this.callScreenMenu = {
      title: 'Écran d\'appel',
      icon: 'browser-outline',
      hidden: this.user?.rigths?.canFollow !== true && this.user?.rigths?.isFullRigth !== true,
      children: [
        {
          title: 'Parking',
          icon: 'list-outline',
          link: 'client/call-screen',
          queryParams: { status: 700 },
          selected: this.isSelected('client/call-screen'),
          hidden: this.user?.rigths?.canViewParking !== true && this.user?.rigths?.isFullRigth !== true,
        },
        //{
        //  title: 'Parking usine',
        //  icon: 'list-outline',
        //  link: 'client/call-screen',
        //  queryParams: { status: 400 },
        //},
        {
          title: 'Chargement usine',
          icon: 'list-outline',
          link: 'client/call-screen',
          queryParams: { status: 450 },
          selected: this.isSelected('client/call-screen'),
          hidden: this.user?.rigths?.canViewFactoryLoading !== true && this.user?.rigths?.isFullRigth !== true,
        },
        // {
        //   title: 'Chargement matières',
        //   icon: 'list-outline',
        //   link: 'client/truck-call-screen',
        // },
      ]
    };
  }

  constructor(
    @Inject(Router) private router: Router,
    private storageSrv: StorageService
  ) {
    this.router.events.subscribe((event) => {
      if (event instanceof NavigationEnd) {
        this.url = event.url;
        // Réinitialiser tous les états "selected" des menus
        this.resetAllMenuSelections();
        // Mettre à jour les menus actifs quand l'URL change
        this.updateActiveMenus();
      }
    });
  }

  /**
   * Vérifie si un lien correspond à l'URL actuelle
   * @param link Lien à vérifier
   * @returns true si le lien correspond à l'URL actuelle
   */
  isSelected(link: string): boolean {
    // Si le lien est vide ou si l'URL n'est pas définie, retourner false
    if (!link || !this.url) return false;

    // Extraire le chemin de base de l'URL actuelle (sans les paramètres de requête)
    const currentUrlBase = this.url.split('?')[0];

    // Vérifier si le lien correspond exactement à l'URL actuelle ou à sa base
    if (this.url === link || currentUrlBase === link) return true;

    // Vérifier si l'URL actuelle commence par le lien (pour les sous-routes)
    // Mais seulement si le lien n'est pas vide et si l'URL commence par le lien suivi d'un slash ou d'un point d'interrogation
    if (link && link !== '/' && (
        this.url.startsWith(link + '/') ||
        this.url.startsWith(link + '?') ||
        currentUrlBase.startsWith(link + '/') ||
        this.url === link
    )) {
      return true;
    }

    return false;
  }

  /**
   * Réinitialise l'état "selected" de tous les menus
   */
  resetAllMenuSelections(): void {
    // Fonction récursive pour réinitialiser l'état "selected" d'un menu et de ses enfants
    const resetMenu = (menu: any) => {
      if (!menu) return;

      // Réinitialiser l'état "selected" du menu
      menu.selected = false;

      // Réinitialiser l'état "selected" des enfants si présents
      if (menu.children && Array.isArray(menu.children)) {
        menu.children.forEach((child: any) => resetMenu(child));
      }
    };

    // Réinitialiser tous les menus
    if (this.items && Array.isArray(this.items)) {
      this.items.forEach(item => resetMenu(item));
    }

    // Réinitialiser également les menus individuels
    resetMenu(this.removalMenu);
    resetMenu(this.removalMenuInsp);
    resetMenu(this.adminMenu);
    resetMenu(this.reportingMenu);
    resetMenu(this.callScreenMenu);
  }

  /**
   * Met à jour les menus actifs en fonction de l'URL actuelle
   * Ne met à jour que l'état "selected" des sous-menus, pas des menus parents
   */
  updateActiveMenus(): void {
    // Mettre à jour l'état "selected" pour tous les menus
    this.items.forEach(item => {
      // Ne pas mettre à jour l'état "selected" du menu parent
      // item.selected = this.isItemActive(item);

      // Vérifier les sous-menus si présents
      if (item.children) {
        item.children.forEach(child => {
          // Mettre à jour l'état "selected" du sous-menu
          child.selected = this.isItemActive(child);

          // Vérifier les sous-sous-menus si présents
          if (child.children) {
            child.children.forEach(grandChild => {
              // Mettre à jour l'état "selected" du sous-sous-menu
              grandChild.selected = this.isItemActive(grandChild);
            });
          }
        });
      }
    });
  }

  /**
   * Vérifie si un item de menu est actif en fonction de l'URL actuelle
   * @param item Item de menu à vérifier
   * @returns true si l'item est actif
   */
  isItemActive(item: any): boolean {
    if (!item || !this.url) return false;

    // Extraire le chemin de base de l'URL actuelle (sans les paramètres de requête)
    // const currentUrlBase = this.url.split('?')[0]; // Non utilisé ici, mais utilisé dans isSelected

    // Vérifier le lien direct
    if (item.link && this.isSelected(item.link)) {
      // Si l'item a des paramètres de requête, vérifier s'ils correspondent
      if (item.queryParams) {
        // Extraire les paramètres de requête de l'URL actuelle
        const urlParams = new URLSearchParams(this.url.split('?')[1] || '');

        // Vérifier si tous les paramètres de requête de l'item sont présents dans l'URL
        for (const key in item.queryParams) {
          if (urlParams.get(key) !== String(item.queryParams[key])) {
            return false;
          }
        }
      }

      return true;
    }

    // Vérifier les enfants si présents
    if (item.children) {
      return item.children.some((child: any) => this.isItemActive(child));
    }

    return false;
  }

  generateMenuItems(): any {
    // Toujours afficher tous les menus principaux
    // Les sous-menus seront conditionnés par les autorisations
    return [this.removalMenu, this.adminMenu, this.reportingMenu, this.callScreenMenu];
  }


}
