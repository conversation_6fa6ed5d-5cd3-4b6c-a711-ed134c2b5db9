import { Component, OnInit } from '@angular/core';
import { OrderService } from '../../menu-order/order.service';
import { Location } from '@angular/common';

import { Filter, Order, OrderStatusDelivery } from 'src/app/shared/models/order';
import { StorageService } from 'src/app/shared/services/storage.service';
import { CommonService } from 'src/app/shared/services/common.service';
import { AuthorizationRemoval } from 'src/app/shared/models/authorization-removal';
import { RemovalService } from '../removal-management/removal.service';
import { NbToastrService } from '@nebular/theme';
import { PaginationConfig } from 'src/app/shared/services/pagination.service';

@Component({
  selector: 'clw-delevery-orders',
  templateUrl: './delevery-orders.component.html',
  styles: [
  ]
})
export class DeleveryOrdersComponent implements OnInit {

  currentStatus: number | string = "all"; isLoading = false;
  orders: Order[] = [];
  currentFilter: { type: string | null, value: string } = { type: null, value: '' };
  fileterData: any = {};

  // Configuration de la pagination
  paginationConfig: PaginationConfig = {
    currentPage: 1,
    itemsPerPage: 10,
    totalItems: 0,
    maxSize: 10
  };

  constructor(private orderService: OrderService,
    public commonService: CommonService,
    private removalSvr: RemovalService,
    private location: Location,
    private toastrService: NbToastrService,
    private storageSrv: StorageService,

  ) { }

  async ngOnInit(): Promise<void> {
    await this.getAllOrders();

  }

  async getAllOrders() {
    this.isLoading = true;
    this.orders = [];

    try {
      const options = {
        ...this.fileterData,
        offset: (this.paginationConfig.currentPage - 1) * this.paginationConfig.itemsPerPage,
        limit: this.paginationConfig.itemsPerPage,
      };
      const { data, count } = await this.orderService.searchOrders(options);

      if (data && data.length > 0) {
        this.orders = data;
        this.paginationConfig = {
          ...this.paginationConfig,
          totalItems: count
        };

        // Vérification de la page courante
        const totalPages = Math.ceil(this.paginationConfig.totalItems / this.paginationConfig.itemsPerPage);
        if (this.paginationConfig.currentPage > totalPages && totalPages > 0) {
          this.paginationConfig.currentPage = totalPages;
          await this.getAllOrders(); // Recharger avec la bonne page
        }

        this.toastrService.success(
          `Succès :commandes récupérées.`,
          'Récupération réussie',
          { status: 'success', duration: 3000 }
        );
      } else {
        this.toastrService.warning(
          'Aucune commande trouvée.',
          'Attention',
          { status: 'warning', duration: 3000 }
        );
      }
    } catch (error) {
      this.toastrService.danger(
        'Une erreur est survenue lors de la récupération des commandes.',
        'Erreur',
        { status: 'danger', duration: 3000 }
      );
      console.error('Erreur lors de la récupération des commandes :', error);
    } finally {
      this.isLoading = false;
    }
  }

  async onStatusChange(status: number | string) {
    this.paginationConfig.currentPage = 1;
    if (status === 'all') {
      this.fileterData = {};
      await this.getAllOrders();
    } else {
      this.fileterData.statusDelivery = status;
      await this.getAllOrders();
    }
  }

  onRefresh(): void {
    this.fileterData = {};
    this.getAllOrders();
  }

  onExport(): void {
    // Logique d'export
    console.log('Export demandé');
  }

  goBack() {
      this.location.back()
    
  }


  async onFilter(filterData: Filter): Promise<void> {
    let hasValue = false;

    if (filterData.type === 'orderNumber') {
      this.fileterData.erpReference = filterData.value;
      hasValue = !!filterData.value;
    } else if (filterData.type === 'client') {
      this.fileterData.companyName = filterData.value;
      hasValue = !!filterData.value;
    } else if (filterData.type === 'product') {
      // Correction de l'opérateur d'assignation (=== → =)
      this.fileterData.product = filterData.value;
      hasValue = !!filterData.value;
    } else if (filterData.type === 'deliveryPoint') {
      this.fileterData.deliveryPoint = filterData.value;
      hasValue = !!filterData.value;
    }

    if (hasValue) {
      await this.getAllOrders();
    }
  }
  onPartitionComplete(partitionData: any): void {
    // const storedData = this.storageSrv.getObject('groupedOrders');
    // this.ordersGroup.push(order);
    console.log(partitionData, 'Recuperation des orders dans onPartitionComplete');



  }

  onPageChange(page: number): void {
    if (this.isLoading) return;
    this.paginationConfig.currentPage = page;
    this.getAllOrders();
  }

  onItemsPerPageChange(itemsPerPage: number): void {
    this.paginationConfig.itemsPerPage = itemsPerPage;
    this.paginationConfig.currentPage = 1;
    this.getAllOrders();
  }



  async onCreateAE() {
    this.isLoading = true;
    try {
      // Vérifier d'abord si nous avons des commandes
      if (!this.commonService.ordersGroup || this.commonService.ordersGroup.length === 0) {
        this.toastrService.warning(
          'Aucune commande disponible.',
          'Attention',
          { status: 'warning', duration: 3000 }
        );
        this.isLoading = false;
        return;
      }

      console.log('Commandes avant traitement:', JSON.stringify(this.commonService.ordersGroup));
      
      // Créer une copie profonde des commandes pour ne pas modifier les originales trop tôt
      const processedOrders = JSON.parse(JSON.stringify(this.commonService.ordersGroup));
      
      // Pour chaque commande, ne conserver que les articles avec quantityShipped > 0
      const filteredOrders = [];
      
      for (const order of processedOrders) {
        if (!order.cart || !Array.isArray(order.cart.items)) continue;
        
        // Filtrer les items pour ne garder que ceux avec quantityShipped > 0
        const filteredItems = order.cart.items.filter(item => 
          item && Number(item.quantityShipped || 0) > 0
        );
        
        if (filteredItems.length > 0) {
          // Créer une copie de la commande avec seulement les articles qui ont quantityShipped > 0
          const filteredOrder = { ...order };
          filteredOrder.cart = { ...order.cart };
          filteredOrder.cart.items = filteredItems;
          filteredOrders.push(filteredOrder);
        }
      }
      
      console.log('Commandes filtrées:', filteredOrders.length, JSON.stringify(filteredOrders));
      
      if (filteredOrders.length === 0) {
        this.toastrService.warning(
          'Aucune commande avec articles à expédier trouvée.',
          'Attention',
          { status: 'warning', duration: 3000 }
        );
        this.isLoading = false;
        return;
      }

      // Ajouter quantityShipped à quantityDelivery pour toutes les commandes filtrées
      // et dans le même temps, mettre à jour les commandes originales
      for (let i = 0; i < filteredOrders.length; i++) {
        const filteredOrder = filteredOrders[i];
        
        // Trouver la commande originale correspondante
        const originalOrderIndex = this.commonService.ordersGroup.findIndex(
          order => order.appReference === filteredOrder.appReference
        );
        
        if (originalOrderIndex === -1) continue;
        
        for (const filteredItem of filteredOrder.cart.items) {
          // Trouver l'item correspondant dans la commande originale
          const originalItem = this.commonService.ordersGroup[originalOrderIndex].cart.items.find(
            item => item && item.product && filteredItem.product && 
                  item.product._id === filteredItem.product._id &&
                  item.packaging && filteredItem.packaging &&
                  item.packaging._id === filteredItem.packaging._id
          );
          
          if (originalItem) {
            const currentDelivery = Number(originalItem.quantityDelivery || 0);
            const shipped = Number(originalItem.quantityShipped || 0);
            
            // Ajouter quantityShipped à quantityDelivery dans la commande originale
            originalItem.quantityDelivery = currentDelivery + shipped;
            
            // Mettre à jour également dans la version filtrée
            filteredItem.quantityDelivery = currentDelivery + shipped;
          }
        }
      }

      // Créer un nouvel objet orders avec uniquement les propriétés spécifiées
      const newOrders = filteredOrders.map((order) => ({
        cart: {
          ...order.cart,
          shipping: {
            label: order?.cart?.shipping?.label || '',
            address: order?.cart?.shipping?.address || '',
            deliveryDate: order?.cart?.shipping?.deliveryDate,
            location: order?.cart?.shipping?.location,
          }
        },
        company: order.company,
        appReference: order.appReference,
        erpReference: order?.erpReference,
        customerDeliveryDestination: order?.customerDeliveryDestination,
        carrier: order?.carrier,
        user: {
          firstName: order.user?.firstName,
          lastName: order.user?.lastName,
          email: order.user?.email,
          tel: order.user?.tel,
        },
      }));

      const data: AuthorizationRemoval = {
        groupedOrders: [...newOrders],
      };

      const res = await this.removalSvr.createAuthorization(data);
      if (res?.message && res?.message.includes("New authorization removal created.")) {
        // Réinitialiser quantityShipped à 0 pour tous les articles qui ont été expédiés
        for (const order of this.commonService.ordersGroup) {
          if (!order.cart || !Array.isArray(order.cart.items)) continue;
          
          for (const item of order.cart.items) {
            if (item && Number(item.quantityShipped || 0) > 0) {
              item.quantityShipped = 0;
            }
          }
        }

        this.toastrService.success(
          'L\'AE a été créée avec succès.',
          'Succès',
          { status: 'success', duration: 3000 }
        );
        await this.updateOrder();
      }

    } catch (error) {
      this.toastrService.danger(
        'Une erreur est survenue lors de la création de l\'AE:.',
        'Erreur',
        { status: 'danger', duration: 3000 }
      );
    } finally {
      this.isLoading = false;
    }
  }


  async updateOrder() {
    this.isLoading = true;

    try {
      if (!this.commonService.ordersGroup || this.commonService.ordersGroup.length === 0) {
        this.toastrService.warning(
          'Aucune commande à mettre à jour.',
          'Attention',
          { status: 'warning', duration: 3000 }
        );
        return;
      }

      const updatedOrders = this.commonService.ordersGroup.map(order => {
        if (!order?.cart?.items || order.cart.items.length === 0) {
          return order;
        }

        let allItemsComplete = true;
        let anyItemDelivered = false;

        for (const item of order.cart.items) {
          const currentDelivery = +(item.quantityDelivery || 0);
          const totalQuantity = item.quantity || 0;
          
          if (currentDelivery > 0) {
            anyItemDelivered = true;
          }
          
          // Vérifier si la livraison est complète
          if (currentDelivery < totalQuantity) {
            allItemsComplete = false;
          }
        }

        if (allItemsComplete) {
          order.statusDelivery = OrderStatusDelivery.COMPLETED; // 300
        } else if (anyItemDelivered) {
          order.statusDelivery = OrderStatusDelivery.PROCESS; // 200
        } else {
          order.statusDelivery = OrderStatusDelivery.WAITING; // 100
        }

        return order;
      });

      const updatePromises = updatedOrders.map(order =>
        this.orderService.updateOrder(order)
      );

      const results = await Promise.all(updatePromises);

      const successCount = results.filter(res => res?.message).length;

      if (successCount > 0) {
        this.toastrService.success(
          `${successCount} commande(s) mise(s) à jour avec succès.`,
          'Succès',
          { status: 'success', duration: 3000 }
        );
      } else {
        this.toastrService.warning(
          'Aucune commande n\'a été mise à jour.',
          'Attention',
          { status: 'warning', duration: 3000 }
        );
      }

      this.commonService.ordersGroup = [];
      this.getAllOrders();

    } catch (error) {
      this.toastrService.danger(
        'Une erreur est survenue lors de la mise à jour des commandes.',
        'Erreur',
        { status: 'danger', duration: 3000 }
      );
      console.error('Erreur lors de la mise à jour des commandes :', error);
    } finally {
      this.isLoading = false;
    }
  }

}
