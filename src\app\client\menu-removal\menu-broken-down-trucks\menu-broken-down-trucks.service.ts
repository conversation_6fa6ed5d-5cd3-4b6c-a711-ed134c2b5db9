import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BaseUrlService } from 'src/app/shared/services/base-url.service';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root'
})
export class MenuBrokenDownTrucksService {
  BASE_URL: string;
  constructor(
    private http: HttpClient,
    private baseUrlSrv: BaseUrlService,
  ) {
    this.BASE_URL = `${this.baseUrlSrv.getOrigin()}${environment.basePath}`;

  }


  async getAuthorizationRemoval(param: any): Promise<any> {
    let params = new HttpParams();
    if (param?.status) { params = params.append('status', `${param?.status}`) }
    if (param?.offset) { params = params.append('offset', `${param?.offset}`) }
    if (param?.limit) { params = params.append('limit', `${param?.limit}`) }
    if (param?.dateStart) { params = params.append('dateStart', `${param?.dateStart}`) }
    if (param?.immatriculation) { params = params.append('immatriculation', `${param?.immatriculation}`) }
    return await this.http.get(`${this.BASE_URL}/authorization-removal`, { params }).toPromise();
  }

  async getAlltrucks(): Promise<any> {
    return await this.http.get(`${this.BASE_URL}/trucks`, {}).toPromise();
  }

  async updateAuthRemoval(param: any,id: string,): Promise<any> {
    return await this.http.put(`${this.BASE_URL}/authorization-removal/${id}`, param).toPromise();

  }
}
