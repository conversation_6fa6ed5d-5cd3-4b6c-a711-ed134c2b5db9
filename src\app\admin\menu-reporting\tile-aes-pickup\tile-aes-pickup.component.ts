import { Component, OnInit } from '@angular/core';
import { RenderTypeValue } from 'src/app/shared/models/render-type.enum';
import { MenuReportingService } from '../menu-reporting.service';

@Component({
  selector: 'clw-tile-aes-pickup',
  templateUrl: './tile-aes-pickup.component.html',
  styles: [
  ]
})
export class TileAesPickupComponent implements OnInit {
  isLoading: boolean;
  data: any;

  constructor(
    private reportingSrv: MenuReportingService
  ) { }

  async ngOnInit() {
    this.refresh();
  }

  async refresh() {
    this.isLoading = true;
    try {
      const query = {
        FreightHandlingCode: RenderTypeValue.PICKUP
      }
      const res = await this.reportingSrv.getStatusAEs(query);
      this.data = this.normalizeLabels(res);
      console.log(this.data);
      
      
      this.isLoading = false;
    } catch (error) {
      console.log(error);
      this.isLoading = false;
    }
  }

  normalizeLabels(data): any {
    const normalizedData = {};
  
    Object.keys(data).forEach(key => {
      const normalizedKey = key
        .normalize("NFD") // Normaliser l'Unicode pour séparer les accents des lettres
        .replace(/[\u0300-\u036f]/g, '') // Supprimer les accents
        .replace(/ /g, ''); // Supprimer les espaces
  
      normalizedData[normalizedKey] = data[key];
    });
  
    return normalizedData;
  }
}
