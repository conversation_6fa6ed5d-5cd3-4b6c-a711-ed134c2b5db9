version: '3'
services:
    mongodb:
        image: 'mongo:4.4.8'
        restart: unless-stopped
        volumes:
            - ./mongo-volume:/data/db
        ports:
            - '27018:27017'
    nginx:
        image: 'londotech/cimencam-logistic-nginx:latest'
        restart: unless-stopped
        ports:
                - '3017:80'
    pdf:
        image: 'londotech/pdf-api:1.0.0'
        restart: unless-stopped
        environment:
            - NODE_ENV=staging
        entrypoint: npm run start:staging
    jde:
        image: 'londotech/jde-api:latest'
        restart: unless-stopped
        environment:
            - NODE_ENV=staging
            - JDE_BASE_URL=https://cp-webm-q.ea.holcim.net
            - EMAIL_TO=<EMAIL>, <EMAIL>
            - EMAIL_CC=<EMAIL>, <EMAIL>, <EMAIL>
        entrypoint: npm run start:staging
    api:
        image: 'londotech/cimencam-logistic-api:latest'
        restart: unless-stopped
        environment:
            - NODE_ENV=staging
            - DB_MONGO_HOST=mongodb
            - DB_MONGO_NAME=cimencam-logistic
            - JDE_API_URL=http://jde:3000
            - PDF_API_URL=http://pdf:3000
        ports:
            - '3000:3000'
        entrypoint: npm run start:staging
        depends_on:
            - mongodb
            - jde
    website:
        image: 'londotech/cimencam-logistic-website:latest'
        restart: unless-stopped
        depends_on:
            - api