import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { OrderStatusDelivery } from 'src/app/shared/models/order';
import { PaginationConfig } from 'src/app/shared/services/pagination.service';

@Component({
  selector: 'clw-status-filter',
  templateUrl: './status-filter.component.html',
  styles: [
  ]
})
export class StatusFilterComponent implements OnInit {
  @Input() activeStatus: number | string = "all";
  @Input() total: number = 0;
  @Input() paginationConfig: PaginationConfig;
  
  @Output() statusChange = new EventEmitter<number | string>();
  @Output() pageChange = new EventEmitter<number>();
  @Output() itemsPerPageChange = new EventEmitter<number>();
  
  offset = 1; 
  limit = 20;
  endIndex = 20;
  startIndex = 1;
  

  
  statuses = [
    { label: "Tous", value: "all", ariaLabel: "Afficher toutes les commandes" },
    {
      label: "En attente",
      value: OrderStatusDelivery.WAITING,
      ariaLabel: "Afficher les commandes en attente",
    },
    {
      label: "En cours",
      value: OrderStatusDelivery.PROCESS,
      ariaLabel: "Afficher les commandes en cours",
    },
    {
      label: "Terminées",
      value: OrderStatusDelivery.COMPLETED,
      ariaLabel: "Afficher les commandes terminées",
    },
  ];

  constructor() { }

  ngOnInit(): void {
    // Initialisation des indices
    this.calculateIndices();
  }
  
  // Nouvelle méthode pour calculer les indices
  private calculateIndices(): void {
    this.startIndex = (this.offset - 1) * this.limit + 1;
    this.endIndex = Math.min(this.offset * this.limit, this.total);
  }
  
  onStatusClick(status: number | string) {
    this.activeStatus = status;
    this.offset = 1; // Réinitialiser à la première page lors d'un changement de statut
    this.calculateIndices();
    this.statusChange.emit(status);
  }
  
  onPageChange(page: number): void {
    this.pageChange.emit(page);
  }
  
  onItemsPerPageChange(itemsPerPage: number): void {
    this.itemsPerPageChange.emit(itemsPerPage);
  }
  
  async previousPage(): Promise<void> {
    if (this.offset <= 1) {
      return; // Ne pas reculer si on est à la première page
    }
    
    this.offset -= 1;
    this.calculateIndices();
    this.pageChange.emit(this.offset);
  }
  
  async nextPage(): Promise<void> {
    if (this.endIndex >= this.total) {
      return; 
    }
    
    this.offset += 1;
    this.calculateIndices();
    this.pageChange.emit(this.offset);
  }
  
}  