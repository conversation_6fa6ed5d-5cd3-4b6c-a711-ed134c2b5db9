import { Component, OnInit } from '@angular/core';
import { CommonService } from '../shared/services/common.service';

@Component({
  selector: 'clw-new-home',
  templateUrl: './new-home.component.html',
  styles: [
  ]
})
export class NewHomeComponent implements OnInit {

  isShow: boolean = false
  deviceType: string = '';
  resizeListener: any;
  showAuthModal: boolean;


  constructor(public commonService: CommonService) {

  }

  ngOnInit(): void {
    this.setDeviceType();

    this.resizeListener = () => {
      this.setDeviceType();
    };
    window.addEventListener('resize', this.resizeListener);
  }

  faqItems = [
    {
      id: 1,
      question: 'Lorem ipsum dolor sit amet consectetur. Velit congue diam tellus vitae nibh hendrerit massa. Sed ipsum volutpat eget lectus feugiat. Vel et ac viverra morbi nascetur. Semper sed in sed ',
      answer: 'Lorem ipsum dolor sit amet consectetur. Velit congue diam tellus vitae nibh hendrerit massa. Sed ipsum volutpat eget lectus feugiat. Vel et ac viverra morbi nascetur. Semper sed in sed  Velit congue diam tellus vitae nibh hendrerit massa. Sed ipsum volutpat eget lectus fugiat. Vel at ac viverra morbi venenatis tempor sed in sed.',
      isOpen: false,
    },
    {
      id: 2,
      question: 'Lorem ipsum dolor sit amet consectetur. Velit congue diam tellus vitae nibh hendrerit massa. Sed ipsum volutpat eget lectus feugiat. Vel et ac viverra morbi nascetur. Semper sed in sed ',
      answer: 'Lorem ipsum dolor sit amet consectetur. Velit congue diam tellus vitae nibh hendrerit massa. Sed ipsum volutpat eget lectus feugiat. Vel et ac viverra morbi nascetur. Semper sed in sed  Velit congue diam tellus vitae nibh hendrerit massa. Sed ipsum volutpat eget lectus fugiat. Vel at ac viverra morbi venenatis tempor sed in sed.',
      isOpen: false
    },
    {
      id: 3,
      question: 'Lorem ipsum dolor sit amet consectetur. Velit congue diam tellus vitae nibh hendrerit massa. Sed ipsum volutpat eget lectus feugiat. Vel et ac viverra morbi nascetur. Semper sed in sed ',
      answer: 'Lorem ipsum dolor sit amet consectetur. Velit congue diam tellus vitae nibh hendrerit massa. Sed ipsum volutpat eget lectus feugiat. Vel et ac viverra morbi nascetur. Semper sed in sed  Velit congue diam tellus vitae nibh hendrerit massa. Sed ipsum volutpat eget lectus fugiat. Vel at ac viverra morbi venenatis tempor sed in sed.',
      isOpen: false
    },
    {
      id: 4,
      question: 'Lorem ipsum dolor sit amet consectetur. Velit congue diam tellus vitae nibh hendrerit massa. Sed ipsum volutpat eget lectus feugiat. Vel et ac viverra morbi nascetur. Semper sed in sed ',
      answer: 'Lorem ipsum dolor sit amet consectetur. Velit congue diam tellus vitae nibh hendrerit massa. Sed ipsum volutpat eget lectus feugiat. Vel et ac viverra morbi nascetur. Semper sed in sed  Velit congue diam tellus vitae nibh hendrerit massa. Sed ipsum volutpat eget lectus fugiat. Vel at ac viverra morbi venenatis tempor sed in sed.',
      isOpen: false
    },
    {
      id: 5,
      question: 'Lorem ipsum dolor sit amet consectetur. Velit congue diam tellus vitae nibh hendrerit massa. Sed ipsum volutpat eget lectus feugiat. Vel et ac viverra morbi nascetur. Semper sed in sed ',
      answer: 'Lorem ipsum dolor sit amet consectetur. Velit congue diam tellus vitae nibh hendrerit massa. Sed ipsum volutpat eget lectus feugiat. Vel et ac viverra morbi nascetur. Semper sed in sed  Velit congue diam tellus vitae nibh hendrerit massa. Sed ipsum volutpat eget lectus fugiat. Vel at ac viverra morbi venenatis tempor sed in sed.',
      isOpen: false
    },
    {
      id: 6,
      question: 'Lorem ipsum dolor sit amet consectetur. Velit congue diam tellus vitae nibh hendrerit massa. Sed ipsum volutpat eget lectus feugiat. Vel et ac viverra morbi nascetur. Semper sed in sed ',
      answer: 'Lorem ipsum dolor sit amet consectetur. Velit congue diam tellus vitae nibh hendrerit massa. Sed ipsum volutpat eget lectus feugiat. Vel et ac viverra morbi nascetur. Semper sed in sed  Velit congue diam tellus vitae nibh hendrerit massa. Sed ipsum volutpat eget lectus fugiat. Vel at ac viverra morbi venenatis tempor sed in sed.',
      isOpen: false
    },
    {
      id: 7,
      question: 'Lorem ipsum dolor sit amet consectetur. Velit congue diam tellus vitae nibh hendrerit massa. Sed ipsum volutpat eget lectus feugiat. Vel et ac viverra morbi nascetur. Semper sed in sed ',
      answer: 'Lorem ipsum dolor sit amet consectetur. Velit congue diam tellus vitae nibh hendrerit massa. Sed ipsum volutpat eget lectus feugiat. Vel et ac viverra morbi nascetur. Semper sed in sed  Velit congue diam tellus vitae nibh hendrerit massa. Sed ipsum volutpat eget lectus fugiat. Vel at ac viverra morbi venenatis tempor sed in sed.',
      isOpen: false
    },
  ];

  featuresList = [
    {
      icon: 'assets/images/icons/shipp.png',
      title: 'Gestion des enlèvements',
      description1: 'Historique des Bons en rendu',
      description2: 'Historique des Bons en PickUp',
      description3: 'Attributions des Bons',
      description4: 'File d’attente',
      description5: 'Camions en panne',
      description6: 'Attribution des quais'
    },
    {
      icon: 'assets/images/icons/shipp2.png',
      title: 'Administration',
      description1: 'Gestion des utilisateurs',
      description2: 'Gestion des transporteurs',
      description3: 'Gestion des Inspections'
    },
    {
      icon: 'assets/images/icons/shipp2.png',
      title: 'Reporting',
      description1: 'Nombre de camions',
      description2: 'Suivi des utilisateurs',
      description3: 'Classement des transporteurs',
      description4: 'Temps Moyen d’attente'
    },
    {
      icon: 'assets/images/icons/shipp.png',
      title: 'Écran d’appel',
      description1: 'Classement par arrivée des transporteurs',
      description2: 'Suivi du temps d’attente',
      description3: 'Attribution de quai'
    }
  ];

  platformBenefits = [
    {
      icon: 'assets/images/icons/rappel.png',
      title: "Réduction du temps d'attente des camions",
      description: "Avec une gestion optimisée des plannings et des flux, notre plateforme réduit considérablement les temps d'attente, améliorant ainsi la productivité et limitant les coûts opérationnels."
    },
    {
      icon: 'assets/images/icons/help-customer.png',
      title: "Amélioration de la qualité de service",
      description: "En optimisant la coordination des processus logistiques, notre solution garantit une meilleure satisfaction client en réduisant les erreurs et en augmentant l'efficacité des opérations."
    },
    {
      icon: 'assets/images/icons/award-crown.png',
      title: "Reporting consolidé de la solution",
      description: "Grâce à un tableau de bord intuitif et des rapports consolidés en temps réel, notre plateforme offre une visibilité complète sur vos opérations logistiques, facilitant une prise de décision rapide et éclairée."
    }
  ];

  toggleIcon(selectedItem) {
    this.faqItems = this.faqItems.map(item => {
      if (item.id === selectedItem.id) {
        return { ...item, isOpen: !item.isOpen };
      }
      return item;
    });
  }

  showModal() {
    this.commonService.showAuthModal = !this.commonService.showAuthModal;
  }


  ngOnDestroy() {
    if (this.resizeListener) {
      window.removeEventListener('resize', this.resizeListener);
    }
  }

  setDeviceType() {
    const width = window.innerWidth;
    this.deviceType = width <= 600 ? 'mobile' : 'desktop';
  }


}
