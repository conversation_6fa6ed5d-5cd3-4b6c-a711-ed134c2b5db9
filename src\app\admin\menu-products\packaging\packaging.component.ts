import { Location } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { NbDialogService, NbToastrService } from '@nebular/theme';
import * as moment from 'moment';
import { PackagingService } from '../packaging/packaging.service';

@Component({
  selector: 'clw-packaging',
  templateUrl: './packaging.component.html',
  styles: [
  ]
})
export class PackagingComponent implements OnInit {


  isLoading: boolean
  packagings = [];
  dialogRef: any;
  selectedPackaging: any;

  constructor(
    private dialogSvr: NbDialogService,
    private packagingSvr: PackagingService,
    private toastrSvr: NbToastrService,
    private router: Router,
    private location: Location,
  ) { }

  async ngOnInit(): Promise<void> {
    this.packagings = await this.packagingSvr.getPackagings();
  }

  openModal(dailog?: any): void {
    this.packagingSvr.currentPackaging = this.selectedPackaging;
    this.dialogRef = this.dialogSvr.open(dailog, { closeOnBackdropClick: false, autoFocus: true })
    this.dialogRef.onClose.subscribe(() => {
    });


  }
  openAddModal(dialog: any): any {
    this.resetFields();
    this.openModal(dialog);
  }

  async addPackaging(): Promise<any> {
    try {
      this.isLoading = true;
      if (this.selectedPackaging.label === '' || this.selectedPackaging.unit.baseUnit === '' || this.selectedPackaging.unit.baseUnitValue === '' || this.selectedPackaging.unit.baseUnitValue === 0 || this.selectedPackaging.unit.ratio === 0 || this.selectedPackaging.unit.ratio === '') {
        return this.toastrSvr.danger('Veuillez renseigner tout les champs', 'Donnés incorrectes');
      }
      if (this.ifPackagingExist()) {
        return this.toastrSvr.danger('Vous essayez d\'ajouter un packaging qui existe déjà', 'Donnés incorrectes');
      }
      if (this.ifPackagingExist()) {
        return this.toastrSvr.danger('Vous essayez d\'ajouter un packaging qui existe déjà', 'Donnés incorrectes');
      }
      await this.packagingSvr.addPackagings(this.selectedPackaging);
      this.dialogRef.close();
      this.packagings = await this.packagingSvr.getPackagings();
      this.toastrSvr.success('Packaging ajouté avec succès', 'Ajout réussi');
    } catch (error) {
      this.toastrSvr.danger('Nous n\'avons pas pu ajouter ce packaging', 'Echec d\'ajout')
    } finally {
      this.isLoading = false;
    }
  }

  openEditModal(dialog: any, packaging: any): any {
    this.selectedPackaging = { ...packaging };
    this.openModal(dialog);
  }

  async editPackaging(): Promise<any> {
    try {
      this.isLoading = true;
      if (this.selectedPackaging.label === '' || this.selectedPackaging.unit.baseUnit === '' || this.selectedPackaging.unit.baseUnitValue === '' || this.selectedPackaging.unit.baseUnitValue === 0 || this.selectedPackaging.unit.ratio === 0 || this.selectedPackaging.unit.ratio === '') {
        return this.toastrSvr.danger('Veuillez renseigner tout les champs', 'Donnés incorrectes');
      }
      if (this.ifPackagingExist()) {
        return this.toastrSvr.danger('Vous essayez d\'ajouter un packaging qui existe déjà', 'Donnés incorrectes');
      }
      if (this.selectedPackaging.unit.ratio <= 0 || this.selectedPackaging.unit.baseUnitValue <= 0) {
        return this.toastrSvr.danger('Vous devez ajouter des valeurs entières et non nulles', 'Donnés incorrectes');
      }
      await this.packagingSvr.editPackagings(this.selectedPackaging);
      this.dialogRef.close();
      this.packagings = await this.packagingSvr.getPackagings();
      this.toastrSvr.success('Packaging edité avec succès', 'Edition réussi');
    } catch (error) {
      this.toastrSvr.danger('Nous n\'avons pas pu editer ce packaging', 'Echec d\'edition')
    } finally {
      this.isLoading = false;
    }
  }

  openDeleteModal(dialog: any, packaging: any): any {
    this.selectedPackaging = { ...packaging };
    this.openModal(dialog);
  }

  async deletePackaging(): Promise<any> {
    try {
      this.isLoading = true;
      await this.packagingSvr.deletePackagings();
      this.dialogRef.close();
      this.packagings = await this.packagingSvr.getPackagings();
      this.toastrSvr.success('Packaging edité avec succès', 'Edition réussi');
    } catch (error) {
      this.toastrSvr.danger('Nous n\'avons pas pu editer ce packaging', 'Echec d\'edition')
    } finally {
      this.isLoading = false;
    }
  }

  resetFields() {
    this.selectedPackaging = {
      label: '',
      code: moment().valueOf(),
      unit: {
        baseUnit: '',
        baseUnitValue: 0,
        ratio: 0,

      }

    }
  }


  ifPackagingExist() {
    for (const element of this.packagings) {
      if (this.selectedPackaging.label === element.label && this.selectedPackaging?.unit?.baseUnit === element.unit?.baseUnit && this.selectedPackaging.unit?.baseUnitValue === element.unit?.baseUnitValue && this.selectedPackaging.unit.ratio === element.unit?.ratio) {
        return true;
      }
    }
    return false
  }
  goBack() {
    if (window.history.length > 1) {
      this.location.back()
    } else {
      this.router.navigate(['/home'])
    }
  }

}
