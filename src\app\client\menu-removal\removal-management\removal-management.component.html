<div class="common-form-container removal-management-container">
  <div class="header">
    <!-- <nb-icon icon="undo-outline" [options]="{ animation: { type: 'zoom' } }" class="remove-icon" status="primary"
      (click)='goTo()'>
    </nb-icon> -->
    <h1 class="title">Attribution des bons de commande</h1>
    <div class="col col-paginator">
      <clw-pagination [config]="paginationConfig" (pageChange)="onPageChange($event)"
        (itemsPerPageChange)="onItemsPerPageChange($event)">
      </clw-pagination>
    </div>
  </div>

  <div class="removals-container" cdkDropListGroup>
    <div class="removals">
      <div class="search-bar">
        <div class="left-area">
          <!-- <div class="ae-select">
          </div> -->
          <div class="filter-elts selected" style="padding-top: 11px;
">
            <input fieldSize="small" id="typeInput" [(ngModel)]="aeNumberFilter"
              (ngModelChange)="onModelLoadNumberChange($event)" nbInput fullWidth status="primary" type="text"
              placeholder="Rechercher un BON" [nbAutocomplete]="autoComplete1" />
            <nb-autocomplete #autoComplete1 (selectedChange)="onSelectionLoadNumberChange($event)">
              <nb-option (click)='refresh()'>Tous les Bons</nb-option>
              <nb-option *ngFor="let option of filteredAeNumbers$ | async" [value]="option">{{option}}</nb-option>
            </nb-autocomplete>
          </div>
          <div class="filter-elt select width">
            <input nbInput [nbDatepicker]="datePickerStart" [(ngModel)]="startDate" status="success" fieldSize="small"
              (ngModelChange)="onModelDateChange($event)" class="empty-input height width-input" placeholder="Début" />
            <nb-datepicker #datePickerStart>
            </nb-datepicker>
          </div>
          <div class="filter-elt select width">
            <input nbInput [nbDatepicker]="datepickerEnd" [(ngModel)]="endDate" status="success" fieldSize="small"
              (ngModelChange)="onModelDateChange($event)" class="empty-input height width-input" placeholder="Fin" />
            <nb-datepicker #datepickerEnd></nb-datepicker>
          </div>
          <!-- <div class="flex-col">
          </div> -->
        </div>
        <!-- <nb-form-field>
          <input fullWidth fieldSize="small" type="text" class="input-elmt" nbInput status="primary" [(ngModel)]="loadNumber"
            (ngModelChange)="onAeChange($event)" placeholder="Saisissez le N° de L'AE">
          <button nbSuffix nbButton ghost>
            <nb-icon icon="search-outline" pack="eva" status="primary"> </nb-icon>
          </button>
        </nb-form-field> -->

        <!-- <div class="select-filters"> -->
        <div class="right-area">
          <div class="filter-elt select">
            <nb-select status="primary" size="small" fullWidth placeholder="filtrer par produit"
              [(selected)]="productFilter" (selectedChange)='onProductChange($event)'>
              <nb-option fieldSize="small" (click)='refresh()'>Tous les produits</nb-option>
              <nb-option fieldSize="small" [value]="product" *ngFor="let product of productsLabels ">{{ product }}
              </nb-option>
            </nb-select>
          </div>

          <button nbButton status="basic" fieldSize="small" class="search-btn reset-btn" (click)="reset()">
            <nb-icon icon="refresh-outline" [options]="{ animation: { type: 'zoom' } }"></nb-icon>
            REINITIALISER
          </button>

        </div>
        <!-- <div class="filter-elt select">
            <nb-select status="primary" size="small" fullWidth placeholder="filtrer par client"
              [(selected)]="loadNumber" (selectedChange)='clientChange($event)'>
              <nb-option fieldSize="small" (click)='refresh()'>Tous les clients</nb-option>
              <nb-option fieldSize="small" [value]="ae" *ngFor="let ae of aeRemovals">{{ ae?.soldto?.name && ae?.soldto?.code}
              </nb-option>
            </nb-select>
          </div> -->
        <!-- </div> -->
      </div>
      <div class="ae-container" [nbSpinner]="isLoading" nbSpinnerStatus="primary" cdkDropList id="aeList"
        #aeList="cdkDropList" [cdkDropListData]="aeRemovals" (cdkDropListDropped)="moveTo($event)">
        <div class="ae" *ngFor="let removal of aeRemovals" cdkDrag>
          <div class="ae-header">
            <p class="ae-num">BON N°{{removal?.LoadNumber}}</p>
            <!-- <button nbButton ghost status="primary" size="small" (click)="downloadAePdf(removal)">
              <nb-icon icon="download-outline"></nb-icon>
            </button> -->
          </div>
          <div class="ae-content" (click)="openEditDialog(editDialog,removal, true)">
            <div class="row">
              <div class="infors">
                <div class="key">Status:</div>
                <div class="value color" [ngStyle]="getBackgroungColor(removal?.status)">{{removal?.status | status: false}}</div>
              </div>
              <div class="infors">
                <div class="key">Date création:</div>
                <div class="value">{{removal?.dates?.created || removal?.dates?.updated | date : 'shortDate': 'fr'}}</div>
              </div>
            </div>
            <div class="row">
              <div class="infors">
                <div class="key">Quantité:</div>
                <div class="value">{{getQuantityDelivery(removal?.groupedOrders)}}T</div>
              </div>
              <div class="infors">
                <div class="key">Détails:</div>
                <div class="value" nbTooltip="{{getConcatenatedLabels(removal?.groupedOrders)}}" nbTooltipPlacement="bottom">
                  {{getConcatenatedLabels(removal?.groupedOrders) |truncateString:20}}</div>
              </div>
            </div>
            <div class="row">
              <div class="infors-long" style="width: 50% !important;">
                <div class="key" nbTooltip="{{ removal?.SoldToDescription?.join(',')}}">Client:</div>
                <div class="value">{{ removal?.SoldToDescription?.join('et') || 'N/A' | truncateString:15}}</div>
              </div>
              <div class="infors">
                <div class="key" nbTooltip="{{removal?.ShipmentNumber}}">Code_Adresse:</div>
                <div class="value">{{removal?.ShipmentNumber}}</div>
              </div>
            </div>
          </div>
          <div class="dragPreview" *cdkDragPreview style="padding: 10px 25px; background-color: #0B305C; font-size: 16px; color: #fff;">
            BON N°{{removal?.LoadNumber}}
          </div>
        </div>
      </div>
    </div>
    <div class="carrier">
      <div class="search-bar">
        <div class="text">Liste des Transporteurs</div>
        <div class="search-input">
          <nb-form-field>
            <input fullWidth type="text" class="input-elmt" nbInput status="primary" [(ngModel)]="label"
              (ngModelChange)="onCarrierChange($event)" placeholder="Saisissez le nom du transporteur">
            <button nbSuffix nbButton ghost>
              <nb-icon icon="search-outline" pack="eva" status="primary"> </nb-icon>
            </button>
          </nb-form-field>
        </div>
      </div>
      <div class="carrier-container" [nbSpinner]="iscarrierLoading" nbSpinnerStatus="primary">
        <div class="carrier-AE" *ngFor="let carrier of carriersAe, let i= index">
          <div class="carrier-name" nbTooltip="{{carrier?.label}}" nbTooltipPlacement="top">{{carrier?.label |
            truncateString: 20}}</div>
          <div class="container-AE" cdkDropList id="aeAttribute" #aeAttributeList="cdkDropList"
            [cdkDropListConnectedTo]="[aeList]" [cdkDropListData]="carrier.removals"
            (cdkDropListDropped)="moveTo($event, i, carrier)">
            <div class="attribute-AE" [ngStyle]="getBackgroungColor(ae?.status)"
              (click)="openEditDialog(editDialog,ae, false)" *ngFor="let ae of carrier?.removals" cdkDrag>
              {{ae?.LoadNumber}}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="legend">
    <div class="content" *ngFor="let legend of legends">
      <div class="bullet" [ngStyle]="getLegendStyle(legend?.status)"></div>
      <div class="text">{{legend?.label}}</div>
    </div>
  </div>
</div>


<ng-template #editDialog let-data let-ref="dialogRef">
  <nb-card [nbSpinner]="isConfirmLoading" nbSpinnerStatus="danger">
    <nb-card-header class="form--header">Assignation direct dU BON N°{{selectedAE?.LoadNumber}}</nb-card-header>
    <nb-card-body>
      <div class="form-group">
        <label for="CarrierSelect" class="label">Transporteur</label>
        <input fieldSize="small" id="typeInput" [(ngModel)]="carrierFilter"
          (ngModelChange)="onModelCarrierChange($event)" nbInput fullWidth status="primary" type="text"
          placeholder="Rechercher le transporteur" [nbAutocomplete]="autoComplete1" />
        <nb-autocomplete #autoComplete1 (selectedChange)="onSelectionCarrierChange($event)">
          <nb-option *ngFor="let option of filteredCarrier$ | async" [value]="option">{{option}}</nb-option>
        </nb-autocomplete>
      </div>

      <!-- <div class="form-group">
        <label for="truckSelect" class="label">Camion</label>
        <nb-select size="medium" [(selected)]="selectedTruck" [fullWidth]="true" [disabled]="isConfirmLoading"
          id="truckSelect" placeholder="{{selectedAE?.carrier?.truck?.immatriculation || 'Sélectionnez un camion'}}">
          <nb-option [value]="truck" *ngFor="let truck of trucks">
            {{truck?.immatriculation}}
          </nb-option>
        </nb-select>
      </div> -->

      <!-- <div class="form-group">
        <label for="driverSelect" class="label">Chauffeur</label>
        <nb-select size="medium" [(selected)]="selectedDriver" [fullWidth]="true" [disabled]="isConfirmLoading"
          id="driverSelect" placeholder="{{selectedAE?.carrier?.dirver?.fullname || 'Sélectionnez un chauffeur'}}">
          <nb-option [value]="driver" *ngFor="let driver of drivers">
            {{driver?.fullname}}
          </nb-option>
        </nb-select>
      </div> -->
    </nb-card-body>
    <nb-card-footer class="form--footer">
      <button nbButton ghost (click)="ref.close(false)" outline status="basic">
        <nb-icon icon="close-square-outline" [options]="{ animation: { type: 'zoom' } }" class="remove-icon"></nb-icon>
        Annuler
      </button>
      <button nbButton (click)="editAe()" outline status="primary">
        <nb-icon icon="save-outline" [options]="{ animation: { type: 'zoom' } }" class="remove-icon"></nb-icon>
        Enregistrer
      </button>
    </nb-card-footer>
  </nb-card>
</ng-template>