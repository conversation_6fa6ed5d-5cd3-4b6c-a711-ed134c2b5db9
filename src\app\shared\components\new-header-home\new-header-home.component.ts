import { Component, OnInit, ViewEncapsulation, HostListener } from '@angular/core';
import { CommonService } from '../../services/common.service';

@Component({
  selector: 'clw-new-header-home',
  templateUrl: './new-header-home.component.html',
  styles: [
  ],
  encapsulation: ViewEncapsulation.None
})
export class NewHeaderHomeComponent implements OnInit {

  openModal: boolean = false;
  isMenuOpen: boolean = false;

  constructor(
    private commonService: CommonService
  ) {
  }

  ngOnInit(): void {

  }


  toggleMenu() {
    this.isMenuOpen = !this.isMenuOpen;
  }

  showModal() {
    this.commonService.showAuthModal = !this.commonService.showAuthModal;
    this.toggleMenu()
  }

  scrollToSection(section: string): void {
    this.commonService.scrollToElement(section);
  }
}
