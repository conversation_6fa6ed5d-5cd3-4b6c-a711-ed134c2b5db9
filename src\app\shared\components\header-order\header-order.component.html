<div class="header-pages">
  <div class="order-container">
    <div class="left-block">
      <a (click)="toggleSidebar()" href="#" class="sidebar-toggle">
        <nb-icon icon="menu-2-outline" status="primary" class="header-menu-icon"></nb-icon>
      </a>
      <div class="icon-frame">
        <div class=" icon icon_orderlogo"></div>
      </div>
      <!-- <div class="title">FLUID</div> -->
      <!-- <nb-select class="carrier-name input-width" fullWidth placeholder="Veuillez selectionner une usine."
        status='primary' size="large" class="empty-input" [(selected)]='storageSrv.curentFactory'  (selectedChange)="onFactorySelected($event)" >
        
        <nb-option [value]='factory' *ngFor="let factory of factoriesAuthor">{{factory | getStoreLabel}} 
        </nb-option>
      </nb-select> -->
    </div> 
    <div class="right-block">

      <!-- <button nbButton status="sucess" size="small" class="login-btn" (click)="nagivate()">
        <nb-icon icon="shopping-bag-outline"></nb-icon>
        NOUVELLE COMMANDE
      </button> -->
      <div class="info-profil">
        <div class="customer-infos" nbTooltip="{{connectedUser?.lname}} {{connectedUser?.fname}}"
          nbTooltipPlacement="top">
          <div class="welcome">Bienvenue, {{connectedUser?.lname | truncateString:15}}
            {{connectedUser?.fname | truncateString:15}}</div>

          <div class="user-function">{{connectedUser?.role}}</div>
        </div>
        <div class="profil" [ngStyle]="getImgProfil()"></div>
        <button nbButton [nbContextMenu]="items" title="menu" nbContextMenuTag="my-context-menu" class="vertical-point">
        </button>
      </div>
    </div>

  </div>