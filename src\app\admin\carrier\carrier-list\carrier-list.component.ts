import { Component, OnInit, ViewEncapsulation, ChangeDetectorRef } from '@angular/core';
import { Router } from '@angular/router';
import { NbDialogRef, NbDialogService, NbToastrService } from '@nebular/theme';
import * as moment from 'moment';
import { Carrier } from 'src/app/shared/models/carrier';
import { StorageService } from 'src/app/shared/services/storage.service';
import { CarrierService } from '../carriers.service';
import { ImageCompressor } from 'image-compressor';
import { AuthService } from 'src/app/shared/services/auth.service';
import { PaginationConfig } from 'src/app/shared/services/pagination.service';


@Component({
  selector: 'clw-carrier-list',
  templateUrl: './carrier-list.component.html',
  styles: [
    `
    .pagination-container {
      display: flex;
      justify-content: flex-end;
      padding: 10px 20px;
      margin-bottom: 10px;
    }
    `
  ],
  encapsulation: ViewEncapsulation.None
})
export class CarrierListComponent implements OnInit {
  min: Date;
  rangeFilter: { start: any; end: any } = { start: null, end: null };
  isLoading: boolean;
  carriers: Carrier[];
  connectedUser: any;
  label: any;
  carrierobjet: any;
  imageTest: string;
  carrierTest: any;
  addCarrier: Carrier;
  adddialogRef: NbDialogRef<any>;
  imgResultAfterCompress: string;
  imageSrc: string;
  file: any;
  filename: any;
  isdisplay = false;

  offset;
  limit = 15;
  endIndex = 20;
  startIndex = 1;
  total: number;

  // Configuration de la pagination
  paginationConfig: PaginationConfig = {
    currentPage: 1,
    itemsPerPage: 15,
    totalItems: 0,
    maxSize: 10
  };



  constructor(
    private toastrSvr: NbToastrService,
    private storageSvr: StorageService,
    private carrierSvr: CarrierService,
    private dialogService: NbDialogService,
    private authSvr: AuthService,
    private router: Router,
    private cdr: ChangeDetectorRef
  ) { }

  ngOnInit(): void {
    this.isdisplay = true;

    // Initialiser la configuration de pagination
    this.paginationConfig = {
      currentPage: 1,
      itemsPerPage: 15,
      totalItems: 0,
      maxSize: 10
    };

    this.getAllCarriers();
  }

  showdetail(carrier: Carrier) {
    this.storageSvr.setObject("currCarrier", carrier)
    this.carrierSvr.selectedCarrier = carrier;
    this.router.navigate(['admin/carrier/carrier-detail'])

  }

  updateDateStart(startDate: any): void {
    this.min = moment(startDate).startOf('day').toDate();
    this.rangeFilter.start = moment(startDate).startOf('day').valueOf();
  }
  updateDateEnd(endDate: any): void {

    this.rangeFilter.end = moment(endDate).endOf('day').toDate();
  }

  async getAllCarriers(): Promise<any> {
    try {
      this.isLoading = true;

      const options = {
        carrierLabel: this.label,
        limit: this.paginationConfig.itemsPerPage,
        offset: (this.paginationConfig.currentPage - 1) * this.paginationConfig.itemsPerPage
      };

      const { data, count } = await this.carrierSvr.getAllCarriers(options);
      this.carriers = data;
      this.total = count;

      // Créer une nouvelle instance de PaginationConfig pour forcer la détection des changements
      this.paginationConfig = {
        ...this.paginationConfig,
        totalItems: count
      };

      // Pour la compatibilité avec le code existant
      this.endIndex = Math.min((this.paginationConfig.currentPage * this.paginationConfig.itemsPerPage), count);
      this.startIndex = count === 0 ? 0 : (this.paginationConfig.currentPage - 1) * this.paginationConfig.itemsPerPage + 1;

      // Forcer la détection des changements
      this.cdr.detectChanges();

      // Vérification si la page actuelle est valide
      const totalPages = Math.ceil(this.paginationConfig.totalItems / this.paginationConfig.itemsPerPage);
      if (this.paginationConfig.currentPage > totalPages && totalPages > 0) {
        this.paginationConfig = {
          ...this.paginationConfig,
          currentPage: totalPages
        };
        this.cdr.detectChanges();
        await this.getAllCarriers();
        return;
      }
    } catch (error) {
      this.toastrSvr.danger('Impossible de récupérer la liste des transporteurs', 'Erreur connexion !');
    } finally {
      this.isLoading = false;
    }
  }


  /**
   * Méthode appelée lorsque l'utilisateur change de page
   * @param page Numéro de la page sélectionnée
   */
  onPageChange(page: number): void {
    if (this.isLoading) return; // Éviter les requêtes multiples pendant le chargement

    // Créer une nouvelle instance de PaginationConfig pour forcer la détection des changements
    this.paginationConfig = {
      ...this.paginationConfig,
      currentPage: page
    };

    // Forcer la détection des changements
    this.cdr.detectChanges();

    this.getAllCarriers();
  }

  /**
   * Méthode appelée lorsque l'utilisateur change le nombre d'éléments par page
   * @param itemsPerPage Nombre d'éléments par page sélectionné
   */
  onItemsPerPageChange(itemsPerPage: number): void {
    // Créer une nouvelle instance de PaginationConfig pour forcer la détection des changements
    this.paginationConfig = {
      ...this.paginationConfig,
      itemsPerPage: itemsPerPage,
      currentPage: 1 // Revenir à la première page
    };

    // Forcer la détection des changements
    this.cdr.detectChanges();

    this.getAllCarriers();
  }

  // Méthodes de compatibilité avec l'ancien code
  async nextPage(): Promise<any> {
    this.onPageChange(this.paginationConfig.currentPage + 1);
  }

  async previousPage(): Promise<any> {
    this.onPageChange(this.paginationConfig.currentPage - 1);
  }
  resetCarrier() {
    this.addCarrier = {
      label: '',
      rccm: '',
      niu: '',
      email: '',
      logo: '',
      localisation: '',
    }
  }

  openInsertCarrier() {
    this.router.navigate(['admin/carrier/carrier-add'])

  }

  async saveCarriers(): Promise<any> {
    this.isLoading = true;
    try {
      if (!this.addCarrier?.label || !this.addCarrier?.localisation || !this.addCarrier?.email || !this.addCarrier?.tel) {
        this.toastrSvr.danger("Veuillez renseigner le(s) champ(s) vide(s)", 'Donnée(s) manquante(s)');
        return;
      }

      if (!this.authSvr.validateEmail(this.addCarrier.email)) {
        this.toastrSvr.danger('Verifiez votre email', 'Données incorrectes');
        return;
      }

      this.compressFile(this.imageSrc, async (compressedSrc) => {
        this.addCarrier.logo = compressedSrc
        await this.carrierSvr.insertCarrier(this.addCarrier);
        this.toastrSvr.success('Utilisateur Ajouté', 'Opération réussie !');
        this.adddialogRef.close();
        this.getAllCarriers();

      })
    }
    catch (error) {
      return this.toastrSvr.danger('Impossile de creer cet utilisateur', 'Erreur connexion !');

    } finally {
      this.isLoading = false;
    }

  }

  getFile(fileDataSet: any): any {

    this.file = fileDataSet.target.files[0];
    const type = this.file.type;
    if (!type.includes('image')) {
      this.file = null;
      return this.toastrSvr.danger('Veuillez importer uniquement les images', 'Donnés incorrectes');

    }
    this.filename = this.file.name;

    const reader = new FileReader();
    if (fileDataSet.target.files && fileDataSet.target.files.length) {
      const [file] = fileDataSet.target.files;
      reader.readAsDataURL(file);

      reader.onload = () => {
        this.imageSrc = reader.result as string;
      };

    }
  }

  compressFile(imageSrc: string, callback: any): any {
    const imageCompressor = new ImageCompressor;

    const compressorSettings = {
      toWidth: 150,
      toHeight: 150,
      mimeType: 'image/png',
      mode: 'strict',
      quality: 0.8,
      grayScale: false,
      sepia: false,
      threshold: false,
      vReverse: false,
      hReverse: false,
      speed: 'low'
    };

    imageCompressor.run(imageSrc, compressorSettings, callback);
  }


}
