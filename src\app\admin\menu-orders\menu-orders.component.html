<div class="order-list-container">
    <div class="order-container">
        <div class="label-historic-order">
            <button nbButton>
                <nb-icon icon="undo-outline" (click)="goBack()" status="success"></nb-icon>
            </button>
            <h6>Liste des commandes</h6>
        </div>
        <div class="filter-area">
            <div class="left-area">
                <div class="filter-label">Filtrer par</div>
                <div class="filter-group">
                    <input type="text" placeholder="N° X3" fieldSize="small" nbInput class="empty-input"
                        [(ngModel)]="filter.erpReference" [disabled]="isLoading" />
                </div>
                <div class="filter-group">
                    <input type="text" placeholder="Client" fieldSize="small" nbInput class="empty-input"
                        [(ngModel)]="filter.clientName" [disabled]="isLoading" />
                </div>
                <div class="filter-group">
                    <input type="text" placeholder="Date de début" nbInput fieldSize="small" class="empty-input"
                        [nbDatepicker]="datePickerStart" [(ngModel)]="rangeFilter.start"
                        (dateChange)="updateDateStart($event)" [disabled]="isLoading" />
                    <nb-datepicker #datePickerStart></nb-datepicker>
                </div>
                <div class="filter-group">
                    <input type="text" placeholder="Date de fin" nbInput fieldSize="small" class="empty-input"
                        [nbDatepicker]="datepickerEnd" [(ngModel)]="rangeFilter.end"
                        (dateChange)="updateDateEnd($event)" [disabled]="isLoading || !rangeFilter.start" />
                    <nb-datepicker #datepickerEnd></nb-datepicker>
                </div>
            </div>
            <div class="right-area">
                <button nbButton status="success" class="search-btn" (click)="searchOrders()" [disabled]="isLoading">
                    <nb-icon icon="search-outline"></nb-icon>
                    RECHERCHER
                </button>
                <button nbButton status="basic" fieldSize="small" class="search-btn reset-btn" (click)="reset()">
                    <nb-icon icon="refresh-outline" [options]="{ animation: { type: 'zoom' } }"></nb-icon>
                    REINITIALISER
                </button>
            </div>
            
        </div>
        <nb-card>
            <nb-list class="element-head">
                <nb-list-item class="list-elt-header paginator">
                    <div class="col col-paginator">
                        <clw-pagination
                            [config]="paginationConfig"
                            (pageChange)="onPageChange($event)"
                            (itemsPerPageChange)="onItemsPerPageChange($event)">
                        </clw-pagination>
                    </div>
                </nb-list-item>
                <nb-list-item class="list-elt-header paginator">
                    <div class="col col-num">N°</div>
                    <div class="col col-ref">Numéro commande</div>
                    <div class="col col-jde">N° X3</div>
                    <div class="col col-name">Client</div>
                    <div class="col col-date">Date création</div>
                    <div class="col col-hour">Heure création</div>
                    <div class="col col-amount">Montant</div>
                    <div class="col col-status">Statut</div>
                    <div class="col col-action"></div>
                </nb-list-item>
            </nb-list>
            <nb-list class="element-head scrool-style">
                <nb-list-item class="list-elt-header" *ngFor="let order of orders; let i = index">
                    <div class="col col-num">{{(paginationConfig.currentPage - 1) * paginationConfig.itemsPerPage + i + 1}}</div>
                    <div class="col col-ref">{{order?.appReference}}</div>
                    <div class="col col-jde">{{order?.erpReference}}</div>
                    <div class="col col-name">{{order?.company?.name}}</div>
                    <div class="col col-date">{{formatDate(order?.dates?.created)}}</div>
                    <div class="col col-hour">{{formatTime(order?.dates?.created)}}</div>
                    <div class="col col-amount">{{calculateTotalAmount(order) | number}} XAF</div>
                    <div class="col col-status">
                        <nb-badge text="{{order?.status | status : false }}" class="badge" position="top start"
                            status="{{order?.status | status : true }}"></nb-badge>
                    </div>
                    <div class="col col-action">
                        <div class="action-icons">
                            <button nbTooltip="Detail" nbTooltipPlacement="top" nbTooltipStatus
                                (click)='openDetailModal(detailDialog, order)' status="basic" class="btn-background">
                                <nb-icon icon="file-text-outline" status="primary"
                                    [options]="{ animation: { type: 'zoom' } }"></nb-icon>
                            </button>
                            <!-- <button nbTooltip="Supprimer" nbTooltipPlacement="top" status="basic">
                  <nb-icon icon="trash-2-outline" status="danger" [options]="{ animation: { type: 'zoom' } }"
                    (click)="deleteInfosModal(deleteDialog, order)"></nb-icon>
                </button> -->
                            <button nbTooltip="Découper" nbTooltipPlacement="top" status="basic" class="btn-background">
                                <nb-icon icon="scissors-outline" status="info"
                                    [options]="{ animation: { type: 'zoom' } }"></nb-icon>
                            </button>
                        </div>
                    </div>
                </nb-list-item>
                <nb-list-item class="empty-list" *ngIf="!orders?.length">
                    <img src="../../../../assets/images/empty-list.png" alt="liste vide">
                    Aucune commande trouvée
                </nb-list-item>
            </nb-list>
        </nb-card>
    </div>
</div>

<ng-template #detailDialog let-data let-ref="dialogRef">
    <div class="order-detail-container">
        <nb-card>
            <nb-card-header class="form--header">Détail de la commande</nb-card-header>
            <nb-card-body>
                <div class="order-infor">
                    <div class="right-block">
                        <p class="title">N° commande Cadyst grain: </p>
                        <p class="title">N° commande X3: </p>
                        <p class="title">Client: </p>
                        <p class="title">Date de création: </p>
                    </div>
                    <div class="left-block">
                        <p class="value">{{selectedOrder?.appReference}}</p>
                        <p class="value">{{selectedOrder?.erpReference}}</p>
                        <p class="value">{{selectedOrder?.company?.name}}</p>
                        <p class="value">{{formatDate(selectedOrder?.dates?.created)}} à
                            {{formatTime(selectedOrder?.dates?.created)}}</p>
                    </div>
                </div>
                <div class="list">
                    <header class="form--header seperator">Liste des produits</header>
                    <div class="list-elt-header">
                        <div class="col col-desc">Description</div>
                        <div class="col col-weight">Quantité</div>
                        <div class="col col-price">PU</div>
                        <div class="col col-amount">Montant</div>
                    </div>
                    <div class="list-elt-contain" *ngFor="let item of selectedOrder?.cart?.items">
                        <div class="list-elt">
                            <div class="col col-desc">
                                <div class="col-desc-elt">
                                    <div class="col-desc-elt-contain">
                                        <img [src]="item?.product?.image" alt="product" class="product-image">
                                    </div>
                                    <div class="label">{{item?.product?.label}}</div>
                                </div>
                            </div>
                            <div class="col col-weight">{{item?.quantity}} {{item?.packaging?.unit?.symbol}}</div>
                            <div class="col col-price">{{item?.unitPrice | number}} XAF</div>
                            <div class="col col-amount">{{item?.quantity * item?.unitPrice | number}} XAF</div>
                        </div>
                    </div>
                </div>
                <header class="header">Récapitulatif de la commande</header>
                <div class="order-infor">
                    <div class="right-block">
                        <p class="title">Point de retrait:</p>
                        <p class="title">Mode de paiement: </p>
                        <p class="title">Total HT:</p>
                        <p class="title">TVA:</p>
                        <p class="title">Total TTC:</p>
                    </div>
                    <div class="left-block">
                        <p class="value">{{selectedOrder?.cart?.store?.label}}</p>
                        <p class="value">{{selectedOrder?.payment?.mode?.label}}</p>
                        <p class="value">{{selectedOrder?.cart?.amount?.HT | number}} XAF</p>
                        <p class="value">{{selectedOrder?.cart?.amount?.VAT | number}} XAF</p>
                        <p class="value">{{selectedOrder?.cart?.amount?.TTC | number}} XAF</p>
                    </div>
                </div>
            </nb-card-body>
            <nb-card-footer class="form--footer">
                <button nbButton outline status="basic" class="" (click)="ref.close()" [disabled]="isLoading">
                    <nb-icon icon="close-square-outline" [options]="{ animation: { type: 'zoom' } }"
                        class="remove-icon">
                    </nb-icon>
                    Fermer
                </button>
            </nb-card-footer>
        </nb-card>
    </div>
</ng-template>