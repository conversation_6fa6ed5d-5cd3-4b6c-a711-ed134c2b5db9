.common-form-container {
    @include container-page;
    .header {
        height: 50px;
        @include v-center;
        .remove-icon {
            margin-right: 15px;
        }
    }
    .title {
        font-family: $font-regular;
        font-size: 17px;
        margin: 0 !important;
    }
    .filter-container .filter-elt.input {
        width: 17%;
        min-width: 100px;
        input {
            width: 100%;
            padding-top: 3px;
            padding-bottom: 3px;
            box-sizing: border-box;
        }
    }
    .filter-container {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 62px;
        .left-block {
            @include v-center;
            width: 100%;
            .filter-label {
                margin-right: 10px;
            }
            .filter-elt {
                margin-right: 10px;
                font-family: $font-regular;
            }
            .filter-elt.select {
                width: 25%;
                min-width: 130px;
                max-width: 220px !important;
                nb-select .select-button {
                    width: 100%;
                    padding-right: 0px;
                    box-sizing: border-box;
                }
            }
        }
        .btn-contain {
            @include v-center;
            .space {
                margin-right: 15px;
            }
            .color-text {
                color: #fff;
            }
        }
    }
}