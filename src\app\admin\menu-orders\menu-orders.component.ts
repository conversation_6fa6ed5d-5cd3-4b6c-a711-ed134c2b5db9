import { Component, OnInit, OnDestroy, Pipe, PipeTransform } from '@angular/core';
import { NbDialogRef, NbDialogService, NbToastrService } from '@nebular/theme';
import { StorageService } from 'src/app/shared/services/storage.service';
import { AuthService } from 'src/app/shared/services/auth.service';
import { OrderService, OrderSearchFilters, OrderResponse, } from '../../client/menu-order/order.service';
import { Location } from '@angular/common';
import { Router } from '@angular/router';
import * as moment from 'moment';
import get from 'lodash-es/get';
import { Subject } from 'rxjs';
import { takeUntil, debounceTime, distinctUntilChanged } from 'rxjs/operators';
import { Order } from 'src/app/shared/models/order';
import { PaginationConfig } from '../../shared/services/pagination.service';

/**
 * Pi<PERSON> pour transformer le statut en texte ou couleur
 */
@Pipe({
    name: 'status'
})
export class OrderStatusPipe implements PipeTransform {
    transform(value: number, isColor: boolean): string {
        const statuses = { 100: 'initiée', 200: 'validée', 300: 'validée', 500: 'annulée' };
        const colors = { 100: 'basic', 200: 'success', 300: 'success', 500: 'danger' };
        return isColor ? colors[value] || 'basic' : statuses[value] || 'inconnu';
    }
}

/**
 * Pipe pour transformer le statut de livraison en texte ou couleur
 */
@Pipe({
    name: 'deliveryStatus'
})
export class DeliveryStatusPipe implements PipeTransform {
    transform(value: number, isColor: boolean): string {
        const statuses = {
            0: 'En attente de livraison',
            1: 'Livraison partielle',
            2: 'Livraison complète'
        };
        const colors = {
            0: 'warning',
            1: 'info',
            2: 'success'
        };
        return isColor ? colors[value] || 'basic' : statuses[value] || 'inconnu';
    }
}

/**
 * Interface pour l'élément produit dans une commande
 */
interface OrderProduct {
    image?: string;
    label?: string;
    packaging?: string;
    quantity?: number;
}

@Component({
    selector: 'clw-menu-orders',
    templateUrl: './menu-orders.component.html',
    styleUrls: []
})


export class MenuOrdersComponent implements OnInit, OnDestroy {
    // Gestion des dialogues
    detailDetailRef: NbDialogRef<any>;
    deleteDialogRef: NbDialogRef<any>;
    editDialogRef: NbDialogRef<any>;


    // État de chargement
    isLoading = false;

    // Commande sélectionnée
    selectedOrder: Order | null = null;

    // Pagination
    paginationConfig: PaginationConfig = {
        currentPage: 1,
        itemsPerPage: 10,
        totalItems: 0,
        maxSize: 10
    };

    // Filtres
    filter: OrderSearchFilters = {
        erpReference: '',
        clientName: ''
    };

    rangeFilter: { start: any; end: any } = { start: null, end: null };
    selectedStatus: number | null = null;

    // Données
    orders: Order[] = [];
    user: any;

    // Gestion des souscriptions
    private destroy$ = new Subject<void>();
    private filterChange$ = new Subject<void>();

    constructor(
        private dialogSvr: NbDialogService,
        private orderSvr: OrderService,
        private storageSvr: StorageService,
        private router: Router,
        private location: Location,
        private authSvr: AuthService,
        private toastrSrv: NbToastrService,
    ) { }

    /**
     * Initialisation du composant
     */
    ngOnInit(): void {
        // Récupérer l'utilisateur connecté
        this.user = this.storageSvr.getObject('user');

        // Configurer l'observable pour les changements de filtre avec un délai
        this.filterChange$
            .pipe(
                takeUntil(this.destroy$),
                debounceTime(300),
                distinctUntilChanged()
            )
            .subscribe(() => {
                this.loadOrders();
            });

        // Charger les commandes initiales
        this.loadOrders();
    }

    /**
     * Nettoyage lors de la destruction du composant
     */
    ngOnDestroy(): void {
        this.destroy$.next();
        this.destroy$.complete();
    }

    /**
     * Ouvre la modal de détail d'une commande
     */
    openDetailModal(dialog: any, order: Order): void {
        this.selectedOrder = order;
        this.detailDetailRef = this.dialogSvr.open(dialog);
    }

    /**
     * Ouvre la modal de suppression d'une commande
     */
    deleteInfosModal(dialog: any, order: Order): void {
        this.selectedOrder = order;
        this.deleteDialogRef = this.dialogSvr.open(dialog);
    }

    /**
     * Ouvre la modal d'édition d'une commande
     */
    editInfosModal(dialog: any, order: Order): void {
        this.selectedOrder = order;
        this.editDialogRef = this.dialogSvr.open(dialog);
    }

    /**
     * Met à jour la date de début
     */
    updateDateStart(startDate: any): void {
        this.rangeFilter.start = startDate ? moment(startDate).startOf('day').valueOf() : null;

        // Vérifier la cohérence des dates
        if (this.validateDateRange()) {
            this.triggerFilterChange();
        }
    }

    /**
     * Met à jour la date de fin
     */
    updateDateEnd(endDate: any): void {
        this.rangeFilter.end = endDate ? moment(endDate).endOf('day').valueOf() : null;

        // Vérifier la cohérence des dates
        if (this.validateDateRange()) {
            this.triggerFilterChange();
        }
    }

    /**
     * Vérifie la cohérence des dates sélectionnées
     */
    private validateDateRange(): boolean {
        if (this.rangeFilter.start && this.rangeFilter.end && this.rangeFilter.start > this.rangeFilter.end) {
            this.toastrSrv.danger(
                'La date de début ne peut pas être supérieure à la date de fin',
                'Erreur de date'
            );

            // Réinitialiser la date qui vient d'être modifiée
            this.rangeFilter.start = null;
            this.rangeFilter.end = null;
            return false;
        }
        return true;
    }

    /**
     * Déclenche le changement de filtre
     */
    triggerFilterChange(): void {
        this.filterChange$.next();
    }

    /**
     * Récupère toutes les commandes avec filtres appliqués
     */
    async loadOrders(): Promise<void> {
        if (this.isLoading) return;

        // Vérifier la cohérence des dates
        if (!this.validateDateRange()) {
            return;
        }

        this.isLoading = true;

        try {
            const options = {
                limit: this.paginationConfig.itemsPerPage,
                offset: (this.paginationConfig.currentPage - 1) * this.paginationConfig.itemsPerPage,
                filters: {
                    ...this.filter,
                    startDate: this.rangeFilter.start ? moment(this.rangeFilter.start).format('YYYY-MM-DD') : undefined,
                    endDate: this.rangeFilter.end ? moment(this.rangeFilter.end).format('YYYY-MM-DD') : undefined,
                    status: this.selectedStatus || undefined
                }
            };

            // Suppression des propriétés undefined dans les filtres
            Object.keys(options.filters).forEach(key => {
                if (options.filters[key] === undefined || options.filters[key] === '') {
                    delete options.filters[key];
                }
            });

            const response = await this.orderSvr.getOrders(options);
            this.handleOrderResponse(response);
        } catch (error) {
            this.handleError('Erreur lors du chargement des commandes', error);
        } finally {
            this.isLoading = false;
        }
    }

    /**
     * Recherche de commandes avec critères spécifiques
     */
    async searchOrders(): Promise<void> {
        if (this.isLoading) return;

        // Vérifier la cohérence des dates
        if (!this.validateDateRange()) {
            return;
        }

        this.isLoading = true;

        try {
            // Paramètres de base pour la pagination
            const params: any = {
                offset: (this.paginationConfig.currentPage - 1) * this.paginationConfig.itemsPerPage,
                limit: this.paginationConfig.itemsPerPage
            };

            // Ajouter uniquement les filtres non vides
            if (this.filter.erpReference) {
                params.erpReference = this.filter.erpReference.toString().trim();
            }

            if (this.filter.clientName) {
                params.clientName = this.filter.clientName.trim();
            }

            if (this.rangeFilter.start) {
                params.startDate = moment(this.rangeFilter.start).format('YYYY-MM-DD');
            }

            if (this.rangeFilter.end) {
                params.endDate = moment(this.rangeFilter.end).format('YYYY-MM-DD');
            }

            if (this.selectedStatus) {
                params.status = this.selectedStatus;
            }

            const response = await this.orderSvr.searchOrders(params);
            this.handleOrderResponse(response);
        } catch (error) {
            this.handleError('Erreur lors de la recherche des commandes', error);
        } finally {
            this.isLoading = false;
        }
    }

    /**
     * Traite la réponse des commandes
     */
    private handleOrderResponse(response: OrderResponse): void {
        if (response && response.data) {
            // Vérifier si des filtres sont actifs
            const hasActiveFilters =
                this.filter.erpReference ||
                this.filter.clientName ||
                this.rangeFilter.start ||
                this.rangeFilter.end ||
                this.selectedStatus;

            // Afficher un message si aucun résultat avec filtres actifs
            if (response.data.length === 0 && hasActiveFilters) {
                this.toastrSrv.info('Aucune commande ne correspond à votre recherche', 'Information');
            }

            this.orders = response.data;
            
            // Créer un nouvel objet pour forcer la détection des changements
            this.paginationConfig = {
                ...this.paginationConfig,
                totalItems: response.count || 0
            };
            
            // S'assurer que la page courante est valide
            const totalPages = Math.ceil(this.paginationConfig.totalItems / this.paginationConfig.itemsPerPage);
            if (this.paginationConfig.currentPage > totalPages) {
                this.paginationConfig.currentPage = 1;
            }
            
            console.log('Pagination config after update:', this.paginationConfig);
        } else {
            this.orders = [];
            this.paginationConfig = {
                ...this.paginationConfig,
                totalItems: 0,
                currentPage: 1
            };
        }
    }

    /**
     * Gère les erreurs
     */
    private handleError(message: string, error: any): void {
        console.error(`${message}:`, error);
        this.toastrSrv.danger(message, 'Erreur');
        this.orders = [];
        this.paginationConfig.totalItems = 0;
    }

    /**
     * Page suivante
     */
    async nextPage(): Promise<void> {
        if (this.paginationConfig.currentPage * this.paginationConfig.itemsPerPage >= this.paginationConfig.totalItems || this.isLoading) return;
        this.paginationConfig.currentPage += 1;
        await this.loadOrders();
    }

    /**
     * Page précédente
     */
    async previousPage(): Promise<void> {
        if (this.paginationConfig.currentPage <= 1 || this.isLoading) return;
        this.paginationConfig.currentPage -= 1;
        await this.loadOrders();
    }

    /**
     * Retour à la page précédente
     */
    goBack(): void {
        if (window.history.length > 1) {
            this.location.back();
        } else {
            this.router.navigate(['/home']);
        }
    }

    /**
     * Calcule le montant total d'une commande
     */
    calculateTotalAmount(order: Order): number {
        return order?.cart?.amount?.TTC || 0;
    }

    /**
     * Formate une date
     */
    formatDate(date: number): string {
        if (!date) return '';
        return moment(date).format('DD/MM/YYYY');
    }

    /**
     * Formate une heure
     */
    formatTime(date: number): string {
        if (!date) return '';
        return moment(date).format('HH:mm');
    }

    /**
     * Récupère les détails des produits d'une commande
     */
    getProductDetails(order: Order): OrderProduct[] {
        return order?.cart?.items?.map(item => ({
            image: item?.product?.img,
            label: item?.product?.label,
            packaging: item?.packaging?.label,
            quantity: item?.quantity
        })) || [];
    }

    /**
     * Réinitialise tous les filtres
     */
    reset(): void {
        this.filter = {
            erpReference: '',
            clientName: ''
        };
        this.rangeFilter = { start: null, end: null };
        this.selectedStatus = null;
        this.paginationConfig.currentPage = 1;
        this.loadOrders();
    }

    /**
     * Déclenché lors d'un changement de filtre
     */
    onFilterChange(): void {
        this.triggerFilterChange();
    }

    onPageChange(page: number): void {
        console.log('Changement de page vers', page);
        if (this.isLoading) {
            console.log('Chargement en cours, page non changée');
            return;
        }
        if (page < 1) {
            console.log('Page invalide:', page);
            return;
        }
        
        this.paginationConfig.currentPage = page;
        console.log('Pagination config après changement:', this.paginationConfig);
        this.loadOrders();
    }

    onItemsPerPageChange(itemsPerPage: number): void {
        this.paginationConfig.itemsPerPage = itemsPerPage;
        this.paginationConfig.currentPage = 1;
        this.loadOrders();
    }
} 