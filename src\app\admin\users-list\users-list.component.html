<div class="users-list-container">
  <div class="label-users-list">
    <button nbButton>
      <nb-icon icon="undo-outline" (click)="goBack()" status="success"></nb-icon>
    </button>
    <h6>Liste des Utilisateurs</h6>
  </div>
  <div class="filter-area">
    <div class="left-area">
      <div class="filter-label">Filtrer par</div>
      <div class="filters">
        <input type="text" placeholder="Nom" fieldSize="small" nbInput class="empty-input" [(ngModel)]='name'
          (ngModelChange)="onModelStoreChange($event)" [nbAutocomplete]="autoComplete1" [nbTooltip]="getTooltipText()"
          nbTooltipPlacement="bottom" />
        <nb-autocomplete #autoComplete1 (selectedChange)="onSelectionStoreChange($event)">
          <nb-option *ngFor="let option of filteredNames$ | async" [value]="option">{{option}}
          </nb-option>
        </nb-autocomplete>
      </div>
      <!--      <input type="text" placeholder="Debut" nbInput fieldSize="small" class="empty-input"
        [nbDatepicker]="datePickerStart" [disabled]="isLoading" />
      <nb-datepicker #datePickerStart (dateChange)="updateDateStart($event)">
      </nb-datepicker>

      <input type="text" placeholder="Date de fin" nbInput fieldSize="small" class="empty-input"
        [nbDatepicker]="datepickerEnd" [disabled]="isLoading || !rangeFilter.start" />
      <nb-datepicker #datepickerEnd [min]="min" (dateChange)="updateDateEnd($event)">
      </nb-datepicker> -->
    </div>
    <div class="right-area">
      <button nbButton status="primary" outline class="search-btn" (click)="search()">
        <nb-icon icon="search-outline" status="primary"></nb-icon>
        Filtrer
      </button>
      <button nbButton status="primary" outline class="search-btn" (click)="resetFilter()">
        <nb-icon icon="refresh-outline" status="primary"></nb-icon>
        Réinitialiser
      </button>
      <button nbButton status="success" class="search-btn" (click)="openNewUser(addUserDialog)">
        <nb-icon icon="plus-square-outline"></nb-icon>
        Ajouter
      </button>
    </div>
  </div>
        <div class="pagination-container" *ngIf="total > 0">
        <clw-pagination [config]="paginationConfig" (pageChange)="onPageChange($event)"
          (itemsPerPageChange)="onItemsPerPageChange($event)">
        </clw-pagination>
      </div>

  <nb-card [nbSpinner]='isLoading' nbSpinnerStatus='primary' nbSpinnerMessage='chargement' class="block">
    <div class="menus-block">

      <div class="menu-elt" *ngFor="let user of users" (click)="openDetailsUser(detailsDialog, user)">
        <div class="user-image" [ngStyle]="user?.img | getImgStyle:'assets/images/account.png' ">
          <div class="title-block">
            <p *ngFor="let item of user?.lname | curveText:180" [style]='item.style'>{{item?.letter}}</p>
          </div>
        </div>
        <div class="text-block">
          {{ user.function }}
        </div>
      </div>
    </div>

    <div class="menus-block" *ngIf='users?.length===0 && !isLoading'>
      <div class="not-found-block">
        <div class="image-block">
          <img src="../../../assets/images/EMPTY FOLDER VECTOR.png" alt="no search result image">
          <p>Aucun utilisateur trouvé</p>
        </div>
      </div>
    </div>
  </nb-card>

</div>

<ng-template #detailsDialog let-data let-ref="dialogRef">

  <div class="card-container">
    <nb-flip-card [showToggleButton]="false" [flipped]="flipped">
      <nb-card-front>
        <nb-card class="rm-width">
          <nb-card-body class="rm-padding">
            <nb-card class="details-user-container">
              <nb-tabset>
                <nb-tab tabTitle="Informations générales">
                  <nb-card-header class="user-card-header remove-padding">
                    <div class="user-image width-img"
                      [ngStyle]="currUser?.img | getImgStyle:'assets/images/account.png' "></div>
                    <div class="user-profile">
                      <div class="user-name"> {{currUser?.lname}}</div>
                      <div class="user-function">{{currUser?.fname}}</div>
                    </div>
                    <div class="btn-actions">
                      <button nbbutton ghost size="medium" class="icon-button" nbTooltip="Editer"
                        nbTooltipPlacement="top" nbTooltipStatus="basic" (click)="edit()">
                        <nb-icon icon="edit-2-outline" status="warning"></nb-icon>
                      </button>
                      <button nbbutton ghost size="medium" class="icon-button" nbTooltip="Supprimer"
                        nbTooltipPlacement="top" nbTooltipStatus="basic"
                        (click)="openDeleteModal(confirmDleteUserDialog)">
                        <nb-icon icon="trash-2-outline" status="danger"></nb-icon>
                      </button>
                      <button nbButton size="medium" ghost *ngIf="currUser.enable" status="basic"
                        (click)="openActionDialog(confirmDeactivateUserDialog)" nbTooltip="Désactiver"
                        nbTooltipStatus="default" nbTooltipPlacement="bottom" class="icon-btn">
                        <nb-icon icon="eye-outline" [options]="{ animation: { type: 'zoom' } }" status="success"
                          class="remove-icon"></nb-icon>
                      </button>
                      <button nbButton size="medium" ghost *ngIf="!currUser.enable" status="basic"
                        (click)="openActionDialog(confirmDeactivateUserDialog)" nbTooltip="Activer"
                        nbTooltipStatus="default" nbTooltipPlacement="bottom" class="icon-btn">
                        <nb-icon icon="eye-off-outline" [options]="{ animation: { type: 'zoom' } }" status="danger"
                          class="remove-icon"></nb-icon>
                      </button>
                    </div>
                  </nb-card-header>
                  <nb-card-body class="overflow">
                    <div class="form-group">
                      <div class="attribute-block">
                        <div class="icon-btn">
                          <nb-icon icon="home-outline"></nb-icon>
                        </div>
                        <label class="label" for="nameInput">Matricule</label>
                      </div>
                      <div class="text">{{currUser?.matricule}}</div>
                    </div>
                    <div class="form-group">
                      <div class="attribute-block">
                        <div class="icon-btn">
                          <nb-icon icon="phone-outline"></nb-icon>
                        </div>
                        <label class="label" for="nameInput">N° téléphone</label>
                      </div>
                      <div class="text">{{currUser?.tel}}</div>
                    </div>
                    <div class="form-group">
                      <div class="attribute-block">
                        <div class="icon-btn">
                          <nb-icon icon="email-outline"></nb-icon>
                        </div>
                        <label class="label" for="nameInput">Email</label>
                      </div>
                      <div class="text">{{currUser?.email}}</div>
                    </div>
                    <div class="form-group">
                      <div class="attribute-block">
                        <div class="icon-btn">
                          <nb-icon icon="calendar-outline"></nb-icon>
                        </div>
                        <label class="label" for="nameInput">Date création</label>
                      </div>
                      <div class="text">{{currUser?.createAt | date: "dd/MM/yyyy"}}
                      </div>
                    </div>
                  </nb-card-body>
                </nb-tab>
                <nb-tab tabTitle="Privilèges">
                  <div class="container-privilege">
                    <div class="acces-rigth">
                      <div class="label-title">Descriptions</div>
                      <div class="label-title">Statut</div>
                    </div>

                    <!-- Super Administrateur -->
                    <div class="section-header">
                      <nb-icon icon="shield-outline" class="section-icon"></nb-icon>
                      <h5 class="section-title">Super Administrateur</h5>
                    </div>
                    <div class="acces-rigth insert-border">
                      <div class="rigth-label">Super Administrateur (accès à tous les menus)</div>
                      <nb-toggle disabled [(ngModel)]="currUser.rigths.isFullRigth"></nb-toggle>
                    </div>

                    <!-- Menu Administration -->
                    <div class="section-header">
                      <nb-icon icon="settings-outline" class="section-icon"></nb-icon>
                      <h5 class="section-title">Menu Administration</h5>
                    </div>
                    <div class="acces-rigth insert-border">
                      <div class="rigth-label">Accès au menu Administration</div>
                      <nb-toggle disabled class="toggle-width" [(ngModel)]="currUser.rigths.isAdmin"></nb-toggle>
                    </div>
                    <div class="acces-rigth insert-border">
                      <div class="rigth-label">Gérer les Utilisateurs</div>
                      <nb-toggle disabled class="toggle-width" [(ngModel)]="currUser.rigths.canManageUsers"></nb-toggle>
                    </div>
                    <div class="acces-rigth insert-border">
                      <div class="rigth-label">Gérer les Points d'Enlèvements</div>
                      <nb-toggle disabled class="toggle-width"
                        [(ngModel)]="currUser.rigths.canManageStores"></nb-toggle>
                    </div>
                    <div class="acces-rigth insert-border">
                      <div class="rigth-label">Gérer les Transporteurs</div>
                      <nb-toggle disabled class="toggle-width"
                        [(ngModel)]="currUser.rigths.canManageCarriers"></nb-toggle>
                    </div>
                    <div class="acces-rigth insert-border">
                      <div class="rigth-label">Gérer les Inspections</div>
                      <nb-toggle disabled class="toggle-width"
                        [(ngModel)]="currUser.rigths.canManageInspections"></nb-toggle>
                    </div>
                    <div class="acces-rigth insert-border">
                      <div class="rigth-label">Gérer les Commandes</div>
                      <nb-toggle disabled class="toggle-width"
                        [(ngModel)]="currUser.rigths.canManageOrders"></nb-toggle>
                    </div>

                    <!-- Menu Gestion Enlèvements -->
                    <div class="section-header">
                      <nb-icon icon="clipboard-outline" class="section-icon"></nb-icon>
                      <h5 class="section-title">Menu Gestion Enlèvements</h5>
                    </div>
                    <div class="acces-rigth insert-border">
                      <div class="rigth-label">Accéder au menu Gestion Enlèvements</div>
                      <nb-toggle disabled [(ngModel)]="currUser.rigths.canManageRemovals"></nb-toggle>
                    </div>
                    <div class="acces-rigth insert-border">
                      <div class="rigth-label">Accéder à l'Historique Bon</div>
                      <nb-toggle disabled [(ngModel)]="currUser.rigths.canViewHistory"></nb-toggle>
                    </div>
                    <div class="acces-rigth insert-border">
                      <div class="rigth-label">Attribuer les Bons</div>
                      <nb-toggle disabled [(ngModel)]="currUser?.rigths.canAllocate"></nb-toggle>
                    </div>
                    <div class="acces-rigth insert-border">
                      <div class="rigth-label">Voir les Transports Validés</div>
                      <nb-toggle disabled [(ngModel)]="currUser.rigths.canViewTransports"></nb-toggle>
                    </div>
                    <div class="acces-rigth insert-border">
                      <div class="rigth-label">Voir les Camions Chargés</div>
                      <nb-toggle disabled [(ngModel)]="currUser.rigths.canViewLoadedTrucks"></nb-toggle>
                    </div>
                    <div class="acces-rigth insert-border">
                      <div class="rigth-label">Voir la File d'Attente</div>
                      <nb-toggle disabled [(ngModel)]="currUser.rigths.canViewQueue"></nb-toggle>
                    </div>
                    <div class="acces-rigth insert-border">
                      <div class="rigth-label">Créer un Enlèvement</div>
                      <nb-toggle disabled [(ngModel)]="currUser.rigths.canRemove"></nb-toggle>
                    </div>
                    <div class="acces-rigth insert-border">
                      <div class="rigth-label">Attribuer les Quais</div>
                      <nb-toggle disabled [(ngModel)]="currUser.rigths.canAllocateDock"></nb-toggle>
                    </div>
                    <div class="acces-rigth insert-border">
                      <div class="rigth-label">Voir les Camions en Panne</div>
                      <nb-toggle disabled [(ngModel)]="currUser.rigths.canViewBrokenTrucks"></nb-toggle>
                    </div>
                    <div class="acces-rigth insert-border">
                      <div class="rigth-label">Voir l'Historique des Inspections</div>
                      <nb-toggle disabled [(ngModel)]="currUser?.rigths.canViewInspections"></nb-toggle>
                    </div>

                    <!-- Menu Reporting -->
                    <div class="section-header">
                      <nb-icon icon="trending-up-outline" class="section-icon"></nb-icon>
                      <h5 class="section-title">Menu Reporting</h5>
                    </div>
                    <div class="acces-rigth insert-border">
                      <div class="rigth-label">Accès au menu Reporting</div>
                      <nb-toggle disabled [(ngModel)]="currUser.rigths.canViewReporting"></nb-toggle>
                    </div>
                    <div class="acces-rigth insert-border">
                      <div class="rigth-label">Voir les Bons Rendus</div>
                      <nb-toggle disabled [(ngModel)]="currUser.rigths.canViewRenderReporting"></nb-toggle>
                    </div>
                    <div class="acces-rigth insert-border">
                      <div class="rigth-label">Voir les Bons Pickup</div>
                      <nb-toggle disabled [(ngModel)]="currUser.rigths.canViewPickupReporting"></nb-toggle>
                    </div>

                    <!-- Menu Écran d'appel -->
                    <div class="section-header">
                      <nb-icon icon="browser-outline" class="section-icon"></nb-icon>
                      <h5 class="section-title">Menu Écran d'appel</h5>
                    </div>
                    <div class="acces-rigth insert-border">
                      <div class="rigth-label">Accès au menu Écran d'appel</div>
                      <nb-toggle disabled [(ngModel)]="currUser.rigths.canFollow"></nb-toggle>
                    </div>
                    <div class="acces-rigth insert-border">
                      <div class="rigth-label">Voir le Parking</div>
                      <nb-toggle disabled [(ngModel)]="currUser.rigths.canViewParking"></nb-toggle>
                    </div>
                    <div class="acces-rigth insert-border">
                      <div class="rigth-label">Voir le Chargement Usine</div>
                      <nb-toggle disabled [(ngModel)]="currUser.rigths.canViewFactoryLoading"></nb-toggle>
                    </div>

                    <!-- Autres autorisations -->
                    <div class="section-header">
                      <nb-icon icon="options-2-outline" class="section-icon"></nb-icon>
                      <h5 class="section-title">Autres autorisations</h5>
                    </div>
                    <div class="acces-rigth insert-border">
                      <div class="rigth-label">Inspecter les camions</div>
                      <nb-toggle disabled class="toggle-width" [(ngModel)]="currUser.rigths.isInspector"></nb-toggle>
                    </div>
                    <div class="acces-rigth insert-border">
                      <div class="rigth-label">Enregistrer les camions</div>
                      <nb-toggle disabled [(ngModel)]="currUser.rigths.canSaveTruck"></nb-toggle>
                    </div>
                    <div class="acces-rigth insert-border">
                      <div class="rigth-label">Gérer les autorisations d'enlèvement</div>
                      <nb-toggle disabled [(ngModel)]="currUser.rigths.canManageRemovalsAuth"></nb-toggle>
                    </div>
                    <div class="acces-rigth insert-border">
                      <div class="rigth-label">Passer des commandes</div>
                      <nb-toggle disabled [(ngModel)]="currUser.rigths.canOrder"></nb-toggle>
                    </div>

                    <!-- Accès aux usines -->
                    <div class="section-header">
                      <nb-icon icon="home-outline" class="section-icon"></nb-icon>
                      <h5 class="section-title">Accès aux usines</h5>
                    </div>
                    <div class="acces-rigth insert-border" *ngFor="let factory of factories">
                      <div class="rigth-label"> Données de {{factory | getStoreLabel}}</div>
                      <nb-toggle disabled [(ngModel)]="currUser.rigths[factory]"
                        (checkedChange)="onToggleChange(factory , $event)"></nb-toggle>
                    </div>
                    <div class="acces-rigth insert-border">
                      <div class="rigth-label">Tous les usines</div>
                      <nb-toggle disabled [(ngModel)]="currUser.rigths.allStore"></nb-toggle>
                    </div>

                  </div>
                </nb-tab>
              </nb-tabset>
            </nb-card>
          </nb-card-body>
          <nb-card-footer class="form--footer">
            <button nbButton outline class="btn-border border" (click)="ref.close()" [disabled]="isLoading">
              <nb-icon icon="close-square-outline"></nb-icon>
              Fermer
            </button>
          </nb-card-footer>
        </nb-card>
      </nb-card-front>

      <nb-card-back>
        <nb-card class="rm-width">
          <nb-card-body class="rm-padding">
            <nb-card class="details-user-container">
              <nb-tabset>
                <nb-tab tabTitle="Informations générales">
                  <nb-card-header class="user-card-header">
                    <label for="file">
                      <div class="user-image"
                        nbTooltip="{{(imgResultAfterCompress) ? 'Modifier votre photo de profil' : 'Ajouter une photo de profil'}}"
                        [ngStyle]="imageSrc | getImgStyle:'assets/images/account.png' ">
                      </div>
                    </label>
                    <input type="file" id="file" style="display: none;" (change)='getFile($event)'>
                  </nb-card-header>
                  <nb-card-body class="overflow">
                    <div class="form-group">
                      <div class="attribute-block">
                        <div class="icon-btn">
                          <nb-icon icon="person-outline"></nb-icon>
                        </div>
                        <label class="label" for="nameInput">Nom(s)</label>
                      </div>
                      <input nbInput class="user-name input-width" status="basic" type="text"
                        placeholder="{{currUser?.lname || 'Veuillez entrer le nom' }}" [(ngModel)]="currUser.lname">
                    </div>
                    <div class="form-group">
                      <div class="attribute-block">
                        <div class="icon-btn">
                          <nb-icon icon="person-outline"></nb-icon>
                        </div>
                        <label class="label" for="nameInput">Prénom(s)</label>
                      </div>
                      <input nbInput class="user-name input-width" status="basic" type="text"
                        placeholder="{{currUser?.fname || 'Veuillez entrer le prénom' }}" [(ngModel)]="currUser.fname">
                    </div>
                    <div class="form-group">
                      <div class="attribute-block">
                        <div class="icon-btn">
                          <nb-icon icon="home-outline"></nb-icon>
                        </div>
                        <label class="label" for="nameInput">Matricule</label>
                      </div>
                      <input nbInput class="user-name input-width" status="basic" type="text"
                        placeholder="{{(currUser?.matricule) || 'Veuillez entrer le matricule' }}"
                        [(ngModel)]="currUser.matricule">
                    </div>
                    <div class="form-group">
                      <div class="attribute-block">
                        <div class="icon-btn">
                          <nb-icon icon="phone-outline"></nb-icon>
                        </div>
                        <label class="label" for="nameInput">N° de téléphone</label>
                      </div>
                      <input nbInput class="user-name input-width" status="basic" type="number" max=10 min=1
                        placeholder="{{(currUser?.tel) || 'Veuillez entrer le N° tél' }}" [(ngModel)]="currUser.tel">
                    </div>
                    <div class="form-group">
                      <div class="attribute-block">
                        <div class="icon-btn">
                          <nb-icon icon="email-outline"></nb-icon>
                        </div>
                        <label class="label" for="nameInput">Email</label>
                      </div>
                      <input nbInput class="user-name input-width" status="basic" type="email" pattern=".+@globex\.com"
                        size="30" required placeholder=" {{(currUser?.email) || 'Veuillez entrer l`email'}}"
                        [(ngModel)]="currUser.email">
                    </div>
                    <div class="form-group">
                      <div class="attribute-block">
                        <div class="icon-btn">
                          <nb-icon icon="lock-outline"></nb-icon>
                        </div>
                        <label class="label" for="nameInput">Mot de passe</label>
                      </div>
                      <input nbInput class="user-name input-width" status="basic" type="text" size="30" required
                        placeholder="Entrer le nouveau mot de passe" [(ngModel)]="password">
                    </div>
                  </nb-card-body>
                </nb-tab>
                <nb-tab tabTitle="Privilèges">
                  <div class="container-privilege" [class.view-only-mode]="!flipped">
                    <div class="acces-rigth">
                      <div class="label-title">Descriptions</div>
                      <div class="label-title">Statut</div>
                    </div>
                    <div class="view-mode-notice" *ngIf="!flipped">
                      <nb-icon icon="info-outline"></nb-icon>
                      <span>Mode lecture seule. Cliquez sur "Éditer" pour modifier les autorisations.</span>
                    </div>

                    <!-- Super Administrateur -->
                    <div class="section-header">
                      <nb-icon icon="shield-outline" class="section-icon"></nb-icon>
                      <h5 class="section-title">Super Administrateur</h5>
                    </div>
                    <div class="acces-rigth insert-border">
                      <div class="rigth-label">Super Administrateur (accès à tous les menus)</div>
                      <nb-toggle [(ngModel)]="currUser.rigths.isFullRigth"></nb-toggle>
                    </div>

                    <!-- Menu Administration -->
                    <div class="section-header">
                      <nb-icon icon="settings-outline" class="section-icon"></nb-icon>
                      <h5 class="section-title">Menu Administration</h5>
                    </div>
                    <div class="acces-rigth insert-border">
                      <div class="rigth-label">Accès au menu Administration</div>
                      <nb-toggle class="toggle-width" [(ngModel)]="currUser.rigths.isAdmin"></nb-toggle>
                    </div>
                    <div class="acces-rigth insert-border">
                      <div class="rigth-label">Gérer les Utilisateurs</div>
                      <nb-toggle class="toggle-width" [(ngModel)]="currUser.rigths.canManageUsers"></nb-toggle>
                    </div>
                    <div class="acces-rigth insert-border">
                      <div class="rigth-label">Gérer les Points d'Enlèvements</div>
                      <nb-toggle class="toggle-width" [(ngModel)]="currUser.rigths.canManageStores"></nb-toggle>
                    </div>
                    <div class="acces-rigth insert-border">
                      <div class="rigth-label">Gérer les Transporteurs</div>
                      <nb-toggle class="toggle-width" [(ngModel)]="currUser.rigths.canManageCarriers"></nb-toggle>
                    </div>
                    <div class="acces-rigth insert-border">
                      <div class="rigth-label">Gérer les Inspections</div>
                      <nb-toggle class="toggle-width" [(ngModel)]="currUser.rigths.canManageInspections"></nb-toggle>
                    </div>
                    <div class="acces-rigth insert-border">
                      <div class="rigth-label">Gérer les Commandes</div>
                      <nb-toggle class="toggle-width" [(ngModel)]="currUser.rigths.canManageOrders"></nb-toggle>
                    </div>

                    <!-- Menu Gestion Enlèvements -->
                    <div class="section-header">
                      <nb-icon icon="clipboard-outline" class="section-icon"></nb-icon>
                      <h5 class="section-title">Menu Gestion Enlèvements</h5>
                    </div>
                    <div class="acces-rigth insert-border">
                      <div class="rigth-label">Accéder au menu Gestion Enlèvements</div>
                      <nb-toggle [(ngModel)]="currUser.rigths.canManageRemovals"></nb-toggle>
                    </div>
                    <div class="acces-rigth insert-border">
                      <div class="rigth-label">Accéder à l'Historique Bon</div>
                      <nb-toggle [(ngModel)]="currUser.rigths.canViewHistory"></nb-toggle>
                    </div>
                    <div class="acces-rigth insert-border">
                      <div class="rigth-label">Attribuer les Bons</div>
                      <nb-toggle [(ngModel)]="currUser?.rigths.canAllocate"></nb-toggle>
                    </div>
                    <div class="acces-rigth insert-border">
                      <div class="rigth-label">Voir les Transports Validés</div>
                      <nb-toggle [(ngModel)]="currUser.rigths.canViewTransports"></nb-toggle>
                    </div>
                    <div class="acces-rigth insert-border">
                      <div class="rigth-label">Voir les Camions Chargés</div>
                      <nb-toggle [(ngModel)]="currUser.rigths.canViewLoadedTrucks"></nb-toggle>
                    </div>
                    <div class="acces-rigth insert-border">
                      <div class="rigth-label">Voir la File d'Attente</div>
                      <nb-toggle [(ngModel)]="currUser.rigths.canViewQueue"></nb-toggle>
                    </div>
                    <div class="acces-rigth insert-border">
                      <div class="rigth-label">Créer un Enlèvement</div>
                      <nb-toggle [(ngModel)]="currUser.rigths.canRemove"></nb-toggle>
                    </div>
                    <div class="acces-rigth insert-border">
                      <div class="rigth-label">Attribuer les Quais</div>
                      <nb-toggle [(ngModel)]="currUser.rigths.canAllocateDock"></nb-toggle>
                    </div>
                    <div class="acces-rigth insert-border">
                      <div class="rigth-label">Voir les Camions en Panne</div>
                      <nb-toggle [(ngModel)]="currUser.rigths.canViewBrokenTrucks"></nb-toggle>
                    </div>
                    <div class="acces-rigth insert-border">
                      <div class="rigth-label">Voir l'Historique des Inspections</div>
                      <nb-toggle [(ngModel)]="currUser?.rigths.canViewInspections"></nb-toggle>
                    </div>

                    <!-- Menu Reporting -->
                    <div class="section-header">
                      <nb-icon icon="trending-up-outline" class="section-icon"></nb-icon>
                      <h5 class="section-title">Menu Reporting</h5>
                    </div>
                    <div class="acces-rigth insert-border">
                      <div class="rigth-label">Accès au menu Reporting</div>
                      <nb-toggle [(ngModel)]="currUser.rigths.canViewReporting"></nb-toggle>
                    </div>
                    <div class="acces-rigth insert-border">
                      <div class="rigth-label">Voir les Bons Rendus</div>
                      <nb-toggle [(ngModel)]="currUser.rigths.canViewRenderReporting"></nb-toggle>
                    </div>
                    <div class="acces-rigth insert-border">
                      <div class="rigth-label">Voir les Bons Pickup</div>
                      <nb-toggle [(ngModel)]="currUser.rigths.canViewPickupReporting"></nb-toggle>
                    </div>

                    <!-- Menu Écran d'appel -->
                    <div class="section-header">
                      <nb-icon icon="browser-outline" class="section-icon"></nb-icon>
                      <h5 class="section-title">Menu Écran d'appel</h5>
                    </div>
                    <div class="acces-rigth insert-border">
                      <div class="rigth-label">Accès au menu Écran d'appel</div>
                      <nb-toggle [(ngModel)]="currUser.rigths.canFollow"></nb-toggle>
                    </div>
                    <div class="acces-rigth insert-border">
                      <div class="rigth-label">Voir le Parking</div>
                      <nb-toggle [(ngModel)]="currUser.rigths.canViewParking"></nb-toggle>
                    </div>
                    <div class="acces-rigth insert-border">
                      <div class="rigth-label">Voir le Chargement Usine</div>
                      <nb-toggle [(ngModel)]="currUser.rigths.canViewFactoryLoading"></nb-toggle>
                    </div>

                    <!-- Autres autorisations -->
                    <div class="section-header">
                      <nb-icon icon="options-2-outline" class="section-icon"></nb-icon>
                      <h5 class="section-title">Autres autorisations</h5>
                    </div>
                    <div class="acces-rigth insert-border">
                      <div class="rigth-label">Inspecter les camions</div>
                      <nb-toggle class="toggle-width" [(ngModel)]="currUser.rigths.isInspector"></nb-toggle>
                    </div>
                    <div class="acces-rigth insert-border">
                      <div class="rigth-label">Enregistrer les camions</div>
                      <nb-toggle [(ngModel)]="currUser.rigths.canSaveTruck"></nb-toggle>
                    </div>
                    <div class="acces-rigth insert-border">
                      <div class="rigth-label">Gérer les autorisations d'enlèvement</div>
                      <nb-toggle [(ngModel)]="currUser.rigths.canManageRemovalsAuth"></nb-toggle>
                    </div>
                    <div class="acces-rigth insert-border">
                      <div class="rigth-label">Passer des commandes</div>
                      <nb-toggle [(ngModel)]="currUser.rigths.canOrder"></nb-toggle>
                    </div>

                    <!-- Accès aux usines -->
                    <div class="section-header">
                      <nb-icon icon="home-outline" class="section-icon"></nb-icon>
                      <h5 class="section-title">Accès aux usines</h5>
                    </div>
                    <div class="acces-rigth insert-border" *ngFor="let factory of factories">
                      <div class="rigth-label"> Données de {{factory | getStoreLabel}}</div>
                      <nb-toggle [(ngModel)]="currUser.rigths[factory]"
                        (checkedChange)="onToggleChange(factory , $event)"></nb-toggle>
                    </div>
                    <div class="acces-rigth insert-border">
                      <div class="rigth-label">Tous les usines</div>
                      <nb-toggle [(ngModel)]="currUser.rigths.allStore"></nb-toggle>
                    </div>

                  </div>
                </nb-tab>
              </nb-tabset>
            </nb-card>
          </nb-card-body>
          <nb-card-footer class="form--footer">
            <button nbButton ghost status="basic" (click)="back()" [disabled]="isLoading">
              <nb-icon icon="close-square-outline"></nb-icon>
              Annuler
            </button>
            <button nbButton outline status="success" (click)="updateUsers()" [disabled]="isLoading" class="btn-border">
              <nb-icon icon="save-outline"></nb-icon>
              Enregistrer
            </button>
          </nb-card-footer>
        </nb-card>
      </nb-card-back>
    </nb-flip-card>

  </div>
</ng-template>

<ng-template #addUserDialog let-data let-ref="dialogRef">
  <nb-card class="details-user-container">
    <nb-card-body class="rm-padding">

      <nb-tabset fullWidth>
        <nb-tab tabTitle="Informations générales">

          <nb-card-header class="user-card-header remove-padding">
            <label for="file">
              <div class="user-image width-img"
                nbTooltip="{{(imgResultAfterCompress) ? 'Modifier votre photo de profil' : 'Ajouter une photo de profil'}}"
                [ngStyle]="imageSrc | getImgStyle:'assets/images/account.png' ">
              </div>
            </label>
            <input type="file" id="file" style="display: none;" (change)='getFile($event)'>
          </nb-card-header>
          <nb-card-body class="overflow">
            <div class="form-group">
              <div class="attribute-block">
                <div class="icon-btn">
                  <nb-icon icon="person-outline"></nb-icon>
                </div>
                <label class="label" for="nameInput">Nom(s)</label>
              </div>
              <input nbInput class="user-name input-width" status="basic" type="text"
                placeholder="Veuillez entrer le nom" [(ngModel)]="addUser.lname">
            </div>
            <div class="form-group">
              <div class="attribute-block">
                <div class="icon-btn">
                  <nb-icon icon="person-outline"></nb-icon>
                </div>
                <label class="label" for="nameInput">Prénom(s)</label>
              </div>
              <input nbInput class="user-name input-width" status="basic" type="text"
                placeholder="Veuillez entrer le prénom" [(ngModel)]="addUser.fname">
            </div>
            <div class="form-group">
              <div class="attribute-block">
                <div class="icon-btn">
                  <nb-icon icon="home-outline"></nb-icon>
                </div>
                <label class="label" for="nameInput">Matricule</label>
              </div>
              <input nbInput class="user-name input-width" status="basic" type="text"
                placeholder="Veuillez entrer le matricule" [(ngModel)]="addUser.matricule">
            </div>
            <div class="form-group">
              <div class="attribute-block">
                <div class="icon-btn">
                  <nb-icon icon="phone-outline"></nb-icon>
                </div>
                <label class="label" for="nameInput">N° téléphone</label>
              </div>
              <input nbInput class="user-name input-width" status="basic" type="number"
                placeholder="Veuillez entrer le N° tél" min="10" max="10" [(ngModel)]="addUser.tel">
            </div>
            <div class="form-group">
              <div class="attribute-block">
                <div class="icon-btn">
                  <nb-icon icon="email-outline"></nb-icon>
                </div>
                <label class="label" for="nameInput">Email</label>
              </div>
              <input nbInput class="user-name input-width" status="basic" type="email"
                placeholder="Veuillez entrer l'email" pattern=".+@globex\.com" size="30" required
                [(ngModel)]="addUser.email">
            </div>
            <!-- <div class="form-group">
              <div class="attribute-block">
                <div class="icon-btn">
                  <nb-icon icon="email-outline"></nb-icon>
                </div>
                <label class="label" for="nameInput">Mot de passe</label>
              </div>
              <input nbInput class="user-name input-width" status="basic" type="email"
                placeholder="Veuillez entrer l'email" pattern=".+@globex\.com" size="30" required>
            </div> -->
          </nb-card-body>
          <!-- <nb-card-footer class="form--footer">
            <button nbButton ghost status="basic" (click)="ref.close()" [disabled]="isLoading">
              <nb-icon icon="close-square-outline"></nb-icon>
              Annuler
            </button>
            <button nbButton outline status="success" (click)="insertUsers()" [disabled]="isLoading" class="btn-border">
              <nb-icon icon="save-outline"></nb-icon>
              Enregistrer
            </button>
          </nb-card-footer> -->
        </nb-tab>
        <nb-tab tabTitle="Privilèges">
          <div class="acces-rigth">
            <div class="label-title">Descriptions</div>
            <div class="label-title">Statut</div>
          </div>
          <!-- Super Administrateur -->
          <div class="section-header">
            <nb-icon icon="shield-outline" class="section-icon"></nb-icon>
            <h5 class="section-title">Super Administrateur</h5>
          </div>
          <div class="acces-rigth insert-border">
            <div class="rigth-label">Super Administrateur (accès à tous les menus)</div>
            <nb-toggle [(ngModel)]="addUser?.rigths.isFullRigth"></nb-toggle>
          </div>

          <!-- Menu Administration -->
          <div class="section-header">
            <nb-icon icon="settings-outline" class="section-icon"></nb-icon>
            <h5 class="section-title">Menu Administration</h5>
          </div>
          <div class="acces-rigth insert-border">
            <div class="rigth-label">Accès au menu Administration</div>
            <nb-toggle class="toggle-width" [(ngModel)]="addUser?.rigths.isAdmin"></nb-toggle>
          </div>

          <div class="acces-rigth insert-border">
            <div class="rigth-label">Gérer les Utilisateurs</div>
            <nb-toggle class="toggle-width" [(ngModel)]="addUser?.rigths.canManageUsers"></nb-toggle>
          </div>

          <div class="acces-rigth insert-border">
            <div class="rigth-label">Gérer les Points d'Enlèvements</div>
            <nb-toggle class="toggle-width" [(ngModel)]="addUser?.rigths.canManageStores"></nb-toggle>
          </div>

          <div class="acces-rigth insert-border">
            <div class="rigth-label">Gérer les Transporteurs</div>
            <nb-toggle class="toggle-width" [(ngModel)]="addUser?.rigths.canManageCarriers"></nb-toggle>
          </div>

          <div class="acces-rigth insert-border">
            <div class="rigth-label">Gérer les Inspections</div>
            <nb-toggle class="toggle-width" [(ngModel)]="addUser?.rigths.canManageInspections"></nb-toggle>
          </div>

          <div class="acces-rigth insert-border">
            <div class="rigth-label">Gérer les Commandes</div>
            <nb-toggle class="toggle-width" [(ngModel)]="addUser?.rigths.canManageOrders"></nb-toggle>
          </div>

          <!-- Menu Gestion Enlèvements -->
          <div class="section-header">
            <nb-icon icon="clipboard-outline" class="section-icon"></nb-icon>
            <h5 class="section-title">Menu Gestion Enlèvements</h5>
          </div>
          <div class="acces-rigth insert-border">
            <div class="rigth-label">Accéder à l'Historique Bon</div>
            <nb-toggle [(ngModel)]="addUser?.rigths.canViewHistory"></nb-toggle>
          </div>

          <div class="acces-rigth insert-border">
            <div class="rigth-label">Attribuer les Bons</div>
            <nb-toggle [(ngModel)]="addUser?.rigths.canAllocate"></nb-toggle>
          </div>

          <div class="acces-rigth insert-border">
            <div class="rigth-label">Voir les Transports Validés</div>
            <nb-toggle [(ngModel)]="addUser?.rigths.canViewTransports"></nb-toggle>
          </div>

          <div class="acces-rigth insert-border">
            <div class="rigth-label">Voir les Camions Chargés</div>
            <nb-toggle [(ngModel)]="addUser?.rigths.canViewLoadedTrucks"></nb-toggle>
          </div>

          <div class="acces-rigth insert-border">
            <div class="rigth-label">Voir la File d'Attente</div>
            <nb-toggle [(ngModel)]="addUser?.rigths.canViewQueue"></nb-toggle>
          </div>

          <div class="acces-rigth insert-border">
            <div class="rigth-label">Créer un Enlèvement</div>
            <nb-toggle [(ngModel)]="addUser?.rigths.canRemove"></nb-toggle>
          </div>

          <div class="acces-rigth insert-border">
            <div class="rigth-label">Attribuer les Quais</div>
            <nb-toggle [(ngModel)]="addUser?.rigths.canAllocateDock"></nb-toggle>
          </div>

          <div class="acces-rigth insert-border">
            <div class="rigth-label">Voir les Camions en Panne</div>
            <nb-toggle [(ngModel)]="addUser?.rigths.canViewBrokenTrucks"></nb-toggle>
          </div>

          <div class="acces-rigth insert-border">
            <div class="rigth-label">Voir l'Historique des Inspections</div>
            <nb-toggle [(ngModel)]="addUser?.rigths.canViewInspections"></nb-toggle>
          </div>

          <!-- Menu Reporting -->
          <div class="section-header">
            <nb-icon icon="trending-up-outline" class="section-icon"></nb-icon>
            <h5 class="section-title">Menu Reporting</h5>
          </div>
          <div class="acces-rigth insert-border">
            <div class="rigth-label">Accès au menu Reporting</div>
            <nb-toggle [(ngModel)]="addUser?.rigths.canViewReporting"></nb-toggle>
          </div>

          <div class="acces-rigth insert-border">
            <div class="rigth-label">Voir les Bons Rendus</div>
            <nb-toggle [(ngModel)]="addUser?.rigths.canViewRenderReporting"></nb-toggle>
          </div>

          <div class="acces-rigth insert-border">
            <div class="rigth-label">Voir les Bons Pickup</div>
            <nb-toggle [(ngModel)]="addUser?.rigths.canViewPickupReporting"></nb-toggle>
          </div>

          <!-- Menu Écran d'appel -->
          <div class="section-header">
            <nb-icon icon="browser-outline" class="section-icon"></nb-icon>
            <h5 class="section-title">Menu Écran d'appel</h5>
          </div>
          <div class="acces-rigth insert-border">
            <div class="rigth-label">Accès au menu Écran d'appel</div>
            <nb-toggle [(ngModel)]="addUser?.rigths.canFollow"></nb-toggle>
          </div>

          <div class="acces-rigth insert-border">
            <div class="rigth-label">Voir le Parking</div>
            <nb-toggle [(ngModel)]="addUser?.rigths.canViewParking"></nb-toggle>
          </div>

          <div class="acces-rigth insert-border">
            <div class="rigth-label">Voir le Chargement Usine</div>
            <nb-toggle [(ngModel)]="addUser?.rigths.canViewFactoryLoading"></nb-toggle>
          </div>

          <!-- Autres autorisations -->
          <div class="section-header">
            <nb-icon icon="options-2-outline" class="section-icon"></nb-icon>
            <h5 class="section-title">Autres autorisations</h5>
          </div>
          <div class="acces-rigth insert-border">
            <div class="rigth-label">Inspecter les camions</div>
            <nb-toggle class="toggle-width" [(ngModel)]="addUser?.rigths.isInspector"></nb-toggle>
          </div>

          <div class="acces-rigth insert-border">
            <div class="rigth-label">Enregistrer les camions</div>
            <nb-toggle [(ngModel)]="addUser?.rigths.canSaveTruck"></nb-toggle>
          </div>

          <div class="acces-rigth insert-border">
            <div class="rigth-label">Gérer les autorisations d'enlèvement</div>
            <nb-toggle [(ngModel)]="addUser?.rigths.canManageRemovalsAuth"></nb-toggle>
          </div>

          <div class="acces-rigth insert-border">
            <div class="rigth-label">Passer des commandes</div>
            <nb-toggle [(ngModel)]="addUser?.rigths.canOrder"></nb-toggle>
          </div>

          <!-- Accès aux usines -->
          <div class="section-header">
            <nb-icon icon="home-outline" class="section-icon"></nb-icon>
            <h5 class="section-title">Accès aux usines</h5>
          </div>
          <div class="acces-rigth insert-border">
            <div class="rigth-label">Accès à tous les magasins</div>
            <nb-toggle [(ngModel)]="addUser?.rigths.allStore"></nb-toggle>
          </div>
          <!-- <nb-card-footer class="form--footer">
            <button nbButton ghost status="basic" (click)="ref.close()" [disabled]="isLoading">
              <nb-icon icon="close-square-outline"></nb-icon>
              Annuler
            </button>
            <button nbButton outline status="success" (click)="insertUsers()" [disabled]="isLoading" class="btn-border">
              <nb-icon icon="save-outline"></nb-icon>
              Enregistrer
            </button>
          </nb-card-footer> -->
        </nb-tab>
      </nb-tabset>

    </nb-card-body>
    <nb-card-footer class="form--footer">
      <button nbButton ghost status="basic" (click)="ref.close()" [disabled]="isLoading">
        <nb-icon icon="close-square-outline"></nb-icon>
        Annuler
      </button>
      <button nbButton outline status="success" (click)="insertUsers()" [disabled]="isLoading" class="btn-border">
        <nb-icon icon="save-outline"></nb-icon>
        Enregistrer
      </button>
    </nb-card-footer>
  </nb-card>
</ng-template>

<ng-template #confirmDleteUserDialog let-data let-ref="dialogRef">
  <nb-card accent="danger" [nbSpinner]="isLoading" nbSpinnerStatus="primary" class="delete-dialog">
    <nb-card-header class="form--header">Supprimer</nb-card-header>

    <nb-card-body>
      <p class="text"> Vous êtes sur le point de supprimer cet utilisateur<span class="label user-full-name">
          {{currUser.fullname}}</span></p>
    </nb-card-body>

    <nb-card-footer class="footer">
      <button nbButton class="btn-contain cancel" outline status="basic" (click)="ref.close()" [disabled]="isLoading">
        <nb-icon icon="close-square-outline" [options]="{ animation: { type: 'zoom' } }" class="remove-icon">
        </nb-icon>
        Annuler
      </button>
      <button nbButton outline status="danger" class="btn-contain" [disabled]="isLoading" (click)="deleteUser()">
        <nb-icon icon="trash-2-outline" [options]="{ animation: { type: 'zoom' } }" class="remove-icon">
        </nb-icon>
        Supprimer
      </button>
    </nb-card-footer>
  </nb-card>
</ng-template>


<ng-template #confirmDeactivateUserDialog let-data let-ref="dialogRef">
  <nb-card accent="danger" [nbSpinner]="isLoading" nbSpinnerStatus="primary" class="delete-dialog">
    <nb-card-header class="form--header">Désactivation</nb-card-header>

    <nb-card-body>
      <p class="text"> Vous êtes sur le point de desactiver cet utilisateur<span class="label user-full-name">
          {{currUser.fullname}}</span></p>
    </nb-card-body>

    <nb-card-footer class="footer">
      <button nbButton class="btn-contain cancel" outline status="basic" (click)="ref.close()" [disabled]="isLoading">
        <nb-icon icon="close-square-outline" [options]="{ animation: { type: 'zoom' } }" class="remove-icon">
        </nb-icon>
        Annuler
      </button>
      <button *ngIf="currUser.enable" nbButton outline status="danger" class="btn-contain" [disabled]="isLoading"
        (click)="deactivateUsers()">
        <nb-icon icon="eye-off-outline" [options]="{ animation: { type: 'zoom' } }" class="remove-icon">
        </nb-icon>
        Désactiver
      </button>
      <button *ngIf="!currUser.enable" nbButton outline status="success" class="btn-contain" [disabled]="isLoading"
        (click)="deactivateUsers()">
        <nb-icon icon="eye-outline" [options]="{ animation: { type: 'zoom' } }" class="remove-icon">
        </nb-icon>
        Réactiver
      </button>
    </nb-card-footer>
  </nb-card>
</ng-template>
