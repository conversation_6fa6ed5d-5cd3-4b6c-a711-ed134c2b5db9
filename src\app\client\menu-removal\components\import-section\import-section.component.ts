import { Component, EventEmitter, Input, OnInit, Output, TemplateRef } from '@angular/core';
import { NbDialogRef, NbDialogService, NbToastrService } from '@nebular/theme';
import { RenderType } from 'src/app/shared/enums/renderType.enum';
import { CartItem } from 'src/app/shared/models/cart.model';
import { Order, OrderStatusDelivery } from 'src/app/shared/models/order';
import { CommonService } from 'src/app/shared/services/common.service';
import { StorageService } from 'src/app/shared/services/storage.service';

@Component({
  selector: 'clw-import-section',
  templateUrl: './import-section.component.html',
  styles: [
  ]
})
export class ImportSectionComponent implements OnInit {

  @Input() partitonOrders: Partial<Order[]>;
  order: Order;
  dialogRef: NbDialogRef<any>;
  avaibleQdty: number;
  groupedOrders: CartItem[] = [];
  currentItem: CartItem = {
    quantity: 0,
    quantityDelivery: 0,
    quantityShipped: 0
  };
  savedOrders: Order[] = [];


  constructor(private dialogSrv: NbDialogService,
    public commonService: CommonService,
    private storageSrv: StorageService,
    private toastSrv: NbToastrService,
    private toastrSvr: NbToastrService,) {

  }



  onChange() {
    console.log('Modification de la partition demandée');
  }

  ngOnInit(): void {

  }

  getStatusText(status: OrderStatusDelivery): string {
    switch (status) {
      case 200: return 'En cours';
      case 300: return 'Terminé';
      case 100: return 'En attente';
      default: return 'Inconnu';
    }
  }

  // getFullName(user: { firstName?: string; lastName?: string } | undefined): string {
  //   return `${user?.firstName || ''} ${user?.lastName || ''}`.trim();
  // }

  getLabel(items: CartItem[]) {
    return items?.map(item => item.product?.label + ' ' + '(' + item?.packaging.label + ')').join(', ')
  }

  getQdty(item: CartItem[]) {
    return item?.reduce((acc, item) => acc + item?.quantity, 0);
  }

  getQdtyDelivery(items: CartItem[]) {
    if (!items || !Array.isArray(items)) {return 0;}

    return items.reduce((acc, item) => {
      if (!item) {return acc;}
      const quantityShipped = item.quantityShipped ?? 0;
      return acc + quantityShipped; }, 0);
  }
  getStatusRender(status: RenderType): string {
    switch (status) {
      case 1: return 'Pickup';
      case 2: return 'Rendu';
      default: return 'Inconnu';
    }
  }

  modifyPartition(dialog: TemplateRef<any>, order: Order): void {

    this.order = order;
    const res = order?.cart?.items?.filter(item => item.quantityShipped > 0);

    if (res?.length > 0) this.groupedOrders = order?.cart?.items;

    this.dialogRef = this.dialogSrv.open(dialog);

  }

  onGetAvaibleQdty(selectedValue: CartItem): void {
    if (selectedValue) {
      const quantity = selectedValue.quantity ?? 0;

      const quantityDelivery = selectedValue.quantityDelivery ?? 0;
      const quantityShipped = selectedValue.quantityShipped ?? 0;
      const removeQdty = quantityDelivery + quantityShipped;

      this.avaibleQdty = quantity - removeQdty;
      this.currentItem = selectedValue;
    } else {
      this.avaibleQdty = 0;
    }
  }

  addNewQdty() {

    if (!this.currentItem?.quantityShipped) {
      this.toastSrv.danger('Veuillez entrer une quantité', 'Erreur');
      return;
    }
    if (this.currentItem?.quantityShipped > this.avaibleQdty) {
      this.toastSrv.danger('La quantité est supérieure à la quantité disponible', 'Erreur');
      return;
    }
    if (this.currentItem?.quantityShipped < 0) {
      this.toastSrv.danger('La quantité ne peut pas être négative', 'Erreur');
      return;
    }
    this.order?.cart?.items?.filter(item => (item.product?.erpRef === this.currentItem.product?.erpRef) && (item?.packaging?._id === this.currentItem?.packaging?._id)).forEach(item => {
      item.quantityShipped = this.currentItem.quantityShipped;
    }
    )
    this.onGetAvaibleQdty(this.currentItem)
    this.addPartition()
    this.toastSrv.success('La quantité a été mise à jour avec succès', 'Succès');
    this.currentItem = {
      quantity: 0,
      quantityDelivery: 0,
      quantityShipped: 0
    };



  }
  addPartition() {
    const existingIndex = this.groupedOrders.findIndex(
      (item) => item.product?.erpRef === this.currentItem.product?.erpRef && item.packaging?._id === this.currentItem.packaging?._id
    );

    if (existingIndex !== -1) {
      this.groupedOrders[existingIndex].quantityShipped = this.currentItem.quantityShipped;

    } else {
      this.groupedOrders.push(this.currentItem);
    }
  }
  modifyQdty(item: CartItem) {
    const existingIndex = this.groupedOrders.findIndex(
      (item) => item.product?.erpRef === this.currentItem.product?.erpRef && item.packaging?._id === this.currentItem.packaging?._id
    );
    if (existingIndex !== -1) {
      this.groupedOrders.splice(existingIndex, 1);
    }

  }
  save(): void {
    let existingIndex = -1;

    // Vérifier si l'ordre a une référence
    if (!this.order?.appReference) {
      this.toastSrv.danger('Référence de commande manquante', 'Erreur');
      return;
    }

    // Rechercher l'index de l'élément existant si la liste n'est pas vide
    if (this.commonService.ordersGroup.length > 0) {
      existingIndex = this.commonService.ordersGroup.findIndex(item => item?.appReference === this.order.appReference);
    }

    // Vérifier la compatibilité des types de livraison avant d'ajouter une nouvelle commande
    const currentRenderType = this.order?.cart?.renderType;

    // Ne vérifier les types de livraison que si nous ajoutons une nouvelle commande
    if (existingIndex === -1 && this.commonService.ordersGroup.length > 0) {
      for (const order of this.commonService.ordersGroup) {
        if (order?.cart?.renderType !== currentRenderType) {
          this.toastSrv.danger(
            'Les commandes groupées doivent avoir le même type de livraison. Veuillez regrouper uniquement des commandes en "rendu" ou en "pickup".',
            'Incompatibilité détectée'
          );
          return;
        }
      }
    }

    // Mettre à jour l'élément existant ou ajouter un nouvel élément
    if (existingIndex !== -1) {
      this.commonService.ordersGroup[existingIndex] = this.order;
      this.toastrSvr.info('La commande a été mise à jour dans le groupe', 'Mise à jour');
    } else {
      this.commonService.ordersGroup.push(this.order);
      this.toastrSvr.success('La commande a été ajoutée au groupe avec succès', 'Succès');
    }

    this.groupedOrders = [];
    this.dialogRef.close();
  }
  deletePartition(order: Order) {
    const existingIndex = this.commonService.ordersGroup.findIndex(
      (item) => item?.appReference === order?.appReference
    );
    if (existingIndex !== -1) {
      this.commonService.ordersGroup[existingIndex].cart.items.forEach(item => { item.quantityShipped = 0; });
      this.commonService.ordersGroup.splice(existingIndex, 1);
      this.toastSrv.success('La commande a été supprimée du groupe avec succès', 'Succès');
    }
  }

}
