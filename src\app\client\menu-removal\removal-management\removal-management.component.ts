import { Component, OnInit, Pipe, PipeTransform, TemplateRef, ViewChild, ViewEncapsulation } from '@angular/core';
import { Router } from '@angular/router';
import { NbDialogRef, NbDialogService, NbToastrService } from '@nebular/theme';
import { of } from 'rxjs';
import { CofirmDialogComponent } from './cofirm-dialog/cofirm-dialog.component';
import { RemovalService } from './removal.service';
import * as moment from 'moment';
import { CarrierService } from 'src/app/admin/carrier/carriers.service';
import { GroupesOrders } from 'src/app/shared/models/authorization-removal';
import { Order } from 'src/app/shared/models/order';
import { PaginationConfig } from 'src/app/shared/services/pagination.service';
import { CommonService } from 'src/app/shared/services/common.service';

@Pipe({
  name: 'status'
})
export class StatusRemovalPipe implements PipeTransform {

  transform(status: number, isColor: boolean): string {
    const colors = { 100: 'basic', 200: 'success', 300: 'warning', 400: 'primary' };
    const statuses = { 100: 'Non assignée', 200: 'Assignée', 300: 'En attente', 400: 'Traitée' };
    if (isColor) { return colors[status]; }
    return statuses[status];
  }
}

@Component({
  selector: 'clw-removal-management',
  templateUrl: './removal-management.component.html',
  styles: [
  ],
  encapsulation: ViewEncapsulation.None
})
export class RemovalManagementComponent implements OnInit {

  aeRemovals: any[];
  carriersAe: any;
  carriers: any[];
  removals: any[];
  drivers: any[];
  trucks: any[];
  carriersTab: any[];
  selectedAE: any;
  selectedCarrier: any;
  selectedTruck: any;
  selectedDriver: any;

  soldto: any;

  dialog: NbDialogRef<any>;
  editDialog: NbDialogRef<any>;

  isConfirmLoading: boolean;
  isLoading: boolean;
  iscarrierLoading: boolean;

  carrierFilter: any;
  filteredCarrier$: any;
  carriersLabels = [];

  aeNumberFilter: any;
  filteredAeNumbers$: any;
  aeNumbers = [];


  offset;
  limit = 20;
  endIndex = 20;
  startIndex = 1;
  total: number;

  status = 100;
  loadNumber: any;
  label: any;
  startDate = new Date(new Date().getFullYear(), 0, 1);
  endDate = new Date(new Date().getFullYear(), 11, 31);


  filteredProduct$: any;
  productFilter: any;
  productsLabels = ['AMIGO', 'LA CAMEROUNAISE', 'COLOMBE', 'PELICAN', 'INDUSTRIELLE'];

  legends = [
    { status: 300, label: 'En attente validation' },
    // { status: 400, label: 'Traitée' },
    { status: 100, label: 'Non assignée' },
    { status: 200, label: 'Assignée' },
  ];

  paginationConfig: PaginationConfig = {
    currentPage: 1,
    itemsPerPage: 10,
    totalItems: 0,
    maxSize: 10
  };

  constructor(
    private router: Router,
    private dialogSrv: NbDialogService,
    private removalSrv: RemovalService,
    private toastSrv: NbToastrService,
    private carrierSrv: CarrierService,
    private commonService: CommonService
  ) { }
  //TODO get trucks & drivers by carriers
  async ngOnInit(): Promise<any> {
    await this.getAuthorizationRemoval();
    await this.getCarriersAuthorizationRemoval();
    const { data: trucks } = await this.carrierSrv.getTrucks();
    this.trucks = trucks;
    const { data } = await this.carrierSrv.getAllCarriers({});
    this.carriers = data;
    await this.getDataForFilter();
    this.carriers.forEach((elt: any) => this.carriersLabels.push(elt?.label));
    const { data: drivers } = await this.carrierSrv.getDrivers();
    this.drivers = drivers;
    setInterval(async () => {
      await this.getAuthorizationRemoval();
      await this.getDataForFilter();
    }, 300000);
  }

  refresh(): void {
    location.reload()
  }

  getConcatenatedLabels(groupedOrders: GroupesOrders): string {
    return groupedOrders?.map((order: Order) =>
      order?.cart?.items
        .filter(item => item?.quantityDelivery > 0)
        .map(item => `${item?.product?.label} (${item?.packaging?.label})`)
        .join(', ')
    ).join(', ') || 'N/A';
  }

  getQuantityDelivery(groupOders: GroupesOrders): number {
    return groupOders.map((order: Order) => order?.cart?.items.reduce((total, cartElement) => total + ((cartElement?.quantityDelivery * cartElement?.packaging.unit?.value) / 1000), 0)).reduce((sum, quantity) => sum + quantity, 0);
  }
  async onAeChange(value: any): Promise<any> {
    if (!value) { return this.aeRemovals = this.removals; }
    this.aeRemovals = this.removals;
    this.aeRemovals = this.aeRemovals.filter(elt => elt.LoadNumber.toLowerCase().includes(value.toLowerCase()));
  }

  async clientChange(event: any): Promise<any> {
    if (event?.soldto) {
      this.soldto = event.soldto
      await this.getAuthorizationRemoval()
    }
  }

  async onProductChange(value: any): Promise<any> {
    if (value) {
      this.productFilter = value;
      await this.getAuthorizationRemoval();
    }
  }

  async onCarrierChange(value: any): Promise<any> {
    if (!value) { return this.carriersAe = this.carriersTab; }
    this.carriersAe = this.carriersTab;
    this.carriersAe = this.carriersAe.filter(elt => elt.label.toLowerCase().includes(value.toLowerCase()));

  }

  async previousPage(): Promise<any> {
    this.offset -= 1;
    if (this.offset <= 0) { this.offset; }
    this.startIndex = (this.offset - 1) * this.limit;
    if (this.startIndex === 0) { this.startIndex = 1; }
    this.endIndex = this.offset * this.limit;
    await this.getAuthorizationRemoval();
  }
  async nextPage(): Promise<any> {
    if (this.endIndex >= this.total) {
      this.endIndex = this.total;
      return;
    }
    this.offset += 1;
    this.startIndex = (this.offset - 1) * this.limit;
    this.endIndex = this.offset * this.limit;
    if (this.startIndex <= 0) { this.startIndex = 1; }
    await this.getAuthorizationRemoval();
  }

  openEditDialog(dialog: TemplateRef<any>, ae: any, isAe?: boolean): any {
    this.selectedAE = ae;
    this.selectedCarrier = ae.carrier;
    this.selectedTruck = ae?.carrier?.truck;
    this.selectedDriver = ae?.carrier?.driver;
    if (this.selectedAE?.status === 200) { return this.toastSrv.warning(`Cette AE a déjà été validée par un transporteur`, `Déjà validée`); }
    this.editDialog = this.dialogSrv.open(dialog, {})
    this.editDialog.onClose.subscribe(async (result) => {
      this.selectedAE = null;
      if (!result) { return; }
      await this.getAuthorizationRemoval();
      await this.getCarriersAuthorizationRemoval();
    })
  }

  async getAuthorizationRemoval(): Promise<any> {
    const options = {
      offset: this.paginationConfig.currentPage,
      limit: this.paginationConfig.itemsPerPage,
      status: this.status,
      product: this.productFilter,
      LoadNumber: parseInt(this.aeNumberFilter),
      enable: true,
      soldto: this.soldto,
      startDate: moment(this.startDate).format('MM-DD-YYYY'),
      endDate: moment(this.endDate).format('MM-DD-YYYY'),
      FreightHandlingCode: 'A0',
      way: 1,
      sort: '_id'
    }
    this.isLoading = true;
    try {
      const { data, count } = await this.removalSrv.getAuthorizationRemoval(options);
      this.aeRemovals = [...data];
      this.paginationConfig.totalItems = count;
      this.total = count;
      this.paginationConfig = { ...this.paginationConfig };
    } catch (error) {
      this.toastSrv.danger(`Erreur de connexion internet`, `Echec operation`)
      return error;
    } finally { this.isLoading = false; }
  }

  async reset() {
    this.productFilter = '';
    this.aeNumberFilter = '';
    this.startDate = new Date(new Date().getFullYear(), 0, 1);
    this.endDate = new Date(new Date().getFullYear(), 11, 31);
    this.paginationConfig.currentPage = 1;
    await this.getAuthorizationRemoval();
    await this.getDataForFilter();
  }

  async getDataForFilter(): Promise<void> {
    const { dataLoadNumber } = await this.removalSrv.getFilterData({
      keyForFilters: ['LoadNumber'],
      status: this.status,
      product: this.productFilter,
      LoadNumber: parseInt(this.aeNumberFilter),
      enable: true,
      soldto: this.soldto,
      startDate: moment(this.startDate).format('MM-DD-YYYY'),
      endDate: moment(this.endDate).format('MM-DD-YYYY'),
      FreightHandlingCode: 'A0'
    },);

    this.aeNumbers = dataLoadNumber ?? [];
    this.filteredAeNumbers$ = of(this.aeNumbers);
  }

  async getCarriersAuthorizationRemoval(): Promise<any> {

    this.iscarrierLoading = true;
    try {
      const { data } = await this.removalSrv.getCarrierAuthorizationRemoval();
      this.carriersAe = data;
      this.carriersTab = [...this.carriersAe]
    } catch (error) {
      console.log(error);
      return this.toastSrv.danger(`Erreur de connexion internet`, `Echec operation`)
    } finally { this.iscarrierLoading = false; }
  }

  async editAe(): Promise<any> {
    this.selectedCarrier = this.carriers.find((elt: any) => elt.label === this.carrierFilter)
    if (!this.selectedCarrier) {
      return this.toastSrv.warning(`Veuillez remplir tous les champs`, `Données manquantes`);
    }
    const carrier = this.selectedCarrier
    this.isConfirmLoading
    try {
      const id = this.selectedAE?._id || this.selectedAE?.removalId;
      const data = { carrier, status: 200 }
      await this.removalSrv.updateCarrierAuthorizationRemoval(id, data);
      this.editDialog.close(true);
    } catch (error) {
      return this.toastSrv.danger(`Erreur lors de l'édition de l'authorisation d'enlevement`, `Echec édition`)
    } finally { this.isConfirmLoading = false; this.selectedAE = null }
  }

  goTo() { this.router.navigate(['client/order/list']) }

  async moveTo(event: any, index?: number, carrier?: any): Promise<any> {
    this.selectedAE = { ...event.previousContainer.data[event.previousIndex] };

    // s'applique lorsqu'on assigne AE a un transporteur
    if (event.previousContainer.id === 'aeList' && event.container.id === 'aeAttribute') {
      this.removalSrv.data = { label: carrier?.label, oldStatus: 100, newStatus: 300, LoadNumber: this.selectedAE?.LoadNumber, PromisedDeliveryDate: this.selectedAE?.PromisedDeliveryDate, DateRequestedShipment: this.selectedAE?.DateRequestedShipment };
      this.openConfirmDialog(this.selectedAE?._id, { status: 300, "dates.assigned": moment().valueOf(), carrier });
    }

    // s'applique lorsqu'on retire AE a un transporteur
    if (event.previousContainer.id === 'aeAttribute' && event.container.id === 'aeList') {
      this.removalSrv.data = { label: this.selectedAE?.carrier?.label, oldStatus: 300, newStatus: 100, LoadNumber: this.selectedAE?.LoadNumber };
      if (this.selectedAE.status === 200) { return this.toastSrv.warning(`Cette AE a déjà été validée par un transporteur`, `Déjà validée`); }
      this.openConfirmDialog(this.selectedAE?.removalId, { status: 100, carrier: { label: this.selectedAE.carrier.label, email: this.selectedAE.carrier.email } });
    }

    // s'applique lorsqu'on deplace d'un transporteur a un autre
    if (event.container.id === 'aeAttribute' && event.previousContainer.id === 'aeAttribute') {
      this.removalSrv.data = { label: this.selectedAE?.carrier?.label, oldStatus: 300, newStatus: 300, LoadNumber: this.selectedAE?.LoadNumber, newCarrierLabel: carrier?.label };

      if (this.selectedAE?.carrier?._id === carrier?._id) { return }
      if (this.selectedAE.status === 200) { return this.toastSrv.warning(`Cette AE a déjà été validée par un transporteur`, `Déjà validée`); }
      this.openConfirmDialog(this.selectedAE?.removalId, { status: 300, carrier });

    }

  }

  openConfirmDialog(id: string, updateData: any) {
    this.dialog = this.dialogSrv.open(CofirmDialogComponent, {})
    this.dialog.onClose.subscribe(async (result: boolean): Promise<any> => {
      if (!result) { return; }
      if (result) {
        this.isLoading = true;
        try {

          const options = {
            removalDate: this.removalSrv.date,
            shippement: this.removalSrv.shippement,
            ...updateData
          }

          await this.removalSrv.updateCarrierAuthorizationRemoval(id, options);
          this.toastSrv.success(`Authorisation d'enlèvement déplacée avec succès`, 'Opération réussie')
          await this.getAuthorizationRemoval();
          await this.getCarriersAuthorizationRemoval();
        } catch (error) {
          return this.toastSrv.danger(`une erreur c'est produite lors de l'assignation de l'AE`, 'Echec assignation')
        } finally { this.isLoading = false; }
      }
    })
  }

  getBackgroungColor(status: number): any {
    const colors = { 100: 'rgb(151, 156, 164)', 200: '#00d68f', 300: '#ffaa00' };
    const color = colors[status] || '#edf1f7'
    return {
      "background-color": `${color}`,
    }
  }

  getLegendStyle(status: number): any {
    const colors = { 100: 'rgb(151, 156, 164)', 200: '#00d68f', 300: '#ffaa00', 400: '#0B305C' };
    const color = colors[status] || '#edf1f7'
    return {
      "background-color": `${color}`,
    }
  }
  //carrier autocomplete section
  private filterCarrier(value: string): string[] {
    const filterValue = value?.toLowerCase();
    return this.carriersLabels?.filter(optionValue => optionValue?.toLowerCase().includes(filterValue)).sort((a, b) => {
      return a.toString().toLowerCase().indexOf(filterValue?.toLowerCase()) - b.toString().toLowerCase().indexOf(filterValue?.toLowerCase());
    });;
  }

  onModelCarrierChange(value: string): void { this.filteredCarrier$ = of(this.filterCarrier(value)); }

  async onSelectionCarrierChange($event: any): Promise<void> {
    this.carrierFilter = $event;
    this.filteredCarrier$ = of(this.carriersLabels);
  }

  //filter by products section
  private filterProduct(value: string): string[] {
    const filterValue = value.toLowerCase();
    return this.productsLabels.filter(optionValue => optionValue.toLowerCase().includes(filterValue));
  }

  onModelProductChange(value: string): void { this.filteredProduct$ = of(this.filterProduct(value)); }

  async onSelectionProductChange($event: any): Promise<void> {
    if ($event) {
      this.productFilter = $event;
      await this.getAuthorizationRemoval();
      this.filteredProduct$ = of(this.productsLabels);
    }
  }

  //filter by ae number section
  private filterLoadNumber(value: string): string[] {
    const filterValue = value;
    return this.aeNumbers.filter(optionValue => optionValue?.toString()?.toLowerCase()?.includes(filterValue)).sort((a, b) => {
      return a.toString().toLowerCase().indexOf(filterValue.toLowerCase()) - b.toString().toLowerCase().indexOf(filterValue.toLowerCase());
    });;
  }

  onModelLoadNumberChange(value: string): void {
    if (value)
      this.filteredAeNumbers$ = of(this.filterLoadNumber(value.toString()));
  }


  async onSelectionLoadNumberChange($event: any): Promise<void> {
    if ($event) {
      this.aeNumberFilter = $event;
      await this.getAuthorizationRemoval();
      this.filteredAeNumbers$ = of(this.aeNumbers);
    }
  }

  async onModelDateChange(event: any): Promise<any> {
    if (moment(this.endDate).valueOf() < moment(this.startDate).valueOf())
      return this.toastSrv.danger('la date de fin est inférieure à la date de début', 'Erreurs Données!');
    await this.getAuthorizationRemoval();
    await this.getDataForFilter();
  }

  async search() {
    await this.getAuthorizationRemoval();
    await this.getDataForFilter();
  }

  onPageChange(page: number): void {
    if (page !== this.paginationConfig.currentPage) {
      this.paginationConfig.currentPage = page;
      this.getAuthorizationRemoval();
    }
  }

  onItemsPerPageChange(itemsPerPage: number): void {
    if (itemsPerPage !== this.paginationConfig.itemsPerPage) {
      this.paginationConfig.itemsPerPage = itemsPerPage;
      this.paginationConfig.currentPage = 1;
      this.getAuthorizationRemoval();
    }
  }

  async downloadAePdf(removal: any): Promise<void> {
    try {
      this.isLoading = true;
      const response = await this.removalSrv.downloadPdf(removal?.LoadNumber);
      const blob = new Blob([response], { type: 'application/pdf' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `AE_${removal.LoadNumber}.pdf`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      this.toastSrv.success(`Le bon N°${removal.LoadNumber} a été téléchargé avec succès`, 'Téléchargement réussi');
    } catch (error) {
      console.error('Erreur lors du téléchargement du PDF:', error);
      this.toastSrv.danger('Erreur lors du téléchargement du PDF', 'Erreur');
    } finally { this.isLoading = false; }
  }
}

