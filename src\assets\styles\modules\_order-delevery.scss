@import "@nebular/theme/styles/theming";
@import "@nebular/theme/styles/themes/default";

.delivery-orders {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  align-items: center;
  justify-content: center;
  margin-bottom: 2%;
  /* Toujours une marge de 3% en bas */
  height: calc(100vh - 3% - 50px);

  .header {
    margin: 0px !important;
    padding: 0px !important;

    h1 {
      margin: 0px !important;
      padding: 0px !important;
    }
  }


  @media (max-width: 991px) {
    padding: 64px 20px;
  }

  &__content {
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    gap: 16px;

    .cart-container {
      width: 52%;
      display: flex;
      gap: 11px;
      flex-direction: column;

      .detail {
        display: flex;
        height: calc(72vh - 14px);
        overflow: auto;
        flex-direction: column;


        .empty-list-partition {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          height: 100%;
          text-align: center;
          color: #6c757d;

          p {
            color: #8E8E93;
            font-weight: 500;
            font-size: 15px;

          }

          img {
            width: 50rem;
            height: 18rem;
          }
        }


      }

      .cart {
        width: 100%;
        display: flex;
        margin-bottom: 10px;
        flex-direction: column;
        overflow-y: auto;
        padding-right: 10px;


      }

    }

    .recap-order {
      width: 42%;
      display: flex;
      flex-direction: column;
      gap: 2em;

      .order-item {
        margin-top: 14px;
        gap: 1rem;
        display: flex;
        flex-direction: column;
        height: calc(70vh - 14px);
        padding: 10px;
        background: #FAFAFA;
        border-radius: 10px;
        border: 2px dotted #AEAEB2;
        overflow-y: auto;
      }

      clw-import-section {
        width: 100%;
      }



      .footer {
        display: flex;
        padding-bottom: 5px;
        justify-content: center;
        width: 100%;

        button {
          width: 92%;
        }

        .split-btn {
          transition: all 0.3s ease;
        }

        .split-btn.disabled-btn {
          background-color: #CBCBCD !important;
          border-color: #CBCBCD !important;
          color: #ffffff;
          cursor: not-allowed;
        }

      }


    }
  }

  clw-header-delivery {
    width: 100%;
  }

}