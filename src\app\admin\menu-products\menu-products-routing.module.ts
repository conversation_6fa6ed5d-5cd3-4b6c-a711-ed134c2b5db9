import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

const routes: Routes = [
  {
    path: '', loadChildren: () => import('./products/products.module').then(m => m.ProductsModule)
  },
  {
    path: 'category', loadChildren: () => import('./category/category.module').then(m => m.CategoryModule)
  },
  {
    path: 'packaging', loadChildren: () => import('./packaging/packaging.module').then(m => m.PackagingModule)
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class MenuProductsRoutingModule { }
