.filter-area {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 20px 0;
  padding: 15px;
  background-color: #f7f9fc;
  border-radius: 8px;

  .left-area {
    display: flex;
    align-items: center;
    gap: 20px;
    flex-wrap: wrap;
    width: 80%;

    .filter-label {
      font-size: 16px;
      font-weight: 500;
      color: #2d3e50;
    }

    .filter-group {
      display: flex;
      align-items: center;
      gap: 10px;

      input {
        width: 200px;
        height: 40px;
        border: 1px solid #e4e9f2;
        border-radius: 4px;
        padding: 0 15px;
        font-size: 14px;
        transition: all 0.3s ease;

        &:focus {
          border-color: #3366ff;
          box-shadow: 0 0 0 2px rgba(51, 102, 255, 0.1);
        }

        &::placeholder {
          color: #8f9bb3;
        }
      }
    }
  }

  .right-area {
    .search-btn {
      height: 40px;
      padding: 0 20px;
      display: flex;
      align-items: center;
      gap: 8px;
      font-weight: 500;
    }
  }
} 


.order-list-container {
  margin-left: 2%;
  margin-right: 6%;
  .order-container {
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    .label-historic-order {
      @include v-center;
      margin-top: 15px;
      h6 {
        font-family: $font-regular;
        font-size: 18px;
        margin: 0 !important;
      }
      .btn-background{
        background-color: #edf1f7;
        padding:5px;
      }
    }

    .scrool-style{
      overflow-y: auto;
      height: 60vh;
    }
  }
}


