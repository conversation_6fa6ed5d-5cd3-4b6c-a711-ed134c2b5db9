<div class="common-form-container reporting-container">
  <div class="header">
    <nb-icon icon="undo-outline" [options]="{ animation: { type: 'zoom' } }" class="remove-icon" status="primary">
    </nb-icon>
    <h1 class="title">Reporting sur les enlèvements</h1>
  </div>

  <header class="filter-container">
    <div class="left-block">
      <span class="filter-label">Filtrer par</span>
      <!-- <div class="filter-elt select">
                <nb-select status="success" size="small" fullWidth  placeholder="Test">
                    <nb-option>
                    </nb-option>
                </nb-select>
            </div> -->
      <div class="filter-elt input date">
        <input nbInput [nbDatepicker]="datePickerStart" [(ngModel)]="startDate" status="success" size="small"
          placeholder="Début">
        <nb-datepicker #datePickerStart>
        </nb-datepicker>
      </div>

      <div class="filter-elt input date">
        <input nbInput id="test" [nbDatepicker]="datepickerEnd" [(ngModel)]="endDate" status="success" size="small"
          placeholder="Fin">
        <nb-datepicker #datepickerEnd></nb-datepicker>
      </div>

      <div class="filter-elt input date">
        <nb-select class="input-width" status='primary' size="small" [(ngModel)]="orderType"
          placeholder="Type de Bon (ST/SO)">
          <!-- <nb-option *ngFor="let type of orderTypes">{{type}}</nb-option> -->
          <nb-option value="SO">SO</nb-option>
          <nb-option value="ST">ST</nb-option>
          <!-- <nb-option value="">Type de Bon (ST/SO)</nb-option> -->
        </nb-select>
      </div>

    </div>
    <!-- <div class="select-inputs"> -->

    <button nbButton status="basic" size="small" class="search-btn" (click)="refresh()">
      <nb-icon icon="refresh-outline" [options]="{ animation: { type: 'zoom' } }"></nb-icon>
      Réinitialiser
    </button>
    <button nbButton status="success" size="small" class="search-btn" (click)="filterStat()">
      <nb-icon icon="search-outline" [options]="{ animation: { type: 'zoom' } }"></nb-icon>
      Appliquer le filtre
    </button>
    <button nbButton status="success" size="small" class="search-btn" (click)='openExportInspection(exportDialog)'>
      <nb-icon icon="refresh-outline" [options]="{ animation: { type: 'zoom' } }"></nb-icon>
      Exporter
    </button>
  </header>

  <div class="reporting-row">
    <nb-card>
      <nb-card-body>
        <nb-tabset>
          <nb-tab tabTitle="infomation Generale" class="general">
            <div class="reporting row">
              <div class="tile col-1 ae-charge">
                <clw-tile-inspection></clw-tile-inspection>
              </div>
              <div class="tile col-3">
                <clw-tile-numbers></clw-tile-numbers>
              </div>
            </div>
            <div class="reporting height">
              <div class="tile col-1 ae-charge">
                <clw-tile-times></clw-tile-times>
              </div>
            </div>
            <div class="reporting row line2">
              <div class="tile col-2">
                <clw-tile-chart-store></clw-tile-chart-store>
              </div>
              <div class="tile col-2">
                <clw-tile-chart-product></clw-tile-chart-product>
              </div>
            </div>
          </nb-tab>
          <nb-tab tabTitle="Informations des Bons">

            <div class="reporting row">
              <div class="tile col-1">
                <clw-tile-aes-render></clw-tile-aes-render>
              </div>
              <!-- <div class="tile col-1">
                  <clw-tile-aes-pickup></clw-tile-aes-pickup>
                </div> -->
              <!-- <div class="tile col-2">
                  <clw-tile-inspection-ratio></clw-tile-inspection-ratio>
                </div> -->
            </div>

            <div class="reporting">
              <div class="row line2">
                <clw-tile-chart-removals></clw-tile-chart-removals>
              </div>
            </div>
          </nb-tab>
          <nb-tab tabTitle="Classement des Transporteurs">
            <div class="reporting row">
              <div class="tile col-3">
                <clw-tile-carriers></clw-tile-carriers>
              </div>
              <div class="tile col-3">
                <clw-tile-carrier-response-time></clw-tile-carrier-response-time>
              </div>
              <div class="tile col-3">
                <clw-tile-carrier-delivery-time></clw-tile-carrier-delivery-time>
              </div>
            </div>
          </nb-tab>
        </nb-tabset>

      </nb-card-body>
    </nb-card>
  </div>

  <ng-template #exportDialog let-data let-ref="dialogRef" class="dialogRef">
    <div class="order-detail-container-export">
      <nb-card>
        <nb-card-header class="form--header">Exporter les données du Reporting</nb-card-header>
        <div class="export-contenair">
          <div class="filters-export">
            <input nbInput [nbDatepicker]="datePickerStart" [(ngModel)]="startDate" status="success"
              (ngModelChange)="onModelDateChange($event)" fieldSize="small" class="empty-input height"
              placeholder="Début" />
            <nb-datepicker #datePickerStart></nb-datepicker>

            <input nbInput [nbDatepicker]="datepickerEnd" [(ngModel)]="endDate" status="success"
              (ngModelChange)="onModelDateChange($event)" fieldSize="small" class="empty-input height"
              placeholder="Fin" />
            <nb-datepicker #datepickerEnd></nb-datepicker>

            <nb-select class="input-width height" status='primary' size="small" [(ngModel)]="orderType" fullWidth
              placeholder="Type de Bon (ST/SO)">
              <nb-option value="SO">SO</nb-option>
              <nb-option value="ST">ST</nb-option>
            </nb-select>

            <div class="filter-label">Nombre d'elements a exporter. max {{total}}</div>

            <input type="number" placeholder="Entrer" fieldSize="small" class="empty-input height" [(ngModel)]="limit"
              nbInput [value]="limit" />
          </div>
        </div>


        <nb-card-footer class="form--footer">
          <button nbButton outline status="basic" class="" (click)="ref.close(); activeFilter.length =0; ">
            <nb-icon icon="close-square-outline" [options]="{ animation: { type: 'zoom' } }" class="remove-icon">
            </nb-icon>
            fermer
          </button>

          <button nbButton outline status="primary" class="" (click)="exportToExcel();ref.close()">
            <nb-icon icon="done-all-outline" [options]="{ animation: { type: 'zoom' } }" class="remove-icon">
            </nb-icon>
            Exporter
          </button>

        </nb-card-footer>
      </nb-card>
    </div>
  </ng-template>

</div>