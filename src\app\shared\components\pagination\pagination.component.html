<div class="pagination-container" *ngIf="config && config.totalItems > 0">
    <div class="pagination-info">
        <ng-container *ngIf="result">
            {{ result.startIndex }} - {{ result.endIndex }} sur {{ config.totalItems }}
        </ng-container>
    </div>
    <div class="pagination-controls">
        <a href="javascript:void(0)" (click)="onPageChange(config.currentPage - 1)" 
           [class.disabled]="config.currentPage <= 1">
            <nb-icon icon="arrow-ios-back-outline" [options]="{ animation: { type: 'zoom' } }" 
                nbTooltip="Page précédente" nbTooltipPlacement="bottom" nbTooltipStatus="default" 
                status="success">
            </nb-icon>
        </a>
        <a href="javascript:void(0)" (click)="onPageChange(config.currentPage + 1)" 
           [class.disabled]="config.currentPage >= result.totalPages">
            <nb-icon icon="arrow-ios-forward-outline" [options]="{ animation: { type: 'zoom' } }" 
                nbTooltip="Page suivante" nbTooltipPlacement="bottom" nbTooltipStatus="default" 
                status="success">
            </nb-icon>
        </a>
    </div>
</div>