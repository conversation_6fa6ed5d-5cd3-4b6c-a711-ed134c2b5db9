import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

const routes: Routes = [
  {
    path: 'ae-allocation',
    loadChildren: () => import('./removal-management/removal-management.module').then(m => m.RemovalManagementModule)
  },
  {
    path: 'ae-accepted',
    loadChildren: () => import('./accepted-removal-management/accepted-removal-management.module').then(m => m.AcceptedRemovalManagementModule)
  },
  {
    path: 'dock-allocation',
    loadChildren: () => import('./truck-allocation-dock/truck-allocation-dock.module').then(m => m.TruckAllocationDockModule)
  },
  {
    path: 'historic',
    loadChildren: () => import('./historic-removal/historic-removal.module').then(m => m.HistoricRemovalModule)
  },
  {
    path: 'waiting-thread',
    loadChildren: () => import('./waiting-thread/waiting-thread.module').then(m => m.WaitingThreadModule)
  },
  {
    path: 'brokenDownTrucks',
    loadChildren: () => import('./menu-broken-down-trucks/menu-broken-down-trucks.module').then(m => m.MenuBrokenDownTrucksModule)
  },
  {
    path: 'call-screen',
    loadChildren: () => import('./menu-call-screen/menu-call-screen.module').then(m => m.MenuCallScreenModule)
  },
  {
    path: 'truck-call-screen',
    loadChildren: () => import('./truck-call-screen/truck-call-screen.module').then(m => m.TruckCallScreenModule)
  },
  {
    path: 'ae-loading',
    loadChildren: () => import('./loading-removal/loading-removal.module').then(m => m.LoadingRemovalModule)
  },
  {
    path: 'inspections',
    loadChildren: () => import('./inspections/inspections.module').then(m => m.InspectionsModule)
  },
  {
    path: 'material-inspections',
    loadChildren: () => import('./material-inspections/material-inspections.module').then(m => m.MaterialInspectionsModule)
  },
  {
    path: 'loaded-trucks',
    loadChildren: () => import('./loaded-truck/loaded-truck.module').then(m => m.LoadedTruckModule)
  },
  {
    path: 'new-removal',
    loadChildren: () => import('./delevery-orders/delevery-orders.module').then(m => m.DeleveryOrdersModule)
  },

];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class MenuRemovalRoutingModule { }
