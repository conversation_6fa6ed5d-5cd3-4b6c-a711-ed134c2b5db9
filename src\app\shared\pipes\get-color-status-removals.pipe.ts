import { Pipe, PipeTransform } from '@angular/core';
import { StatusRemovals, TruckStatus } from '../models/status-removal.enum';
import { InspectionStatus } from '../enums/inspection.enum';

@Pipe({ name: 'getColorStatusRemovals' })
export class GetColorStatusRemovalsPipe implements PipeTransform {

    transform(status: unknown, ...args: unknown[]): string {
        // Group statuses that map to "warning"
        const warningStatuses = [
            StatusRemovals.WAITING_VALIDATION,
            StatusRemovals.NEW_PARKING,
            TruckStatus.FACTORY,
            StatusRemovals.REJECTED
        ];

        if (status === StatusRemovals.NOT_ASSIGNED) {
            return "basic";
        }
        if (status === StatusRemovals.ASSIGNED) {
            return "success";
        }
        if (status === StatusRemovals.QUEUED) {
            return "info";
        }
        if (status === StatusRemovals.LOADED) {
            return "primary";
        }
        if (status === StatusRemovals.BROKEN_DOWN) {
            return "danger";
        }
        if (status === 0) {
            return "danger";
        }
        if (warningStatuses.includes(status as StatusRemovals)) {
            return "warning";
        }
        return "basic";
    }
}

@Pipe({ name: 'getColorStatusInspection' })
export class GetColorStatusInspectionsPipe implements PipeTransform {

    transform(status: unknown, ...args: unknown[]): string {
        console.log("status", status);
        if (status === InspectionStatus.COMPLETED) {
            return "success"
        }
        if (status === InspectionStatus.REJECTED) {
            return "danger"
        }
        if (status == 0) {
            return "danger"
        }
        return "basic"
    }
}
