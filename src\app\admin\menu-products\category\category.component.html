<div class="common-form-container category-container">
    <div class="header">
        <nb-icon icon="undo-outline" (click)="goBack()" [options]="{ animation: { type: 'zoom' } }" class="remove-icon" status="primary">
        </nb-icon>
        <h1 class="title">Catégorie de produits</h1>
    </div>

    <div class="filter-container">
        <div class="left-block">
            <!-- <div class="filter-label">Filtrer par</div> -->
            <div class="filter-elt store-filter">
                <!--     <nb-select fullWidth placeholder="Point d'enlèvement" status='primary' size="medium" class="empty-input">
                    <nb-option [value]='null'>Tout les Points d'enlèvement </nb-option>
                      <nb-option [value]='store' *ngFor="let store of stores">{{store?.label}}
                    </nb-option>
                </nb-select> -->

            </div>


        </div>
        <div class="btn-contain">
            <!-- <button nbButton outline status="success" class="search-btn filter-btn" (click)='search()'>
                <nb-icon icon="search-outline"></nb-icon>
                Filtrer
            </button> -->
            <button nbButton status="success" class="search-btn" size="small" (click)='openAddModal(addDialog)'>
                <nb-icon icon="plus-square-outline"></nb-icon>
                Ajouter une Catégorie
            </button>
        </div>
    </div>


    <nb-card class="card-container" [nbSpinner]="isLoading" nbSpinnerMessage="Chargement des données"
        nbSpinnerStatus="primary">
        <nb-card-header>
            <nb-list class="element-head  scrool-style">
                <nb-list-item class="list-elt-header list-elt paginator no-border">
                    <div class="col col-label">Libelé</div>
                    <div class="col col-code">Code</div>
                    <div class="col col-actions">
                        <!-- {{ startIndex }} - {{ endIndex }} sur {{ total }} -->
                        <div class="actions">
                            <!-- <nb-icon icon="arrowhead-left" status="basic" [options]="{ animation: { type: 'zoom' } }"
                                (click)="previousPage()"></nb-icon>
                            <nb-icon icon="arrowhead-right" [options]="{ animation: { type: 'zoom' } }"
                                (click)="nextPage()">
                            </nb-icon> -->
                        </div>
                    </div>
                </nb-list-item>
            </nb-list>

        </nb-card-header>
        <nb-card-body>
            <nb-list class="element-head  scrool-style">
                <nb-list-item class="list-elt-header list-elt" *ngFor='let category of categories'>
                    <div class="col col-label">{{category?.label}}</div>
                    <div class="col col-code">{{category?.code}}</div>

                    <div class="col col-actions">
                        <!-- <button nbTooltip="Detail" nbTooltipPlacement="top" nbTooltipStatus status="basic"
                            class="btn-background">
                            <nb-icon icon="file-text-outline" status="primary"
                                [options]="{ animation: { type: 'zoom' } }"></nb-icon>
                        </button> -->
                        <button nbTooltip="Editer" nbTooltipPlacement="top" status="basic"
                            (click)='openEditModal(editDialog, category)'>
                            <nb-icon icon="edit-2-outline" status="warning" [options]="{ animation: { type: 'zoom' } }">
                            </nb-icon>
                        </button>
                        <button nbTooltip="Supprimer" nbTooltipPlacement="top" status="basic" (click)='openDeleteModal(deleteDialog, category)'>
                            <nb-icon icon="trash-2-outline" status="danger" [options]="{ animation: { type: 'zoom' } }">
                            </nb-icon>
                        </button>
                    </div>
                </nb-list-item>
                <nb-list-item class="not-found" *ngIf='categories?.length===0'>
                    <img src="../../../../assets/images/EMPTY FOLDER VECTOR.png" alt="">
                    <h1>
                        Aucune Catégorie trouvée
                    </h1>
                </nb-list-item>
            </nb-list>
        </nb-card-body>
    </nb-card>
</div>
<ng-template #deleteDialog let-data let-ref="dialogRef" class="dialogRef">

    <nb-card>
        <nb-card-header class="form--header">Supprimer le produit</nb-card-header>
        <nb-card-body>
            <p> Etes-Vous sûr de vouloir supprimer {{selectedCategory.label}} ?</p>
        </nb-card-body>
        <nb-card-footer class="form--footer">
            <button nbButton outline ghost status="basic" class="" [disabled]="isLoading" (click)='ref.close()'>
                <nb-icon icon="close-square-outline" [options]="{ animation: { type: 'zoom' } }" class="remove-icon">
                </nb-icon>
                Annuler
            </button>
            <button nbButton outline status="danger" class="" [disabled]="isLoading" (click)='deleteCategory()'>
                <nb-icon icon="trash-2-outline" [options]="{ animation: { type: 'zoom' } }" class="remove-icon">
                </nb-icon>
                Supprimer
            </button>
        </nb-card-footer>
    </nb-card>

</ng-template>

<ng-template #addDialog let-data let-ref="dialogRef" class="dialogRef">
    <div class="category-container">
        <nb-card class="edit-order">
            <nb-card-header class="form--header">Ajouter les informations</nb-card-header>


            <nb-card-body>
                <div class="edit-container">
                    <div class="input">
                        <label for="otherTrainingType">Libelé</label>
                        <input nbInput fullWidth size="medium" type="text" class="form-input"
                            [(ngModel)]='selectedCategory.label'>
                    </div>
                </div>
            </nb-card-body>

            <nb-card-footer class="footer">
                <button nbButton outline ghost status="basic" class=""  (click)="ref.close()"
                    [disabled]="isLoading">
                    <nb-icon icon="close-square-outline" [options]="{ animation: { type: 'zoom' } }"
                        class="remove-icon">
                    </nb-icon>
                    Annuler
                </button>
                <button nbButton outline status="success" class="btn-contain" [disabled]="isLoading"
                    (click)='addCategory()'>
                    <nb-icon icon="save-outline" [options]="{ animation: { type: 'zoom' } }" class="remove-icon">
                    </nb-icon>
                    Enregistrer
                </button>
            </nb-card-footer>
        </nb-card>

    </div>

</ng-template>



<ng-template #editDialog let-data let-ref="dialogRef" class="dialogRef">
    <div class="category-container">
        <nb-card class="edit-order">
            <nb-card-header class="form--header">MOdifier les informations</nb-card-header>


            <nb-card-body>
                <div class="edit-container">
                    <div class="input">
                        <label for="otherTrainingType">Libelé</label>
                        <input nbInput fullWidth size="medium" type="text" class="form-input"
                            [(ngModel)]='selectedCategory.label'>
                    </div>
                </div>
            </nb-card-body>

            <nb-card-footer class="footer">
                <button nbButton outline ghost status="basic" class=""  (click)="ref.close()"
                    [disabled]="isLoading">
                    <nb-icon icon="close-square-outline" [options]="{ animation: { type: 'zoom' } }"
                        class="remove-icon">
                    </nb-icon>
                    Annuler
                </button>
                <button nbButton outline status="success" class="btn-contain" [disabled]="isLoading"
                    (click)='editCategory()'>
                    <nb-icon icon="save-outline" [options]="{ animation: { type: 'zoom' } }" class="remove-icon">
                    </nb-icon>
                    Enregistrer
                </button>
            </nb-card-footer>
        </nb-card>

    </div>

</ng-template>

<!-- <ng-template #detailDialog let-data let-ref="dialogRef">

    <div class="detail-price-container">
        <nb-card>
            <nb-card-header class="form--header">Détail du Point d'enlèvement
            </nb-card-header>
            <nb-card-body>
                <div class="detail-container">
                    <div class="image">
                        <img [src]="selectedPrice?.product?.img" alt="">
                    </div>
                    <div class="row">
                        <div class="title">Produit:</div>
                        <div class="value">{{selectedPrice?.product?.label}} ({{selectedPrice?.product?.normLabel}})
                        </div>
                    </div>
                    <div class="row">
                        <div class="title">Point d'enlèvement:</div>
                        <div class="value">{{selectedPrice?.store?.label}}</div>
                    </div>
                    <div class="row">
                        <div class="title">Catégorie:</div>
                        <div class="value">{{selectedPrice?.productCategory?.label}}</div>
                    </div>
                    <div class="row">
                        <div class="title">Packaging:</div>
                        <div class="value">{{selectedPrice.packaging?.label}}
                            ({{selectedPrice.packaging?.unit?.baseUnitValue}}
                            {{selectedPrice.packaging?.unit?.baseUnit}}) </div>
                    </div>
                </div>
            </nb-card-body>
            <nb-card-footer class="form--footer">
                <button nbButton outline status="basic" class="btn-border border" (click)="ref.close()"
                    [disabled]="isLoading">
                    <nb-icon icon="close-square-outline" [options]="{ animation: { type: 'zoom' } }"
                        class="remove-icon">
                    </nb-icon>
                    Fermer
                </button>


            </nb-card-footer>
        </nb-card>
    </div>
</ng-template> -->
