<div class="common-form-container stores-container">
    <div class="header">
        <nb-icon icon="undo-outline" (click)="goBack()" [options]="{ animation: { type: 'zoom' } }" class="remove-icon" status="primary">
        </nb-icon>
        <h1 class="title">Gestion du Point d'enlèvement</h1>
    </div>

    <div class="filter-container">
        <div class="left-block">
            <div class="filter-label">Filtrer par</div>
            <div class="filters">
              <div class="filter-elt">
                <nb-select fullWidth placeholder="Categorie" status='primary' size="small"
                    (selectedChange)='filterStoreType($event)'>
                    <nb-option [value]='null'>Tout les types </nb-option>
                    <nb-option [value]='type' *ngFor="let type of storeTypes">{{type?.label}}
                    </nb-option>
                </nb-select>
            </div>
            </div>
           <!--  <div class="filter-elt">
                <input id="typeInput" nbInput status='primary' [(ngModel)]="selectedStoreLabel"
                    (ngModelChange)="onModelStoreChange($event)" fullWidth type="text" placeholder="Libelé du Point d'enlèvement"
                    [nbAutocomplete]="autoComplete1" [nbTooltip]="selectedStoreLabel || 'Rechercher'"
                    nbTooltipPlacement="bottom" />
                <nb-autocomplete #autoComplete1 (selectedChange)="onSelectionStoreChange($event)">
                    <nb-option *ngFor="let option of filteredStore$ | async" [value]="option">{{option}}
                    </nb-option>
                </nb-autocomplete>
            </div> -->

            <div class="filter-elt">
                <nb-select fullWidth placeholder="Categorie" status='primary' size="small"
                    (selectedChange)='filterStoreType($event)'>
                    <nb-option [value]='null'>Tout les types </nb-option>
                    <nb-option [value]='type' *ngFor="let type of storeTypes">{{type?.label}}
                    </nb-option>
                </nb-select>
            </div>

        </div>
        <div class="btn-contain">
            <button nbButton status="success" class="search-btn"  size="small" (click)='openAddModalStore(addDialog)'>
                <nb-icon icon="plus-square-outline"></nb-icon>
                Ajouter un Point d'enlèvement
            </button>
        </div>
    </div>

    <div class="cards" [nbSpinner]="isLoading" nbSpinnerStatus="primary">

        <div class="card-container">
            <div class="store-side">
                <div class="store" *ngFor='let store of stores' (click)='selectStore(detailDialog, store)'>
                    <img [src]="getStoreType(store).img" alt="">
                    <label>{{store?.label}}</label>
                </div>
                <div class="no-shadow" (click)='openAddModalStore(addDialog)'>
                    <nb-icon nbTooltip="Ajouter" nbTooltipPlacement="top" status="basic" icon='plus-circle'
                        status='primary'></nb-icon>
                </div>
            </div>
        </div>

    </div>



    <ng-template #detailDialog let-data let-ref="dialogRef">
        <div class="detail-store-container">
            <nb-flip-card [flipped]='flipped' [showToggleButton]='false'>
                <nb-card-front>
                    <nb-card>
                        <nb-card-header class="form--header">Détail du Point d'enlèvement
                            <div class="action-icons">
                                <button nbTooltip="Editer" nbTooltipPlacement="top" status="basic"
                                    (click)='editModal()'>
                                    <nb-icon icon="edit-2-outline" status="warning"
                                        [options]="{ animation: { type: 'zoom' } }"></nb-icon>
                                </button>
                                <button nbTooltip="Supprimer" nbTooltipPlacement="top" status="basic"
                                    (click)='isDeleteModal()'>
                                    <nb-icon icon="trash-2-outline" status="danger"
                                        [options]="{ animation: { type: 'zoom' } }"></nb-icon>
                                </button>
                            </div>
                        </nb-card-header>
                        <nb-card-body>
                            <div class="detail-container">
                                <div class="image">
                                    <img [src]="getStoreType(selectedStore).img" alt="">
                                </div>
                                <div class="row">
                                    <div class="title">Nom du Point d'enlèvement:</div>
                                    <div class="value">{{selectedStore?.label}}</div>
                                </div>
                                <div class="row">
                                    <div class="title">Catégorie:</div>
                                    <div class="value">{{getStoreType(selectedStore).label}}</div>
                                </div>
                                <div class="row">
                                    <div class="title">Référence:</div>
                                    <div class="value"> {{selectedStore?.jdeReference}} </div>
                                </div>
                                <div class="row">
                                    <div class="title">Adresse:</div>
                                    <div class="value"> {{selectedStore?.address?.city + ', '+
                                        selectedStore?.address?.region || 'Aucune Adresse !!! '}} </div>
                                </div>
                            </div>
                        </nb-card-body>
                        <nb-card-footer class="form--footer">
                            <button nbButton outline ghost status="basic" class="btn-border border" (click)="ref.close()"
                                [disabled]="isLoading">
                                <nb-icon icon="close-square-outline" [options]="{ animation: { type: 'zoom' } }"
                                    class="remove-icon">
                                </nb-icon>
                                Fermer
                            </button>


                        </nb-card-footer>
                    </nb-card>
                </nb-card-front>
                <nb-card-back>
                    <nb-card *ngIf='isDelete'>
                        <nb-card-header class="form--header">Supprimer le produit</nb-card-header>
                        <nb-card-body>
                            <p> Etes-Vous sûr de vouloir supprimer le store <span>{{selectedStore.label}} ?</span></p>
                        </nb-card-body>
                        <nb-card-footer class="form--footer">
                            <button nbButton outline status="basic" ghost class="" (click)="backFlip()"
                                [disabled]="isLoading">
                                <nb-icon icon="close-square-outline" ghost [options]="{ animation: { type: 'zoom' } }"
                                    class="remove-icon">
                                </nb-icon>
                                Annuler
                            </button>
                            <button nbButton outline status="danger" class="" [disabled]="isLoading"
                                (click)='deleteStore()'>
                                <nb-icon icon="trash-2-outline" [options]="{ animation: { type: 'zoom' } }"
                                    class="remove-icon">
                                </nb-icon>
                                Supprimer
                            </button>
                        </nb-card-footer>
                    </nb-card>

                    <nb-card *ngIf='isEdit' [nbSpinner]="isLoading" nbSpinnerMessage="Chargement des données"
                        nbSpinnerStatus="primary" class="edit-order">
                        <nb-card-header class="form--header">Modifier les informations</nb-card-header>


                        <nb-card-body>
                            <div class="edit-container">
                                <div class="input">
                                    <label for="labelTrainingType">Nom du Point d'enlèvement</label>
                                    <input nbInput fullWidth size="medium" type="text" class="form-input"
                                        [(ngModel)]='selectedStore.label'>
                                </div>
                                <div class="input">
                                    <label for="otherTrainingType">Categorie</label>
                                    <nb-select fullWidth placeholder="" size="medium" [(selected)]="selectedStore.type">
                                        <nb-option [value]='type.type' *ngFor="let type of storeTypes">{{type?.label}}
                                        </nb-option>
                                    </nb-select>
                                </div>
                                <div class="input">
                                    <label for="otherTrainingType">Reference JDE</label>
                                    <input nbInput fullWidth size="medium" type="text" class="form-input"
                                        [(ngModel)]='selectedStore.jdeReference'>
                                </div>
                                <div class="row">

                                    <div class="input">
                                        <label for="otherTrainingType">Région</label>
                                        <nb-select fullWidth placeholder="" size="medium"
                                            [(selected)]="selectedStore.address.region">
                                            <nb-option [value]='region' *ngFor="let region of regions">{{region}}
                                            </nb-option>
                                        </nb-select>
                                    </div>

                                    <div class="input">
                                        <label for="otherTrainingType">Ville</label>
                                        <input nbInput fullWidth size="medium" type="text" class="form-input"
                                            [(ngModel)]='selectedStore.address.city'>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="input">
                                        <label for="otherTrainingType">Sold TO</label>
                                        <input nbInput fullWidth size="medium" type="number" class="form-input"
                                            [(ngModel)]='selectedStore.jdeSoldToId'>
                                    </div>
                                    <div class="input">
                                        <label for="otherTrainingType">Ship TO</label>
                                        <input nbInput fullWidth size="medium" type="number" class="form-input"
                                            [(ngModel)]='selectedStore.jdeShipToId'>
                                    </div>
                                </div>
                            </div>
                        </nb-card-body>

                        <nb-card-footer class="footer">
                            <button nbButton outline status="basic" ghost class="" (click)="backFlip()"
                                [disabled]="isLoading">
                                <nb-icon icon="close-square-outline" ghost [options]="{ animation: { type: 'zoom' } }"
                                    class="remove-icon">
                                </nb-icon>
                                Annuler
                            </button>
                            <button nbButton outline status="success" class="btn-contain" [disabled]="isLoading"
                                (click)='editStore()'>
                                <nb-icon icon="save-outline" [options]="{ animation: { type: 'zoom' } }"
                                    class="remove-icon">
                                </nb-icon>
                                Enregistrer
                            </button>
                        </nb-card-footer>
                    </nb-card>
                </nb-card-back>
            </nb-flip-card>
        </div>

    </ng-template>

    <ng-template #addDialog let-data let-ref="dialogRef" class="dialogRef">
        <div class="add-store-container">
            <nb-card [nbSpinner]="isLoading" nbSpinnerMessage="Chargement des données" nbSpinnerStatus="primary"
                class="edit-order">
                <nb-card-header class="form--header">Ajouter les informations</nb-card-header>


                <nb-card-body>
                    <div class="edit-container">
                        <div class="input">
                            <label for="labelTrainingType">Nom du Point d'enlèvement</label>
                            <input nbInput fullWidth size="medium" type="text" class="form-input"
                                [(ngModel)]='selectedStore.label'>
                        </div>
                        <div class="input">
                            <label for="otherTrainingType">Categorie</label>
                            <nb-select fullWidth placeholder="" size="medium" [(selected)]="selectedStore.type">
                                <nb-option [value]='type.type' *ngFor="let type of storeTypes">{{type?.label}}
                                </nb-option>
                            </nb-select>
                        </div>
                        <div class="input">
                            <label for="otherTrainingType">Reference JDE</label>
                            <input nbInput fullWidth size="medium" type="text" class="form-input"
                                [(ngModel)]='selectedStore.jdeReference'>
                        </div>
                        <div class="row">

                            <div class="input">
                                <label for="otherTrainingType">Région</label>
                                <nb-select fullWidth placeholder="" size="medium"
                                    [(selected)]="selectedStore.address.region">
                                    <nb-option [value]='region' *ngFor="let region of regions">{{region}}
                                    </nb-option>
                                </nb-select>
                            </div>

                            <div class="input">
                                <label for="otherTrainingType">Ville</label>
                                <input nbInput fullWidth size="medium" type="text" class="form-input"
                                    [(ngModel)]='selectedStore.address.city'>
                            </div>
                        </div>

                        <div class="row">
                            <div class="input">
                                <label for="otherTrainingType">Sold TO</label>
                                <input nbInput fullWidth size="medium" type="number" class="form-input"
                                    [(ngModel)]='selectedStore.jdeSoldToId'>
                            </div>
                            <div class="input">
                                <label for="otherTrainingType">Ship TO</label>
                                <input nbInput fullWidth size="medium" type="number" class="form-input"
                                    [(ngModel)]='selectedStore.jdeShipToId'>
                            </div>
                        </div>
                    </div>
                </nb-card-body>

                <nb-card-footer class="footer">
                    <button nbButton outline status="basic" ghost class="" (click)="ref.close()"
                        [disabled]="isLoading">
                        <nb-icon icon="close-square-outline" [options]="{ animation: { type: 'zoom' } }"
                            class="remove-icon">
                        </nb-icon>
                        Annuler
                    </button>
                    <button nbButton outline status="success" class="btn-contain" [disabled]="isLoading"
                        (click)='addStore()'>
                        <nb-icon icon="save-outline" [options]="{ animation: { type: 'zoom' } }" class="remove-icon">
                        </nb-icon>
                        Enregistrer
                    </button>
                </nb-card-footer>
            </nb-card>

        </div>

    </ng-template>
