import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DragDropModule } from '@angular/cdk/drag-drop';
import { RemovalManagementRoutingModule } from './removal-management-routing.module';
import { RemovalManagementComponent, StatusRemovalPipe } from './removal-management.component';
import { NbAutocompleteModule, NbButtonModule, NbCardModule, NbDatepickerModule, NbDialogModule, NbFormFieldModule, NbIconModule, NbInputModule, NbSelectModule, NbSpinnerModule, NbTooltipModule } from '@nebular/theme';
import { FormsModule } from '@angular/forms';
import { CofirmDialogComponent } from './cofirm-dialog/cofirm-dialog.component';
import { SharedModule } from 'src/app/shared/shared.module';


@NgModule({
  declarations: [
    RemovalManagementComponent,
    StatusRemovalPipe,
    CofirmDialogComponent,
  ],
  imports: [
    CommonModule,
    FormsModule,
    NbIconModule,
    NbCardModule,
    NbInputModule,
    NbTooltipModule,
    NbSelectModule,
    NbButtonModule,
    DragDropModule,
    NbSpinnerModule,
    NbDialogModule,
    SharedModule,
    NbFormFieldModule,
    NbDatepickerModule,
    NbAutocompleteModule,
    RemovalManagementRoutingModule
  ]
})
export class RemovalManagementModule { }
