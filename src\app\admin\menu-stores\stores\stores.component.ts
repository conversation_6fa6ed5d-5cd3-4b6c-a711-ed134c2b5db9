import { NbDialogRef, NbDialogService, NbToastrService } from '@nebular/theme';
import { StoreService } from './../../../shared/services/store.service';
import { Component, OnInit } from '@angular/core';
import { Store } from 'src/app/shared/models/store';
import { Observable, of } from 'rxjs';
import { map } from 'rxjs/operators';
import { Router } from '@angular/router';
import { Location } from '@angular/common';

@Component({
  selector: 'clw-stores',
  templateUrl: './stores.component.html',
  styles: [
  ]
})
export class StoresComponent implements OnInit {
  isLoading: boolean;
  stores: Store[];
  flipped: boolean;
  selectedStore: any;
  isDelete: boolean
  isEdit: boolean;
  dialogRef: NbDialogRef<any>;
  allFactoryLabels: string[]
  filteredFactoryLabels: string[] = [];
  loading = false;

  filteredStore$: Observable<string[]>;
  selectedStoreLabel: any;
  filterForm = {
    status: 200,
    label: ''
  }



  storeTypes: any[] = [
    { type: 100, label: 'Usine', img: '../../../../assets/images/factory.png' },
    // { type: 200, label: 'Dépôt', img: '../../../../assets/images/store.png' }
  ]

  regions = [
    'CENTRE',
    'LITTORAL',
    'OUEST',
    'EST',
    'SUD-OUEST',
    'NORD-OUEST',
    'SUD',
    'NORD',
    'ADAMAOUA',
    'EXTREME NORD',
  ]

  storeType: any;
  storesLabels = [];

  constructor(
    private storeSrv: StoreService,
    private dialogSvr: NbDialogService,
    private toastSrv: NbToastrService,
    private router: Router,
    private location: Location
  ) { }

  async ngOnInit(): Promise<void> {
    await this.getStores()

  }

  async getStores() {
    const response = await this.storeSrv.getStores(this.filterForm);
    this.stores = response.data;
    this.storesLabels = [];
    this.stores.forEach((elt: Store) => { this.storesLabels.push(elt.label) });
    this.allFactoryLabels = this.stores.map(store => store?.label);
    this.filteredFactoryLabels = this.allFactoryLabels;
  }


  getStoreType(store: Store): any {
    return this.storeTypes.find((elt) => {
      return elt.type === store.type;
    });
  }

  resetFilter():void{
    this.filterForm = {
      status: 200,
      label: ''
    };
    this.getStores();
  }

  editModal() {
    this.isEdit = true;
    this.flipped = true;
  }
  isDeleteModal() {
    this.isDelete = true;
    this.flipped = true;
  }

  backFlip() {
    this.isDelete = false;
    this.isEdit = false;
    this.flipped = false;
  }

  selectStore(dialog: any, store: Store) {
    this.storeSrv.currentStore = store;
    this.storeType = store.type;
    this.selectedStore = { ...store }
    this.openModal(dialog);
  }

  openModal(dailog?: any): void {
    this.dialogRef = this.dialogSvr.open(dailog, { closeOnBackdropClick: false, autoFocus: true })
    this.dialogRef.onClose.subscribe(() => {
      this.flipped = false;
      this.isDelete = false;
      this.isEdit = false;
    });
  }

  openAddModalStore(dialog: any) {
    this.resetStore();
    this.openModal(dialog);
  }




  async deleteStore(): Promise<any> {
    this.isLoading = true;
    try {
      await this.storeSrv.deleteStore();
      this.dialogRef.close();
      await this.getStores();
      this.toastSrv.success('Nous avons Supprimé ce Point d\'enlèvement avec succès', 'Point d\'enlèvement supprimé');
    } catch (error) {
      this.toastSrv.danger('Nous n\'avons pas pu supprimer ce Point d\'enlèvement', 'Erreur de connexion');
    } finally {
      this.isLoading = false;
    }
  }

  async editStore(): Promise<any> {
    this.isLoading = true;
    try {
      if (this.selectedStore.label == '' || !this.selectedStore.type || this.selectedStore.address.city == '' || this.selectedStore.address.region == '' || this.selectedStore.jdeReference == '' || this.selectedStore.jdeSoldToId == '' || this.selectedStore.jdeShipToId == '') {
        this.toastSrv.danger('Veuillez renseigner tout les champs', 'Donnés incorrectes');
        return;
      }

      const response = await this.storeSrv.editstores(this.selectedStore);
      if (response.data) {
        this.dialogRef.close();
        await this.getStores();
        this.toastSrv.success('Nous avons Modifié ce Point d\'enlèvement avec succès', 'Point d\'enlèvement Modifié');
      }
    } catch (error) {
      this.toastSrv.danger('Nous n\'avons pas pu modifier ce Point d\'enlèvement', 'Erreur de connexion');
    } finally {
      this.isLoading = false;
    }
  }


  async addStore(): Promise<any> {
    this.isLoading = true;
    try {
      if (this.selectedStore.label == '' || !this.selectedStore.type || this.selectedStore.address.city == '' || this.selectedStore.address.region == '' || this.selectedStore.jdeReference == '' || this.selectedStore.jdeSoldToId == '' || this.selectedStore.jdeShipToId == '') {
        return this.toastSrv.danger('Veuillez renseigner tout les champs', 'Donnés incorrectes');
      }

      const response = await this.storeSrv.addStore(this.selectedStore);
      if (response.data) {
        this.dialogRef.close();
        await this.getStores();
        this.toastSrv.success('Nous avons Ajouté ce Point d\'enlèvement avec succès', 'Point d\'enlèvement Ajouté');
      }
    } catch (error) {
      this.toastSrv.danger('Nous n\'avons pas pu Ajouter ce Point d\'enlèvement', 'Erreur de connexion');
    } finally {
      this.isLoading = false;
    }
  }

  resetStore() {
    this.selectedStore = {
      label: '',
      address: { city: '', region: '' },
      jdeReference: '',
      jdeSoldToId: '',
      jdeShipToId: '',

    }
  }

  private filterStore(value: string): string[] {
    const filterValue = value.toLowerCase();
    return this.storesLabels.filter(optionValue => optionValue.toLowerCase().includes(filterValue));
  }

  onModelStoreChange(value: string): void { this.filteredStore$ = of(this.filterStore(value)); }
  async onSelectionStoreChange($event: any): Promise<void> {
    try {
      this.isLoading = true;
      const response = await this.storeSrv.getStores({ label: $event });
      this.stores = response.data;
    } catch (error) {
      this.toastSrv.danger('Veuillez vérifier votre connexion internet', 'Erreur connexion internet');
      console.error(error);
    } finally {
      this.isLoading = false;
      this.filteredStore$ = of(this.storesLabels);
    }
  }


  async filterStoreType(type: any): Promise<void> {
    this.selectedStoreLabel = null;
    const response = await this.storeSrv.getStores({ type: type?.type });
    this.stores = response.data;
  }


  goBack() {
    if (window.history.length > 1) {
      this.location.back()
    } else {
      this.router.navigate(['/home'])
    }
  }

  onFactoryInputChange(input: string) {
    this.filteredFactoryLabels = this.allFactoryLabels.filter(option =>
      option.toLowerCase().includes(input.toLowerCase())
    );
  }

  onFactorySelected(value: string) {
    if (value === 'Toutes les usines') {
      this.filterForm.label = '';
      this.filteredFactoryLabels = this.allFactoryLabels;
    }
   this.getStores();
  }

}
