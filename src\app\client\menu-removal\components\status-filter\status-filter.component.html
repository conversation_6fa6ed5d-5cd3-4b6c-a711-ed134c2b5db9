<div class="status-filter">
  <div class="filter-buttons">
    <button class="filter-btn" *ngFor="let status of statuses" [class.active]="status.value === activeStatus"
      (click)="onStatusClick(status.value)" [attr.aria-label]="status.ariaLabel"
      [attr.aria-pressed]="status.value === activeStatus">
      {{ status.label }}
    </button>
  </div>

  <div class="pagination-container">
    <clw-pagination [config]="paginationConfig" (pageChange)="onPageChange($any($event))"
      (itemsPerPageChange)="onItemsPerPageChange($any($event))">
    </clw-pagination>
  </div>
</div>