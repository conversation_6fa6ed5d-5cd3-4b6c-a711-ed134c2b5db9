<div>
  <!-- Hero Section -->
  <section class="hero">
    <!-- Contenu principal -->
    <div class="hero-content">
      <img src="assets/images/pngwing.png" alt="image de plusieurs camions" srcset="">
      <h1 class="brand">CADYST GRAIN LOGISTIQUE</h1>
      <p class="description">
        Plateforme de gestion des planifications, Suivi des enlèvements,<br>
        Tracking GPS des camions, et suivi des transporteurs.
      </p>
      <button class="login-btn" status="success" (click)="showModal()">Connexion</button>
    </div>
  </section>






  <!-- Features Section -->
  <section class="features-container" id="features-container">
    <div class="features">
      <div>
        <h2>A propos de la solution</h2>
        <div class="features-grid">
          <nb-card *ngFor="let feature of featuresList, let i = index">
            <nb-card-body>
              <div class="feature-list">
                <img src="{{feature.icon}}" alt="{{feature.title}}" />
                <div>
                  <h3>{{feature.title}}</h3>
                  <ul>
                    <li>{{feature.description1}}</li>
                    <li>{{feature.description2}}</li>
                    <li>{{feature.description3}}</li>
                    <li *ngIf="feature.description4">{{feature.description4}}</li>
                    <li *ngIf="feature.description5">{{feature.description5}}</li>
                    <li *ngIf="feature.description6">{{feature.description6}}</li>
                  </ul>
                </div>
              </div>
            </nb-card-body>
          </nb-card>
        </div>
      </div>
      <div>
        <img src="assets/images/vehicule.png" alt="Flotte de camions" />
      </div>
    </div>
  </section>


  <section class="limit"></section>

  <!-- Platform Preview Section -->
  <section class="platform-preview-container">
    <div class="platform-preview">
      <div class="laptop-container">
        <img src="assets/images/icons/computer-circle.png" alt="Interface de la plateforme" />
      </div>
      <div class="advantages">
        <h2>Avantages offerts par la plateforme</h2>
        <div class="advantage-items">
          <div class="advantage-item" *ngFor="let benefit of platformBenefits">
            <img [src]="benefit.icon" [alt]="benefit.title" />
            <div class="advantage-info">
              <h3>{{benefit.title}}</h3>
              <p>{{benefit.description}}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>


  <section class="faq-container" id="faq-container">
    <div class="faq">
      <h2>Questions fréquentes</h2>
      <div class="accordion">
        <div class="accordion-item" *ngFor="let item of faqItems">
          <div class="accordion-header" (click)="toggleIcon(item)">
            <span class="question-text">{{ item.question }}</span>
            <span class="custom-icon">
              <span *ngIf="item.isOpen" class="minus">-</span>
              <span *ngIf="!item.isOpen" class="plus">+</span>
            </span>
          </div>
          <div class="accordion-body" [class.open]="item.isOpen">
            {{ item.answer }}
          </div>
        </div>
      </div>
    </div>
  </section>


  <!-- Footer -->
  <div class="footer-content">
    <div class="footer-links">
      <p>CADYST GRAIN LOGISTIQUE</p>
      <p>Mention légales</p>
      <p>All rights reserved 2025</p>
    </div>
  </div>


  <clw-auth-modal class="modal" *ngIf="commonService.showAuthModal"></clw-auth-modal>
</div>