
import { HttpInterceptor, HttpRequest, HttpHandler, HttpEvent, HttpErrorResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, BehaviorSubject, throwError } from 'rxjs';
import { catchError, switchMap, filter, take } from 'rxjs/operators';
import { AuthService } from '../shared/services/auth.service';
import { StorageService } from '../shared/services/storage.service';

@Injectable()
export class RefreshTokenInterceptor implements HttpInterceptor {
    private isRefreshing = false;
    private refreshTokenSubject: BehaviorSubject<any> = new BehaviorSubject<any>(null);

    constructor(
        private storageSrv: StorageService,
        private authSvr: AuthService) { }

    intercept(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {

        return next.handle(req).pipe(catchError(error => {
            if (error instanceof HttpErrorResponse
                && error.status === 401
                && error.error.type === 'TokenExpired') {
                return this.getNewToken(req, next);
            }
            return throwError(error);
        }));
    }

    getNewToken(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
        if (this.isRefreshing) {
            return this.refreshTokenSubject.pipe(
                filter(token => token != null),
                take(1),
                switchMap(tokenString => {
                    return next.handle(this.addToken(req, tokenString));
                }));
        }

        this.isRefreshing = true;
        this.refreshTokenSubject.next(null);

        return this.authSvr.refreshToken().pipe(
            switchMap((token: any) => {
                this.isRefreshing = false;
                this.storageSrv.setObject('oauth', token);
                this.refreshTokenSubject.next(token.access_token);
                return next.handle(this.addToken(req, token.access_token));
            }));
    }

    private addToken(req: HttpRequest<any>, token: string): HttpRequest<any> {
        return req.clone({
            headers: req.headers.set('Authorization', `Bearer ${token}`)
        });
    }
}
