import { Component, OnInit } from '@angular/core';
import { Chart, registerables } from 'chart.js'
import { MenuReportingService } from '../../menu-reporting/menu-reporting.service';
import { RenderTypeValue } from 'src/app/shared/models/render-type.enum';

Chart.register(...registerables)

@Component({
  selector: 'clw-tile-chart-store',
  templateUrl: './tile-chart-store.component.html',
  styles: [
  ]
})
export class TileChartStoreComponent implements OnInit {
  isLoading: boolean;
  data: any;

  store: any;

  constructor(
    private reportingSrv: MenuReportingService
  ) { }

  async ngOnInit() {
    try {
      this.isLoading = true;
      const query = {
        type: RenderTypeValue.RENDER
      }
      this.data = await this.reportingSrv.getStatusInspections(query);
      this.renderCarrierChart();
    } catch (error) {
      console.log(error);
    } finally { this.isLoading = false;}
  }

  renderCarrierChart() {
    this.store = new Chart('canvas-store', {
      type: 'doughnut',
      data: {
        labels: ['ACCEPTEES', 'REFUSEES'],
        datasets: [
          {
            // label: 'My First Dataset',
            data: [this.data.nbStatusOk, this.data.nbStatusNotOk],
            backgroundColor: [
              'rgb(255, 99, 132)',
              'rgb(54, 162, 235)',
            ],
          },
        ],
      },
      options: {},
    });
  }

  async refresh() {
    this.isLoading = true;
    try {
      const query = {
        type: RenderTypeValue.RENDER
      }
      this.data = await this.reportingSrv.getStatusInspections(query);
      this.renderCarrierChart();
      this.isLoading = false;
    } catch (error) {
      console.log(error);
      this.isLoading = false;
      return error;
    }
  }
}
