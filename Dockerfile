FROM node:14.16.0-alpine3.11

USER root

# Expose the port the app runs in
EXPOSE 4000

RUN NODE_PATH=/usr/lib/node_modules

RUN mkdir -p /usr/src/app

WORKDIR /tmp

ADD package.json ./package.json

COPY ./ssl /tmp/ssl

# COPY ./dist /tmp/dist

COPY ./dist/cimencam-logistique-web/server /tmp/dist/cimencam-logistique-web/server

COPY ./dist/cimencam-logistique-web/browser /tmp/dist/cimencam-logistique-web/browser

# COPY ./dist/cimencam-logistique-web/browser/assets /tmp/dist/cimencam-logistique-web/browser/assets

# COPY ./dist/cimencam-logistique-web/browser/*.(png|ico|ttf|otf) /tmp/dist/cimencam-logistique-web/browser/

# COPY ./dist/cimencam-logistique-web/server /tmp/dist/cimencam-logistique-web/server

# COPY ./dist/cimencam-logistique-web/browser/*.(js|html|css|txt|br|gz) /tmp/dist/cimen

CMD npm run serve:ssr
