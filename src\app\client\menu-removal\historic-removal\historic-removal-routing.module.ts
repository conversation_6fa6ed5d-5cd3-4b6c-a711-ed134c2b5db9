import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { HistoricRemovalComponent } from './historic-removal.component';
import { PickUpRemovalHistoryComponent } from './pick-up-removal-history/pick-up-removal-history.component';

const routes: Routes = [
  {path: '', component: HistoricRemovalComponent},
  {path: 'history-puck-up', component: PickUpRemovalHistoryComponent},
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class HistoricRemovalRoutingModule { }
