.icon {
    display: block;
    width: 20px;
    height: 20px;
    -webkit-background-size: cover;
    background-size: cover;

    &.icon_logo {
        width: 85px;
        height: 59px;
        background-size: contain;
        background-repeat: no-repeat;
        background-image: url(/assets/images/icons/CADYSTLOGO.png);
    }

    &.icon_orderlogo {
        width: 107px;
        height: 47px;
        background-size: contain;
        background-repeat: no-repeat;
        background-position: center;
        cursor: pointer;
        background-image: url(/assets/images/icons/CADYSTLOGO.png);
    }

    &.icon_whitelogo {
        width: 85px;
        height: 59px;
        background-size: contain;
        background-repeat: no-repeat;
        background-image: url(/assets/images/icons/NEW_LOGO_Fluid.png);
    }

    &.icon_logo_blue {
        width: 50px;
        height: 50px;
        background-size: contain;
        background-repeat: no-repeat;
        // background-image: url(/assets/images/icons/logo-bicec-blue.jpg);
    }

    &.icon_googleplay {
        width: 120px;
        height: 40px;
        background-size: contain;
        background-repeat: no-repeat;
        background-image: url(/assets/images/icons/googleplay.png);
    }

    &.icon_appstore {
        width: 120px;
        height: 40px;
        background-size: contain;
        background-repeat: no-repeat;
        background-image: url(/assets/images/icons/appstore.png);
    }

    &.icon_book {
        width: 80px;
        height: 80px;
        background-size: contain;
        background-repeat: no-repeat;
        background-image: url(/assets/images/icons/book-open.png);
    }

    &.icon_people {
        width: 80px;
        height: 80px;
        background-size: contain;
        background-repeat: no-repeat;
        background-image: url(/assets/images/icons/people.png);
    }

    &.icon_person {
        width: 80px;
        height: 80px;
        background-size: contain;
        background-repeat: no-repeat;
        background-image: url(/assets/images/icons/person-done.png);
    }

    &.icon_clock {
        width: 80px;
        height: 80px;
        background-size: contain;
        background-repeat: no-repeat;
        background-image: url(/assets/images/icons/clock.png);
    }

    &.icon_left_arrow {
        width: 30px;
        height: 30px;
        background-size: contain;
        background-repeat: no-repeat;
        background-image: url(/assets/images/icons/left-arrow.png);
    }

    &.icon_phone {
        width: 120px;
        height: 40px;
        background-size: contain;
        background-repeat: no-repeat;
        background-image: url(/assets/images/icons/phone.png);
    }

    &.icon_template {
        width: 50px;
        height: 50px;
        background-size: contain;
        background-repeat: no-repeat;
        background-image: url(/assets/images/icons/templates-icon.png);
    }

    &.icon_activity {
        width: 50px;
        height: 50px;
        background-size: contain;
        background-repeat: no-repeat;
        background-image: url(/assets/images/icons/activities-icon.png);
    }

    &.icon_message {
        width: 50px;
        height: 50px;
        background-size: contain;
        background-repeat: no-repeat;
        background-image: url(/assets/images/icons/messages-icon.png);
    }

    &.icon_history {
        width: 50px;
        height: 50px;
        background-size: contain;
        background-repeat: no-repeat;
        background-image: url(/assets/images/icons/history-icon.png);
    }

    &.icon_reporting {
        width: 80px;
        height: 80px;
        background-size: contain;
        background-repeat: no-repeat;
        background-image: url(/assets/images/icons/reporting.png);
    }

    &.icon_briefcase {
        width: 80px;
        height: 80px;
        background-size: contain;
        background-repeat: no-repeat;
        background-image: url(/assets/images/icons/briefcase.png);
    }

    &.icon_calendar {
        width: 80px;
        height: 80px;
        background-size: contain;
        background-repeat: no-repeat;
        background-image: url(/assets/images/icons/calendar.png);
    }

    &.icon_file {
        width: 80px;
        height: 80px;
        background-size: contain;
        background-repeat: no-repeat;
        background-image: url(/assets/images/icons/file-text.png);
    }

    &.icon_layer {
        width: 80px;
        height: 80px;
        background-size: contain;
        background-repeat: no-repeat;
        background-image: url(/assets/images/icons/layers.png);
    }

    &.icon_mail {
        width: 80px;
        height: 80px;
        background-size: contain;
        background-repeat: no-repeat;
        background-image: url(/assets/images/icons/email.png);
    }

    &.icon_setting {
        width: 80px;
        height: 80px;
        background-size: contain;
        background-repeat: no-repeat;
        background-image: url(/assets/images/icons/parameter.png);
    }

    &.icon_clipboard {
        width: 80px;
        height: 80px;
        background-size: contain;
        background-repeat: no-repeat;
        background-image: url(/assets/images/icons/clipboard.png);
    }

    &.icon_grid {
        width: 80px;
        height: 80px;
        background-size: contain;
        background-repeat: no-repeat;
        background-image: url(/assets/images/icons/grid.png);
    }

    &.icon_shield {
        width: 80px;
        height: 80px;
        background-size: contain;
        background-repeat: no-repeat;
        background-image: url(/assets/images/icons/shield.png);
    }

    &.icon_cube {
        width: 80px;
        height: 80px;
        background-size: contain;
        background-repeat: no-repeat;
        background-image: url(/assets/images/icons/cube.png);
    }

    &.icon_layout {
        width: 80px;
        height: 80px;
        background-size: contain;
        background-repeat: no-repeat;
        background-image: url(/assets/images/icons/layout.png);
    }

    &.icon_npm {
        width: 80px;
        height: 80px;
        background-size: contain;
        background-repeat: no-repeat;
        background-image: url(/assets/images/icons/npm.png);
    }

    &.icon_shopping-bag {
        width: 76px;
        height: 100px;
        background-size: contain;
        background-position: center;
        background-repeat: no-repeat;
        background-image: url(/assets/images/icons/shoppingbag.png);
    }

    &.icon_shipped {
        width: 76px;
        height: 100px;
        background-size: contain;
        background-position: center;
        background-repeat: no-repeat;
        background-image: url(/assets/images/icons/shipped.png);
    }

    &.icon_calendar {
        width: 76px;
        height: 100px;
        background-size: contain;
        background-position: center;
        background-repeat: no-repeat;
        background-image: url(/assets/images/icons/calendar.png);
    }

    &.icon_track {
        width: 76px;
        height: 100px;
        background-size: contain;
        background-position: center;
        background-repeat: no-repeat;
        background-image: url(/assets/images/icons/track.png);
    }

    &.icon_notification {
        width: 76px;
        height: 100px;
        background-size: contain;
        background-position: center;
        background-repeat: no-repeat;
        background-image: url(/assets/images/icons/notification.png);
    }

    &.icon_linechart {
        width: 76px;
        height: 100px;
        background-size: contain;
        background-position: center;
        background-repeat: no-repeat;
        background-image: url(/assets/images/icons/linechart.png);
    }
}