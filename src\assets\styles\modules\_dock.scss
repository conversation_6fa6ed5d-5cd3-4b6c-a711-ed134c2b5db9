.dock-management-container {
  // background-color: #f3fbff;
  height: calc(100vh - 76px);

  .dock-truck-container {
    // height: calc(100% - 76px);
    height: calc(100% - 70px);
    display: flex;
  }

  .dock-truck-container .truck {
    width: 380px;
    height: 100%;
    background-color: #fff;
    margin-right: 15px;
    border-radius: 5px;

    .search-bar {
      height: 70px;
      border-bottom: 2px solid #e9e9e9;
      @include vh-center;
      padding: 0 30px;

      nb-form-field {
        width: 100%;
      }
    }

    .truck-container {
      height: calc(100% - 70px);
      padding: 15px 30px;
      overflow: auto;
      display: flex;
      flex-direction: column;
      justify-content: flex-start;

      .truck-box {
        box-shadow: 0px 4px 17px rgba(0, 0, 0, 0.06);
        border-radius: 7px;
        border: 1px solid #efefef;
        margin-bottom: 10px;
        height: 145px;
        width: 100%;
        padding: 16px;
        cursor: move;
        @include v-center;

        .logo {
          @include vh-center;
          border-radius: 50%;
          border: 2px solid $color-primary;
          width: 55px;
          height: 55px;
          margin-right: 10px;

          nb-icon {
            font-size: 15px;
          }
        }

        .infors {
          height: 100%;
          width: calc(100% - 60px);
          display: flex;
          justify-content: center;
          flex-direction: column;

          .row {
            @include v-center;
            margin-bottom: 5px;

            .key,
            .value {
              width: 50%;
              @include v-center;
              justify-content: flex-start;
              font-family: $font-regular;
              font-size: 11px;
            }

            .key {
              margin-right: 5px;
            }
          }
        }
      }
    }
  }

  .dock-truck-container .dock {
    height: 100%;
    width: calc(100% - 380px);

    // background-color: #f3fbff;
    .search-bar {
      height: 70px;
      @include vh-center;
      padding: 0 16px;

      .text {
        width: 100%;
        @include v-center;
        height: 100%;
        font-size: 1.2em;
      }

      .search-input {
        width: 100%;
        @include v-center;
        height: 100%;

        .filter-elt {
          width: 100%;
        }
      }
    }

    .dock-container {
      height: calc(100% - 70px);
      padding: 0px 10px;
      overflow: auto;
      flex-wrap: wrap;
      display: flex;
      justify-content: space-between;

      .dock-box {
        border: 1px solid $color-primary;
        border-radius: 7px;
        margin-bottom: 10px;
        height: 250px;
        padding: 10px;
        width: calc(50% - 5px);

        .dock-code {
          border-radius: 7px;
          background-color: $color-primary;
          color: #fff;
          font-weight: 700;
          font-family: $font-regular;
          text-transform: capitalize;
          width: 32%;
          height: 25px;
          padding: 0 5px;
          @include vh-center;
          font-size: 12px;
          margin-bottom: 16px;
        }

        .trucks-dock {
          width: 100%;
          height: calc(100% - 25px);
          overflow: auto;
          flex-wrap: wrap;
          @include v-center;
        }

        .truck-box-container {
          height: calc(100% - 35px);
          overflow: auto;
        }

        .truck-box {
          box-shadow: 0px 4px 17px rgba(0, 0, 0, 0.06);
          border-radius: 7px;
          border: 1px solid #efefef;
          margin-bottom: 7px;
          height: 80px;
          padding: 9px;
          cursor: move;
          background-color: #fff;
          @include v-center;

          .logo {
            @include vh-center;
            border-radius: 50%;
            border: 2px solid $color-primary;
            width: 30px;
            height: 30px;
            margin-right: 7px;

            nb-icon {
              font-size: 15px;
            }
          }

          .infors {
            height: 100%;
            width: calc(100% - 40px);
            display: flex;
            justify-content: center;
            flex-direction: column;

            .row {
              @include v-center;

              .key,
              .value {
                // width: 50%;
                @include v-center;
                justify-content: flex-start;
                font-family: $font-regular;
                font-size: 11px;
              }

              .key {
                margin-right: 2px;
              }
            }
          }

          .arrow {
            @include vh-center;
            cursor: pointer;
            // nb-icon{
            //   font-size: 10px;
            // }
          }
        }
      }
    }
  }

  .input-elmt {
    width: 100%;
    font-family: $font-regular;
    font-size: 13px;
    color: #000;
  }

  nb-form-field .input-elmt::placeholder {
    font-family: $font-regular;
    font-size: 13px;
    color: #000;
  }

  .legend {
    width: 50%;
    padding: 0 15px;
    margin-top: 10px;
    @include v-center;

    .content {
      width: 155px;
      @include v-center;

      .bullet {
        height: 15px;
        width: 15px;
        margin-right: 5px;
        border-radius: 50%;
      }

      .text {
        width: calc(100% - 20px);
        font-family: $font-regular;
        font-size: 11px;
      }
    }
  }

  .dragPreview {
    padding: 10px 25px;
    background-color: $color-primary;
    font-size: 16px;
    font-family: $font-bold;
    color: #fff;
  }
}

.cdk-drag-preview {
  box-sizing: border-box;
  border-radius: 4px;
  box-shadow: 0 5px 5px -3px rgba(0, 0, 0, 0.2), 0 8px 10px 1px rgba(0, 0, 0, 0.14), 0 3px 14px 2px rgba(0, 0, 0, 0.12);
}

.cdk-drag-animating {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

.ae-container.cdk-drop-list-dragging .ae:not(.cdk-drag-placeholder) {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

.example-custom-placeholder {
  background: #ccc;
  border: dotted 3px #999;
  min-height: 60px;
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

@media screen and (max-width: 1500px) {
  .dock-management-container .dock-container {}
}