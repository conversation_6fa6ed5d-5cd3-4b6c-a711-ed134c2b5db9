import * as moment from 'moment';
import cloneDeep from 'lodash-es/cloneDeep';
import { filter, map } from 'rxjs/operators';
import { Component, OnInit, TemplateRef, ViewChild, ChangeDetectorRef } from '@angular/core';
import { RemovalService } from '../removal-management/removal.service';
import { NbDialogRef, NbDialogService, NbMenuService, NbToastrService } from '@nebular/theme';
import { AuthorizationRemoval, StatusAEsJde } from 'src/app/shared/models/authorization-removal';

import { InspectionDecision, WaitingThread } from 'src/app/shared/models/inspectionData-enum';
import { CommonService } from 'src/app/shared/services/common.service';
import { InspectionStatus } from 'src/app/shared/enums/inspection.enum';
import { PaginationConfig } from 'src/app/shared/services/pagination.service';

@Component({
  selector: 'clw-inspections',
  templateUrl: './inspections.component.html',
  styles: [
  ]
})
export class InspectionsComponent implements OnInit {
  @ViewChild('detailDialog') detailDialog: TemplateRef<any>;
  @ViewChild('updateDialog') updateDialog: TemplateRef<any>;
  @ViewChild('imageDialog') imageDialog: TemplateRef<any>;
  @ViewChild('validateDialog') validateDialog: TemplateRef<any>;

  imageGood = '../../assets/images/icons/checkmark-green.svg';
  imageBad = '../../assets/images/icons/close-default.svg';
  loading: any;
  total: number;
  dialogRef: any;
  images = [];
  inspection: any;
  inspections: any[] = [];
  isLoading: boolean;
  dataForFilter: any;
  dataDrivers: any;
  activeFilter = [];
  dataAELabels: any;
  dataAELabelsExport: any;
  dataCarrierLabels: any;
  currentInspections: any;
  dataDecisionsLabels: any;
  dataTruckImmatriculation: any;
  validateRef: NbDialogRef<any>;
  detailDetailRef: NbDialogRef<any>;
  modifyInspectionRef: NbDialogRef<any>;
  inspectionDecision = InspectionStatus;

  filterForm = {
    status: '',
    LoadNumber: '',
    driver: '',
    carrierLabel: '',
    truck: '',
    LoadStatus: '',
    startDate: new Date(new Date().getFullYear(), 0, 1),
    endDate: new Date(new Date().getFullYear(), 11, 31),
  }

  InspectionDecisions = ['ACCEPTEE', 'REJETEE'];
  waitingThreads = ['PARKING USINE', 'PARKING']
  thread: any;
  selectedImage: any;
  selectedImageIndex: number | null = null;
  file: any;
  filename: any;
  imageSrc: string;
  isImageUploaded: boolean;
  dataInpections: any;

  jdeStatus = [
    { name: "Non approuvée", code: StatusAEsJde.NOTAPPROVED },
    { name: "Créer et approuvée", code: StatusAEsJde.APPROVED },
    { name: "Rejetée", code: StatusAEsJde.REJECTED },
    { name: "Facturée", code: StatusAEsJde.INVOICED },
    { name: 'Au chargement partielle', code: StatusAEsJde.LOADSTATUS60 },
    { name: "Sortie livraison", code: StatusAEsJde.OUTPUT },
    { name: "En saisie", code: StatusAEsJde.CHECK_IN },
  ]

  actionItems = [
    {
      title: 'Detail',
      icon: 'file-text-outline',
      data: { action: 'detail' }
    },
    {
      title: 'Modifier',
      icon: 'edit-2-outline',
      data: { action: 'update' }
    },
    {
      title: 'Voir image',
      icon: 'image-outline',
      data: { action: 'image' }
    }
  ];
  private contextInspection: any;
  paginationConfig: PaginationConfig = {
    currentPage: 1,
    itemsPerPage: 10, // pagination 10 par 10
    totalItems: 0,
    maxSize: 10
  };


  constructor(
    private dialogSvr: NbDialogService,
    private removalSvr: RemovalService,
    private commonService: CommonService,
    private toastrSvr: NbToastrService,
    private nbMenuService: NbMenuService,
    private cdr: ChangeDetectorRef
  ) {
    this.nbMenuService.onItemClick()
    .pipe(
      filter(({ tag }) => tag === 'action-menu'),
      map(({ item }) => item.data)
    )
    .subscribe(data => {
      switch(data.action) {
        case 'detail':
          this.openDetailModal(this.detailDialog, this.contextInspection);
          break;
        case 'update':
          this.openUpdateInspection(this.updateDialog, this.contextInspection);
          break;
        case 'image':
          this.openImageInspection(this.imageDialog, this.contextInspection);
          break;
        case 'validate':
          this.openValidateInspection(this.validateDialog, this.contextInspection);
          break;
      }
    });
  }

  async ngOnInit(): Promise<void> {
    this.paginationConfig = {
      currentPage: 1,
      itemsPerPage: 10, // pagination 10 par 10
      totalItems: 0,
      maxSize: 10
    };
    await this.getInspections();
    await this.getDataForFilter();
    this.activeFilter.length = 0;
  }

  setContextInspection(inspection: any) {
    this.contextInspection = inspection;

    // Mise à jour dynamique du menu selon le statut de l'inspection
    this.actionItems = [
      {
        title: 'Detail',
        icon: 'file-text-outline',
        data: { action: 'detail' }
      },
      {
        title: 'Modifier',
        icon: 'edit-2-outline',
        data: { action: 'update' }
      },
      {
        title: 'Voir image',
        icon: 'image-outline',
        data: { action: 'image' }
      }
    ];

    // Ajout conditionnel de l'option "Envoyer au parking"
    if (inspection.status === this.inspectionDecision.COMPLETED) {
      this.actionItems.push({
        title: 'Envoyer au parking',
        icon: 'arrow-circle-right-outline',
        data: { action: 'validate' }
      });
    }
  }
  setCarrierInspection() {
    if (!('carrier' in this.currentInspections)) {
      this.currentInspections.carrier = {
        driver: {
          fullname: '',
          tel: '',
          cni: '',
          license: ''
        },
        truckImmatriculation: '',
        truck: {
          immatriculation: ''
        }
      };
    }
  }

  async getInspections(): Promise<void> {
    const options = {
      limit: this.paginationConfig.itemsPerPage,
      offset: (this.paginationConfig.currentPage - 1) * this.paginationConfig.itemsPerPage,
      ...this.filterForm,
      startDate: moment(this.filterForm.startDate).format('MM-DD-YYYY'),
      endDate: moment(this.filterForm.endDate).format('MM-DD-YYYY'),
    }
    try {
      this.isLoading = true;
      const result = await this.removalSvr.getAllInspections(options);
      console.log('API result:', result); // <-- debug log
      const data = result.data;
      const totalItems = typeof result['count'] === 'number'
        ? result['count']
        : (typeof result['total'] === 'number' ? result['total'] : 0);
      this.inspections = Array.isArray(data) ? data : [];
      this.paginationConfig = {
        ...this.paginationConfig,
        totalItems
      };
      this.cdr.detectChanges();
    } catch (error) {
      console.log(error);
      this.toastrSvr.danger('Impossible de récupérer la liste des AE', 'Erreur connexion !');
    } finally { this.isLoading = false; }
  }

  async getDataForFilter() {
    try {
      this.loading = true;
      this.dataForFilter = await this.removalSvr.getFilterData({
        ...this.filterForm,
        startDate: moment(this.filterForm.startDate).format('MM-DD-YYYY'),
        endDate: moment(this.filterForm.endDate).format('MM-DD-YYYY'),
        keyForFilters: ['LoadNumber', 'decision', 'carrier.label', 'carrier.driver.fullname', 'carrier.truckImmatriculation',]
      },);
      this.dataDrivers = this.dataForFilter['datacarrierdriver.fullname'] ?? [];
      this.dataCarrierLabels = this.dataForFilter['datacarrierlabel'] ?? [];
      this.dataAELabels = this.dataForFilter?.dataLoadNumber;
      this.dataDecisionsLabels = this.dataForFilter?.datadecision;
      this.dataTruckImmatriculation = this.dataForFilter['datacarriertruckImmatriculation'];

    } catch (error) {
      return error;
    } finally { this.loading = false }
  }

  async sendToPark(): Promise<any> {
    try {
      this.isLoading = true;
      const currentAE = await this.removalSvr.getAuthorizationRemoval({ LoadNumber: this.inspection?.LoadNumber }) as unknown as AuthorizationRemoval;
      currentAE.inspection = {
        id: this.inspection._id,
        status: this.inspection.status
      }
      const updateAe = {
        ...currentAE,
        parkTime: moment().valueOf(),
        status: this.thread === WaitingThread.PARKING_USINE ? 400 : 700,
      };
      await this.removalSvr.updateAe(currentAE?._id, updateAe);

      await this.getInspections();
      return this.toastrSvr.success(`Cette inspection a été mise à jour avec succès`, `Mise à jour réussi`);
    } catch (error) {
      this.toastrSvr.danger(`Une erreur est survenue lors de la mise à jour de cette inspection.`, 'Erreur de mise à jour');
    } finally {
      this.isLoading = false;
    }
  }

  async saveInspection(): Promise<any> {
    try {
      this.isLoading = true;
      await this.removalSvr.updateInspection(this.currentInspections?._id, { ...this.currentInspections })
      if (this.currentInspections?.decision === InspectionDecision.REJECTED) {
        const res = await this.removalSvr.getAuthorizationRemoval({ LoadNumber: this.currentInspections?.LoadNumber }) as unknown as AuthorizationRemoval;
        const currentAE = res?.data[0];

        const updateAe = {
          ...currentAE,
          inspection: {
            _id: this.currentInspections?._id,
            status: false
          },
          status: 200,
        };
        await this.removalSvr.updateAe(currentAE?._id, updateAe);
      }
      await this.getInspections();
      return this.toastrSvr.success(`Cette inspection a été mise à jour avec succès`, `Mise à jour réussi`);
    } catch (error) {
      this.toastrSvr.danger(`Une erreur est survenue lors de la mise à jour de cette inspection.`, 'Erreur de mise à jour');

    } finally {
      this.isLoading = false;
      this.isImageUploaded = false;
    }
  }

  async openDetailModal(dialog?: any, inspection?: any): Promise<void> {
    this.inspection = { ...inspection };
    this.dialogRef = this.openModal(dialog);
  }

  openUpdateInspection(dialog?: any, inspection?: any) {
    this.currentInspections = cloneDeep(inspection);
    this.setCarrierInspection();
    // Normalize truckImmatriculation for ngModel binding
    if (this.currentInspections?.carrier) {
      this.currentInspections.carrier.truckImmatriculation =
        this.currentInspections.carrier.truckImmatriculation ||
        this.currentInspections.carrier.immatriculation ||
        '';
    }
    this.dialogRef = this.openModal(dialog);
  }

  openImageInspection(dialog?: any, inspection?: any) {
    this.currentInspections = cloneDeep(inspection);

    this.images = inspection.image;

    this.dialogRef = this.openModal(dialog);
  }

  openExportInspection(dialog?: any) {
    this.activeFilter.push(0);
    this.dialogRef = this.openModal(dialog);
  }

  selectImage(image: string, index: number) {
    this.selectedImage = image;
    this.selectedImageIndex = index;
  }

  clearSelectedImage() {
    this.selectedImage = null;
    this.selectedImageIndex = null;
  }

  getFile(fileDataSet: any, index: number): any {
    this.file = fileDataSet.target.files[0];
    const type = this.file.type;
    if (!type.includes('image')) {
      this.file = null;
      return this.toastrSvr.danger('Veuillez importer uniquement les images', 'Donnés incorrectes');
    }
    this.filename = this.file.name;
    const reader = new FileReader();
    if (fileDataSet.target.files && fileDataSet.target.files.length) {
      const [file] = fileDataSet.target.files;
      reader.readAsDataURL(file);
      reader.onload = () => {
        this.imageSrc = reader.result as string;
        this.images[index] = this.imageSrc;
        this.currentInspections.image = this.images;
        this.isImageUploaded = true;
      };
    }
  }

  updateImage() {
    document.getElementById('file').click()

  }

  async openValidateInspection(dialog?: any, inspection?: any): Promise<void> {
    this.inspection = { ...inspection };

    this.dialogRef = this.openModal(dialog);
  }

  async onSelectionAEChange(event: any): Promise<void> {
    if (event?.keyToReset) {
      console.log(event?.dataToReset);
      this.filterForm[event?.keyToReset] = '';
      this[event?.dataToReset](null);
    }
    if (event?.$event)
      await this.getInspections();
  }

  async onSelectionAEChangeExport(event: any): Promise<void> {
    if (event?.keyToReset) {
      console.log(event?.dataToReset);
      this.filterForm[event?.keyToReset] = '';
      this[event?.dataToReset](null);
    }
    if (event?.$event)
      await this.getInspections();
  }

  openModal(dailog: any): void {
    this.dialogRef = this.dialogSvr.open(dailog, {});
  }

  onPageChange(page: number): void {
    this.paginationConfig = {
      ...this.paginationConfig,
      currentPage: page
    };
    this.getInspections();
  }

  onItemsPerPageChange(itemsPerPage: number): void {
    this.paginationConfig = {
      ...this.paginationConfig,
      itemsPerPage,
      currentPage: 1
    };
    this.getInspections();
  }

  reset() {
    this.filterForm = {
      status: '',
      LoadStatus: '',
      LoadNumber: '',
      driver: '',
      carrierLabel: '',
      truck: '',
      startDate: new Date(new Date().getFullYear(), 0, 1),
      endDate: new Date(new Date().getFullYear(), 11, 31),
    }
    this.activeFilter.length = 0;
    this.paginationConfig = {
      ...this.paginationConfig,
      currentPage: 1
    };
    this.getInspections();
    this.getDataForFilter();
  }

  async onModelDateChange(event: any): Promise<void> {
    if (moment(this.filterForm.endDate).valueOf() < moment(this.filterForm.startDate).valueOf()) {
      this.toastrSvr.danger('la date de fin est inférieure à la date de début', 'Erreurs Données!');
      return;
    }
    this.paginationConfig = {
      ...this.paginationConfig,
      currentPage: 1
    };
    await this.getInspections();
    await this.getDataForFilter();
  }

  onModelAeChange(value: any): void {
    if (value === null)
      this.dataAELabels = this.dataForFilter?.dataLoadNumber;

    if (value)
      this.dataAELabels = this.dataForFilter?.dataLoadNumber?.filter((data: string) => JSON.stringify(data)?.includes(value)).sort((a, b) => {
        return a.toString().toLowerCase().indexOf(value?.toLowerCase()) - b.toString().toLowerCase().indexOf(value?.toLowerCase());
      });;
  }


  onModelCarrierChange(value: any): void {
    if (value === null)
      this.dataCarrierLabels = this.dataForFilter['datacarrierlabel'];

    if (value)
      this.dataCarrierLabels = this.dataForFilter['datacarrierlabel']?.filter((data: string) =>
        data.toLowerCase().includes(value?.toLowerCase())).sort((a, b) => {
          return a.toString().toLowerCase().indexOf(value?.toLowerCase()) - b.toString().toLowerCase().indexOf(value?.toLowerCase());
        });;
  }

  onModelTruckImmatriculationChange(value: any): void {
    if (value === null)
      this.dataTruckImmatriculation = this.dataForFilter['datacarriertruckImmatriculation'];

    if (value)
      this.dataTruckImmatriculation = this.dataForFilter['datacarriertruckImmatriculation']?.filter((data: string) =>
        data.toLowerCase().includes(value?.toLowerCase())).sort((a, b) => {
          return a.toString().toLowerCase().indexOf(value?.toLowerCase()) - b.toString().toLowerCase().indexOf(value?.toLowerCase());
        });
  }

  onModelTruckDriverChange(value: any): void {
    if (value === null)
      this.dataTruckImmatriculation = this.dataForFilter['datacarrier.driver.fullname'];

    if (value)
      this.dataTruckImmatriculation = this.dataForFilter['datacarrier.driver.fullname']?.filter((data: string) =>
        data.toLowerCase().includes(value?.toLowerCase())).sort((a, b) => {
          return a.toString().toLowerCase().indexOf(value?.toLowerCase()) - b.toString().toLowerCase().indexOf(value?.toLowerCase());
        });
  }

  async exportToExcel() {
    this.isLoading = true;
    await this.getInspections();
    this.dataInpections = this.inspections.map(elt => {
      const data: any = {};
      data['N° AE'] = elt?.LoadNumber || 'N/A';
      data['Type AE'] = elt?.type === "C9" ? "PICK UP" : "RENDU";
      data['Date création'] = moment(elt?.date).format('YYYY-MM-DD');
      data['Transporteur'] = elt?.carrier?.label || (elt?.authorizationRemoval && elt?.authorizationRemoval[0]?.Alpha) || 'N/A';
      data['Immatriculation Camion'] = elt?.carrier?.truckImmatriculation || elt?.carrier?.immatriculation
        || (elt?.authorizationRemoval && elt?.authorizationRemoval[0]?.carrier?.truck?.immatriculation) || elt?.tractor?.label || 'N/A';
      data['Chauffeur'] = elt?.carrier?.driver?.fullname || elt?.driver ||
        (elt?.authorizationRemoval && elt?.authorizationRemoval[0]?.carrier?.driver?.fullname) || 'N/A';
      data['Non du contrôleur'] = (elt?.userData && elt?.userData[0]?.fname || '') + ' ' + (elt?.userData && elt?.userData[0]?.lname || '') ||
        (elt?.userData && elt?.userData[0]?.email) || 'N/A';
      data['Site'] = elt?.BusinessUnit || (elt?.authorizationRemoval && elt?.authorizationRemoval[0]?.BusinessUnit) || 'N/A';
      data['Decision'] = elt?.decision || 'N/A';
      if (elt?.decision && elt?.decision.includes('REJETEE')) {
        const notValidCheckList = elt?.checkList?.filter((check: { state: boolean; }) => !check?.state);
        data['Raison du rejet'] = notValidCheckList.map((elt: { label: string; }) => `Non Conformité ${elt?.label}`).join(', ');
      }
      return data;
    });
    this.commonService.exportRetriveExcelFile(this.dataInpections, 'Liste des inspections');
    this.isLoading = false;
  }

  verifyname(event: any) {
    const input = event.target as HTMLInputElement;
    this.currentInspections.carrier.driver.fullname = input.value.replace(/[`£µ§²ù!@#$%^&*()+\=\[\]{};':"\\|,.<>\/?~]/g, '');
  }
  verifyCni(event: any) {
    const input = event.target as HTMLInputElement;
    this.currentInspections.carrier.cni = input.value.replace(/[`£µ§²ù!@#$%^&*()+\=\[\]{};':"\\|,.<>\/?~]/g, '');
  }
  verifyPermis(event: any) {
    const input = event.target as HTMLInputElement;
    this.currentInspections.carrier.license = input.value.replace(/[`£µ§²ù!@#$%^&*()+\=\[\]{};':"\\|,.<>\/?~]/g, '');
  }
  verifyTel(event: any) {
    const input = event.target as HTMLInputElement;
    if (this.currentInspections?.carrier?.driver?.tel?.length > 12) {
      this.currentInspections.carrier.tel = input.value.replace(/[`^0-9a-zA-Z£µ§²ù!@#$%^&*()+\=\-\[\]{};':"\\|,._<>\/?~]$/g, '');
      this.toastrSvr.warning(`Le numéro de téléphone ne doit pas être supérieur à 12`);
    } else {
      this.currentInspections.carrier.tel = input.value.replace(/[`^a-zA-Z£µ§²ù!@#$%^&*()+\=\-\[\]{};':"\\|,._<>\/?~]/g, '');
    }
  }

}
