export interface Store {
    _id?: string;
    label?: string;
    jdeReference?: string;
    jdeSoldToId?: number;
    jdeShipToId?: number;
    storeRef?: string;
    type: StoreType;
    address?: {
        region?: string;
        city?: string;
    }
}

export enum StoreType {
    FACTORY = 100,
    DEFAULT = 200,
}

export const  StoreListeLibrairie = {

    ​'CM12000':'DÉPÔT DE YAOUNDÉ',
    ​'CM17000':'MAGASIN MORTIER BONABÉRIE',
    ​'CM13010':'DÉPÔT DE YAOUNDÉ',
    ​'CM13000':'DÉPÔT DE DOUALA',
    ​'CM12070':'DÉPÔT GAROUA BOULAI',
    ​'CM12040':'DÉPÔT KYE OSSI',
    ​'CM12110':'DÉPÔT KOUSSÉRI',
    ​'CM12100':'DÉPÔT GAROUA',
    ​'CM12090':'DÉPÔT BÉLABO',
    ​'CM12030':'DÉPÔT NGAOUNDÉRÉ',
    ​'CM12020':'DÉPÔT MAROUA',
    ​'CM10020':'USINE DE NOMAYOS',
    ​'CM10010':'USINE DE FIGUIL',
    ​'CM10000':'USINE DE BONABERI',
    ​'CM00000':'TOUTES LES USINES',

  
}