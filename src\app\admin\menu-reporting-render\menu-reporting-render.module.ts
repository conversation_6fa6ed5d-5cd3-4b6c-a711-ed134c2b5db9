import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { NbCardModule,
  NbInputModule,
  NbButtonModule,
  NbIconModule,
  NbSpinnerModule,
  NbListModule,
  NbTooltipModule,
  NbFormFieldModule,
  NbDatepickerModule,
  NbProgressBarModule,
  NbToggleModule,
  NbSelectModule, 
  NbTabsetModule} from '@nebular/theme';
import { MenuReportingRenderRoutingModule } from './menu-reporting-render-routing.module';
import { MenuReportingRenderComponent } from './menu-reporting-render.component';
import { SharedModule } from 'src/app/shared/shared.module';
import { TileInspectionComponent } from './tile-inspection/tile-inspection.component';
import { TileNumbersComponent } from './tile-numbers/tile-numbers.component';
import { TileAesRenderComponent } from './tile-aes-render/tile-aes-render.component';
import { TileCarriersComponent } from './tile-carriers/tile-carriers.component';
import { TileCarrierResponseTimeComponent } from './tile-carrier-response-time/tile-carrier-response-time.component';
import { TileCarrierDeliveryTimeComponent } from './tile-carrier-delivery-time/tile-carrier-delivery-time.component';
import { TileChartStoreComponent } from './tile-chart-store/tile-chart-store.component';
import { TileTimesComponent } from './tile-times/tile-times.component';
import { TileChartRemovalsComponent } from './tile-chart-removals/tile-chart-removals.component';
import { TileChartProductComponent } from './tile-chart-product/tile-chart-product.component';
import { TileInspectionRatioComponent } from './tile-inspection-ratio/tile-inspection-ratio.component';



@NgModule({
  declarations: [
    MenuReportingRenderComponent,
    TileInspectionComponent,
    TileNumbersComponent,
    TileAesRenderComponent,
    TileCarriersComponent,
    TileCarrierResponseTimeComponent,
    TileCarrierDeliveryTimeComponent,
    TileChartStoreComponent,
    TileTimesComponent,
    TileChartRemovalsComponent,
    TileChartProductComponent,
    TileInspectionRatioComponent
 
  ],
  imports: [
    CommonModule,
    NbCardModule,
    FormsModule,
    NbIconModule,
    NbListModule,
    NbInputModule,
    SharedModule,
    NbTabsetModule,
    NbButtonModule,
    NbToggleModule,
    NbSelectModule,
    NbSpinnerModule,
    NbTooltipModule,
    NbFormFieldModule,
    NbDatepickerModule,
    NbProgressBarModule,
    MenuReportingRenderRoutingModule
  ]
})
export class MenuReportingRenderModule { }
