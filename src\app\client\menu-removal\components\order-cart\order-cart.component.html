<!-- order-card.component.html -->
<div class="cart" *ngFor="let order of orders">
    <nb-card class="order-card">
        <nb-card-body>
            <!-- Header avec numéro de bon et date de création -->
            <div class="left-bloc">
                <div class="order-header">
                    <div class="order-icon-container">
                        <img src="../../../../assets/images/document-text.png" class="document-icon" alt="liste vide">
                    </div>
                    <div class="order-info">
                        <div class="order-title">
                            <h5>N° X3 {{order?.erpReference}}</h5>
                        </div>
                        <p class="creation-date"><PERSON><PERSON><PERSON> le {{order?.created_at | date:'dd/MM/yyyy à HH'}} H
                            {{order.created_at | date:'mm'}}</p>
                    </div>
                </div>

                <div class="products-section">
                    <div class="product-icon-container">
                        <nb-icon icon="shopping-bag-outline" class="product-icon"></nb-icon>
                    </div>
                    <div class="product-info">
                        <p class="section-label">Produits</p>
                        <p class="product-name">{{ getLabel(order?.cart?.items)}}</p>
                    </div>
                </div>

                <div class="details-grid">
                    <div class="product-icon-container">
                        <nb-icon icon="menu-2-outline" class="product-icon"></nb-icon>
                    </div>

                    <div class="detail-column">
                        <p class="section-label" nbTooltip="{{order?.company?.name }}">Nom Client</p>
                        <p class="detail-value" nbTooltip="{{order?.company?.name }}">{{ order?.company?.name |
                            truncateString:12}}</p>
                    </div>
                    <div class="detail-column">
                        <p class="section-label">Quantité(s)</p>
                        <p class="detail-value">{{getQdty(order?.cart?.items)}}Sacs</p>
                    </div>
                    <div class="detail-column">
                        <p class="section-label">Livrés</p>
                        <p class="detail-value delivered">{{getQdtyDelivery(order?.cart?.items)}} Sacs</p>
                    </div>
                </div>


            </div>

            <div class="rigth-bloc">

                <div class="status">
                    <span class="render-pill"
                        [ngClass]="'render-' + order?.cart?.renderType">{{getStatusRender(order?.cart?.renderType)}}</span>
                    <span class="status-pill"
                        [ngClass]="'status-' + order?.statusDelivery">{{getStatusText(order?.statusDelivery)}}</span>

                </div>
                <div class="action-buttons">
                    <button nbButton filled status="primary" class="split-btn"
                        (click)="onSplitOrder(newAesDialog, order)">
                        Découper la commande
                    </button>
                    <button nbButton outline status="primary" class="detail-btn"
                        (click)="onViewDetails(detailDialog, order)">
                        Detail de la commande
                    </button>

                </div>
            </div>

        </nb-card-body>

        <!-- Indicateur de statut en bas de la carte -->
        <div class="status-bar" [style.width.%]="calculateDeliveryPercentage(order)"
            [ngClass]="'status-' + order?.statusDelivery"></div>
    </nb-card>


</div>

<ng-template #newAesDialog let-ref="dialogRef">

    <div class="partition-container">
        <div class="modal-header">
            <img src="../../../../assets/images/close-circle.png" (click)="ref.close(); groupedOrders =[];"
                class="close" alt="liste vide">

        </div>
        <div class="container">

            <div class="partition-card-info">
                <nb-card-body>
                    <!-- Header avec numéro de bon et date de création -->
                    <div class="left-bloc">
                        <div class="order-header">
                            <div class="order-icon-container">
                                <img src="../../../../assets/images/document-text.png" class="document-icon"
                                    alt="liste vide">
                            </div>
                            <div class="order-info">
                                <div class="order-title">
                                    <h5>N° X3 {{order?.erpReference}}</h5>
                                </div>
                                <p class="creation-date">Créer le {{order.created_at | date:'dd/MM/yyyy à HH'}} H
                                    {{order.created_at | date:'mm'}}</p>
                            </div>
                        </div>
                        <div class="details-grid">
                            <div class="product-icon-container">
                                <nb-icon icon="menu-2-outline" class="product-icon"></nb-icon>
                            </div>

                            <div class="detail-column">
                                <p class="section-label" nbTooltip="{{order?.company?.name }}">Nom Client</p>
                                <p class="detail-value" nbTooltip="{{order?.company?.name }}">{{order?.company?.name |
                                    truncateString:12}}</p>
                            </div>
                            <div class="detail-column">
                                <p class="section-label">Quantité(s)</p>
                                <p class="detail-value">{{getQdty(order?.cart?.items)}}</p>
                            </div>
                            <div class="detail-column">
                                <p class="section-label">Livrés</p>
                                <p class="detail-value delivered">{{getQdtyDelivery(order?.cart?.items)}} Sacs</p>
                            </div>
                        </div>

                    </div>

                    <div class="rigth-bloc">
                        <div class="status">
                            <span class="render-pill"
                                [ngClass]="'render-' + order?.cart?.renderType">{{getStatusRender(order?.cart?.renderType)}}</span>

                            <span class="status-pill"
                                [ngClass]="'status-' + order.statusDelivery">{{getStatusText(order.statusDelivery)}}</span>
                        </div>
                    </div>

                </nb-card-body>

                <!-- Indicateur de statut en bas de la carte -->

                <div class="status-bar" [style.width.%]="calculateDeliveryPercentage(order)"
                    [ngClass]="'status-' + order?.statusDelivery"></div>

            </div>
            <div class="products-section">
                <div class="product-icon-container">
                    <nb-icon icon="shopping-bag-outline" class="product-icon"></nb-icon>
                </div>
                <div class="product-info">
                    <p class="section-label">Produits</p>
                    <div class="products-container">
                        <div class="product-item" *ngFor="let item of order?.cart?.items">
                            <img [src]="item?.product?.image" alt="{{ item?.product?.label }}" class="product-image">
                            <div class="product-name">{{ item?.product?.label }} ({{item?.packaging?.label}} ) - {{
                                item?.quantity - (item?.quantityShipped ?? 0) }} sac(s)
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="partition-card-main">
                <nb-card-body>
                    <!-- Header avec numéro de bon et date de création -->
                    <div class="left-bloc">
                        <div class="order-header">
                            <div class="order-icon-container">
                                <nb-icon icon="shopping-bag-outline" class="product-icon"></nb-icon>
                            </div>
                            <div class="order-info">
                                <div class="order-title">
                                    <h5>Partitionner</h5>
                                </div>

                            </div>
                        </div>

                        <div class="filter-product-main">
                            <div class="filters">
                                <nb-select status="success" class="nbSelect-width" size="small"
                                    placeholder="Selectionner un produit" (selectedChange)="onGetAvaibleQdty($event)">
                                    <nb-option [value]="label" *ngFor="let label of order?.cart?.items">
                                        {{ label?.product?.label }} {{label?.packaging?.label}}
                                    </nb-option>
                                </nb-select>

                                <input type="number" placeholder="Entrer la quantité" fieldSize="small" nbInput
                                    [disabled]="currentItem?.quantityDelivery === currentItem?.quantity"
                                    [(ngModel)]="currentItem.quantityShipped" class="empty-input height" />

                            </div>

                            <div class="available-qdty" *ngIf="avaibleQdty">
                                <span class="qdty">
                                    Quantité disponible :{{avaibleQdty}}
                                </span>
                            </div>
                        </div>

                        <div class="action-buttons">
                            <button nbButton filled [disabled]="!currentItem?.quantityShipped"
                                [ngClass]="{'disabled-btn': !currentItem?.quantityShipped}" status="primary"
                                class="split-btn" (click)="addNewQdty()">
                                Enregistrer la partition
                            </button>

                        </div>
                    </div>

                    <div class="rigth-bloc">

                        <div class="order-header">
                            <div class="order-icon-container">
                                <nb-icon icon="shopping-bag-outline" class="product-icon"></nb-icon>
                            </div>
                            <div class="order-info">
                                <div class="order-title">
                                    <h5>Liste des partitions</h5>
                                </div>

                            </div>
                        </div>

                        <div class="ae-partition-list">
                            <div class="aes-list" *ngFor="let elts of groupedOrders;">
                                <div class="label">
                                    <span class="elts">{{elts?.product?.label}} {{elts?.packaging?.label}}</span>

                                </div>
                                <div class="quantity">
                                    <span class="elts">{{elts?.quantityShipped}}</span>
                                    <span class="modify" (click)="modifyQdty(elts)">Modifier</span>
                                </div>

                            </div>
                            <div class="empty-list" *ngIf="!groupedOrders.length">
                                <nb-icon icon="info-outline"></nb-icon>
                                <p>Les produits partionnés apparaissent ici</p>
                                <p></p>
                            </div>

                        </div>


                        <div class="action-buttons">
                            <button nbButton [disabled]="!groupedOrders.length"
                                [ngClass]="{'disabled-btn': !groupedOrders?.length}" filled status="primary"
                                class="split-btn" (click)="save()">
                                Enregistrer les partition dans l’AE
                            </button>

                        </div>
                    </div>

                </nb-card-body>

            </div>

        </div>


    </div>
</ng-template>

<ng-template #detailDialog let-data let-ref="dialogRef">
    <div class="order-detail-container">
        <nb-card>
            <nb-card-header class="form--header">Détail de la commande</nb-card-header>
            <nb-card-body>
                <div class="order-infor">
                    <div class="right-block">
                        <p class="title">N° commande Cadyst grain: </p>
                        <p class="title">N° commande X3: </p>
                        <p class="title">Client: </p>
                        <p class="title">Date de création: </p>
                    </div>
                    <div class="left-block">
                        <p class="value">{{selectedOrder?.appReference}}</p>
                        <p class="value">{{selectedOrder?.erpReference}}</p>
                        <p class="value">{{selectedOrder?.company?.name}}</p>
                        <p class="value">{{formatDate(selectedOrder?.dates?.created)}} à
                            {{formatTime(selectedOrder?.dates?.created)}}</p>
                    </div>
                </div>
                <div class="list">
                    <header class="form--header seperator">Liste des produits</header>
                    <div class="list-elt-header">
                        <div class="col col-desc">Description</div>
                        <div class="col col-weight">Quantité</div>
                        <div class="col col-price">PU</div>
                        <div class="col col-amount">Montant</div>
                    </div>
                    <div class="list-elt-contain" *ngFor="let item of selectedOrder?.cart?.items">
                        <div class="list-elt">
                            <div class="col col-desc">
                                <div class="col-desc-elt">
                                    <div class="col-desc-elt-contain">
                                        <img [src]="item?.product?.image" alt="product" class="product-image">
                                    </div>
                                    <div class="label">{{item?.product?.label}}</div>
                                </div>
                            </div>
                            <div class="col col-weight">{{item?.quantity}} {{item?.packaging?.unit?.symbol}}</div>
                            <div class="col col-price">{{item?.unitPrice | number}}</div>
                            <div class="col col-amount">{{item?.quantity * item?.unitPrice | number}}</div>
                        </div>
                    </div>
                </div>
                <header class="header" style="color: #0b305c; font-weight: 600;">Récapitulatif de la livraison</header>
                <div class="order-infor">
                    <div class="right-block">
                        <p class="title">Point de retrait:</p>
                        <p class="title">Adresse de livraison prévue: </p>
                        <p class="title">Mode</p>
                        <p class="title">Montant</p>
                    </div>
                    <div class="left-block">
                        <p class="value">{{selectedOrder?.cart?.renderType === 1? selectedOrder?.cart?.store?.label :
                            selectedOrder?.cart?.shipping?.label || 'N/A' }}</p>
                        <p class="value"> {{ selectedOrder?.cart?.shipping?.deliveryDate ? (selectedOrder.cart.shipping.deliveryDate | date: 'dd/MM/yyyy':'fr') : 'N/A' }}</p>
                        <p class="value">{{getStatusRender(selectedOrder?.cart?.renderType)}}</p>
                        <p class="value">{{selectedOrder?.cart?.amount?.shipping | number:''}}</p>
                    </div>
                </div>
            </nb-card-body>
            <nb-card-footer class="form--footer">
                <button nbButton outline status="basic" class="" (click)="ref.close()">
                    <nb-icon icon="close-square-outline" [options]="{ animation: { type: 'zoom' } }"
                        class="remove-icon">
                    </nb-icon>
                    Fermer
                </button>
            </nb-card-footer>
        </nb-card>
    </div>
</ng-template>