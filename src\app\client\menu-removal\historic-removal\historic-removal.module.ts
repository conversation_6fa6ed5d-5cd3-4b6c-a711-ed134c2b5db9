import { NgModule, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { HistoricRemovalRoutingModule } from './historic-removal-routing.module';
import { SharedModule } from 'src/app/shared/shared.module';
import { HistoricRemovalComponent } from './historic-removal.component';
import { PickUpRemovalHistoryComponent } from './pick-up-removal-history/pick-up-removal-history.component';
import { 
  NbTabsetModule, NbCardModule, NbIconModule, NbListModule, 
  NbBadgeModule, NbDialogModule, NbButtonModule, NbSpinnerModule, 
  NbTooltipModule, NbSelectModule, NbInputModule, NbDatepickerModule, 
  NbAutocompleteModule 
} from '@nebular/theme';

@NgModule({
  declarations: [
    HistoricRemovalComponent,
    PickUpRemovalHistoryComponent
  ],
  imports: [
    CommonModule,
    FormsModule,
    SharedModule,
    HistoricRemovalRoutingModule,
    NbTabsetModule,
    NbCardModule,
    NbIconModule,
    NbListModule,
    NbBadgeModule,
    NbDialogModule,
    NbButtonModule,
    NbSpinnerModule,
    NbTooltipModule,
    NbSelectModule,
    NbInputModule,
    NbDatepickerModule,
    NbAutocompleteModule
  ],
  exports: [
    HistoricRemovalComponent,
    PickUpRemovalHistoryComponent
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class HistoricRemovalModule { }
