import { Component, OnInit, ViewEncapsulation } from '@angular/core';
import { Chart, registerables } from 'chart.js'
import { MenuReportingService } from '../menu-reporting.service';
import { RenderType } from 'src/app/shared/models/authorization-removal';

Chart.register(...registerables)

@Component({
  selector: 'clw-tile-chart-product',
  templateUrl: './tile-chart-product.component.html',
  styles: [
  ],
  encapsulation: ViewEncapsulation.None
})
export class TileChartProductComponent implements OnInit {

  product: any;
  isLoading: boolean;
  labels = [];
  productData: { data: number[], labels: string[] };

  constructor(
    private reportingSrv: MenuReportingService,
  ) { }

  async ngOnInit(): Promise<void> {
    await this.refresh();
  }

  async refresh() {
    this.isLoading = true;
    this.productData = await this.reportingSrv.getStatsProduct( { FreightHandlingCode: RenderType.PICKUP });    
    this.renderProduct();
    this.isLoading = false;
  }

  renderProduct(): void {
    let colors = [];
    this.productData?.data?.forEach((elt, i) => {
      colors.push(`rgb(255, ${(elt % (i + (elt/i)) || 255) * (i * 0.9) + 0.5}, ${72 * i}, 0.6)`)
    });
    if (this.product) { this.product.destroy(); }

    this.product = new Chart('canvas-product', {
      type: 'bar',
      data: {
        labels: this.productData.labels,
        datasets: [{
          label: 'Produits',
          data: this.productData.data,
          backgroundColor: colors,
          borderColor: colors,

        }],
      },
      options: {
        scales: {
          y: {
            beginAtZero: true
          }
        }
      },
    });
  }

}
