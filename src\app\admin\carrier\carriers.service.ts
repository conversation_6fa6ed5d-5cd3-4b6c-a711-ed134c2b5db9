import { HttpClient, HttpParams } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { Carrier } from "src/app/shared/models/carrier";
import { BaseUrlService } from "src/app/shared/services/base-url.service";
import { environment } from "src/environments/environment";
import { PaginationParams, Truck, TruckResponse } from "src/app/shared/models/trucks";
import { DriverParams, Driver, DriversResponse } from "src/app/shared/models/drivers";
import { lastValueFrom } from 'rxjs';

interface ApiResponse<T> {
  data: T[];
  count: number;
}

@Injectable({
  providedIn: 'root'
})
export class CarrierService {
  BASE_URL: string;
  selectedCarrier: Carrier;

  constructor(
    private http: HttpClient,
    private baseUrlSrv: BaseUrlService,
  ) {
    this.BASE_URL = `${this.baseUrlSrv.getOrigin()}${environment.basePath}`;
  }

  async getAllCarriers(param: PaginationParams): Promise<ApiResponse<Carrier>> {
    let params = new HttpParams();
    if (param?.offset) { params = params.append('offset', `${param?.offset}`) }
    if (param?.limit) { params = params.append('limit', `${param?.limit}`) }
    return lastValueFrom(this.http.get<ApiResponse<Carrier>>(`${this.BASE_URL}/carriers`, { params }));
  }

  async getAlltrucks(param: any): Promise<TruckResponse> {
    let params = new HttpParams();

    if (param?.code) { params = params.append('carrierRef', `${param?.code}`) }
    if (param?.immatriculation) { params = params.append('immatriculation', `${param?.immatriculation}`) }
    params = params.append('offset', '0');
    if (param?.limit) { params = params.append('limit', `${param?.limit}`) }

    return await lastValueFrom(this.http.get<TruckResponse>(`${this.BASE_URL}/trucks`, { params }));
  }

  async getTrucks(): Promise<ApiResponse<Truck>> {
    return lastValueFrom(this.http.get<ApiResponse<Truck>>(`${this.BASE_URL}/trucks`));
  }

  async getAllDrivers(params: DriverParams): Promise<DriversResponse> {
    let httpParams = new HttpParams();
    if (params?.code) { httpParams = httpParams.append('carrierRef', `${params.code}`); }
    if (params?.fullname) { httpParams = httpParams.append('fullname', `${params.fullname}`); }
    httpParams = httpParams.append('offset', '0');
    if (params?.limit) { httpParams = httpParams.append('limit', `${params.limit}`); }

    return await lastValueFrom(this.http.get<DriversResponse>(`${this.BASE_URL}/drivers/code/carriers`, { params: httpParams }));
  }

  async updateCarrier(carrier: Carrier): Promise<ApiResponse<Carrier>> {
    return lastValueFrom(this.http.put<ApiResponse<Carrier>>(`${this.BASE_URL}/carriers/${carrier._id}`, carrier));
  }

  async deleteCarrier(carrier: Carrier): Promise<ApiResponse<Carrier>> {
    return lastValueFrom(this.http.delete<ApiResponse<Carrier>>(`${this.BASE_URL}/carriers/${carrier._id}`));
  }

  async updateTrucksCarrier(id: string, truck: Truck): Promise<ApiResponse<Truck>> {
    return lastValueFrom(this.http.put<ApiResponse<Truck>>(`${this.BASE_URL}/trucks/${id}`, truck));
  }

  async insertCarrier(carrier: Carrier): Promise<ApiResponse<Carrier>> {
    return lastValueFrom(this.http.post<ApiResponse<Carrier>>(`${this.BASE_URL}/carriers`, carrier));
  }

  async insertTrucks(trucks: Truck[]): Promise<ApiResponse<Truck>> {
    return lastValueFrom(this.http.post<ApiResponse<Truck>>(`${this.BASE_URL}/trucks/many`, trucks));
  }

  async deleteTruck(truck: Truck): Promise<ApiResponse<Truck>> {
    return lastValueFrom(this.http.delete<ApiResponse<Truck>>(`${this.BASE_URL}/trucks/${truck._id}`));
  }

  async insertDrivers(drivers: Driver[]): Promise<ApiResponse<Driver>> {
    return lastValueFrom(this.http.post<ApiResponse<Driver>>(`${this.BASE_URL}/drivers/many`, drivers));
  }

  async deleteDriver(driver: Driver): Promise<ApiResponse<Driver>> {
    return lastValueFrom(this.http.delete<ApiResponse<Driver>>(`${this.BASE_URL}/drivers/${driver._id}`));
  }

  async updateDriversCarrier(id: string, driver: Driver): Promise<ApiResponse<Driver>> {
    return lastValueFrom(this.http.put<ApiResponse<Driver>>(`${this.BASE_URL}/drivers/${id}`, driver));
  }

  async getPdfQrCode(truck: Truck): Promise<{ pdfLink: string }> {
    const result = await lastValueFrom(this.http.post<{ pdfPath: string }>(`${this.BASE_URL}/trucks/qrcode`, truck));
    return { pdfLink: `${this.baseUrlSrv.getOrigin()}${result?.pdfPath}` };
  }

  async getDrivers(): Promise<ApiResponse<Driver>> {
    return lastValueFrom(this.http.get<ApiResponse<Driver>>(`${this.BASE_URL}/drivers`));
  }
}