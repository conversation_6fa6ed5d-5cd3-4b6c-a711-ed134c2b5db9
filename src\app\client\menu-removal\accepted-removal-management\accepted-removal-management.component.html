<div class="common-form-container menu-removal-container">
  <div class="header">
    <!-- <nb-icon icon="undo-outline" [options]="{ animation: { type: 'zoom' } }" class="remove-icon" status="primary"
        (click)='goTo()'>
      </nb-icon> -->
    <h1 class="title">Transport Validé</h1>
  </div>

  <div class="removal-container">
    <div class="filter-area">
      <div class="left-area">
        <div class="filter-label">Filtrer par</div>
        <div class="filters">
          <!-- <input nbInput [nbDatepicker]="datePickerStart" [(ngModel)]="filterForm.startDate" status="success" fieldSize="small" class="nbSelect-width"
                      placeholder="Début"/>
                    <nb-datepicker #datePickerStart (dateChange)="onSelectionAEChange($event, 'startDate')">
                    </nb-datepicker>

                    <input nbInput id="test" [nbDatepicker]="datepickerEnd" [(ngModel)]="filterForm.endDate" status="success" fieldSize="small" class="empty-input height"
                      placeholder="Fin"/>
                    <nb-datepicker #datepickerEnd (dateChange)="onSelectionAEChange($event, 'endDate')"></nb-datepicker> -->
          <nb-select status="success" class="nbSelect-width" size="small" [(selected)]="filterForm.OrderType"
            (selectedChange)="onSelectionAEChange($event, 'OrderType')" placeholder="Type Bon">
            <nb-option value="reset"> Tous les types </nb-option>
            <nb-option value="SO"> SO </nb-option>
            <nb-option value="ST"> ST </nb-option>
          </nb-select>

          <input type="text" placeholder="N° Bon" fieldSize="small" nbInput class="nbSelect-width"
            [nbAutocomplete]="autoComplete1" [(ngModel)]="filterForm.LoadNumber"
            (ngModelChange)="onModelAEChange($event)" />
          <nb-autocomplete #autoComplete1 (selectedChange)="onSelectionAEChange($event, 'LoadNumber')">
            <nb-option *ngIf="loading"> Chargement... </nb-option>
            <nb-option value="reset"> Tous les Bons</nb-option>
            <nb-option *ngFor="let option of dataAELabels" [value]="option">{{option}}
            </nb-option>
          </nb-autocomplete>

          <input type="text" placeholder="Transporteur" fieldSize="small" nbInput class="empty-input height"
            [nbAutocomplete]="autoComplete2" [(ngModel)]="filterForm.carrierLabel"
            (ngModelChange)="onModelCarrierChange($event)" />
          <nb-autocomplete #autoComplete2 (selectedChange)="onSelectionAEChange($event, 'carrierLabel')">
            <nb-option value="reset"> Tous les transporteurs</nb-option>
            <nb-option *ngFor="let option of dataTransporters" [value]="option">{{option}}
            </nb-option>
            <nb-option *ngIf="loading"> Chargement... </nb-option>
          </nb-autocomplete>

          <input type="text" placeholder="Produit" fieldSize="small" nbInput class="empty-input height"
            [nbAutocomplete]="autoComplete3" [(ngModel)]="filterForm.ItemNumber"
            (ngModelChange)="onModelProductsChange($event)" />
          <nb-autocomplete #autoComplete3 (selectedChange)="onSelectionAEChange($event, 'ItemNumber')">
            <nb-option value="reset"> Tous les Produits </nb-option>
            <nb-option *ngFor="let option of dataProducts" [value]="option">{{option }}
            </nb-option>
            <nb-option *ngIf="loading"> Chargement... </nb-option>
          </nb-autocomplete>

          <input type="text" placeholder="Client" fieldSize="small" nbInput class="empty-input height"
            [nbAutocomplete]="autoComplete4" [(ngModel)]="filterForm.company"
            (ngModelChange)="onModelCompanyChange($event)" />
          <nb-autocomplete #autoComplete4 (selectedChange)="onSelectionAEChange($event, 'company')">
            <nb-option value="reset"> Tous les clients </nb-option>
            <nb-option *ngFor="let option of dataSoldToDescLabels" [value]="option">{{option }}
            </nb-option>
            <nb-option *ngIf="loading"> Chargement... </nb-option>
          </nb-autocomplete>

          <input type="text" placeholder="Point de livraison" fieldSize="small" nbInput class="empty-input height"
            [nbAutocomplete]="autoComplete5" [(ngModel)]="filterForm.shipping"
            (ngModelChange)="onModelDeliveryPointChange($event)" />
          <nb-autocomplete #autoComplete5 (selectedChange)="onSelectionAEChange($event, 'shipping')">
            <nb-option value="reset"> Tous les Points de livraison </nb-option>
            <nb-option *ngFor="let option of dataShipToDescLabels" [value]="option">{{option }}
            </nb-option>
            <nb-option *ngIf="loading && !dataShipToDescLabels?.length"> Chargement... </nb-option>
          </nb-autocomplete>
        </div>
      </div>
      <div class="right-area">
        <button nbButton status="basic" fieldSize="small" class="search-btn reset-btn" (click)="reset()">
          <nb-icon icon="refresh-outline"></nb-icon>
          REINITIALISER
        </button>
        <button nbButton status="success" fieldSize="small" class="search-btn" (click)="openDialogExport(exportDialog)">
          <nb-icon icon="download-outline"></nb-icon>
          EXPORTER
        </button>
      </div>
    </div>


    <nb-card [nbSpinner]="isLoading">
      <nb-list class="element-head">
        <!-- Composant de pagination -->
        <nb-list-item class="list-elt-header paginator" *ngIf="total > 0">
          <div class="col col-paginator">
            <clw-pagination [config]="paginationConfig" (pageChange)="onPageChange($event)"
              (itemsPerPageChange)="onItemsPerPageChange($event)">
            </clw-pagination>
          </div>
        </nb-list-item>
        <!-- En-têtes de colonnes -->
        <nb-list-item class="list-elt-header paginator">
          <div class="col col-num">N°</div>
          <div class="col col-ref">N° Bon</div>
          <div class="col col-date">Date création</div>
          <div class="col col-date">Transporteur</div>
          <div class="col col-hour">Produit</div>
          <div class="col col-shipto">Addresse</div>
          <div class="col col-shipto">Compagnie</div>
          <div class="col col-details">Quantité</div>
          <div class="col col-status">Statut</div>
          <div class="col col-action"></div>
        </nb-list-item>
      </nb-list>
      <nb-list class="element-head scrool-style" nbSpinnerStatus="primary">
        <nb-list-item class="list-elt-header" *ngFor="let removal of removals, index as i">
          <div class="col col-num">{{ (paginationConfig.currentPage - 1) * paginationConfig.itemsPerPage + i + 1 }}</div>
          <div class="col col-ref">{{removal?.LoadNumber}}</div>
          <div class="col col-date date-size">{{removal?.dates?.created || removal?.dates?.updated |
            date:"dd/MM/YYYY"}}
            {{removal?.dates?.created || removal?.dates?.updated | date:"hh:mm"}}
          </div>
          <div class="col col-date">{{removal?.carrier?.label ||
                                    removal?.carrier?.driver?.fullname || removal?.Alpha || 'N/A'}}
          </div>
          <div class="col col-hour">
            {{getConcatenatedLabels(removal?.ItemNumber)}}
          </div>
          <div class="col col-shipto" title="{{ removal?.ShipTo || 'N?A'}} ">
            {{truncate(removal?.ShipTo, 20)}}</div>
          <div class="col col-shipto" title="{{ removal?.SoldToDescription || 'N?A'}} ">
            {{truncate(removal?.SoldToDescription, 20)}}</div>
          <div class="col col-details">{{removal?.TransactionQuantity}}T</div>
          <div class="col col-status">
            <nb-badge [text]="removal?.enable == false ? '0' : removal?.status | getStatusRemovals" class="badge"
              position="top start"
              [status]="removal?.enable == false ? '0' : removal?.status | getColorStatusRemovals"></nb-badge>
          </div>
          <div class="col col-action">
            <div class="action-icons">
              <button nbTooltip="Detail" class="button-margin" nbTooltipPlacement="top" nbTooltipStatus
                (click)='openDetailModal(detailDialog, removal)'>
                <nb-icon icon="file-text-outline" status="primary" [options]="{ animation: { type: 'zoom' } }">
                </nb-icon>
              </button>
              <!-- <button [nbTooltip]="isEnabled(removal)?'Désactiver AE':'Activer AE'"
                                nbTooltipPlacement="top" nbTooltipStatus
                                (click)="openEnableRemoval(deleteDialog, removal)">
                                <nb-icon [icon]="isEnabled(removal)?'eye-outline':'eye-off-outline'"
                                    [status]="isEnabled(removal)?'info':'danger'"
                                    [options]="{ animation: { type: 'zoom' } }"></nb-icon>
                            </button> -->
            </div>
          </div>
        </nb-list-item>
      </nb-list>
    </nb-card>
  </div>
</div>


<ng-template #detailDialog let-data let-ref="dialogRef" class="dialogRef">
  <div class="order-detail-container her-width">
    <nb-card>
      <nb-card-header class="form--header">détail de l'autorisation</nb-card-header>
      <nb-card-body>
        <div class="order-infor order-pick-up">

          <table class="order-table">
            <tr>
              <td class="title">N° Bon:</td>
              <td class="order-right">{{removal?.LoadNumber}}</td>
            </tr>
            <tr>
              <td class="title">statut:</td>
              <td class="order-right">{{removal?.status | getStatusRemovals}}</td>
            </tr>
            <tr>
              <td class="title">Statut du chargement:</td>
              <td class="order-right">{{removal?.LoadStatus | getStatusLabelRemovals}}</td>
            </tr>
            <tr>
              <td class="title">Numéro de la commande:</td>
              <td class="order-right">{{removal?.SalesOrderNumber || 'non disponible'}}</td>
            </tr>
            <tr>
              <td class="title">Produit:</td>
              <td class="order-right">{{getConcatenatedLabels(removal?.ItemNumber)}}</td>
            </tr>
            <tr>
              <td class="title">Quantité de la commande:</td>
              <td class="order-right">{{removal?.TransactionQuantity || 'non disponible'}}</td>
            </tr>
            <tr>
              <td class="title">Quantité à livrer:</td>
              <td class="order-right">{{removal?.QuantityShipped}}</td>
            </tr>
            <tr>
              <td class="title">Date de requête d'enlèvement:</td>
              <td class="order-right">{{removal?.RequestedDate}}</td>
            </tr>
            <tr>
              <td class="title">Transporteur :</td>
              <td class="order-right">{{removal?.carrier?.label || 'non disponible'}}</td>
            </tr>
            <tr>
              <td class="title">Conducteur :</td>
              <td class="order-right">{{removal?.carrier?.driver?.fullname || 'non disponible'}}</td>
            </tr>
            <tr>
              <td class="title">Immatriculation:</td>
              <td class="order-right">{{removal?.carrier?.truckImmatriculation || 'non disponible'}}</td>
            </tr>
            <tr>
              <td class="title">Type d'enlèvement (Pickup/Rendu):</td>
              <td class="order-right">{{FreightHandlingCode(removal?.FreightHandlingCode) || 'non disponible'}} </td>
            </tr>
            <tr>
              <td class="title">Immatriculation du véhicule:</td>
              <td class="order-right">{{removal?.PrimaryVehicleID || 'non disponible'}}</td>
            </tr>
            <tr>
              <td class="title">Temps mis dans la file d'attente:</td>
              <td class="order-right">{{(removal?.dockTimeStart - removal?.parkTime) | getNbMinRemovals}}
              </td>
            </tr>
            <tr>
              <td class="title">Temps mis dans le quai:</td>
              <td class="order-right">{{(removal?.departureTime - removal?.dockTimeStart)
                |getNbMinRemovals}}</td>
            </tr>
          </table>
        </div>
      </nb-card-body>
      <nb-card-footer class="form--footer">
        <button nbButton outline status="basic" class="" (click)="ref.close()">
          <nb-icon icon="close-square-outline" [options]="{ animation: { type: 'zoom' } }" class="remove-icon">
          </nb-icon>
          fermer
        </button>
      </nb-card-footer>
    </nb-card>
  </div>
</ng-template>

<ng-template #deleteDialog let-data let-ref="dialogRef" class="dialogRef">
  <div class="removal-queue-container">
    <nb-card [nbSpinner]="isLoading">
      <nb-card-header>
        <h6>{{isEnabled()?'Désactivation':'Activation'}} du Bon{{currentRemoval.LoadNumber}}</h6>
      </nb-card-header>
      <nb-card-body>
        <p>vous êtes sur le point {{isEnabled()?'de désactiver':'d\'activer'}}le Bon {{currentRemoval.LoadNumber}}
        </p>
      </nb-card-body>
      <nb-card-footer class="form--footer">
        <div class="footer-button">
          <button nbButton outline status="basic" class="" (click)="ref.close()">
            ANNULER
          </button>
          <button nbButton outline status="success" class="" (click)="ref.close(true)">
            <nb-icon icon="done-all-outline" [options]="{ animation: { type: 'zoom' } }" class="remove-icon">
            </nb-icon>
            Valider
          </button>
        </div>
      </nb-card-footer>
    </nb-card>
  </div>
</ng-template>

<ng-template #exportDialog let-data let-ref="dialogRef" class="dialogRef">
  <div class="export-container">
    <nb-card [nbSpinner]="isLoading">
      <nb-card-header>
        <h6>Exporter Bons</h6>
      </nb-card-header>
      <nb-card-body>
        <p>Sélectionnez un intervalle pour Exporter la liste</p>
        <div class="filter">
          <div class="filter-elt input date">
            <input nbInput [nbDatepicker]="datePickerStart" [(ngModel)]="startDate" status="success" size="small"
              placeholder="Début">
            <nb-datepicker #datePickerStart>
            </nb-datepicker>
          </div>

          <div class="filter-elt input date">
            <input nbInput id="test" [nbDatepicker]="datepickerEnd" [(ngModel)]="endDate" status="success" size="small"
              placeholder="Fin">
            <nb-datepicker #datepickerEnd></nb-datepicker>
          </div>
        </div>
      </nb-card-body>
      <nb-card-footer class="form--footer">
        <div class="footer-button">
          <button nbButton outline status="basic" class="" (click)="ref.close()">
            ANNULER
          </button>
          <button nbButton outline status="success" class="" (click)="exportAeToExcel()">
            <nb-icon icon="done-all-outline" [options]="{ animation: { type: 'zoom' } }" class="remove-icon">
            </nb-icon>
            Valider
          </button>
        </div>
      </nb-card-footer>
    </nb-card>
  </div>
</ng-template>
