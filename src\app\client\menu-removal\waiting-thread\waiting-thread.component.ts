// import { WaitingThreadService } from './waiting-thread.service';
import { Location } from '@angular/common';
import cloneDeep from 'lodash-es/cloneDeep';
import { Component, OnInit, ViewEncapsulation, PipeTransform, Pipe, TemplateRef, ViewChild, ChangeDetectorRef } from '@angular/core';
import { Router } from '@angular/router';
import { NbDialogRef, NbDialogService, NbMenuService, NbToastrService } from '@nebular/theme';
import * as moment from 'moment';
import { filter, map } from 'rxjs/operators';
import { RemovalService } from '../removal-management/removal.service';
import { AuthorizationRemoval, GroupesOrders, NewManualAes, OrderType, parkingStatus, RenderType, StatusAEsJde, UpdateQueuesAesType } from 'src/app/shared/models/authorization-removal';
import { RenderTypeValue, RenderTypeValueLibrary } from 'src/app/shared/models/render-type.enum';
import { StatusRemovals } from 'src/app/shared/models/status-removal.enum';
import { TruckInspection } from 'src/app/shared/models/truck-inspection';
import { Dock } from 'src/app/shared/models/dock';
import { CommonService } from 'src/app/shared/services/common.service';
import { of } from 'rxjs';
import { PaginationConfig } from 'src/app/shared/services/pagination.service';
import { Order } from 'src/app/shared/models/order';

@Pipe({
  name: 'status'
})

export class StatusRemovalPipe implements PipeTransform {
  transform(status: number, isColor: boolean): string {
    const colors = { 400: 'primary' };
    const statuses = { 400: 'EN ATTENTE' };
    if (isColor) { return colors[status]; }
    return statuses[status];
  }

}

@Component({
  selector: 'clw-waiting-thread',
  templateUrl: './waiting-thread.component.html',
  encapsulation: ViewEncapsulation.None
})
export class WaitingThreadComponent implements OnInit {
  @ViewChild('autoInput') input: any;
  waitingThread: any;
  waitingThreadTab: any[];
  waitingThreadTabDock: AuthorizationRemoval[];
  waitingTheadTabNew: any[];
  displayLoading: boolean;
  isLoading: boolean;
  load: boolean;
  initialTruckTonnage: number;

  selectedWaiting: any;
  dataCarrierLabels: string[];
  timer: any;
  result: number;
  waitingTime: any;
  dialogRef: NbDialogRef<any>;
  actionStatus: number;
  docks: Dock[];

  // Active tab tracking for unified pagination
  activeTab: string = 'Chargement usine';

  // Pagination configuration for factory loading tab
  factoryPaginationConfig: PaginationConfig = {
    currentPage: 1,
    itemsPerPage: 10,
    totalItems: 0,
    maxSize: 10
  };

  // Pagination configuration for parking tab
  parkingPaginationConfig: PaginationConfig = {
    currentPage: 1,
    itemsPerPage: 10,
    totalItems: 0,
    maxSize: 10
  };

  // Current pagination config that changes based on active tab
  currentPaginationConfig: PaginationConfig = {
    currentPage: 1,
    itemsPerPage: 10,
    totalItems: 0,
    maxSize: 10
  };

  // Legacy pagination properties
  offset = 0;
  limit = 20;

  selectedDock: any;
  inspections: TruckInspection[];
  currentInspections: TruckInspection;
  statusChoices: { label: string, value: boolean }[] = [{ label: 'OK', value: true }, { label: 'PAS OK', value: false }];
  InspectionDecisions = ['ACCEPTEE', 'REJETEE'];

  selectedTruck: string;
  dataForFilter: any;

  changeAE: AuthorizationRemoval;

  updatedAesQueues: UpdateQueuesAesType;
  removalCurrent: AuthorizationRemoval;
  dataAELabelsProduct = [];
  orderTypeFilter: any;
  filteredOptions$: any;
  loadNumber = '';

  filterForm = {

    LoadNumber: 0,
    ItemNumber: '',
    SoldTo: '',
    FreightHandlingCode: '',
    OrderType: '',
  };



  aesType = [
    { name: 'En pickup', code: RenderType.PICKUP },
    { name: 'En rendu', code: RenderType.RENDER },
  ]
  orderType = [
    { name: 'SO', code: OrderType.EXTERN },
    { name: 'ST', code: OrderType.INTERN },
  ]
  jdeStatus = [
    { name: "Non approuvée", code: StatusAEsJde.NOTAPPROVED },
    { name: "Créer et approuvée", code: StatusAEsJde.APPROVED },
    { name: "Rejetée", code: StatusAEsJde.REJECTED },
    { name: "Facturée", code: StatusAEsJde.INVOICED },
    { name: 'Au chargement partielle', code: StatusAEsJde.LOADSTATUS60 },
    { name: "Sortie livraison", code: StatusAEsJde.OUTPUT },
    { name: "En saisie", code: StatusAEsJde.CHECK_IN },
  ]

  newManualAe: NewManualAes = {
    LoadNumber: '',
    // ItemNumber: '',
    OrderType: '',
    SoldTo: '',
    ShipTo: '',
    Alpha: '',
    LoadStatus: '',
    ShipToDescription: '',
    TransactionQuantity: '',
    FreightHandlingCode: '',

  }

  DataLoadNumbers: AuthorizationRemoval[] = [];

  filteredProduct$: any;
  productFilter: any;
  productsLabels = [
    "ROBSB1",
    "ROBSN1",
    "MIXSF1",
    "MIXSB1",
    "HYDSB1",
    "MTC300G",
    "ROBVB1",
    "ROBVN1",
    "SUBSB1",
    "ALCSF1",
    "MTP209G",
    "ROBVF1",
    "MTC324G",
    "MTP160G",
    "MTP209B",
    "MTC300B",
    "ROBSF1",
    "MTP244R10",
    "MTP244R06",
    "MTC314G",
    "MTP160B",
    "C52NVB1",
    "MIXVB1B"];
  dataOrderTypeLabels: any[] = [];
  DataLoadNumbersForFilters: AuthorizationRemoval[] = [];
  selectedItem: any


  constructor(
    private router: Router,
    private dialogSrv: NbDialogService,
    private toastSrv: NbToastrService,
    private toastrSvr: NbToastrService,
    private removalSrv: RemovalService,
    public commonSrv: CommonService,
    private location: Location,
    private nbMenuService: NbMenuService,
    private cdr: ChangeDetectorRef
  ) { }

  items = [
    { title: 'Parking', icon: 'collapse-outline' },
    { title: 'En panne', icon: 'pause-circle-outline' },
  ];
  itemsNew = [
    { title: 'En panne', icon: 'pause-circle-outline' },
  ];


  async ngOnInit(): Promise<void> {
    // Initialize pagination configurations
    this.factoryPaginationConfig = {
      currentPage: 1,
      itemsPerPage: 10,
      totalItems: 0,
      maxSize: 10
    };

    this.parkingPaginationConfig = {
      currentPage: 1,
      itemsPerPage: 10,
      totalItems: 0,
      maxSize: 10
    };

    // Initialize current pagination config with factory config (default tab)
    this.updateCurrentPaginationConfig();

    await this.getWaitingThread();
    await this.getDataForFilter();
    setInterval(() => {
      this.result = moment().valueOf();
    }, 1000);
    setInterval(async () => {
      this.getWaitingThread();
    }, 300_000)
    this.nbMenuService.onItemClick()
      .pipe(
        filter(({ tag }) => tag === 'my-context-menu'),
      )
      .subscribe(() => { });

    await this.getInspections();
    await this.getAvailableDock();
  }

  /**
   * Handle tab change event and update active tab and pagination config
   * @param event Tab change event from nb-tabset
   */
  onTabChange(event: any): void {
    if (event && event.tabTitle) {
      this.activeTab = event.tabTitle;
      this.updateCurrentPaginationConfig();
    }
  }

  /**
   * Update current pagination config based on active tab
   */
  private updateCurrentPaginationConfig(): void {
    if (this.activeTab === 'Chargement usine') {
      this.currentPaginationConfig = { ...this.factoryPaginationConfig };
    } else if (this.activeTab === 'Parking') {
      this.currentPaginationConfig = { ...this.parkingPaginationConfig };
    }
  }

  /**
   * Unified page change handler that delegates to appropriate tab-specific method
   * @param page New page number
   */
  onCurrentPageChange(page: number): void {
    if (this.activeTab === 'Chargement usine') {
      this.onFactoryPageChange(page);
    } else if (this.activeTab === 'Parking') {
      this.onParkingPageChange(page);
    }
  }

  /**
   * Unified items per page change handler that delegates to appropriate tab-specific method
   * @param itemsPerPage New items per page value
   */
  onCurrentItemsPerPageChange(itemsPerPage: number): void {
    if (this.activeTab === 'Chargement usine') {
      this.onFactoryItemsPerPageChange(itemsPerPage);
    } else if (this.activeTab === 'Parking') {
      this.onParkingItemsPerPageChange(itemsPerPage);
    }
  }

  /**
   * Handle page change for factory loading tab
   * @param page New page number
   */
  onFactoryPageChange(page: number): void {
    // Create a new instance of PaginationConfig to force change detection
    this.factoryPaginationConfig = {
      ...this.factoryPaginationConfig,
      currentPage: page
    };

    // Update current pagination config if this is the active tab
    if (this.activeTab === 'Chargement usine') {
      this.currentPaginationConfig = { ...this.factoryPaginationConfig };
    }

    // Force change detection
    this.cdr.detectChanges();

    // Reload data with new pagination
    this.getWaithingThreadDock();
  }

  /**
   * Handle items per page change for factory loading tab
   * @param itemsPerPage New items per page value
   */
  onFactoryItemsPerPageChange(itemsPerPage: number): void {
    // Create a new instance of PaginationConfig to force change detection
    this.factoryPaginationConfig = {
      ...this.factoryPaginationConfig,
      itemsPerPage: itemsPerPage,
      currentPage: 1 // Reset to first page
    };

    // Update current pagination config if this is the active tab
    if (this.activeTab === 'Chargement usine') {
      this.currentPaginationConfig = { ...this.factoryPaginationConfig };
    }

    // Force change detection
    this.cdr.detectChanges();

    // Reload data with new pagination
    this.getWaithingThreadDock();
  }

  /**
   * Handle page change for parking tab
   * @param page New page number
   */
  onParkingPageChange(page: number): void {
    // Create a new instance of PaginationConfig to force change detection
    this.parkingPaginationConfig = {
      ...this.parkingPaginationConfig,
      currentPage: page
    };

    // Update current pagination config if this is the active tab
    if (this.activeTab === 'Parking') {
      this.currentPaginationConfig = { ...this.parkingPaginationConfig };
    }

    // Force change detection
    this.cdr.detectChanges();

    // Reload data with new pagination
    this.getWaithingThreadNew();
  }

  /**
   * Handle items per page change for parking tab
   * @param itemsPerPage New items per page value
   */
  onParkingItemsPerPageChange(itemsPerPage: number): void {
    // Create a new instance of PaginationConfig to force change detection
    this.parkingPaginationConfig = {
      ...this.parkingPaginationConfig,
      itemsPerPage: itemsPerPage,
      currentPage: 1 // Reset to first page
    };

    // Update current pagination config if this is the active tab
    if (this.activeTab === 'Parking') {
      this.currentPaginationConfig = { ...this.parkingPaginationConfig };
    }

    // Force change detection
    this.cdr.detectChanges();

    // Reload data with new pagination
    this.getWaithingThreadNew();
  }

  async getAvailableDock() {
    const query = {
      status: JSON.stringify({ $ne: StatusRemovals.LOADED }),
      dockCode: JSON.stringify({ $in: this.docks?.map(dock => dock?.code) }),
      projection: 'dockCode'
    }
    const { data: occupiedDocks } = await this.removalSrv.getAuthorizationRemoval({ ...query });

    const occupiedCodes = new Set(occupiedDocks.map(dock => dock?.dockCode));
    this.docks = this.docks?.filter(dock => !occupiedCodes.has(dock.code));
  }

  async removeToDuck(): Promise<any> {
    this.isLoading = true;
    try {
      const departureTime = moment().valueOf();
      await this.removalSrv.updateAe(this.selectedWaiting?._id, { status: 500, departureTime });

      // Actualiser la liste du chargement usine
      await this.getWaitingThread();

      this.toastSrv.success(`Le camion a été sorti de l'usine avec succès`, 'Opération réussie');

      // Fermer la modale après l'opération
      if (this.dialogRef) {
        this.dialogRef.close(true);
      }
    } catch (error) {
      this.toastSrv.danger(`Une erreur est survenue lors de la sortie du camion`, 'Échec de l\'opération');
      return error;
    } finally {
      this.isLoading = false;
    }
  }

  openSendToQueueDialog(dialog: TemplateRef<any>, waiting?: any) {
    this.initialTruckTonnage = null;
    this.selectedWaiting = { ...waiting }
    this.dialogRef = this.dialogSrv.open(dialog);
    this.selectedTruck = waiting?.carrier?.truck?.immatriculation || waiting?.carrier?.truckImmatriculation;
    this.dialogRef.onClose.subscribe(async (result: boolean) => {
      // Toujours actualiser la liste, même si l'utilisateur a annulé
      // pour s'assurer que les données sont à jour
      await this.getWaitingThread();
      await this.getAvailableDock();
    })
  }

  init() {
    this.newManualAe = {
      LoadNumber: null,
      ItemNumber: null,
      SoldTo: null,
      status: null,
      TransactionQuantity: null,
    }

  }

  async reset() {
    // Reset pagination configurations to first page
    this.factoryPaginationConfig = {
      ...this.factoryPaginationConfig,
      currentPage: 1
    };

    this.parkingPaginationConfig = {
      ...this.parkingPaginationConfig,
      currentPage: 1
    };

    // Force change detection
    this.cdr.detectChanges();

    // Reload data
    await this.getWaitingThread();
    await this.getAvailableDock();
  }

  async getDataForFilter() {
    try {
      this.isLoading = true;
      this.dataForFilter = await this.removalSrv.getFilterData({
        ...this.filterForm,
        keyForFilters: ['OrderType', 'carrier.label']
      },);
      this.dataOrderTypeLabels = this.dataForFilter?.dataOrderType ?? [];
      this.dataCarrierLabels = this.dataForFilter['datacarrierlabel'] ?? [];

    } catch (error) {
      console.error(error);
      return error;
    } finally { this.isLoading = false }
  }

  async getWaitingThread(): Promise<any> {
    this.isLoading = true;
    try {
      // await this.getWaithingThreadFirst();
      await this.getWaithingThreadDock();
      await this.getWaithingThreadNew();
      this.docks = await this.removalSrv.getTruckByDock({});
      this.productFilter = null;
      this.orderTypeFilter = null;
    } catch (error) {
      console.log(error);
      return this.toastSrv.danger(`Erreur de connexion internet`, `Echec operation`)
    } finally { this.isLoading = false; }
  }

  // async getWaithingThreadFirst() {
  //   this.displayLoading = true;
  //   if (this.productFilter === 'MULTIX') {
  //     this.productFilter = 'MIX'
  //   }
  //   const options = { status: 400, moved: 400, product: this.productFilter, OrderType: this.orderTypeFilter, sort: 'factoryParkTimes.entry', way: 1 }
  //   const { data } = await this.removalSrv.getAuthorizationRemoval(options);
  //   this.waitingThreadTab = data?.sort((currentAE, nextAE) => currentAE?.factoryParkTimes?.at(-1)?.entry - nextAE?.factoryParkTimes?.at(-1)?.entry);
  //   this.displayLoading = false;
  // }

  getConcatenatedLabels(groupedOrders: GroupesOrders): string {
    return groupedOrders?.map((order: Order) =>
      order?.cart?.items
        .filter(item => item?.quantityDelivery > 0)
        .map(item => `${item?.product?.label} (${item?.packaging?.label})`)
        .join(', ')
    ).join(', ') || 'N/A';
  }

  getQuantity(groupOders: GroupesOrders): number {
    const result = groupOders
      .map((order: Order) =>
        order?.cart?.items.reduce((total, cartElement) =>
          total + ((cartElement?.quantity * cartElement?.packaging.unit?.value) / 1000), 0)
      )
      .reduce((sum, quantity) => sum + quantity, 0);

    return parseFloat(result.toFixed(2));
  }

  getQuantityDelivery(groupOders: GroupesOrders): number {
    const result = groupOders
      .map((order: Order) =>
        order?.cart?.items.reduce((total, cartElement) =>
          total + ((cartElement?.quantityDelivery * cartElement?.packaging.unit?.value) / 1000), 0)
      )
      .reduce((sum, quantity) => sum + quantity, 0);

    return parseFloat(result.toFixed(2));
  }

  async getWaithingThreadDock() {
    this.displayLoading = true;
    if (this.productFilter === 'MULTIX') {
      this.productFilter = 'MIX'
    }

    // Calculate pagination parameters
    const limit = this.factoryPaginationConfig.itemsPerPage;
    const offset = (this.factoryPaginationConfig.currentPage - 1) * limit;

    const options = {
      status: 450,
      OrderType: this.orderTypeFilter,
      product: this.productFilter,
      sort: 'dockTimeStart',
      way: 1,
      limit,
      offset
    };

    try {
      const { data, count } = await this.removalSrv.getAuthorizationRemoval(options);
      this.waitingThreadTabDock = data;

      // Update pagination config with total count
      this.factoryPaginationConfig = {
        ...this.factoryPaginationConfig,
        totalItems: count || 0
      };

      // Update current pagination config if this is the active tab
      if (this.activeTab === 'Chargement usine') {
        this.currentPaginationConfig = { ...this.factoryPaginationConfig };
      }

      // Force change detection to update pagination display
      this.cdr.detectChanges();
    } catch (error) {
      console.error('Error loading factory data:', error);
      this.toastSrv.danger('Erreur lors du chargement des données', 'Erreur');
    } finally {
      this.displayLoading = false;
    }
  }

  async getWaithingThreadNew() {
    this.displayLoading = true;

    if (this.productFilter === 'MULTIX') {
      this.productFilter = 'MIX'
    }

    // Calculate pagination parameters
    const limit = this.parkingPaginationConfig.itemsPerPage;
    const offset = (this.parkingPaginationConfig.currentPage - 1) * limit;

    const options = {
      status: 700,
      moved: 700,
      product: this.productFilter,
      OrderType: this.orderTypeFilter,
      sort: 'parkTime',
      way: 1,
      limit,
      offset
    };

    try {
      const { data, count } = await this.removalSrv.getAuthorizationRemoval(options);

      this.waitingTheadTabNew = data.sort((currentAE, nextAE) =>
        currentAE.newParkTimes?.at(-1).entry - nextAE.newParkTimes?.at(-1).entry
      );

      // Update pagination config with total count
      this.parkingPaginationConfig = {
        ...this.parkingPaginationConfig,
        totalItems: count || 0
      };

      // Update current pagination config if this is the active tab
      if (this.activeTab === 'Parking') {
        this.currentPaginationConfig = { ...this.parkingPaginationConfig };
      }

      // Force change detection to update pagination display
      this.cdr.detectChanges();
    } catch (error) {
      console.error('Error loading parking data:', error);
      this.toastSrv.danger('Erreur lors du chargement des données', 'Erreur');
    } finally {
      this.displayLoading = false;
    }
  }

  getColorStyle(time: any): any {
    if (!time) return;

    const hours = 5400001; // 5400001 equal 1h30 min
    const minutes = 1800001;
    const waitingTime = moment(this.result).diff(moment(time));

    if (waitingTime >= hours) {
      return {
        color: '#FF4F4F'
      };
    }
    if (waitingTime >= minutes && waitingTime < hours) {
      return {
        color: '#F4D954'
      }
    }
    return {
      color: '#4DE352'
    }

  }

  getColorStyleStatus(time: any): any {
    if (!time) return;

    const hours = 5400001; // 5400001 equal 1h30 min
    const minutes = 1800001; // 5400001 equal 30 min
    const waitingTime = moment(this.result).diff(moment(time));

    if (waitingTime >= hours) {
      return {
        color: '#FF4F4F',
        'background-color': 'rgb(58 56 56)',
        border: '1px solid rgb(94 56 56)'
      };
    }
    if (waitingTime >= minutes && waitingTime < hours) {
      return {
        color: 'rgb(255 233 79)',
        'background-color': ' rgb(58, 56, 56)',
        border: '1px solid rgb(78 77 63)'
      }
    }
    if (!time) return;

    return {
      color: 'rgb(86 255 91)',
      'background-color': 'rgb(58, 56, 56)',
      border: '1px solid rgb(64 82 56)'
    }

  }

  async openUpdateAE(dialog: TemplateRef<any>, status: number, waiting: any): Promise<any> {

    this.actionStatus = status;
    this.selectedWaiting = { ...waiting };
    this.initialTruckTonnage = null;
    this.dialogRef = this.dialogSrv.open(dialog);

    this.dialogRef.onClose.subscribe(async (result) => {
      try {
        this.isLoading = true
        if (!result) { return }
        if (this.actionStatus === 600) {
          waiting.carrier.dateStartBrokenDown = moment().valueOf();
          waiting.status = 600;
        }
        if (this.actionStatus === 700) {
          !waiting.dockCode || waiting.dockCode === 0 ? waiting.parkTime = moment().valueOf() : waiting.dockTimeStart = moment().valueOf();
          waiting.status = 700;
        }
        if (this.actionStatus === 400) {
          !waiting.dockCode || waiting.dockCode === 0 ? waiting.parkTime = moment().valueOf() : waiting.dockTimeStart = moment().valueOf();
          waiting.status = 400;
        }
        await this.removalSrv.updateAe(waiting._id, waiting);
        await this.getWaitingThread();
        this.toastSrv.success('Ce Bon a été mise à jour avec succès', 'Mise à jour réussi')
      } catch (error) {
        this.toastSrv.danger('Une Erreur est survenue lors de la mise à jour de ce Bon', 'Échec de mise à jour')
      } finally {
        this.isLoading = false
      }
    })
  }

  async openUpdateTruckModal(dialog: TemplateRef<any>, status: number, inspection: TruckInspection): Promise<any> {

    this.actionStatus = status;
    this.dialogRef = this.dialogSrv.open(dialog);

    this.dialogRef.onClose.subscribe(async (result) => {
      try {
        this.isLoading = true;
        if (!result) { return }
        inspection.dateTruckStartBrokenDown = moment().valueOf();
        inspection.truckStatus = status;

        await this.removalSrv.updateInspection(inspection?._id, inspection);
        await this.getInspections();
        this.toastSrv.success('Cette inspection a été mise à jour avec succès', 'Mise à jour réussi')
      } catch (error) {
        this.toastSrv.danger('Une Erreur est survenue lors de la mise à jour de cette inspection', 'Échec de mise à jour')
      } finally {
        this.isLoading = false
      }
    })
  }

  async opentruckReceivedModal(dialog: TemplateRef<any>, inspection: TruckInspection) {
    this.dialogRef = this.dialogSrv.open(dialog);
    this.currentInspections = inspection;
  }

  async UpdateTruckInspection(): Promise<void> {
    try {
      this.isLoading = true;

      this.currentInspections.truckStatus = StatusRemovals.LOADED;

      await this.removalSrv.updateInspection(this.currentInspections?._id, this.currentInspections);

      this.toastSrv.success('Cette inspection a été mise à jour avec succès', 'Mise à jour réussi')
    } catch (error) {
      this.toastSrv.danger('Une Erreur est survenue lors de la mise à jour de cette inspection', 'Échec de mise à jour');
      return;
    } finally {
      this.isLoading = false;
    }
    this.dialogRef.close();
    await this.getInspections();
  }

  async sendToDock(): Promise<any> {
    try {
      this.isLoading = true
      if (!this.selectedDock) {
        return this.toastSrv.danger('Veuillez sélectionner un quai', 'Données incorrectes');
      }
      // if (!this.selectedWaiting?.initialTruckTonnage) {
      //   return this.toastSrv.warning('Le poids initial du camion est requis.', 'Information manquante');
      // }
      if (!this.selectedWaiting.dockCode) {
        this.selectedWaiting.dockTimeStart = moment().valueOf();
      }
      this.selectedWaiting.status = 450;
      this.selectedWaiting.dockCode = this.selectedDock.code;
      await this.removalSrv.updateAe(this.selectedWaiting._id, this.selectedWaiting);
      await this.getWaitingThread();
      await this.getAvailableDock();
      this.toastSrv.success(
        `Votre véhicule a bien été affecté au quai numéro ${this.selectedDock.code}.`,
        'Mise à jour effectuée'
      );
      this.dialogRef.close();
    } catch (error) {
      this.toastSrv.danger('Une erreur est survenu lors de cette mise à jour', 'Échec de mise à jour')
    } finally {
      this.isLoading = false;
      this.selectedDock = null;
    }
  }

  // async sendToDock(): Promise<any> {
  //   try {
  //     this.isLoading = true;

  //     if (!this.selectedDock) {
  //       return this.toastSrv.danger('Veuillez sélectionner un quai', 'Données incorrectes');
  //     }

  //     // Dans tous les cas, on met à jour le quai
  //     this.selectedWaiting.dockCode = this.selectedDock.code;

  //     // Cas où les 2 champs sont bien remplis => traitement complet
  //     if (this.selectedWaiting?.initialTruckTonnage ||  this.selectedWaiting?.dockCode) {
  //       if (!this.selectedWaiting?.dockCode) {
  //         this.selectedWaiting.dockTimeStart = moment().valueOf();
  //       }
  //       this.selectedWaiting.status = 450;
  //     }

  //     await this.removalSrv.updateAe(this.selectedWaiting?._id, this.selectedWaiting);
  //     await this.getWaitingThread();
  //     await this.getAvailableDock();

  //     if (this.selectedWaiting?.initialTruckTonnage && this.selectedWaiting?.dockCode) {
  //       this.toastSrv.success('Cette mise à jour a été effectuée avec succès', 'Mise à jour réussie');
  //     } else {
  //       this.toastSrv.info('Le quai a été attribué, mais le traitement complet est en attente.', 'Mise à jour partielle');
  //     }

  //     this.dialogRef.close();
  //   } catch (error) {
  //     this.toastSrv.danger('Une erreur est survenue lors de cette mise à jour', 'Échec de mise à jour');
  //   } finally {
  //     this.isLoading = false;
  //     this.selectedDock = null;
  //   }
  // }



  async saveInitialWeight(): Promise<any> {
    try {
      this.isLoading = true
      if (!this.initialTruckTonnage) {
        return this.toastSrv.danger('Veuillez renseigner le poids initial', 'Données incorrectes');
      }

      this.selectedWaiting.initialTruckTonnage = this.initialTruckTonnage;
      await this.removalSrv.updateAe(this.selectedWaiting._id, this.selectedWaiting);
      await this.getWaitingThread();
      this.toastSrv.success('Le poids initial a été enregistré avec succès', 'Mise à jour réussi');
      this.dialogRef.close();
    } catch (error) {
      this.toastSrv.danger('Une erreur est survenu lors de cette mise à jour', 'Échec de mise à jour')
    } finally {
      this.isLoading = false;
      this.initialTruckTonnage = null;
    }
  }

  async getInspections(): Promise<void> {

    try {
      this.isLoading = true;
      const { data } = await this.removalSrv.getAllInspections({
         truckStatus: parkingStatus.FACTORY,
         LoadNumber: {$exists: false}
        });
      this.inspections = data;
    }
    catch (error) {
      this.toastrSvr.danger('Impossible de récupérer la liste des AE', 'Erreur connexion !');

    } finally { this.isLoading = false; }
  }

  goBack() {
    if (window.history.length > 1) {
      this.location.back()
    } else {
      this.router.navigate(['/home'])
    }
  }

  private filterProduct(value: string): string[] | void {
    if (!value) return;

    const filterValue = value?.toLowerCase();
    return this.productsLabels.filter(optionValue => optionValue?.toLowerCase().includes(filterValue)).sort((a, b) => {
      return a.toString().toLowerCase().indexOf(filterValue?.toLowerCase()) - b.toString().toLowerCase().indexOf(filterValue?.toLowerCase());
    });

  }

  onModelProductChange(value: string): void {
    if (!value) return;

    this.filteredProduct$ = of(this.filterProduct(value));
  }

  onModelTypeChange(value: any): void {
    if (value == null)
      this.dataOrderTypeLabels = this.dataForFilter?.dataOrderType;

    return this.dataOrderTypeLabels = this.dataForFilter?.dataOrderType?.filter((data: string) =>
      data?.toLowerCase().includes(value?.toLowerCase())).sort((a: string, b: string) => {
        return a.toString().toLowerCase().indexOf(value?.toLowerCase()) - b.toString().toLowerCase().indexOf(value?.toLowerCase());
      });
  }

  async onSelectionProductChange(event: any): Promise<void> {
    if (!event) return;

    this.productFilter = event;
    this.filteredProduct$ = of(this.productsLabels);
  }
  async onSelectionOrderTypeChange(event: any): Promise<void> {
    if (!event) return;

    this.orderTypeFilter = event;
    // Refresh the data with the new filter
    await this.getWaitingThread();
  }

  async UpdateNewAE(dialog: TemplateRef<any>, waiting: AuthorizationRemoval): Promise<any> {
    this.isLoading = true
    this.load = true;
    this.removalCurrent = waiting;
    this.dialogRef = this.dialogSrv.open(dialog);

    try {
      const { dataLoadNumber } = await this.removalSrv.getFilterData({
        keyForFilters: ['LoadNumber'],
        SoldTo: this.removalCurrent?.SoldTo,
        FreightHandlingCode: this.removalCurrent?.FreightHandlingCode,
        status: this.removalCurrent?.FreightHandlingCode === RenderTypeValue.PICKUP ? StatusRemovals.ASSIGNED : StatusRemovals.NOT_ASSIGNED,
      });

      this.DataLoadNumbers = this.DataLoadNumbersForFilters = dataLoadNumber;
    } catch (err) {
      console.log(err);
      this.toastrSvr.danger('Une erreur est survenus', 'Erreur connexion !');
    } finally { this.isLoading = false; this.load = false; }

    this.dialogRef.onClose.subscribe(async (result) => {
      if (!result) {
        return this.changeAE = null;
      }

      if (result) {
        this.displayLoading = true
        try {
          this.updatedAesQueues = {
            currentId: this.removalCurrent?._id,
            replaceId: this.changeAE?._id
          }
          await this.removalSrv.updateQueuesAes(this.updatedAesQueues);
          await this.getWaithingThreadNew();
          await this.getWaitingThread();
          this.toastSrv.success('Ce Bon a  été  remplacer avec succès', 'Mise à jour réussi')
          this.changeAE = null;

        } catch (error) {
          console.log(error);
          this.toastrSvr.danger(' Veiller ressayer', 'Erreur connexion !');

        } finally {
          this.displayLoading = false;
          this.filterForm = null;
        }

      }
    });
  }

  async resetAes(dialog: TemplateRef<any>, waiting: AuthorizationRemoval): Promise<any> {
    this.removalCurrent = waiting
    this.dialogRef = this.dialogSrv.open(dialog);
    this.dialogRef.onClose.subscribe(async (result) => {
      if (!result) return;

      this.displayLoading = true
      try {
        await this.removalSrv.resetAes(this.removalCurrent);
        await this.getWaithingThreadNew();
        await this.getWaitingThread();
        this.toastSrv.success('Ce Bon a été réinitialiser avec succès', 'Mise à jour réussi');
      } catch (error) {
        console.log(error);
        this.toastrSvr.danger('Veiller ressayer', 'Erreur connexion !');

      } finally { this.isLoading = false; this.load = false; }
    });
  }

  async onSelectionAE(event: any): Promise<void> {
    if (!event) return
    try {
      this.isLoading = true;
      const removalsData = await this.removalSrv.getAuthorizationRemoval({ LoadNumber: event });
      this.changeAE = removalsData.data[0];

    } catch (err) {
      console.log(err);
      this.toastrSvr.danger('Veiller ressayer', 'Erreur connexion !');

    } finally { this.isLoading = false; this.load = false; }
  }

  async onAesChange(event: any) {
    if (!event) return;

    const filterValue = `${event?.toLowerCase()}`;
    this.DataLoadNumbers = this.DataLoadNumbersForFilters?.filter((data: any) => JSON.stringify(data).includes(`${event}`)).sort((a, b) => {
      return a.toString().toLowerCase().indexOf(filterValue?.toLowerCase()) - b.toString().toLowerCase().indexOf(filterValue?.toLowerCase());
    });;

  }

  FreightHandlingCode(HandlingCode: any) {
    return RenderTypeValueLibrary[HandlingCode];
  }


  async newAes(): Promise<any> {

    if (this.validateNewManualAe(this.newManualAe)) {
      try {
        this.isLoading = true;
        await this.removalSrv.newAes(this.newManualAe);
        this.init();
        this.dialogRef.close();
        this.getWaitingThread()
        this.toastSrv.success('Ce Bon a été créer avec succès', 'Mise à jour réussi');
      } catch (error) {
        console.log(error)
        this.toastrSvr.danger('Veiller ressayer', 'Ce Bon existe déjà.!');

      } finally { this.isLoading = false; }

    } else {
      return;
    }
  }

  validateNewManualAe(newManualAe: any): boolean {
    if (this.hasEmptyString(newManualAe)) {
      this.toastrSvr.danger('Veuillez remplir tous les champs', 'Veuillez corriger');
      return false;
    }
    const loadNumber = newManualAe.LoadNumber;
    const regex = /^\d{7}$/;
    if (!(regex.test(loadNumber)) || isNaN(Number(loadNumber))) {
      this.toastrSvr.danger('La longueur du numéro de Bon doit être de 7 chiffres et être un nombre', 'Veuillez corriger');
      return false;
    }

    if (newManualAe.SoldTo?.length !== 6 || newManualAe.ShipTo?.length !== 6) {
      this.toastrSvr.danger('La longueur des numéros SoldTo et ShipTo doit être de 6 chiffres', 'Veuillez corriger');
      return false;
    }
    return true;
  }

  hasEmptyString(obj: Record<string, any>): boolean {
    return Object.values(obj).some(value => typeof value === 'string' && value === '');
  }

  openUpdateInspection(dialog?: any, inspection?: TruckInspection) {
    this.currentInspections = cloneDeep(inspection);
    this.dialogRef = this.dialogSrv.open(dialog);
  }

  onModelCarrierChange(value: any): void {
    this.onModelChange(value, 'datacarrierlabel', 'dataCarrierLabels');
  }

  onModelChange(value: any, dataKey: string, targetArray: string): void {
    if (value === null) {
      this[targetArray] = this.dataForFilter[dataKey];
      return;
    }
    const normalizedValue = value?.toLowerCase() || '';

    const filteredData = this.dataForFilter[dataKey]?.filter((data: string) =>
      data?.toLowerCase().includes(normalizedValue)
    );
    const sortedData = filteredData?.sort((a: string, b: string) => {
      const indexA = a?.toLowerCase().indexOf(normalizedValue);
      const indexB = b?.toLowerCase().indexOf(normalizedValue);
      return indexA - indexB;
    });

    this[targetArray] = sortedData;
  }
  verifyTel(event: any): any {
    const phoneNumber = event?.target?.value || '';
    const sanitizedPhoneNumber = this.commonSrv.sanitizePhoneNumber(phoneNumber);

    if (this.commonSrv.isValidPhoneNumber(sanitizedPhoneNumber)) {
      this.currentInspections.driverTel = sanitizedPhoneNumber;
      return this.toastrSvr.danger('Renseignez le bon format du numéro de téléphone, ex: 6xXxXxXxX');
    }

    this.currentInspections.driverTel = sanitizedPhoneNumber;
  }

  async saveInspection(): Promise<void> {
    try {
      this.isLoading = true;
      await this.removalSrv.updateInspection(this.currentInspections?._id, { ...this.currentInspections });

      await this.getInspections();

      this.toastrSvr.success(`Cette inspection a été mise à jour avec succès`, `Mise à jour réussi`);
    } catch (error) {
      this.toastrSvr.danger(`Une erreur est survenue lors de la mise à jour de cette inspection.`, 'Erreur de mise à jour');
    } finally {
      this.isLoading = false;
    }
  }

  getQuaiName(number: number): string | null {
    const data = {
      1: 'QUAI1',
      2: 'QUAI2',
      3: 'QUAI3',
      4: 'QUAI4',
      5: 'QUAI5',
      6: 'QUAI6',
    };

    return data[number]; // Return null if the number doesn't exist
  }
}



