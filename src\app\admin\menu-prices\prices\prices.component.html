<div class="common-form-container prices-container">
    <div class="header">
        <nb-icon icon="undo-outline" (click)="goBack()" [options]="{ animation: { type: 'zoom' } }" class="remove-icon" status="primary">
        </nb-icon>
        <h1 class="title">Offres de prix</h1>
    </div>

    <div class="filter-container">
        <div class="left-block">
            <div class="filter-label">Filtrer par</div>
            <div class="filter-elt store-filter">
                <nb-select fullWidth placeholder="Point d'enlèvement" status='primary' size="small" class="empty-input"
                    (selectedChange)='filterStore($event)'>
                    <nb-option [value]='null'>Tout les Points d'enlèvement </nb-option>
                    <nb-option [value]='store' *ngFor="let store of stores">{{store?.label}}
                    </nb-option>
                </nb-select>

            </div>
            <div class="filter-elt product-filter">
                <nb-select fullWidth status='primary' class="empty-input" placeholder="Produit" size="small"
                    (selectedChange)='filterProduct($event)'>
                    <nb-option [value]='null'>Tout les Produits </nb-option>
                    <nb-option [value]='product' *ngFor="let product of products">{{product?.label}}
                        ({{product.normLabel}})
                    </nb-option>
                </nb-select>
            </div>
            <div class="filter-elt">
                <nb-select fullWidth placeholder="Categorie" status='primary' size="small" class="empty-input"
                    (selectedChange)='filterCategory($event)'>
                    <nb-option [value]='null'>Toutes les Catégories </nb-option>
                    <nb-option [value]='category' *ngFor="let category of categories">{{category?.label}}
                    </nb-option>
                </nb-select>
            </div>
            <div class="filter-elt">
                <nb-select fullWidth placeholder="Type" status='primary' class="empty-input" size="small"
                    (selectedChange)='filterPackaging($event)'>
                    <nb-option [value]='null'>Tout les Packagings </nb-option>
                    <nb-option [value]='packaging' *ngFor="let packaging of packagings">{{packaging?.label}}
                        ({{packaging?.unit?.baseUnitValue}}
                        {{packaging?.unit?.baseUnit}})
                    </nb-option>
                </nb-select>
            </div>

        </div>
        <div class="btn-contain">
            <button nbButton outline status="success" class="search-btn filter-btn" size="small" (click)='search()'>
                <nb-icon icon="search-outline"></nb-icon>
                Filtrer
            </button>
            <button nbButton status="success" class="search-btn"  size="small" (click)='openAddModal(addDialog)'>
                <nb-icon icon="plus-square-outline"></nb-icon>
                Ajouter un prix
            </button>
        </div>
    </div>


    <nb-card class="card-container" [nbSpinner]="isLoading" nbSpinnerMessage="Chargement des données"
        nbSpinnerStatus="primary">
        <nb-card-header>
            <nb-list class="element-head  scrool-style">
                <nb-list-item class="list-elt-header list-elt paginator no-border">
                    <div class="col col-image"></div>
                    <div class="col col-product">Produit</div>
                    <div class="col col-code">Code</div>
                    <div class="col col-store">Point d'enlèvement</div>
                    <div class="col col-category">Catégorie</div>
                    <div class="col col-type">Packaging</div>
                    <div class="col col-weight">Poids</div>
                    <div class="col col-amount">Prix (XAF)</div>
                    <div class="col col-action">
                        {{ startIndex }} - {{ endIndex }} sur {{ total }}
                        <div class="actions">
                            <nb-icon icon="arrowhead-left" status="basic" [options]="{ animation: { type: 'zoom' } }"
                                (click)="previousPage()"></nb-icon>
                            <nb-icon icon="arrowhead-right" [options]="{ animation: { type: 'zoom' } }"
                                (click)="nextPage()">
                            </nb-icon>
                        </div>
                    </div>
                </nb-list-item>
            </nb-list>

        </nb-card-header>
        <nb-card-body>
            <nb-list class="element-head  scrool-style">
                <nb-list-item class="list-elt-header list-elt" *ngFor='let price of prices'>
                    <div class="col col-image">
                        <img [src]="price?.product?.img" alt="">
                    </div>
                    <div class="col col-product">{{price?.product?.label}}</div>
                    <div class="col col-code" [nbTooltip]="price?.product?.normLabel" nbTooltipPlacement="top" nbTooltipStatus status="basic">{{price?.product?.normLabel | truncateString:10}}</div>
                    <div class="col col-store" [nbTooltip]="price?.store.label" nbTooltipPlacement="top" nbTooltipStatus status="basic">{{price?.store.label | truncateString:15}}</div>
                    <div class="col col-category">{{price?.productCategory?.label}}</div>
                    <div class="col col-type">{{price?.packaging?.label}}</div>
                    <div class="col col-weight">{{price?.packaging?.unit?.baseUnitValue}}
                        {{price?.packaging?.unit?.baseUnit}}</div>
                    <div class="col col-amount">{{price?.amount}}</div>
                    <div class="col col-action">
                        <button nbTooltip="Detail" nbTooltipPlacement="top" nbTooltipStatus status="basic"
                            class="btn-background" (click)='openDetailModal(detailDialog,price)'>
                            <nb-icon icon="file-text-outline" status="primary"
                                [options]="{ animation: { type: 'zoom' } }"></nb-icon>
                        </button>
                        <button nbTooltip="Editer" nbTooltipPlacement="top" status="basic"
                            (click)='openEditModal(editDialog, price)'>
                            <nb-icon icon="edit-2-outline" status="warning" [options]="{ animation: { type: 'zoom' } }">
                            </nb-icon>
                        </button>
                        <button nbTooltip="Supprimer" nbTooltipPlacement="top" status="basic"
                            (click)='openDeleteModal(deleteDialog, price)'>
                            <nb-icon icon="trash-2-outline" status="danger" [options]="{ animation: { type: 'zoom' } }">
                            </nb-icon>
                        </button>
                    </div>
                </nb-list-item>
                <nb-list-item class="not-found" *ngIf='prices?.length===0 || !prices'>
                    <img src="../../../../assets/images/EMPTY FOLDER VECTOR.png" alt="">
                    <h1>
                        Aucun prix trouvé
                    </h1>
                </nb-list-item>
            </nb-list>
        </nb-card-body>
    </nb-card>
</div>
<ng-template #deleteDialog let-data let-ref="dialogRef" class="dialogRef">

    <nb-card>
        <nb-card-header class="form--header">Supprimer le produit</nb-card-header>
        <nb-card-body>
            <p> Etes-Vous sûr de vouloir supprimer ce Prix ?</p>
        </nb-card-body>
        <nb-card-footer class="form--footer">
            <button nbButton outline ghost status="basic" class="" [disabled]="isLoading" (click)='ref.close()'>
                <nb-icon icon="close-square-outline" [options]="{ animation: { type: 'zoom' } }" class="remove-icon">
                </nb-icon>
                Annuler
            </button>
            <button nbButton outline status="danger" class="" [disabled]="isLoading" (click)='deletePrice()'>
                <nb-icon icon="trash-2-outline" [options]="{ animation: { type: 'zoom' } }" class="remove-icon">
                </nb-icon>
                Supprimer
            </button>
        </nb-card-footer>
    </nb-card>

</ng-template>

<ng-template #addDialog let-data let-ref="dialogRef" class="dialogRef">
    <div class="add-price-container">
        <nb-card class="edit-order">
            <nb-card-header class="form--header">Ajouter les informations</nb-card-header>


            <nb-card-body>
                <div class="edit-container">
                    <div class="input">
                        <label for="otherTrainingType">Produit</label>
                        <nb-select fullWidth placeholder="" size="medium" [(selected)]='selectedProduct'>
                            <nb-option [value]='product' *ngFor="let product of products">{{product?.label}}
                                ({{product.normLabel}})
                            </nb-option>
                        </nb-select>
                    </div>
                    <div class="input">
                        <label for="otherTrainingType">Point d'enlèvement</label>
                        <nb-select fullWidth placeholder="" size="medium" [(selected)]='selectedStore'>
                            <nb-option [value]='store' *ngFor="let store of stores">{{store?.label}}
                            </nb-option>
                        </nb-select>
                    </div>
                    <div class="input">
                        <label for="otherTrainingType">Catégorie</label>
                        <nb-select fullWidth placeholder="" size="medium" [(selected)]='selectedCategory'>
                            <nb-option [value]='category' *ngFor="let category of categories">{{category?.label}}
                            </nb-option>
                        </nb-select>
                    </div>
                    <div class="input">
                        <label for="otherTrainingType">Packaging</label>
                        <nb-select fullWidth placeholder="" size="medium" [(selected)]='selectedPackaging'>
                            <nb-option [value]='packaging' *ngFor="let packaging of packagings">{{packaging?.label}}
                                ({{packaging?.unit?.baseUnitValue}}
                                {{packaging?.unit?.baseUnit}})
                            </nb-option>
                        </nb-select>
                    </div>
                    <div class="input">
                        <label for="otherTrainingType">Prix (XAF)</label>
                        <input nbInput fullWidth size="medium" type="number" class="form-input" [(ngModel)]='amount'>
                    </div>
                </div>
            </nb-card-body>

            <nb-card-footer class="footer">
                <button nbButton outline status="basic" ghost class="btn-border border"  (click)="ref.close()"
                    [disabled]="isLoading">
                    <nb-icon icon="close-square-outline" [options]="{ animation: { type: 'zoom' } }"
                        class="remove-icon">
                    </nb-icon>
                    Annuler
                </button>
                <button nbButton outline status="success" class="btn-contain" [disabled]="isLoading"
                    (click)='addPrice()'>
                    <nb-icon icon="save-outline" [options]="{ animation: { type: 'zoom' } }" class="remove-icon">
                    </nb-icon>
                    Enregistrer
                </button>
            </nb-card-footer>
        </nb-card>

    </div>

</ng-template>



<ng-template #editDialog let-data let-ref="dialogRef" class="dialogRef">
    <div class="add-price-container">
        <nb-card nbSpinnerMessage="Chargement des données" nbSpinnerStatus="primary" class="edit-order">
            <nb-card-header class="form--header">Modifier les informations</nb-card-header>


            <nb-card-body>
                <div class="edit-container">
                    <div class="input">
                        <label for="otherTrainingType">Produit</label>
                        <nb-select fullWidth placeholder="" size="medium" [(selected)]='selectedProduct'>
                            <nb-option [value]='product' *ngFor="let product of products">{{product?.label}}
                                ({{product.normLabel}})
                            </nb-option>
                        </nb-select>
                    </div>
                    <div class="input">
                        <label for="otherTrainingType">Point d'enlèvement</label>
                        <nb-select fullWidth placeholder="" size="medium" [(selected)]='selectedStore'>
                            <nb-option [value]='store' *ngFor="let store of stores">{{store?.label}}
                            </nb-option>
                        </nb-select>
                    </div>
                    <div class="input">
                        <label for="otherTrainingType">Catégorie</label>
                        <nb-select fullWidth placeholder="" size="medium" [(selected)]='selectedCategory'>
                            <nb-option [value]='category' *ngFor="let category of categories">{{category?.label}}
                            </nb-option>
                        </nb-select>
                    </div>
                    <div class="input">
                        <label for="otherTrainingType">Packaging</label>
                        <nb-select fullWidth placeholder="" size="medium" [(selected)]='selectedPackaging'>
                            <nb-option [value]='packaging' *ngFor="let packaging of packagings">{{packaging?.label}}
                                ({{packaging?.unit?.baseUnitValue}}
                                {{packaging?.unit?.baseUnit}})
                            </nb-option>
                        </nb-select>
                    </div>
                    <div class="input">
                        <label for="otherTrainingType">Prix (XAF)</label>
                        <input nbInput fullWidth size="medium" type="number" class="form-input" [(ngModel)]='amount'>
                    </div>
                </div>
            </nb-card-body>

            <nb-card-footer class="footer">
                <button nbButton outline status="basic" ghost class="btn-border border"  (click)="ref.close()"
                    [disabled]="isLoading">
                    <nb-icon icon="close-square-outline" [options]="{ animation: { type: 'zoom' } }"
                        class="remove-icon">
                    </nb-icon>
                    Annuler
                </button>
                <button nbButton outline status="success" class="btn-contain" [disabled]="isLoading"
                    (click)='editPrice()'>
                    <nb-icon icon="save-outline" [options]="{ animation: { type: 'zoom' } }" class="remove-icon">
                    </nb-icon>
                    Enregistrer
                </button>
            </nb-card-footer>
        </nb-card>

    </div>

</ng-template>

<ng-template #detailDialog let-data let-ref="dialogRef">

    <div class="detail-price-container">
        <nb-card>
            <nb-card-header class="form--header">Détail du Point d'enlèvement
            </nb-card-header>
            <nb-card-body>
                <div class="detail-container">
                    <div class="image">
                        <img [src]="selectedPrice?.product?.img" alt="">
                    </div>
                    <div class="row">
                        <div class="title">Produit:</div>
                        <div class="value">{{selectedPrice?.product?.label}} ({{selectedPrice?.product?.normLabel}})
                        </div>
                    </div>
                    <div class="row">
                        <div class="title">Point d'enlèvement:</div>
                        <div class="value">{{selectedPrice?.store?.label}}</div>
                    </div>
                    <div class="row">
                        <div class="title">Catégorie:</div>
                        <div class="value">{{selectedPrice?.productCategory?.label}}</div>
                    </div>
                    <div class="row">
                        <div class="title">Packaging:</div>
                        <div class="value">{{selectedPrice.packaging?.label}}
                            ({{selectedPrice.packaging?.unit?.baseUnitValue}}
                            {{selectedPrice.packaging?.unit?.baseUnit}}) </div>
                    </div>
                </div>
            </nb-card-body>
            <nb-card-footer class="form--footer">
                <button nbButton outline status="basic" class="btn-border border" (click)="ref.close()"
                    [disabled]="isLoading">
                    <nb-icon icon="close-square-outline" [options]="{ animation: { type: 'zoom' } }"
                        class="remove-icon">
                    </nb-icon>
                    Fermer
                </button>


            </nb-card-footer>
        </nb-card>
    </div>
</ng-template>
