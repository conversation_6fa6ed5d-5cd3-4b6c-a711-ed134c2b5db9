.prices-container {
    .filter-container {
        .left-block {
            .filter-elt {
                width: 14%;
            }
            .store-filter{
                width: 23%
            }
            .product-filter{
                width: 29%
            }
        }
        .btn-contain {
            @include v-center;
            .space {
                margin-right: 15px;
            }
            .search-btn {
                width: 50%;
                margin-right: 5px;
            }
            .color-text {
                color: #fff;
            }
        }
    }
    .card-container {

        nb-card-body{
            max-height: 65vh;
        }

        .not-found{
            display: flex;;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            min-height: 200px;
            img{
                height: 140px;
            }
            h1{
                font-size: 18px;
            }
        }
        .list-elt {
            .col-image {
                width: 5%;
                img {
                    height: 31px;
                }
            }

            .col-product {
                width: 10%;
            }
            .col-code {
                width: 15%;
            }
            .col-store {
                width: 15%;
            }
            .col-category {
                width: 15%;
            }
            .col-type {
                width: 10%;
            }
            .col-weight {
                width: 10%;
            }
            .col-weight {
                width: 10%;
            }
            .col-amount {
                width: 15%;
            }
            .col-action{
                button{
                    background: none;
                }
            }
        }

        .form-footer {
            width: 100%;
            display: flex;
            justify-content: center;
            margin: 25px 0;
            button {
                margin: 0 30px;
                max-width: 120px;
            }
        }
    }
}

.add-price-container {
    nb-card {
        .row {
            display: flex;
            justify-content: space-between;
            .input {
                width: 45%;
            }
        }
    }
}

.detail-price-container {
    @include vh-center;
    nb-card {
        width: 30vw;
        .image {
            display: flex;
            justify-content: center;
            img {
                width: 100px;
            }
        }
        border-radius: 9px;
        .form--header {
            display: flex;
            justify-content: space-between;
            .action-icons {
                display: flex;
                justify-content: flex-end;
            }
        }

        .detail-container {
            display: flex;
            flex-direction: column;

            span {
                font-weight: bold;
            }

            .image {
                margin-bottom: 25px;

                img {
                    width: 100px;
                }
            }

            .row {
                justify-content: space-between;
                display: flex;
                margin-bottom: 15px;

                .title {
                    font-size: 15px;
                    font-weight: 600;
                }

                .value {
                    font-size: 14px;
                    text-transform: uppercase;

                }
            }
        }
    }
}
