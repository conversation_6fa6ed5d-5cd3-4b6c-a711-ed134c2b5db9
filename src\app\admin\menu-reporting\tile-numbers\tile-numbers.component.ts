import { Component, OnInit } from '@angular/core';
import { MenuReportingService } from '../menu-reporting.service';
import { RenderTypeValue } from 'src/app/shared/models/render-type.enum';

@Component({
  selector: 'clw-tile-numbers',
  templateUrl: './tile-numbers.component.html',
  styles: [
  ]
})
export class TileNumbersComponent implements OnInit {
  isLoading: boolean;
  data: any;

  constructor(
    private reportingSrv: MenuReportingService
  ) { }

  async ngOnInit() {
    this.refresh();
  }

  async refresh() {
    this.isLoading = true;
    try {
      const query = {
        FreightHandlingCode: RenderTypeValue.PICKUP
      }
      this.data = await this.reportingSrv.getEffectives(query);
      this.isLoading = false;
    } catch (error) {
      console.log(error);
    } finally { this.isLoading = false; }
  }

}
