<div class="tile-sales-container">
    <nb-card accent="success" [nbSpinner]="isLoading" nbSpinnerStatus="success"
        nbSpinnerMessage="Chargement des données">
        <nb-card-header class="card-header-wrapper">
            <div class="card-header">
                Statut des BonS en rendu
            </div>
        </nb-card-header>
        <nb-card-body>
            <div class="progress-info" *ngFor="let status of data | keyvalue">
                <div class="subtitle"> {{status.key}} :
                    <span class="h4">{{status.value}}</span>
                </div>
                <hr color="#f7f9fc" />
            </div>
        </nb-card-body>
    </nb-card>
</div>