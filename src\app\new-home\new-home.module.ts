import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NewHomeComponent } from './new-home.component';
import { NewHomeRoutingModule } from './new-home-routing.module';
import { NbAccordionModule, NbButtonModule, NbCardModule, NbIconModule, NbLayoutModule } from '@nebular/theme';
import { SharedModule } from '../shared/shared.module';

@NgModule({
  declarations: [
    NewHomeComponent,
  ],
  imports: [
    CommonModule,
    NewHomeRoutingModule,
    NbLayoutModule,
    NbButtonModule,
    NbCardModule,
    NbAccordionModule,
    NbIconModule,
    SharedModule,
  ]
})
export class NewHomeModule { }
