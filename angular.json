{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "cli": {"analytics": "782da85c-924b-45e9-b8e1-7666eb253207"}, "version": 1, "newProjectRoot": "projects", "projects": {"cimencam-logistique-web": {"projectType": "application", "schematics": {"@schematics/angular:component": {"inlineStyle": true, "style": "scss"}, "@schematics/angular:application": {"strict": true}}, "root": "", "sourceRoot": "src", "prefix": "clw", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist/cimencam-logistique-web/browser", "index": "src/index.html", "main": "src/main.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "allowedCommonJsDependencies": ["crypto-js", "eva-icons", "image-compressor", "moment", "lodash", "rxjs/internal/operators/filter"], "assets": ["src/favicon.ico", "src/assets", {"glob": "**/*", "input": "node_modules/ng2-pdfjs-viewer/pdfjs", "output": "/assets/pdfjs"}], "styles": ["src/styles.scss"], "scripts": []}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "10mb", "maximumError": "15mb"}, {"type": "anyComponentStyle", "maximumWarning": "2kb", "maximumError": "4kb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "outputHashing": "all"}, "staging": {"budgets": [{"type": "initial", "maximumWarning": "10mb", "maximumError": "15mb"}, {"type": "anyComponentStyle", "maximumWarning": "2kb", "maximumError": "4kb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.staging.ts"}], "outputHashing": "all"}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "development"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"browserTarget": "cimencam-logistique-web:build:production"}, "staging": {"browserTarget": "cimencam-logistique-web:build:staging"}, "development": {"browserTarget": "cimencam-logistique-web:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "cimencam-logistique-web:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.spec.json", "karmaConfig": "karma.conf.js", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/styles.scss"], "scripts": []}}, "server": {"builder": "@angular-devkit/build-angular:server", "options": {"outputPath": "dist/cimencam-logistique-web/server", "main": "server.ts", "tsConfig": "tsconfig.server.json", "inlineStyleLanguage": "scss"}, "configurations": {"production": {"outputHashing": "media", "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}]}, "staging": {"outputHashing": "media", "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.staging.ts"}]}, "development": {"optimization": false, "sourceMap": true, "extractLicenses": false}}, "defaultConfiguration": "production"}, "serve-ssr": {"builder": "@nguniversal/builders:ssr-dev-server", "configurations": {"development": {"browserTarget": "cimencam-logistique-web:build:development", "serverTarget": "cimencam-logistique-web:server:development"}, "production": {"browserTarget": "cimencam-logistique-web:build:production", "serverTarget": "cimencam-logistique-web:server:production"}, "staging": {"browserTarget": "cimencam-logistique-web:build:staging", "serverTarget": "cimencam-logistique-web:server:staging"}}, "defaultConfiguration": "development"}, "prerender": {"builder": "@nguniversal/builders:prerender", "options": {"routes": ["/"]}, "configurations": {"production": {"browserTarget": "cimencam-logistique-web:build:production", "serverTarget": "cimencam-logistique-web:server:production"}, "staging": {"browserTarget": "cimencam-logistique-web:build:staging", "serverTarget": "cimencam-logistique-web:server:staging"}, "development": {"browserTarget": "cimencam-logistique-web:build:development", "serverTarget": "cimencam-logistique-web:server:development"}}, "defaultConfiguration": "production"}}}}}