<div class="common-form-container inspection-container">
  <div class="header">
    <nb-icon icon="undo-outline" (click)="goBack()" [options]="{ animation: { type: 'zoom' } }" class="remove-icon" status="primary">
    </nb-icon>
    <h1 class="title">DONNEES DE L'INSPECTION</h1>
  </div>

  <div class="filter-container">
    <div class="left-block">
    </div>
    <div class="btn-contain">
      <button nbButton status="success" class="search-btn" size="small" (click)="openAddModal(addDialog)">
        <nb-icon icon="plus-square-outline"></nb-icon>
        Ajouter une inspection
      </button>
    </div>
  </div>

  <nb-card class="card-container" [nbSpinner]="isLoading" nbSpinnerStatus="primary" nbSpinnerMessage="Chargement des données">
    <nb-card-header>
      <nb-list class="element-head scrool-style">
        <nb-list-item class="list-elt-header list-elt paginator no-border">
          <div class="col col-code">Code</div>
          <div class="col col-label">Label</div>
          <div class="col col-status">Statut</div>
          <div class="col col-actions">
            <div class="actions">
            </div>
          </div>
        </nb-list-item>
      </nb-list>
    </nb-card-header>
    <nb-card-body class="body-size">
      <nb-list class="element-head scrool-style">
        <nb-list-item class="list-elt-header list-elt" *ngFor="let inspection of inspections; index as i">
          <div class="col col-code">{{inspection?.code || i+1}}</div>
          <div class="col col-label">{{inspection?.label}}</div>
          <div class="col col-status">
            <nb-badge [text]="inspection?.status?.label" 
                     [status]="inspection?.status?.code === 1 ? 'success' : 'warning'"
                     class="badge">
            </nb-badge>
          </div>
          <div class="col col-actions">
            <button nbTooltip="Editer" nbTooltipPlacement="top" status="basic" (click)="openEditModal(editdialog, inspection)">
              <nb-icon icon="edit-2-outline" status="warning" [options]="{ animation: { type: 'zoom' } }">
              </nb-icon>
            </button>
            <button nbTooltip="Supprimer" nbTooltipPlacement="top" status="basic" (click)="openDeleteModal(deleteDialog, inspection)">
              <nb-icon icon="trash-2-outline" status="danger" [options]="{ animation: { type: 'zoom' } }">
              </nb-icon>
            </button>
          </div>
        </nb-list-item>
        <nb-list-item class="not-found" *ngIf="this.inspections.length===0 && !isLoading">
          <img src="../../../../assets/images/EMPTY FOLDER VECTOR.png" alt="">
          <h6>
            Aucune donnée trouvée
          </h6>
        </nb-list-item>
      </nb-list>
    </nb-card-body>
  </nb-card>
</div>

 <ng-template #addDialog let-data let-ref="dialogRef" class="dialogRef">
  <div class="add-jde">
    <nb-card  [nbSpinner]="isConfirmLoading" nbSpinnerStatus="primary" nbSpinnerMessage="">
      <nb-card-header>Ajouter une inspection</nb-card-header>
      <nb-card-body>
        <!-- <div class="process-option">
          <label for="">Code</label>
          <input nbInput type="number" placeholder="Veuillez entrer votre le N° Jde">
        </div> -->
        <div class="process-option">
          <label for="">libellé</label>
          <input nbInput fullWidth size="medium" type="text" placeholder="Veuillez entrer un nom" [(ngModel)]="inspection.label">
        </div>
        <div class="process-option">
          <label for="">Statut</label>
          <nb-select name="id" id="id" fullWidth size="medium" [(selected)]="selectedStatus" (selectedChange)="selectStatus($event)">
            <nb-option [value]="status" *ngFor="let status of currStatus">{{status?.label}}</nb-option>
          </nb-select>
        </div>
      </nb-card-body>
      <nb-card-footer>
        <button nbButton status="basic" class="margin-btn" ghost (click)="ref.close()">
          <nb-icon icon="close-square-outline"></nb-icon>
          Annuler
        </button>
        <button nbButton status="primary" outline (click)="insertInspection()">
          <nb-icon icon="save-outline"></nb-icon>
          Enregistrer
        </button>
      </nb-card-footer>
    </nb-card>
  </div>
</ng-template>

<ng-template #editdialog let-data let-ref="dialogRef">
  <div  [nbSpinner]="isConfirmLoading" nbSpinnerStatus="primary" nbSpinnerMessage=""class="add-jde">
    <nb-card>
      <nb-card-header>Modifier les informations</nb-card-header>
      <nb-card-body>
        <div class="process-option">
          <label for="">Libellé</label>
          <input nbInput type="text" placeholder="Modifier le libellé" [(ngModel)]="inspection.label" fullWidth size="medium">
        </div>
        <div class="process-option">
          <label for="">Statut</label>
          <nb-select name="id" id="id" [(selected)]="selectedStatus" (selectedChange)="selectStatus($event)" fullWidth size="medium"
          placeholder="{{inspection?.status?.label|| 'Non renseigner'}}">
            <nb-option [value]="status" *ngFor="let status of currStatus">{{status?.label}}</nb-option>
          </nb-select>
        </div>

      </nb-card-body>
      <nb-card-footer>
        <button nbButton status="basic" class="margin-btn" ghost (click)="ref.close()">
          <nb-icon icon="close-square-outline"></nb-icon>
          Annuler
        </button>
        <button nbButton status="primary" outline (click)="updateInspection()">
          <nb-icon icon="save-outline"></nb-icon>
          Enregistrer
        </button>
      </nb-card-footer>
    </nb-card>
  </div>
</ng-template>

<ng-template #deleteDialog let-data let-ref="dialogRef" class="dialogRef">

  <nb-card  [nbSpinner]="isConfirmLoading" nbSpinnerStatus="primary" nbSpinnerMessage="">
      <nb-card-header class="form--header">Supprimer l'inspection'</nb-card-header>
      <nb-card-body>
          <div> Etes-vous sûr de vouloir supprimer l'inspection {{inspection?.label }}?</div>
      </nb-card-body>
      <nb-card-footer class="form--footer">
          <button nbButton outline ghost status="basic" class="" [disabled]="isLoading" (click)='ref.close()'>
              <nb-icon icon="close-square-outline" [options]="{ animation: { type: 'zoom' } }" class="remove-icon">
              </nb-icon>
              Annuler
          </button>
          <button nbButton outline status="danger" class="" [disabled]="isLoading" (click)='deleteInspection()'>
              <nb-icon icon="trash-2-outline" [options]="{ animation: { type: 'zoom' } }" class="remove-icon">
              </nb-icon>
              Supprimer
          </button>
      </nb-card-footer>
  </nb-card>

</ng-template>
