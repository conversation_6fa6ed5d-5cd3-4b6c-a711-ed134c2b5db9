import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { CategoryProduct } from 'src/app/shared/models/category-product';
import { ErpItemId } from 'src/app/shared/models/jdeProductId';
import { Packaging } from 'src/app/shared/models/packaging';
import { BaseUrlService } from 'src/app/shared/services/base-url.service';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root'
})
export class MenuJdeService {


  BASE_URL: string;
  currentErpId:any;

  constructor(
    private http: HttpClient,
    private baseUrlSrv: BaseUrlService,
  ) {
    this.BASE_URL = `${this.baseUrlSrv.getOrigin()}${environment.basePath}`;
  }


  async getProducts(): Promise<any> {
    return await this.http.get(`${this.BASE_URL}/products`).toPromise();

  }

  async getProductCategories(): Promise<CategoryProduct[]> {
    return await this.http.get<CategoryProduct[]>(`${this.BASE_URL}/category-products`).toPromise();
  }

  async getPackagings(): Promise<Packaging[]> {
    return await this.http.get<Packaging[]>(`${this.BASE_URL}/packagings`).toPromise();

  }

  async getJdeItemsIds(): Promise<ErpItemId[]> {
    return await this.http.get<ErpItemId[]>(`${this.BASE_URL}/erp-items-ids`).toPromise();
  }

  async addItemId(erpItemId: ErpItemId): Promise<any> {
    return await this.http.post(`${this.BASE_URL}/erp-items-ids`, erpItemId).toPromise();
  }

  async deleteItemId(erpItemId: ErpItemId): Promise<any> {
    return await this.http.delete(`${this.BASE_URL}/erp-items-ids/${erpItemId._id}`).toPromise();
  }

  async editItemId(erpItemId: ErpItemId): Promise<any> {
    return await this.http.put(`${this.BASE_URL}/erp-items-ids/${erpItemId._id}`, erpItemId).toPromise();
  }


}
