import * as moment from 'moment';
import { Location } from '@angular/common';
import { NbToastrService } from '@nebular/theme';
import { ActivatedRoute, Router } from '@angular/router';
import { RemovalService } from '../removal-management/removal.service';
import { CommonService } from 'src/app/shared/services/common.service';
import { Component, OnInit, Pipe, PipeTransform, ViewEncapsulation } from '@angular/core';

@Pipe({
  name: 'status'
})

export class StatusRemovalPipe implements PipeTransform {
  transform(status: number, isColor: boolean): string {
    const colors = { 400: 'primary' };
    const statuses = { 400: 'EN ATTENTE' };
    if (isColor) { return colors[status]; }
    return statuses[status];
  }
}
@Component({
  selector: 'clw-menu-call-screen',
  templateUrl: './menu-call-screen.component.html',
  styles: [
  ],
  encapsulation: ViewEncapsulation.None

})

export class MenuCallScreenComponent implements OnInit {

  waitingThread: any;
  waitingThreadTab: any[];
  timer: any;
  result: number;
  waitingTime: any;
  status: any;
  elem: any;
  docks: any[];

  constructor(
    private removalSrv: RemovalService,
    private toastSrv: NbToastrService,
    private route: ActivatedRoute,
    private router: Router,
    private location: Location,
    public commonSvr: CommonService
  ) { }

  isLoading = false;


  async ngOnInit(): Promise<void> {
    await this.getTruckByDock()

    this.route.queryParams.subscribe(async (query) => {
      this.status = query.status
      if (!this.status) {
        this.location.back()
      }
      await this.getWaitingThread();
    });

    setInterval(() => {
      this.result = moment().valueOf();
    }, 1000);

    setInterval(async () => {
      await this.getWaitingThread();
    }, 30000);
  }

  getDurationDate(startDate: number) {
    const currentTime = Date.now();
    const diff = currentTime - startDate;
    const seconds = Math.floor(diff / 1000) % 60;
    const minutes = Math.floor(diff / (1000 * 60)) % 60;
    const hours = Math.floor(diff / (1000 * 60 * 60)) % 24;
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));

    return `${days} jours(s) ${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  }

  getConcatenatedLabels(itemNumber): string {
    return itemNumber?.map(item => `${item?.product?.label} (${item?.packaging?.label})`).join(', ') || 'N/A';
  }
  async getTruckByDock(): Promise<any> {

    this.isLoading = true;
    try {
      this.docks = await this.removalSrv.getTruckByDock({});

    } catch (error) {
      console.log(error);
      return this.toastSrv.danger(`Erreur de connexion internet`, `Echec operation`)
    } finally { this.isLoading = false; }
  }


  goBack() {
    if (window.history.length > 1) {
      this.location.back()
    } else {
      this.router.navigate(['/removal/waiting-thread'])
    }
  }

  async getWaitingThread(): Promise<any> {
    const options = { status: this.status, moved: this.status, sort: `${this.getParkTimeKey(this.status)}.entry`, way: 1 }
    this.isLoading = true;
    try {
      const { data } = await this.removalSrv.getAuthorizationRemoval(options);
      this.waitingThread = data.sort((currentAE, nextAE) => currentAE[`${this.getParkTimeKey(this.status)}`].at(-1).entry - nextAE[`${this.getParkTimeKey(this.status)}`].at(-1).entry);
      this.waitingThreadTab = [...this.waitingThread];
    } catch (error) {
      console.log(error);
      return this.toastSrv.danger(`Erreur de connexion internet`, `Echec operation`)
    } finally { this.isLoading = false; }
  }

  getColorStyle(time: any): any {
    const hours = 5400001; // 5400001 equal 1h30 min
    const minutes = 1800001;
    const waitingTime = moment(this.result).diff(moment(time));

    if (waitingTime >= hours) {
      return {
        color: '#FF4F4F'
      };
    }
    if (waitingTime >= minutes && waitingTime < hours) {
      return {
        color: '#F4D954'
      }
    }
    return {
      color: '#4DE352'
    }

  }

  getColorDOckStyle(time: any): any {
    const hours = 5400001; // 5400001 equal 1h30 min
    const minutes = 1800001; // 5400001 equal 30 min
    const waitingTime = moment(this.result).diff(moment(time));

    if (waitingTime >= hours) {
      return {
        color: '#FF4F4F'
      };
    }
    if (waitingTime >= minutes && waitingTime < hours) {
      return {
        color: '#F4D954'
      }
    }
    return {
      color: '#4DE352'
    }

  }

  getColorStyleStatus(time: any): any {
    const hours = 5400001; // 5400001 equal 1h30 min
    const minutes = 1800001; // 5400001 equal 30 min
    const waitingTime = moment(this.result).diff(moment(time));

    if (waitingTime >= hours) {
      return {
        color: '#FF4F4F',
        'background-color': 'rgb(58 56 56)',
        border: '1px solid rgb(94 56 56)'
      };
    }
    if (waitingTime >= minutes && waitingTime < hours) {
      return {
        color: 'rgb(255 233 79)',
        'background-color': ' rgb(58, 56, 56)',
        border: '1px solid rgb(78 77 63)'
      }
    }

    return {
      color: 'rgb(86 255 91)',
      'background-color': 'rgb(58, 56, 56)',
      border: '1px solid rgb(64 82 56)'
    }

  }

  getParkTimeKey(status: number): string {
    const StatusParkTimeKeyMap = {
      400: 'factoryParkTimes',
      450: 'dockParkTimes',
      600: 'failureParkTimes',
      700: 'newParkTimes',
    }

    return StatusParkTimeKeyMap[status];
  }

}
