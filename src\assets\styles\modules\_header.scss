.header-pages {
  background: $color-secondary;
  padding-bottom: 10px;
  // margin-top: 59px;
  .order-container {
    @include vh-between;

  }

  .left-block {
    @include v-center;
    width: 20%;
    .icon-frame {
      @include vh-center;
      border: 1px solid #f3f3f3;
      box-sizing: border-box;
      margin-right: 10px;
      padding: 3%;
      border-radius: 7px;
    }
    .title {
      color: $color-primary;
      font-family: $font-regular;
      font-size: 1.3rem;
    }

    nb-select{
      max-width: 73em;
      min-width: 20em;
      margin-left: 3em;
      margin-top: 12px;
      font-size: 12px;
      
      select-small-text-font-size{
        font-size: 12px;
      }
  }
    


  }
  .right-block {
    @include v-center;
    justify-content: flex-end;
    width: 40%;
    .login-btn {
      border: 1px solid $color-primary;
      padding: 4px 12px;
      border-radius: 5px;
      margin-right: 10px;
      color: $color-primary;
    }
    .new-purchase-btn {
      background-color: $color-primary;
      border-color: $color-primary;
      color: $color-secondary;
      margin-right: 10px;
    }

    .new-purchase-btn:hover,
    .login-btn:hover {
      @include scale-effect;
    }
    .customer-infos {
      display: block;
      .welcome {
        font-family: $font-regular;
        font-size: 13px;
        color: #000;
        font-weight: 600;
      }
      .user-function {
        font-family: $font-regular;
        font-size: 11px;
        color: #5e5d5d;
      }
    }

    .info-profil {
      @include vh-between;
      .profil {
        cursor: pointer;
        border-radius: 50%;
      }

      .vertical-point {
        background: no-repeat center;
        background-image: url("./../../images/icons/more-infos.png");
        height: 20px;
        width: 4px;
        margin-left: 8px;
        cursor: pointer;
        border: none;
        &:hover {
          transform: scale(1.1);
        }
      }
    }
  }
}
@media only screen and (min-width: 1200px) {
  .header-pages .left-block .title {
    font-size: 15px;
  }
}
