import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Price } from 'src/app/shared/models/price';
import { BaseUrlService } from 'src/app/shared/services/base-url.service';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root'
})
export class PricesService {


  BASE_URL: string;
  currentPrice: Price;

  constructor(
    private http: HttpClient,
    private baseUrlSrv: BaseUrlService,

  ) {
    this.BASE_URL = `${this.baseUrlSrv.getOrigin()}${environment.basePath}`;

  }

  async getPrices(param: any): Promise<any> {
    const params = this.generateQueryParams(param);

    return await this.http.get(`${this.BASE_URL}/prices`,{params}).toPromise();

  }

  async deletePrices(): Promise<any> {
    return await this.http.delete(`${this.BASE_URL}/prices/${this.currentPrice._id}`).toPromise();
  }

  async editPrices(price: Price ): Promise<any> {
    return await this.http.put(`${this.BASE_URL}/prices/${this.currentPrice._id}`, price).toPromise();
  }

  async addPrices(price: Price ): Promise<any> {
    return await this.http.post(`${this.BASE_URL}/prices/`, price).toPromise();
  }


  generateQueryParams(param: any): any {
    let params = new HttpParams();

    const {offset, limit, storeRef,  productRef, categoryCode, packagingCode } = param;

  if (offset) {
    params = params.append('offset', `${offset}`);
  } 
  if (limit) {
    params = params.append('limit', `${limit}`);    
  } 
  if (storeRef) {
    params = params.append('storeRef', `${storeRef}`);    
  } 
  if (productRef) {
    params = params.append('productRef', `${productRef}`);    
  } 
  if (categoryCode) {
    params = params.append('categoryCode', `${categoryCode}`);    
  } 
  if (packagingCode) {
    params = params.append('packagingCode', `${packagingCode}`);    
  } 
    

    return params;
  }
}
