import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NbTabsetModule, NbCardModule, NbIconModule, NbListModule, NbBadgeModule, NbDialogModule, NbButtonModule, NbSpinnerModule, NbTooltipModule, NbSelectModule, NbInputModule, NbDatepickerModule, NbContextMenuModule, NbAutocompleteModule, NbToggleModule } from '@nebular/theme';
import { SharedModule } from 'src/app/shared/shared.module';
import { WaitingThreadRoutingModule } from './waiting-thread-routing.module';
import { StatusRemovalPipe, WaitingThreadComponent } from './waiting-thread.component';
import { FormsModule } from '@angular/forms';


@NgModule({
  declarations: [
    WaitingThreadComponent,
    StatusRemovalPipe
  ],
  imports: [
    FormsModule,
    CommonModule,
    NbTabsetModule,
    NbCardModule,
    NbIconModule,
    NbListModule,
    NbBadgeModule,
    NbToggleModule,
    NbButtonModule,
    NbSpinnerModule,
    NbTooltipModule,
    NbSelectModule,
    NbInputModule,
    SharedModule,
    NbDatepickerModule,
    NbAutocompleteModule,
    NbContextMenuModule,
    WaitingThreadRoutingModule,

  ]
})
export class WaitingThreadModule { }
