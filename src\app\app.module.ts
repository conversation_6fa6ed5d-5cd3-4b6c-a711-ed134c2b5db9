import { CUSTOM_ELEMENTS_SCHEMA, LOCALE_ID, NgModule } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';

import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { NbThemeModule, NbLayoutModule, NbIconModule, NbToastrModule, NbSidebarModule, NbMenuModule, NbDatepickerModule, NbDialogModule, NbTimepickerModule, NbGlobalLogicalPosition } from '@nebular/theme';
import { NbEvaIconsModule } from '@nebular/eva-icons';
import { SharedModule } from './shared/shared.module';
import { HttpClientModule } from '@angular/common/http';
import { registerLocaleData } from '@angular/common';
import { FormsModule } from '@angular/forms';
import localeFr from '@angular/common/locales/fr';
import { httpInterceptorProviders } from './interceptors';
import { DragDropModule } from '@angular/cdk/drag-drop';
import { MenuBrokenDownTrucksModule } from './client/menu-removal/menu-broken-down-trucks/menu-broken-down-trucks.module';
import { AuthGuard } from './shared/guards/auth.guard';
import { AdminGuard } from './shared/guards/admin.guard';

registerLocaleData(localeFr);

@NgModule({
  declarations: [
    AppComponent,
  ],
  imports: [
    BrowserModule.withServerTransition({ appId: 'serverApp' }),
    AppRoutingModule,
    BrowserAnimationsModule,
    NbThemeModule.forRoot({ name: 'default' }),
    NbLayoutModule,
    NbEvaIconsModule,
    DragDropModule,
    NbIconModule,
    SharedModule,
    NbToastrModule.forRoot({
      duration: 6000,
      position: NbGlobalLogicalPosition.BOTTOM_END,
      preventDuplicates: true
    }),
    HttpClientModule,
    FormsModule,
    NbDialogModule.forRoot({ hasBackdrop: true }),
    NbSidebarModule.forRoot(),
    NbDatepickerModule.forRoot(),
    NbTimepickerModule.forRoot(),
    NbMenuModule.forRoot(),
    MenuBrokenDownTrucksModule

  ],
  providers: [
    httpInterceptorProviders,
    // AuthGuard,
    // AdminGuard,
    { provide: LOCALE_ID, useValue: 'fr-FR' }
  ],
  bootstrap: [AppComponent],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class AppModule { }
