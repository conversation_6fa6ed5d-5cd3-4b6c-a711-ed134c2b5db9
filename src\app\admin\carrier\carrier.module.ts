import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { CarrierRoutingModule } from './carrier-routing.module';
import { CarrierComponent } from './carrier.component';
import {
  NbAutocompleteModule,
  NbBadgeModule, NbButtonModule, NbCardModule, NbDatepickerModule,
  NbDialogModule, NbIconModule, NbInputModule, NbListModule, NbSelectModule, NbSpinnerModule,
  NbTabsetModule, NbToastrModule, NbToggleModule, NbTooltipModule
} from '@nebular/theme';
import { CarrierListComponent } from './carrier-list/carrier-list.component';
import { CarrierDetailComponent } from './carrier-detail/carrier-detail.component';
import { FormsModule } from '@angular/forms';
import { CarrierAddComponent } from './carrier-add/carrier-add.component';
import { PdfJsViewerModule } from 'ng2-pdfjs-viewer';
import { SharedModule } from 'src/app/shared/shared.module';


@NgModule({
  declarations: [
    CarrierComponent,
    CarrierListComponent,
    CarrierDetailComponent,
    CarrierAddComponent],
  imports: [
    CommonModule,
    CarrierRoutingModule,
    FormsModule,
    NbIconModule,
    NbInputModule,
    NbSelectModule,
    NbCardModule,
    NbButtonModule,
    NbTooltipModule,
    NbDatepickerModule,
    NbDialogModule.forChild(),
    NbTabsetModule,
    NbToggleModule,
    NbListModule,
    SharedModule,
    NbToastrModule,
    NbBadgeModule,
    NbSpinnerModule,
    PdfJsViewerModule,
    NbAutocompleteModule,
  ]
})
export class CarrierModule { }
