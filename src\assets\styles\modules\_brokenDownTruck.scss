.broken-down-truck-container {
  margin-left: 2%;
  margin-right: 6%;
  margin-top: 2%;

  .label-historic-order {
    @include v-center;

    h6 {
      margin-bottom: 0 !important;
      margin-left: 5px;
    }
  }

  .not-found {
    display: flex;
    justify-content: center;
    flex-direction: column;
    align-items: center;
    width: 100%;
  }

  .title-wrong {
    font-family: $font-bold;
    font-size: 14px;
  }

  .heigth-card {
    height: 68vh;
  }

  .btn-margin {
    margin-right: 15px;
  }

  .group-select {
    display: flex;
  }

}

.btn-raduis {
  border-radius: 5px !important;
}

.truckDetail {
  width: 30vw;
  height: 50vh;
}

span {
  font-weight: bold;
}

.truck-container {
  @include vh-between;

  .truck-label {
    margin-bottom: 10px;
    font-family: $font-regular;
    font-size: 14px;
  }

  .wrong {
    font-family: $font-bold;
  }
}

.ae-contain {
  @include vh-between;
  justify-content: flex-start;

  .truck-label {
    margin-bottom: 10px;
    font-family: $font-regular;
    font-size: 14px;
  }

  .wrong {
    font-family: $font-bold;
  }
}

.assign-truck{
  height: 50vh;
  .truck-assign {
    .group-select {
      .row {
        display: flex;
        justify-content: space-between;

        .input {
          width: 45%;
        }
      }
    }
  }

  // Styles pour l'alignement horizontal des tabsets et pagination
  .tabset-pagination-container {
    display: flex;
    justify-content: space-between;
    align-items: stretch; /* Permet aux éléments enfants de prendre la même hauteur */
    min-height: 48px; /* Hauteur minimale pour uniformiser */
    padding: 0 20px;
    border-bottom: 1px solid #edf1f7;
    background-color: #f8f9fa;
  }

  .tabset-pagination-container nb-tabset {
    display: flex;
    align-items: center;
    min-width: 500px; /* Largeur minimale augmentée pour éviter le chevauchement */
    max-width: 65%; /* Limite légèrement augmentée pour plus d'espace */
    flex-shrink: 0; /* Empêche la compression des tabsets */
  }

  .tabset-pagination-container nb-tabset ::ng-deep .tabset {
    height: 48px; /* Hauteur fixe pour les tabsets */
    display: flex;
    align-items: center;
    margin: 0 !important;
  }

  .tabset-pagination-container nb-tabset ::ng-deep .tab {
    height: 48px; /* Même hauteur pour les onglets */
    display: flex;
    align-items: center;
    min-width: 180px; /* Largeur minimale par onglet */
    flex: 1; /* Répartit l'espace équitablement entre les onglets */
  }

  .tabset-pagination-container nb-tabset ::ng-deep .tab-link {
    height: 48px; /* Même hauteur pour les liens des onglets */
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 20px;
    white-space: nowrap; /* Empêche le retour à la ligne */
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
  }

  .pagination-wrapper {
    display: flex;
    align-items: center;
    height: 48px; /* Même hauteur que les tabsets */
    padding: 0 8px;
    flex-shrink: 0; /* Empêche la compression de la pagination */
    min-width: 200px; /* Largeur minimale pour la pagination */
  }

  .pagination-wrapper ::ng-deep .pagination-container {
    height: 48px;
    display: flex;
    align-items: center;
  }

  .tab-content {
    padding: 0;
  }

  @media (max-width: 768px) {
    .tabset-pagination-container {
      flex-direction: column;
      align-items: stretch;
      min-height: auto;
      padding: 10px;
    }

    .tabset-pagination-container nb-tabset {
      min-width: auto;
      max-width: 100%;
      margin-bottom: 10px;
    }

    .tabset-pagination-container nb-tabset ::ng-deep .tab {
      min-width: 120px; /* Largeur réduite sur mobile */
    }

    .pagination-wrapper {
      width: 100%;
      justify-content: center;
      min-width: auto;
      height: 48px;
    }
  }
}
