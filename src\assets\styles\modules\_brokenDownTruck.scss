.broken-down-truck-container {
  margin-left: 2%;
  margin-right: 6%;
  margin-top: 2%;

  .label-historic-order {
    @include v-center;

    h6 {
      margin-bottom: 0 !important;
      margin-left: 5px;
    }
  }

  .not-found {
    display: flex;
    justify-content: center;
    flex-direction: column;
    align-items: center;
    width: 100%;
  }

  .title-wrong {
    font-family: $font-bold;
    font-size: 14px;
  }

  .heigth-card {
    height: 68vh;
  }

  .btn-margin {
    margin-right: 15px;
  }

  .group-select {
    display: flex;
  }

}

.btn-raduis {
  border-radius: 5px !important;
}

.truckDetail {
  width: 30vw;
  height: 50vh;
}

span {
  font-weight: bold;
}

.truck-container {
  @include vh-between;

  .truck-label {
    margin-bottom: 10px;
    font-family: $font-regular;
    font-size: 14px;
  }

  .wrong {
    font-family: $font-bold;
  }
}

.ae-contain {
  @include vh-between;
  justify-content: flex-start;

  .truck-label {
    margin-bottom: 10px;
    font-family: $font-regular;
    font-size: 14px;
  }

  .wrong {
    font-family: $font-bold;
  }
}

.assign-truck{
  height: 50vh;
  .truck-assign {
    .group-select {
      .row {
        display: flex;
        justify-content: space-between;
  
        .input {
          width: 45%;
        }
      }
    }
  }
}
