<div class="tile-sales-container">
  <nb-card accent="success" [nbSpinner]="isLoading" nbSpinnerStatus="success" nbSpinnerMessage="Chargement des données">
    <nb-card-header class="card-header-wapper">
      <div class="card-header-contain">
        <div>
          Nombre de Bons
        </div>
      </div>
    </nb-card-header>
    <nb-card-body class="body">
      <div class="block1">
        <div class="ae-data">
          <div class="ae-title">
            <div class="ae icon" [ngClass]="{'info':true }">
              <nb-icon icon='file-text-outline' status="info"></nb-icon>
            </div>
            <div class="ae-label">Bons Totales
            </div>
          </div>
          <div class="ae-title ae-center">
            <div class="ae-label ae-value">{{data?.nbAEs}}</div>
          </div>
        </div>
      </div>

      <div class="block2">

        <div class="ae-data">
          <div class="ae-title">
            <div class="ae icon" [ngClass]="{'info':true }">
              <nb-icon icon='file-remove-outline' status="info"></nb-icon>
            </div>
            <div class=" ae-label">Bons en attente
            </div>
          </div>
          <div class="ae-title ae-center">
            <div class="ae-label ae-value">{{data?.nbAEsInAwait}}</div>
          </div>
        </div>
        <div class="ae-data">
          <div class="ae-title">
            <div class="ae icon" [ngClass]="{'success':true }">
              <nb-icon icon='checkmark-circle-2-outline' status="success"></nb-icon>
            </div>
            <div class="ae-label">Bons acceptés
            </div>
          </div>
          <div class="ae-title ae-center">
            <div class="ae-label ae-value">{{data?.nbAccepted}}</div>
          </div>
        </div>
      </div>

      <!-- <div class="block2"> -->
        

        <!-- <div class="ae-data">
          <div class="ae-title">
            <div class="ae icon" [ngClass]="{'info':true }">
              <nb-icon icon='shopping-cart-outline' status="info"></nb-icon>
            </div>
            <div class="ae-label">Bons non préchargés
            </div>
          </div>
          <div class="ae-title ae-center">
            <div class="ae-label ae-value">{{data?.nbAEsPickup}}</div>
          </div>
        </div> -->
      <!-- </div> -->
      <div class="block1">

        <div class="ae-data">
          <div class="ae-title">
            <div class="ae icon" [ngClass]="{'warning':true }">
              <nb-icon icon='file-text-outline' status="warning"></nb-icon>
            </div>
            <div class="ae-label">Bons Annulés
            </div>
          </div>
          <div class="ae-title ae-center">
            <div class="ae-label ae-value">{{data?.nbAEsRenderRejected}}</div>
          </div>
        </div>
        <!-- <div class="ae-data">
          <div class="ae-title">
            <div class="ae icon" [ngClass]="{'info':true }">
              <nb-icon icon='file-text-outline' status="info"></nb-icon>
            </div>
            <div class="ae-label">Bons en <br>préchargés
            </div>
          </div>
          <div class="ae-title ae-center">
            <div class="ae-label ae-value">{{data?.nbAEsInPreLoading}}</div>
          </div>
        </div> -->

      </div>

    </nb-card-body>
  </nb-card>
</div>