// Global styles
.header {
  .nav {
    @include vh-between;
    box-shadow: none;
    padding: 0px;

    .nav-left {
      @include v-center;
      gap: 10rem;

      .logo img {
        height: 4rem;
        width: 10rem; // Ajustable selon le besoin
      }

      .nav-links {
        display: flex;
        gap: 2rem;

        a {
          color: $color-accent;
          text-decoration: none;
          font-weight: 500;
          transition: color 0.3s ease;
          font-size: 1rem;
          font-family: $font-mont-bold;

          &:hover {
            color: $color-primary;
          }
        }

        .router-link-active {
          color: $color-primary;
        }
      }
    }

    .login-btn {
      border: 1px solid $color-fourth;
      padding: 0.8em;
      border-radius: 2em;
      background-color: $color-fourth;
      font-size: 1em;
      width: 7rem;
      color: $color-secondary;
      cursor: pointer;
      font-family: $font-mont-bold;
      font-weight: bold;
      transition: background 0.3s ease;
      text-align: center;

      &:hover {
        background: darken($color-primary, 10%);
        border: 1px solid $color-primary;
      }
    }
  }
}

// Responsivité pour les tablettes et petits écrans
@media (max-width: 800px) {
  .nav {
    gap: 1rem;
    text-align: center;

    .nav-left {
      gap: 1rem;
    }

    .login-btn {
      width: 100%;
      text-align: center;
    }

  }
}

// Responsivité pour les tablettes (entre 768px et 1024px)
@media (min-width: 800px) and (max-width: 1024px) {
  .nav {
    justify-content: space-between;
    text-align: left;

    .nav-left {
      display: flex;
      justify-content: space-between;
      gap: 3rem;

      .nav-links {
        display: flex;
        gap: 1.5rem;
      }
    }

    .login-btn {
      width: 150px;
    }
  }
}