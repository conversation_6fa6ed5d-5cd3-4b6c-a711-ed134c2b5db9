import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { MenuJdeRoutingModule } from './menu-jde-routing.module';
import { MenuJdeComponent } from './menu-jde.component';
import { NbButtonModule, NbCardModule, NbDialogModule, NbIconModule, NbInputModule, NbListModule, NbSelectModule, NbSpinnerModule } from '@nebular/theme';
import { FormsModule } from '@angular/forms';


@NgModule({
  declarations: [
    MenuJdeComponent
  ],
  imports: [
    CommonModule,
    FormsModule,
    MenuJdeRoutingModule,
    NbIconModule,
    NbCardModule,
    NbListModule,NbSpinnerModule,
    NbDialogModule,
    NbButtonModule,
    NbSelectModule,
    NbInputModule,

  ]
})
export class MenuJdeModule { }
