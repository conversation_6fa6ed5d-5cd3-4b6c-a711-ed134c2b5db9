import { CommonService } from './../../../shared/services/common.service';
import { Router } from '@angular/router';
import { CarrierService } from './../carriers.service';
import { Driver } from './../../../shared/models/drivers';
import { Truck } from 'src/app/shared/models/trucks';
import { Carrier } from 'src/app/shared/models/carrier';
import { NbDialogRef, NbDialogService, NbToastrService } from '@nebular/theme';
import { Component, OnInit, TemplateRef } from '@angular/core';
import { ImageCompressor } from 'image-compressor';
import * as moment from 'moment';
import { Location } from '@angular/common';


@Component({
  selector: 'clw-carrier-add',
  templateUrl: './carrier-add.component.html',
  styles: [
  ]
})
export class CarrierAddComponent implements OnInit {
  imgResultAfterCompress: string;
  imageSrc: string;
  file: any;
  filename: any;
  carrier: Carrier;
  flipped: boolean;
  trainDateEnd: any;
  trainDateExpired: any;
  isLoading: boolean
  trainDateStart: any;
  selectedTruck: Truck;
  selectedTraining: any;
  isNewTraining: boolean;
  selectedDriver: Driver;
  isEditTraining: boolean;
  dialogRef: NbDialogRef<any>;
  selectedTrainingIndex: any;

  trucks: Truck[];
  drivers: Driver[];

  trucksTypes = [
    { value: 1, label: 'PLATEAU' },
    { value: 2, label: 'BENNE' },
    { value: 3, label: 'VRAQUIER' },
  ];


  trucksCapacity = [
    { value: 5, label: '5 tonnes' },
    { value: 10, label: '10 tonnes' },
    { value: 15, label: '15 tonnes' },
    { value: 20, label: '20 tonnes' },
    { value: 25, label: '25 tonnes' },
    { value: 35, label: '35 tonnes' },
  ];

  carrierRef: any;


  constructor(
    private toastrSvr: NbToastrService,
    private dialogSrv: NbDialogService,
    private carrierSrv: CarrierService,
    private router: Router,
    private location: Location,
    private commonSrv: CommonService
  ) { }

  ngOnInit(): void {
    this.resetCarier();
  }

  getTrucksCapacity(value: any) {
    const typesName = { 5: '5 t', 10: '10 t', 15: '15 t', 20: '20 t', 25: '25 t' };
    return typesName[value];
  }

  getTrucksTypes(value: any) {
    const typesName = { 1: 'PLATEAU', 2: 'BENNE', 3: 'VRAQUIER' };
    return typesName[value];
  }

  getFile(fileDataSet: any): any {

    this.file = fileDataSet.target.files[0];
    const type = this.file.type;
    if (!type.includes('image')) {
      this.file = null;
      return this.toastrSvr.danger('Veuillez importer uniquement les images', 'Donnés incorrectes');

    }
    this.filename = this.file.name;

    const reader = new FileReader();
    if (fileDataSet.target.files && fileDataSet.target.files.length) {
      const [file] = fileDataSet.target.files;
      reader.readAsDataURL(file);

      reader.onload = () => {
        this.imageSrc = reader.result as string;
      };

    }
  }

  resetCarier() {
    this.carrier = {
      label: '',
      rccm: '',
      niu: '',
      njde: parseInt(''),
      tel: parseInt(''),
      email: '',
      logo: '',
      localisation: '',
    }

    this.trucks = [];
    this.drivers = [];

    this.carrierRef = moment().valueOf();

  }

  resetTruck() {
    this.selectedTruck = {
      immatriculation: '',
      desc: '',
      type: '',
      capacity: null,
      volume: null,
      enable: true,
    }
  }
  resetDriver() {
    this.selectedDriver = {
      cni: '',
      driverCode: '',
      tel1: '',
      tel2: '',
      driverLicense: '',
      licenseExpireDate : '',
      fname: '',
      lname: '',
      fullname: '',
      trainings: [],
      carrier: {}
    }
  }

  getImgStyle(imgUrl?: string): any {
    const image = imgUrl ? imgUrl : 'assets/images/Transport_Types.png';
    return {
      'background-image': `url('${image}')`,
      'background-repeat': 'no-repeat',
      'background-position': 'center',
      'background-size': 'contain',
    };
  }

  curveText(text: string, radius: number): any {
    text = text?.split(' ')[0];
    const textArray = this.truncateString(text, 14)?.split('') || [];

    const deg = 70 / textArray.length;
    let origin = 0;
    const newArray: any[] = [];
    textArray.forEach((e) => {
      newArray.push({ letter: e, style: `height: ${radius}px; position: absolute; transform: rotate(${origin}deg); transform-origin: 0 100 % ` });

      origin += deg;
    });
    return newArray;
  }

  truncateString(str: string, size: number): string {
    if (!str) { return ''; }
    return (str.length > size) ? `${str.substring(0, size - 6)}` : str;
  }


  compressFile(imageSrc: string, callback: any): any {
    const imageCompressor = new ImageCompressor;

    const compressorSettings = {
      toWidth: 150,
      toHeight: 150,
      mimeType: 'image/png',
      mode: 'strict',
      quality: 0.8,
      grayScale: false,
      sepia: false,
      threshold: false,
      vReverse: false,
      hReverse: false,
      speed: 'low'
    };

    imageCompressor.run(imageSrc, compressorSettings, callback);
  }

  openAddModal(dialog: TemplateRef<any>) {
    this.dialogRef = this.dialogSrv.open(dialog);
  }

  openAddTrucsModal(dialog: TemplateRef<any>) {
    this.resetTruck();
    this.openAddModal(dialog);
  }
  openAddDriverModal(dialog: TemplateRef<any>) {
    this.resetDriver();
    this.openAddModal(dialog);
  }


  addTruck(): any {
    try {
      this.isLoading = true
      if (!this.selectedTruck?.capacity || this.selectedTruck?.immatriculation === '' || this.selectedTruck?.type === '') {
        return this.toastrSvr.danger('remplissez tout les champs', 'Donnés incorrectes')
      }
      this.selectedTruck.code = Date.now();
      this.selectedTruck.carrierRef = this.carrierRef
      this.trucks.push(this.selectedTruck);
      this.dialogRef.close();
      this.toastrSvr.success('Ce camion a été bien ajouté', 'Camion ajouté')
    } catch (error) {
      this.toastrSvr.danger('Un problème est survnenu lors de l\'enregistrement de ce camion  ', 'Erreur')
    } finally {
      this.isLoading = false
    }

  }
  addDriver(): any {
    try {
      this.isLoading = true;
      const obj = { ...this.selectedDriver };
      delete obj.fullname;
      delete obj.tel2;
      this.commonSrv.verifyAllFieldsForm(obj);
      this.selectedDriver.fullname = `${this.selectedDriver?.lname} ${this.selectedDriver?.fname}`
      // this.selectedDriver.code = Date.now();
      this.selectedDriver.licenseExpireDate = moment(this.selectedDriver.licenseExpireDate).valueOf();
      this.drivers.push(this.selectedDriver);
      this.dialogRef.close();
      this.toastrSvr.success('Ce chauffeur a été bien ajouté', 'Chauffeur ajouté')
    } catch (error) {
      this.toastrSvr.danger('Un problème est survnenu lors de l\'enregistrement de ce chauffeur  ', error?.message);
    } finally {
      this.isLoading = false
    }

  }

  async addCarrier(): Promise<any> {
    try {
      this.isLoading = true
      if (this.carrier.label === '' || this.carrier.rccm === '' || this.carrier.niu === '' || this.carrier.njde === parseInt('') || this.carrier.email === '') {
        return this.toastrSvr.danger('remplissez tout les champs', 'Donnés incorrectes')
      }
      this.carrier.code = this.carrierRef;
      await this.carrierSrv.insertCarrier(this.carrier);

      if (this.trucks.length > 0) {
        await this.carrierSrv.insertTrucks(this.trucks);
      }
      if (this.drivers.length > 0) {
        await this.carrierSrv.insertDrivers(this.drivers);
      }
      this.toastrSvr.success('Ce transporteur a été bien ajouté', 'Transporteur ajouté');
      this.router.navigate(['admin/carrier']);
    } catch (error) {
      this.toastrSvr.danger('Un problème est survnenu lors de l\'enregistrement de ce transporteur  ', 'Erreur')
    } finally {
      this.isLoading = false
    }

  }

  addTraining() {
    this.isNewTraining = true;
    this.trainDateStart = '';
    this.trainDateEnd = '';
    this.trainDateExpired = '';
    this.selectedTraining = {
      trainingType: '',
      trainerName: '',
      date: {
        start: '',
        end: '',
        expiredDate: ''
      }
    }

    this.toggle(true);
  }

  editTraining(index: any) {
    this.isEditTraining = true;
    this.trainDateStart = new Date(this.selectedDriver?.trainings[index]?.date?.start);
    this.trainDateEnd = new Date(this.selectedDriver?.trainings[index]?.date?.end);
    this.trainDateExpired = new Date(this.selectedDriver?.trainings[index]?.date?.expiredDate);
    this.selectedTrainingIndex = index;
    this.toggle(true);
  }

  toggle(value?: boolean) {
    this.flipped = value != undefined ? value : !this.flipped;
    if (this.flipped === false) {
      this.isNewTraining = false;
      this.isEditTraining = false;
    }
  }

  pushTraining() {
    const date = this.verifyDates();
    if (date)
      return;
    this.selectedDriver?.trainings?.push(this.selectedTraining);
    this.toggle(false);
    this.isNewTraining = false;
  }

  pushEditTraining() {
    const date = this.verifyDates();
    if (date)
      return;
    this.selectedDriver.trainings[this.selectedTrainingIndex] = this.selectedTraining;
    this.toggle(false); 
    this.isNewTraining = false;
  }

  verifyDates() {
    const isInvalidDate = this.selectedTraining?.date?.start > this.selectedTraining?.date?.end ||
      this.selectedTraining?.date?.end > this.selectedTraining?.date?.expiredDate;

    if (isInvalidDate) {
      this.toastrSvr.danger(
        'Les dates entrez ne sont pas correctes. Veuillez verifier vos dates',
        'Erreur de Dates'
      );
    }

    return isInvalidDate;
  }

  updateTrainDateStart(): any {
    this.isNewTraining ? this.selectedTraining.date.start = moment(this.trainDateStart).valueOf() : this.selectedDriver.trainings[this.selectedTrainingIndex].date.start = moment(this.trainDateStart).valueOf();
  }
  updateTrainDateEnd() {
    this.isNewTraining ? this.selectedTraining.date.end = moment(this.trainDateEnd).valueOf() : this.selectedDriver.trainings[this.selectedTrainingIndex].date.end = moment(this.trainDateEnd).valueOf();
  }
  updateTrainDateExpired() {
    this.isNewTraining ? this.selectedTraining.date.expiredDate = moment(this.trainDateExpired).valueOf() : this.selectedDriver.trainings[this.selectedTrainingIndex].date.expiredDate = moment(this.trainDateExpired).valueOf();
  }

  cancel() {
    this.router.navigate(['admin/carrier']);

  }

  truncate(str: any, size: number) {
    return this.commonSrv.truncateString(str, size);
  }

  goBack() {
    if (window.history.length > 1) {
      this.location.back()
    } else {
      this.router.navigate(['/home'])
    }
  }




}
