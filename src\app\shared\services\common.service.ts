import { HttpClient, HttpParams } from '@angular/common/http';
import { environment } from 'src/environments/environment';
import { BaseUrlService } from './base-url.service';
import { Injectable } from '@angular/core';
import isEmpty from 'lodash-es/isEmpty';
import * as moment from 'moment';
import * as FileSaver from 'file-saver';
import * as XLSX from 'xlsx';
import { NbToastrService } from '@nebular/theme';
import { Truck } from '../models/trucks';
import { TruckInspection } from '../models/truck-inspection';
import { Order } from '../models/order';

@Injectable({
  providedIn: 'root'
})
export class CommonService {
  EXCEL_TYPE = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8';
  EXCEL_EXTENSION = '.xlsx';
  showAuthModal: boolean;
  ordersGroup: Order[] = [];

  breadcrumb: { label: string, link: string }[];
  links: any[] = [
    { level: 1, link: 'home', label: 'Accueil' },
    { level: 1, link: 'user', label: 'Accueil' },
    { level: 2, link: 'career', label: '<PERSON>ières' },
    { level: 2, link: 'training', label: 'Formations' },
    { level: 2, link: 'user-profile', label: 'Mon Compte' },
    { level: 3, link: 'courses', label: 'Formations liées à mon poste' },
    { level: 3, link: 'history', label: 'Historique' },
    { level: 3, link: 'idp', label: 'Indices de Développement Personnel' },

    { level: 1, link: 'admin', label: 'Accueil' },
    { level: 2, link: 'reporting', label: 'Reporting' },
    { level: 3, link: 'training', label: 'Formations' },
    { level: 3, link: 'career', label: 'Carrières' },

    { level: 2, link: 'training', label: 'Formations' },
    { level: 3, link: 'history', label: 'Historique' },
    { level: 3, link: 'training-plan', label: 'Plan de formation' },
    { level: 4, link: 'training-add', label: 'Nouvelle formation' },
    { level: 4, link: 'details-plan', label: 'Détail' },

    // menu data

    { level: 2, link: 'data', label: 'Administrations' },
    { level: 3, link: 'employee', label: 'Employés' },
    { level: 4, link: 'idp', label: `Indices de Développement Personnel` },

    { level: 3, link: 'entreprise', label: `Organisation entreprise` },
    { level: 4, link: 'direction', label: `Liste directions` },
    { level: 4, link: 'service', label: `Liste services` },
    { level: 4, link: 'positions', label: `Positions` },
    { level: 5, link: 'group-job', label: `Liste familles métier` },
    { level: 5, link: 'job', label: `Liste métiers` },
    { level: 5, link: 'position', label: `Liste postes` },
    { level: 5, link: 'position-form', label: `Création poste` },
    { level: 5, link: 'position-edit', label: `Edition poste` },
    { level: 5, link: 'employee-type', label: `Types employé` },
    { level: 5, link: 'employee-status', label: `Status employé` },

    { level: 3, link: 'training', label: 'Formations' },
    { level: 4, link: 'training', label: `Liste formations` },
    { level: 4, link: 'form', label: `Création formation` },
    { level: 4, link: 'type', label: `Types de formation` },
    { level: 4, link: 'category', label: `Catégories de formation` },
    { level: 4, link: 'provider', label: `Prestataires` },
    { level: 4, link: 'book', label: `Annuaire des formations` },
    { level: 4, link: 'kind', label: `Genres de formation` },
    { level: 4, link: 'edit', label: `Edition formation` },
    { level: 4, link: 'session', label: `Liste sessions` },
    { level: 4, link: 'session-form', label: `Création session` },
    { level: 4, link: 'session-edit', label: `Edition session` },
  ];

  BASE_URL: string;

  constructor(
    private http: HttpClient,
    private toastrSvr: NbToastrService,
    private baseUrlSrv: BaseUrlService,
  ) {
    this.BASE_URL = `${this.baseUrlSrv.getOrigin()}${environment.basePath}`;
  }

  truncateString(str: string, size: number): any {
    if (!str) { return; }
    return (str.length > size) ? `${str.substring(0, size - 5)}...` : str;
  }

  generateQueryParams(param: any): any {
    let params = new HttpParams();

    const { carrierLabel, carrierId, range, offset, limit, enabled } = param;

    params = params.append('offset', `${offset}`);
    params = params.append('limit', `${limit}`);

    if (enabled) { params = params.append('enabled', `${enabled}`); }

    if (carrierLabel) { params = params.append('label', `${carrierLabel}`); }

    if (carrierId) { params = params.append('carrierId', `${carrierId}`); }

    if (!isEmpty(range) && range.start && range.end) {
      params = params.append('start', moment(range.start).format('YYYY-MM-DD'));
      params = params.append('end', moment(range.end).format('YYYY-MM-DD'));
    }

    return params;
  }

  getRandomInt(min: number, max: number): any {
    min = Math.ceil(min);
    max = Math.floor(max);
    return Math.floor(Math.random() * (max - min)) + min;
  }

  // Common use service

  async getAllTrainingsCategory(param: any): Promise<any> {
    let params = new HttpParams();
    const { label, comment, _id } = param;
    if (label) { params = params.append('label', `${label}`); }
    if (comment) { params = params.append('comment', `${comment}`); }
    if (_id) { params = params.append('_id', `${_id}`); }
    return await this.http.get(`${this.BASE_URL}/training-categories-all`, { params }).toPromise();
  }

  async getPositionBy(param: any): Promise<any> {
    let params = new HttpParams();
    const { label, _id } = param;
    if (_id) { params = params.append('_id', `${_id}`); }
    if (label) { params = params.append('label', `${label}`); }
    return await this.http.get(`${this.BASE_URL}/position`, { params }).toPromise();
  }

  async getAllPositions(): Promise<any> {
    return await this.http.get(`${this.BASE_URL}/positions-all`, {}).toPromise();
  }

  async getAllDirections(): Promise<any> {
    return await this.http.get(`${this.BASE_URL}/directions-all`, {}).toPromise();
  }

  async getAllServices(): Promise<any> {
    return await this.http.get(`${this.BASE_URL}/services-all`, {}).toPromise();
  }

  async updateUser(data: any): Promise<any> {
    return await this.http.put(`${this.BASE_URL}/users/${data._id}`, data).toPromise();
  }

  verifyAllFieldsForm(object: Object): string | boolean | Error {
    for (const key in object) {
      if ([null, 'null', undefined, 'undefined', NaN, 'NaN', '', 0].includes(object[key])) {
        console.log('first level:', key, '=', object[key]);
        throw new Error('Veuillez renseigner tous les champs');
      }

      if (key.includes('tel') && !/^6[0-9]{8}$/.test(object[key])) {
        console.log(key + ': ' + object[key]);

        throw new Error('Renseignez le bon format du numéro de téléphone, ex: 6xXxXxXxX');
      }

      if (!/^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/.test(object[key]) && key === 'email')
        throw new Error('Veuillez entrer un email valide');


      if (typeof object[key] === 'object')
        this.verifyAllFieldsForm(object[key]);
      // const newObject = object[key];
      // for (const keyInObject in newObject) {
      //   if (!newObject[keyInObject]) {
      //     console.log('key in Object', keyInObject);
      //     return 'Veuillez renseigner tous les champs';
      //   }
      // }


    }

    return false;
  }

  sanitizePhoneNumber(phoneNumber: string): string {
    const sanitizedPhoneNumber = phoneNumber?.replace(/[`^a-zA-Z£µ§²ù!@#$%^&*()+=\[\]{};':"\\|,.<>\/?~]/g, '');
    return sanitizedPhoneNumber;
  }

  isValidPhoneNumber(phoneNumber: string): boolean {
    const isValidPhoneNumber = phoneNumber?.length === 9 && /^6[0-9]{8}$/.test(phoneNumber);
    return isValidPhoneNumber;
  }

  verifyTel(event: Event, currentInspections: TruckInspection): void {
    const inputElement = event?.target as HTMLInputElement;
    const phoneNumber = inputElement?.value || '';
    const sanitizedPhoneNumber = this.sanitizePhoneNumber(phoneNumber);

    if (!this.isValidPhoneNumber(sanitizedPhoneNumber)) {
      console.error(`Invalid phone number: ${sanitizedPhoneNumber}`);
      this.toastrSvr.danger('Renseignez le bon format du numéro de téléphone, ex: 6xXxXxXxX');
    }

    currentInspections.driverTel = sanitizedPhoneNumber;
  }

  verifyTonnage(event: any): void {
    const tonnage = event?.target?.value;
    if (tonnage < 0 || tonnage > 45) {
      console.error(`Invalid tonnage value: ${tonnage}`);
      this.toastrSvr.danger('La tonnage doit etre comprise entre 0 et 45');
      event.target.value = '';
    }

  }

  exportRetriveExcelFile(json: any[], excelFileName: string): void {
    const worksheet: XLSX.WorkSheet = XLSX.utils.json_to_sheet(json);
    worksheet['!cols'] = [{ wch: 30 }, { wch: 30 }, { wch: 15 }, { wch: 30 }, { wch: 15 }, { wch: 25 }, { wch: 15 }, { wch: 15 }];

    const workbook: XLSX.WorkBook = {
      Sheets: { data: worksheet },
      SheetNames: ['data']
    };

    const excelBuffer: any = XLSX.write(workbook, {
      bookType: 'xlsx',
      type: 'array'
    });

    this.saveAsExcelFile(excelBuffer, excelFileName);
  }

  getDurationDate(startDate: number) {
    if (!startDate) return '';

    const currentTime = Date.now();
    const diff = currentTime - startDate;
    const seconds = Math?.trunc(diff / 1000) % 60;
    const minutes = Math?.trunc(diff / (1000 * 60)) % 60;
    const hours = Math?.trunc(diff / (1000 * 60 * 60)) % 24;
    const days = Math?.trunc(diff / (1000 * 60 * 60 * 24));

    return `${days} jours(s) ${hours?.toString().padStart(2, '0')}:${minutes?.toString().padStart(2, '0')}:${seconds?.toString().padStart(2, '0')}`;
  }

  convertDurationToTimestamp(duration: string): number {
    const regex = /(\d+)\s+jours\(s\)\s+(\d{2}):(\d{2}):(\d{2})/;
    const match = duration.match(regex);

    if (!match) return 0;

    const days = parseInt(match[1], 10);
    const hours = parseInt(match[2], 10);
    const minutes = parseInt(match[3], 10);
    const seconds = parseInt(match[4], 10);

    const totalMilliseconds =
      (days * 24 * 60 * 60 + hours * 60 * 60 + minutes * 60 + seconds) * 1000;

    return Date.now() - totalMilliseconds;
  }

  saveAsExcelFile(buffer: any, fileName: string): void {
    const data: Blob = new Blob([buffer], {
      type: this.EXCEL_TYPE
    });
    FileSaver.saveAs(
      data, fileName + '_export_' + new Date().getDate() + ':' + new Date().getHours() + ':' + new Date().getMinutes()
      + ':' + new Date().getSeconds() + this.EXCEL_EXTENSION
    );
  }
  scrollToElement(elementClass: string): void {
    const element = document.querySelector(`.${elementClass}`);
    if (element) {
      const headerOffset = 60;
      const elementPosition = element.getBoundingClientRect().top + window.pageYOffset;
      const offsetPosition = elementPosition - headerOffset;
      window.scrollTo({
        top: offsetPosition,
        behavior: 'smooth'
      });
    }
  }

  getItemLabels(ae: any): string {
    return ae?.ItemNumber?.map((item: any) => `${item?.product?.label} (${item?.packaging?.label})`).join(', ');
  }

  convertWeight(value: number, unit: 'kg' | 'ton'): number {
    if (unit === 'kg') {
      return value * 1000; // Convert tons to kg
    } else if (unit === 'ton') {
      return value / 1000; // Convert kg to tons

    }
    throw new Error('Invalid unit. Use "kg" or "ton".');
  }

  formatWeight(value: string): string {
    if (!value) return 'N/A'; // Handle null/undefined case
    const kg = Number(value) * 1000; // Convert tons to kg
    return `${value}T (${kg} KG)`;
  }
}
