<div class="common-form-container menu-removal-container">
    <div class="header">
        <h1 class="title">Liste des Inspections matières</h1>
    </div>

    <div class="removal-container">
        <div class="filter-area">
            <div class="left-area">
                <div class="filter-label">Filtrer par</div>
                <div class="filters" *ngIf="activeFilter?.length == 0">
                    <input type="text" placeholder="Transporteur" fieldSize="small" nbInput class="empty-input height"
                        [nbAutocomplete]="autoComplete4" [(ngModel)]="filterForm.carrierLabel"
                        (ngModelChange)="onModelCarrierChange($event)" />
                    <nb-autocomplete #autoComplete4
                        (selectedChange)="onSelectionAEChange({$event,
                        keyToReset: $event == 'Tous les transporteurs'?  'company' :  '',  dataToReset: 'onModelCompanyChange'})">
                        <nb-option value="Tous les transporteurs"> Tous les transporteurs</nb-option>
                        <nb-option *ngFor="let option of dataCarrierLabels" [value]="option">{{option }}
                        </nb-option>
                        <nb-option *ngIf="loading"> Chargement... </nb-option>
                    </nb-autocomplete>
                    <input type="text" placeholder="Camion" fieldSize="small" nbInput class="empty-input height"
                        [nbAutocomplete]="autoComplete5" [(ngModel)]="filterForm.truck"
                        (ngModelChange)="onModelTruckImmatriculationChange($event)" />
                    <nb-autocomplete #autoComplete5
                        (selectedChange)="onSelectionAEChange({$event,
                         keyToReset: $event == 'Tous les Camion'?  'shipping' : '', dataToReset: 'onModelDeliveryPointChange'})">
                        <nb-option value="Tous les Camion"> Tous les Camions </nb-option>
                        <nb-option *ngFor="let option of dataTruckImmatriculation" [value]="option">{{option }}
                        </nb-option>
                        <nb-option *ngIf="loading && !dataTruckImmatriculation?.length"> Chargement... </nb-option>
                    </nb-autocomplete>
                    <input nbInput [nbDatepicker]="datePickerStart" [(ngModel)]="filterForm.startDate" status="success"
                        (ngModelChange)="onModelDateChange($event)" fieldSize="small" class="empty-input height"
                        placeholder="Début" />
                    <nb-datepicker #datePickerStart>
                    </nb-datepicker>
                    <input nbInput [nbDatepicker]="datepickerEnd" [(ngModel)]="filterForm.endDate" status="success"
                        (ngModelChange)="onModelDateChange($event)" fieldSize="small" class="empty-input height"
                        placeholder="Fin" />
                    <nb-datepicker #datepickerEnd></nb-datepicker>
                </div>
            </div>
            <div class="right-area">
                <button nbButton status="success" fieldSize="small" class="search-btn"
                    (click)='openExportInspection(exportDialog)'>
                    <nb-icon icon="download-outline"></nb-icon>
                    EXPORTER
                </button>
                <button nbButton status="basic" fieldSize="small" class="search-btn reset-btn" (click)="reset()">
                    <nb-icon icon="refresh-outline" [options]="{ animation: { type: 'zoom' } }"></nb-icon>
                    REINITIALISER
                </button>
            </div>
        </div>
        <nb-card [nbSpinner]="isLoading">
            <nb-list class="element-head">
                <nb-list-item class="list-elt-header paginator">
                    <div class="col col-paginator">
                        {{ startIndex }} - {{ endIndex }} sur {{ total }}
                        <nb-icon icon="arrow-ios-back-outline" [options]="{ animation: { type: 'zoom' } }"
                            nbTooltip="Page précédente" nbTooltipPlacement="bottom" nbTooltipStatus="default"
                            status="success" (click)="previousPage()">
                        </nb-icon>
                        <nb-icon icon="arrow-ios-forward-outline" [options]="{ animation: { type: 'zoom' } }"
                            nbTooltip="Page suivante" nbTooltipPlacement="bottom" nbTooltipStatus="default"
                            status="success" (click)="nextPage()">
                        </nb-icon>
                    </div>
                </nb-list-item>
                <nb-list-item class="list-elt-header paginator">
                    <div class="col col-num">N°</div>
                    <div class="col col-date">Date création</div>
                    <div class="col col-name">Transporteur</div>
                    <div class="col col-shipto">Camion</div>
                    <div class="col truck-quanity">Tonnage</div>
                    <div class="col col-name">Chauffeur</div>
                    <div class="col col-shipto">Contrôleur</div>
                    <div class="col col-status">Statut global</div>
                    <div class="col col-truck-status">Statut chargement</div>
                    <div class="col col-action"></div>

                </nb-list-item>
            </nb-list>
            <nb-list class="element-head scrool-style" nbSpinnerStatus="primary">
                <nb-list-item class="list-elt-header" *ngFor="let inspection of inspections, index as i">
                    <div class="col col-num">{{(i + startIndex)}}</div>
                    <div class="col col-date">{{inspection?.date |
                        date:"dd/MM/YYYY"}} {{inspection?.date || inspection?.date | date:"hh:mm"}}
                    </div>
                    <div class="col col-name">{{inspection?.carrier?.label || 'N/A' |truncateString :15}}</div>
                    <div class="col col-shipto">
                        {{inspection?.tractor?.label || 'N/A'}}
                    </div>
                    <div class="col truck-quanity">
                        {{inspection?.truckTonnage || 'N/A'}}
                    </div>
                    <div class="col col-name" title="{{ inspection?.driver || 'N/A'}} ">
                        {{inspection?.driver || 'N/A' |truncateString :15}}</div>
                    <div class="col col-shipto">
                        {{ inspection?.user?.fullname || 'N/A'}}
                    </div>
                    <div class="col col-status">
                        <nb-badge [text]="inspection?.enable == false ? '0' : inspection?.decision" class="badge"
                            position="top start"
                            [status]="inspection?.enable == false ? '0' : inspection?.decision | getColorStatusInspection"></nb-badge>
                    </div>
                    <div class="col col-truck-status">
                        <nb-badge
                            [text]="inspection?.enable == false ? '0' : inspection?.truckStatus | getStatusTrucksLabel"
                            class="badge" position="top start"
                            [status]="inspection?.enable == false ? '0' : inspection?.truckStatus | getColorStatusRemovals"></nb-badge>
                    </div>
                    <div class="col col-action">
                        <div class="action-icons">
                            <button nbTooltip="Detail" class="button-margin" nbTooltipPlacement="top" nbTooltipStatus
                                (click)='openDetailModal(detailDialog, inspection)'>
                                <nb-icon icon="file-text-outline" status="primary"
                                    [options]="{ animation: { type: 'zoom' } }">
                                </nb-icon>
                            </button>
                            <button nbTooltip="Modifier" class="button-margin" nbTooltipPlacement="top" nbTooltipStatus
                                (click)='openUpdateInspection(updateDialog, inspection)'>
                                <nb-icon icon="edit-2-outline" status="primary"
                                    [options]="{ animation: { type: 'zoom' } }">
                                </nb-icon>
                            </button>

                            <button nbTooltip="Voir image" class="button-margin" nbTooltipPlacement="top"
                                nbTooltipStatus (click)='openImageInspection(imageDialog, inspection)'>
                                <nb-icon icon="image-outline" status="primary"
                                    [options]="{ animation: { type: 'zoom' } }">
                                </nb-icon>
                            </button>
                        </div>
                    </div>
                </nb-list-item>
                <nb-list-item class="empty-list" *ngIf="!inspections?.length">
                    <img src="../../../../assets/images/empty-list.png" alt="liste vide">
                    Aucune Inspection trouvée
                </nb-list-item>
            </nb-list>
        </nb-card>
    </div>
</div>


<ng-template #detailDialog let-data let-ref="dialogRef" class="dialogRef">
    <div class="order-detail-container her-width">
        <nb-card>
            <nb-card-header class="form--header">Détail de l'inspection</nb-card-header>
            <nb-card-body>
                <div class="order-infor order-pick-up">
                    <table class="order-table">
                        <tr *ngFor="let check of inspection?.checkList; index as i ">
                            <td>{{check?.label?.slice(0,35)}}</td>
                            <div class="order-right">
                                <img [src]="imageGood" *ngIf="check?.state" class="icon">
                                <img [src]="imageBad" *ngIf="!check?.state" class="icon">
                            </div>
                        </tr>
                    </table>
                </div>
            </nb-card-body>
            <nb-card-footer class="form--footer">
                <button nbButton outline status="basic" class="" (click)="ref.close()">
                    <nb-icon icon="close-square-outline" [options]="{ animation: { type: 'zoom' } }"
                        class="remove-icon">
                    </nb-icon>
                    fermer
                </button>
            </nb-card-footer>
        </nb-card>
    </div>
</ng-template>
<ng-template #updateDialog let-data let-ref="dialogRef" class="dialogRef">
    <div class="order-detail-container her-width">
        <nb-card>
            <nb-card-header class="form--header">Modifier l'inspection</nb-card-header>
            <nb-card-body>
                <nb-tabset>
                    <nb-tab tabTitle="Informations Chauffeur">
                        <div class="form-container">
                            <div class="form-group">
                                <div class="attribute-block">
                                    <label class="text"> Transporteur</label>
                                </div>
                                <input type="text" placeholder="Transporteur" nbInput fullWidth
                                    class="empty-input height" [nbAutocomplete]="autoComplete4"
                                    [(ngModel)]="currentInspections.carrier.label"
                                    (ngModelChange)="onModelCarrierChange($event)" />
                                <nb-autocomplete #autoComplete4>
                                    <nb-option *ngFor="let option of dataCarrierLabels" [value]="option">{{option }}
                                    </nb-option>
                                </nb-autocomplete>
                            </div>
                            <div class="form-group">
                                <div class="attribute-block">
                                    <label class="text" for="nameInput">Nom du chauffeur</label>
                                </div>
                                <input nbInput class="carrier-name input-width" status="basic" type="text" fullWidth
                                    placeholder="Saisissez le nom du chauffeur" [(ngModel)]='currentInspections.driver'>
                            </div>
                            <div class="form-group">
                                <div class="attribute-block">
                                    <label class="text"> Numéro de téléphone</label>
                                </div>
                                <input id="typeInput" nbInput status="basic" type="tel"
                                    (keyup)="validatePhoneNumber($event)" [(ngModel)]="currentInspections.driverTel"
                                    fullWidth placeholder="Saisissez le numero de tel du chauffeur" />
                            </div>
                            <div class="form-group">
                                <div class="attribute-block">
                                    <label class="text">Immatriculation du véhicule</label>
                                </div>
                                <input id="typeInput" nbInput status="basic" type="text"
                                    [(ngModel)]="currentInspections?.tractor.label" fullWidth
                                    placeholder="Saisissez l'immatriculation du Véhicule" />
                            </div>
                            <div class="form-group">
                                <div class="attribute-block">
                                    <label class="text"> Date d'expiration du permis</label>
                                </div>
                                <input nbInput [nbDatepicker]="datePickerStart"
                                    [(ngModel)]="currentInspections.licenseExpirationDate" fullWidth
                                    class="empty-input height" placeholder="Début" />
                                <nb-datepicker #datePickerStart>
                                </nb-datepicker>
                            </div>
                            <div class="form-group">
                                <div class="attribute-block">
                                    <label class="text">Tonnage du camion</label>
                                </div>
                                <input id="typeInput" nbInput status="basic" type="number" [min]="0" max="45"
                                    (keyup)="commonService.verifyTonnage($event)" type="number"
                                    [(ngModel)]="currentInspections.truckTonnage" fullWidth
                                    placeholder="Saisissez le Tonnage du camion" />
                            </div>
                            <div class="form-group">
                                <div class="attribute-block">
                                    <label class="text"> Point de depart</label>
                                </div>
                                <input id="typeInput" nbInput status="basic" type="tel"
                                    [(ngModel)]="currentInspections.truckOrigin" fullWidth
                                    placeholder="Saisissez le point de depart du camion" />
                            </div>
                            <div class="form-group">
                                <div class="attribute-block">
                                    <label class="text"> Destination</label>
                                </div>
                                <input id="typeInput" nbInput status="basic" type="tel"
                                    [(ngModel)]="currentInspections.truckDestination" fullWidth
                                    placeholder="Saisissez la destination du camion" />
                            </div>
                            <div class="form-group">
                                <div class="attribute-block">
                                    <label class="text"> Ridelle du camion</label>
                                </div>
                                <nb-select class="carrier-name input-width"
                                    [placeholder]="currentInspections.statusJacks | getStatusTruck" fullWidth
                                    class="empty-input" [(selected)]='currentInspections.statusJacks'>
                                    <nb-option [value]='status?.value'
                                        *ngFor="let status of statusChoices">{{status?.label}}</nb-option>
                                </nb-select>
                            </div>
                            <div class="form-group">
                                <div class="attribute-block">
                                    <label class="text">Circuit hydraulique(Verins)</label>
                                </div>
                                <nb-select class="carrier-name input-width"
                                    [placeholder]="currentInspections.statusSideBoard | getStatusTruck" fullWidth
                                    class="empty-input" [(selected)]='currentInspections.statusSideBoard'>
                                    <nb-option [value]='status?.value'
                                        *ngFor="let status of statusChoices">{{status?.label}}</nb-option>
                                </nb-select>
                            </div>

                        </div>
                    </nb-tab>

                    <nb-tab tabTitle="Verifications">
                        <div class="order-infor order-pick-up">
                            <table class="order-table">
                                <tr>
                                    <td class="title">Statut global:</td>
                                    <td class="order-right">
                                        <nb-select class="carrier-name input-width" placeholder="Choissisez le statut"
                                            status='primary' size="small" class="empty-input"
                                            [(selected)]='currentInspections.decision'>
                                            <nb-option [value]='decision'
                                                *ngFor="let decision of InspectionDecisions">{{decision}}
                                            </nb-option>
                                        </nb-select>
                                    </td>
                                </tr>
                                <tr *ngFor="let check of currentInspections?.checkList; index as i ">
                                    <td>{{check.label.slice(0,35)}}</td>
                                    <nb-toggle class="order-right" [(ngModel)]="check.state"></nb-toggle>
                                </tr>
                            </table>
                        </div>
                    </nb-tab>
                </nb-tabset>
            </nb-card-body>
            <nb-card-footer class="form--footer">
                <button nbButton outline status="basic" class="" (click)="ref.close()">
                    <nb-icon icon="close-square-outline" [options]="{ animation: { type: 'zoom' } }"
                        class="remove-icon">
                    </nb-icon>
                    fermer
                </button>
                <button nbButton outline status="primary" class="" (click)="saveInspection();ref.close()">
                    <nb-icon icon="done-all-outline" [options]="{ animation: { type: 'zoom' } }" class="remove-icon">
                    </nb-icon>
                    Valider
                </button>
            </nb-card-footer>
        </nb-card>
    </div>
</ng-template>
<ng-template #imageDialog let-data let-ref="dialogRef" class="dialogRef">
    <div class="order-detail-container">
        <nb-card>
            <nb-card-header class="form--header">Détail des images de l'inspection</nb-card-header>
            <div class="gallery-container">

                <div class="displayed-img-container" *ngIf="selectedImage">
                    <img [src]="selectedImage" class="displayed-img">
                    <button nbButton status="danger" (click)="clearSelectedImage()">CLOSE</button>

                    <label *ngIf="images?.length < 0" for="">Aucune image associée</label>

                    <nb-icon icon="edit-2-outline" status="primary" class="edit" (click)="updateImage()"
                        [options]="{ animation: { type: 'zoom' } }"> </nb-icon>
                    <input type="file" id="file" style="display: none;" (change)='getFile($event)'>
                </div>

                <div class="thumb-bar">
                    <img *ngFor="let image of images let i = index" [src]="image" (click)="selectImage(image, i)"
                        class="thumb">
                </div>



            </div>


            <nb-card-footer class="form--footer">
                <button nbButton outline status="basic" class="" (click)="ref.close()">
                    <nb-icon icon="close-square-outline" [options]="{ animation: { type: 'zoom' } }"
                        class="remove-icon">
                    </nb-icon>
                    fermer
                </button>

                <button *ngIf="isImageUploaded == true" nbButton outline status="primary" class=""
                    (click)="saveInspection();ref.close()">
                    <nb-icon icon="done-all-outline" [options]="{ animation: { type: 'zoom' } }" class="remove-icon">
                    </nb-icon>
                    Valider
                </button>

            </nb-card-footer>
        </nb-card>
    </div>
</ng-template>
<ng-template #exportDialog let-data let-ref="dialogRef" class="dialogRef">
    <div class="order-detail-container-export">
        <nb-card>
            <nb-card-header class="form--header">Exporter l'inspection</nb-card-header>
            <div class="export-contenair">
                <div class="filters-export">

                    <input type="text" placeholder="Transporteur" fieldSize="small" nbInput class="empty-input height"
                        [nbAutocomplete]="autoComplete4" [(ngModel)]="filterForm.carrierLabel"
                        (ngModelChange)="onModelCarrierChange($event)" />
                    <nb-autocomplete #autoComplete4>
                        <nb-option value="Tous les transporteurs"> Tous les transporteurs</nb-option>
                        <nb-option *ngFor="let option of dataCarrierLabels" [value]="option">{{option }}
                        </nb-option>
                        <nb-option *ngIf="loading"> Chargement... </nb-option>
                    </nb-autocomplete>
                    <input type="text" placeholder="Camion" fieldSize="small" nbInput class="empty-input height"
                        [nbAutocomplete]="autoComplete5" [(ngModel)]="filterForm.truck"
                        (ngModelChange)="onModelTruckImmatriculationChange($event)" />
                    <nb-autocomplete #autoComplete5>
                        <nb-option value="Tous les Camion"> Tous les Camions </nb-option>
                        <nb-option *ngFor="let option of dataTruckImmatriculation" [value]="option">{{option }}
                        </nb-option>
                        <nb-option *ngIf="loading && !dataTruckImmatriculation?.length"> Chargement... </nb-option>
                    </nb-autocomplete>
                    <input nbInput [nbDatepicker]="datePickerStart" [(ngModel)]="filterForm.startDate" status="success"
                        (ngModelChange)="onModelDateChange($event)" fieldSize="small" class="empty-input height"
                        placeholder="Début" />
                    <nb-datepicker #datePickerStart>
                    </nb-datepicker>
                    <input nbInput [nbDatepicker]="datepickerEnd" [(ngModel)]="filterForm.endDate" status="success"
                        (ngModelChange)="onModelDateChange($event)" fieldSize="small" class="empty-input height"
                        placeholder="Fin" />
                    <nb-datepicker #datepickerEnd></nb-datepicker>
                    <div class="filter-label">Nombre d'éléments à exporter. max {{total}}</div>

                    <input type="number" placeholder="Entrer" fieldSize="small" class="empty-input height"
                        [(ngModel)]="limit" nbInput [value]="limit" />
                </div>
            </div>


            <nb-card-footer class="form--footer">
                <button nbButton outline status="basic" class="" (click)="ref.close(); activeFilter.length =0; ">
                    <nb-icon icon="close-square-outline" [options]="{ animation: { type: 'zoom' } }"
                        class="remove-icon">
                    </nb-icon>
                    fermer
                </button>

                <button nbButton outline status="primary" class="" (click)="exportToExcel();ref.close()">
                    <nb-icon icon="done-all-outline" [options]="{ animation: { type: 'zoom' } }" class="remove-icon">
                    </nb-icon>
                    Exporter
                </button>

            </nb-card-footer>
        </nb-card>
    </div>
</ng-template>