import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BaseUrlService } from 'src/app/shared/services/base-url.service';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root'
})
export class CategoryService {



  BASE_URL: string;
  currentCategory: any;

  constructor(
    private http: HttpClient,
    private baseUrlSrv: BaseUrlService,

  ) {
    this.BASE_URL = `${this.baseUrlSrv.getOrigin()}${environment.basePath}`;

  }

  async getCategories(): Promise<any> {

    return await this.http.get(`${this.BASE_URL}/category-products`).toPromise();

  }

  async deleteCategorys(): Promise<any> {
    return await this.http.delete(`${this.BASE_URL}/category-products/${this.currentCategory._id}`).toPromise();
  }

  async editCategories(category: any): Promise<any> {
    return await this.http.put(`${this.BASE_URL}/category-products/${this.currentCategory._id}`, category).toPromise();
  }

  async addCategories(category: any): Promise<any> {
    return await this.http.post(`${this.BASE_URL}/categoryProduct/`, category).toPromise();
  }


  generateQueryParams(param: any): any {
    let params = new HttpParams();

    const { offset, limit } = param;

    if (offset) {
      params = params.append('offset', `${offset}`);
    }
    if (limit) {
      params = params.append('limit', `${limit}`);
    }


    return params;
  }
}
