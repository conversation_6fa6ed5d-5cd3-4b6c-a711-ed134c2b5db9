export interface Truck {
  _id?: string;
  immatriculation?: string;
  desc?: string;
  code?: number;
  type?: string;
  capacity?: number;
  volume?: number;
  enable?: boolean;
  status?: number;
  carrierRef?: number;
  dateStartBrokenDown?: number;
  dateEndBrokenDown?: number;
  dock?: any;
}

export interface PaginationParams {
  offset?: number;
  limit?: number;
}
export interface CarrierParams extends PaginationParams {
  code?: string;
}

export interface TruckParams extends PaginationParams {
  code?: string | number;
  immatriculation?: string;
}

export interface TruckResponse {
  data: Truck[];
  count: number;
}



