<div class="tile-chart-stores-container">
  <nb-card accent="success" [nbSpinner]="isLoading" nbSpinnerStatus="primary" nbSpinnerMessage="Chargement des données">
    <nb-card-header class="card-header-wrapper">
      <div class="card-header">
        Ratio sur les statuts des Bons en pourcentage
      </div>
    </nb-card-header>
    <nb-card-body class="center-block">
      <div style="width: 50%; display: flex;">
        <canvas id="ratio-inspection" height="250px" width="250px">{{store}}</canvas>
      </div>
    </nb-card-body>
  </nb-card>
</div>