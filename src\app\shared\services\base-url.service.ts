import { Inject, Injectable } from '@angular/core';
import { DOCUMENT } from '@angular/common';
import { environment } from 'src/environments/environment';


@Injectable({
  providedIn: 'root'
})
export class BaseUrlService {
  private origin: string;

  constructor(@Inject(DOCUMENT) private document) {

    this.origin = (!environment.development)
      ? document.location.origin
      : environment.apiUrl;
    this.origin = environment.apiUrl;

  }

  getOrigin(): string {
    return this.origin;
  }
}