<div class="common-form-container delivery-orders">
    <div class="header">
        <nb-icon icon="undo-outline" (click)="goBack()" [options]="{ animation: { type: 'zoom' } }" class="remove-icon"
            status="primary">
        </nb-icon>
        <h1>Création d'AE</h1>
    </div>
    <clw-header-delivery (export)="onExport()" (filter)="onFilter($event)" (refresh)="onRefresh()">
    </clw-header-delivery>

    <div class="delivery-orders__content">

        <div class="cart-container" [nbSpinner]="isLoading">
            <div class="filter">
                <clw-status-filter 
                    [activeStatus]="currentStatus" 
                    [total]="paginationConfig.totalItems"
                    [paginationConfig]="paginationConfig"
                    (statusChange)="onStatusChange($event)"
                    (pageChange)="onPageChange($any($event))"
                    (itemsPerPageChange)="onItemsPerPageChange($any($event))">
                </clw-status-filter>
            </div>
            <div class="detail">
                <clw-order-cart [orders]="orders"></clw-order-cart>
                <div class="empty-list empty-list-partition" *ngIf="!orders?.length">
                    <img src="../../../../assets/images/empty-list.png" alt="liste vide">
                    <p> Aucune commande.</p>
                    <p></p>
                </div>
            </div>

        </div>

        <div class="recap-order" [nbSpinner]="isLoading">
            <clw-import-section [partitonOrders]="commonService.ordersGroup"></clw-import-section>
            <div class="footer">
                <button nbButton filled status="primary" class="split-btn"
                    [disabled]="!commonService.ordersGroup?.length || isLoading"
                    [ngClass]="{'disabled-btn': !commonService.ordersGroup?.length}" (click)="onCreateAE()">
                    Créer AE
                </button>
            </div>
        </div>


    </div>
</div>