<nb-layout *ngIf="deviceType === 'desktop'">

  <nb-layout-header fixed *ngIf="!isCallScreen && !isTruckScreen">
    <clw-new-header-home class="header-width" *ngIf="isHome"></clw-new-header-home>
    <clw-header-order class="header-width" *ngIf="!isHome"></clw-header-order>
  </nb-layout-header>

  <nb-sidebar responsive [(state)]="state" *ngIf="!isHome  && isUserConnected() && !isCallScreen && !isTruckScreen">
    <div class="sidebar-container"><clw-menu></clw-menu></div>



  </nb-sidebar>

  <nb-layout-column [style]="(isCallScreen || isTruckScreen)?'padding: 0px !important; background-color:#333333':''">

    <router-outlet></router-outlet>
  </nb-layout-column>

  <nb-layout-footer fixed>
    <!-- Insert footer here -->
  </nb-layout-footer>

</nb-layout>
<clw-mobile-blocked *ngIf="deviceType === 'mobile'"></clw-mobile-blocked>