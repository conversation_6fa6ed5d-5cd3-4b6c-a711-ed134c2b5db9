import {Injectable} from '@angular/core';
import {ActivatedRouteSnapshot, CanActivate, RouterStateSnapshot, UrlTree} from '@angular/router';
import {NbToastrService} from '@nebular/theme';
import {Observable} from 'rxjs';
import {StorageService} from '../services/storage.service';

@Injectable({providedIn: 'root'})
export class AdminGuard implements CanActivate {
    constructor(private storageSvr : StorageService, private toastSvr : NbToastrService) {}
    canActivate(): boolean {
        const user = this.storageSvr.getObject('user');
        let { rigths } = user;
        if (!rigths?.isAdmin) {
            this.toastSvr.danger(`vous n'avez pas les droits pour acceder a cette page`, 'Access refusé')
            return false;
        }

        // this.toastSvr.success('Vos identifications ont été vérifiés', 'Connexion réussi');
        return true;
    }

}
