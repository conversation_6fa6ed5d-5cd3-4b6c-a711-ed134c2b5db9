import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { MenuBrokenDownTrucksRoutingModule } from './menu-broken-down-trucks-routing.module';
import { MenuBrokenDownTrucksComponent } from './menu-broken-down-trucks.component';
import {
  NbAutocompleteModule, NbButtonModule, NbCardModule, NbSpinnerModule,
  NbDatepickerModule, NbIconModule, NbInputModule, NbTooltipModule, NbSelectModule,
  NbTabsetModule,
  NbBadgeModule,
  NbListModule
} from '@nebular/theme';
import { FormsModule } from '@angular/forms';
import { SharedModule } from 'src/app/shared/shared.module';


@NgModule({
  declarations: [
    MenuBrokenDownTrucksComponent
  ],
  imports: [
    CommonModule,
    MenuBrokenDownTrucksRoutingModule,
    NbCardModule,
    NbDatepickerModule,
    FormsModule,
    NbIconModule,
    NbListModule,
    NbBadgeModule,
    NbInputModule,
    NbButtonModule,
    NbSelectModule,
    NbTabsetModule,
    NbAutocompleteModule,
    NbSpinnerModule,
    NbTooltipModule,
    SharedModule,
  ]
})
export class MenuBrokenDownTrucksModule { }
