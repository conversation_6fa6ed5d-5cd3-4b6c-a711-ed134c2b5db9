// Styles pour les menus actifs
.side-menu-container {
  // Style pour les items de menu
  .menu-item {
    transition: all 0.3s ease;

    // Style pour le survol du menu
    a:hover {
      background-color: rgba(65, 156, 251, 0.05) !important;

      // Style pour l'icône au survol
      .menu-icon {
        color: $color-fourth !important;
      }

      // Style pour le texte au survol
      .menu-title {
        color: $color-fourth !important;
      }
    }
  }

  // Style spécifique pour les sous-menus
  .menu-item .menu-items .menu-item {
    // Style pour les sous-menus actifs
    a.active, &.selected > a {
      background-color: rgba(65, 156, 251, 0.15) !important;
      font-weight: bold !important;

      // Style pour l'icône du sous-menu actif
      .menu-icon {
        color: $color-fourth !important;
      }

      // Style pour le texte du sous-menu actif
      .menu-title {
        color: $color-fourth !important;
      }
    }
  }

  // Style pour les sous-sous-menus
  .menu-item .menu-items .menu-item .menu-items .menu-item {
    a.active, &.selected > a {
      background-color: rgba(65, 156, 251, 0.1) !important;
      border-left: 2px solid $color-fourth !important;
    }
  }

  // Style pour les menus avec sous-menus ouverts (juste une légère indication)
  .menu-item.expanded {
    > a {
      background-color: rgba(65, 156, 251, 0.05) !important;

      .menu-icon {
        color: rgba(65, 156, 251, 0.7) !important;
      }
    }
  }
}
