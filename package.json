{"name": "cadyst-logistique-web", "version": "1.1.16", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test", "dev:ssr": "ng run cimencam-logistique-web:serve-ssr", "serve:ssr": "node dist/cimencam-logistique-web/server/main.js", "build:ssr:staging": "ng build -c=staging && ts-node brotli_compress.ts && ng run cimencam-logistique-web:server", "build:ssr": "ng build -c=production && ts-node brotli_compress.ts && ng run cimencam-logistique-web:server", "prerender": "ng run cimencam-logistique-web:prerender", "build:stats": "ng build -c=production --stats-json", "analyze": "webpack-bundle-analyzer dist/cimencam-logistique-web/browser/stats.json"}, "private": true, "dependencies": {"@angular/animations": "^14.3.0", "@angular/cdk": "^14.2.7", "@angular/common": "^14.3.0", "@angular/compiler": "^14.3.0", "@angular/core": "^14.3.0", "@angular/forms": "^14.3.0", "@angular/material": "^14.2.7", "@angular/platform-browser": "^14.3.0", "@angular/platform-browser-dynamic": "^14.3.0", "@angular/platform-server": "^14.3.0", "@angular/router": "^14.3.0", "@googlemaps/js-api-loader": "^1.12.9", "@nebular/eva-icons": "10.0.0", "@nebular/theme": "^10.0.0", "@nguniversal/express-engine": "^14.2.3", "canvas": "^2.8.0", "chart.js": "^3.6.1", "crypto-js": "^4.1.1", "eva-icons": "^1.1.2", "express": "^4.15.2", "express-static-gzip": "^2.1.7", "file-saver": "^2.0.5", "helmet": "^5.0.1", "image-compressor": "^2.0.3", "localstorage-polyfill": "^1.0.1", "lodash-es": "^4.17.21", "moment": "^2.29.1", "moment-timezone": "^0.5.39", "ng2-pdfjs-viewer": "^14.0.0", "rxjs": "^7.8.2", "spdy": "^4.0.2", "tslib": "^2.3.0", "util": "^0.12.4", "xlsx": "^0.18.5", "zone.js": "~0.11.4"}, "devDependencies": {"@angular-devkit/build-angular": "^14.2.11", "@angular/cli": "^14.2.11", "@angular/compiler-cli": "^14.3.0", "@nguniversal/builders": "^14.2.3", "@schematics/angular": "^14.2.11", "@types/angular-fullscreen": "^1.0.33", "@types/express": "^4.17.0", "@types/file-saver": "^2.0.1", "@types/google.maps": "^3.47.0", "@types/jasmine": "~3.8.0", "@types/lodash-es": "^4.17.12", "@types/moment-timezone": "^0.5.30", "@types/node": "^16.11.7", "@types/spdy": "^3.4.5", "brotli": "^1.3.3", "jasmine-core": "~3.8.0", "karma": "~6.3.0", "karma-chrome-launcher": "~3.1.0", "karma-coverage": "~2.0.3", "karma-jasmine": "~4.0.0", "karma-jasmine-html-reporter": "~1.7.0", "node-gzip": "^1.1.2", "ts-node": "^10.9.1", "typescript": "^4.8.4", "webpack-bundle-analyzer": "^4.8.0"}}