import { CompanyCategory } from "../enums/Company-category.enum";
import { Address } from "../types";

export interface User {
    _id?: string;
    enable?: boolean;
    email?: string;
    lastName?: string;
    firstName?: string;
    matricule?: string;
    password?: string;
    passwordClear?: string;
    fname?: string;
    lname?: string;
    fullname?: string;
    rigths?: {
        // Super Administrateur - accès à tous les menus et sous-menus
        isFullRigth?: boolean;       // Super Administrateur (accès à tous les menus)

        // Menu Administration
        isAdmin?: boolean;           // Accès au menu Administration
        canManageUsers?: boolean;    // Accès au sous-menu Utilisateurs
        canManageStores?: boolean;   // Accès au sous-menu Points d'Enlèvements
        canManageCarriers?: boolean; // Accès au sous-menu Transporteurs
        canManageInspections?: boolean; // Accès au sous-menu Inspection
        canManageOrders?: boolean;   // Accès au sous-menu Gestion des Commandes

        // Menu Gestion Enlèvements
        canManageRemovals?: boolean;  // Accès au menu Gestion Enlèvements
        canViewHistory?: boolean;    // Accès au sous-menu Historique Bon
        canAllocate?: boolean;       // Accès au sous-menu Attribution Bon
        canViewTransports?: boolean; // Accès au sous-menu Transport Validée
        canViewLoadedTrucks?: boolean; // Accès au sous-menu Camions Chargés
        canViewQueue?: boolean;      // Accès au sous-menu File d'Attente
        canRemove?: boolean;         // Accès au sous-menu Créer un Enlèvement
        canAllocateDock?: boolean;   // Accès au sous-menu Attribution Quai
        canViewBrokenTrucks?: boolean; // Accès au sous-menu Camion en Panne
        canViewInspections?: boolean; // Accès au sous-menu Historique Inspection

        // Menu Reporting
        canViewReporting?: boolean;  // Accès au menu Reporting
        canViewRenderReporting?: boolean; // Accès au sous-menu Bons Rendus
        canViewPickupReporting?: boolean; // Accès au sous-menu Bons Pickup

        // Menu Écran d'appel
        canFollow?: boolean;         // Accès au menu Écran d'appel
        canViewParking?: boolean;    // Accès au sous-menu Parking
        canViewFactoryLoading?: boolean; // Accès au sous-menu Chargement Usine

        // Autres autorisations
        canSaveTruck?: boolean;      // Peut enregistrer les camions
        canManageRemovalsAuth?: boolean; // Peut gérer les autorisations d'enlèvement
        isInspector?: boolean;       // Rôle d'inspecteur
        canOrder?: boolean;          // Peut passer les commandes
        allStore?: boolean;          // Accès à tous les magasins/usines
    }
    tel?: string;
    pwdReseted?: boolean;
    createAt?: number;
    img?: string;
    role?: string;
}

export enum CategoryCode {
    ADMIN = 300,
    USER = 100
}

export class Company {
    _id?: string;
    address?: Address;
    name?: string;
    defaultStore?: string;
    afrilandKey?: string;
    nui?: string;
    rccm?: string;
    tel?: number;
    erpShipToDesc?: string;
    erpSoldToId?: string;
    erpShipToId?: number;
    precompteRate?: PrecompteRate;
    category?: CompanyCategory;
    q1Enabled?: boolean;
    annualOrderEnabled?: boolean;
    users?: any[];
    associatedCommercial?: Partial<User>;
    isLoyaltyProgDistributor?: boolean;
    loyaltyProgDistributor?: {
        city: string;
        nbResellers: number;
    };
    points?: number;
    enable?: boolean;
    created_at?: number;
    updateAt?: number;
    logo?: string;
    options?: string[] | any[];
    associatedShippingOption?: {
        optionId: string;
        shippingAddressId: string[];
    };

    constructor() {
        this.name = '';
        this.defaultStore = '';
        this.afrilandKey = '';
        this.nui = '';
        this.rccm = '';
        this.category = CompanyCategory.Baker;
        this.erpShipToDesc = '';
        this.erpSoldToId = '';
        this.erpShipToId = 0;
        this.precompteRate = PrecompteRate.Real;
        this.q1Enabled = false;
        this.annualOrderEnabled = false;
        this.users = [];
        this.options = [];
        this.address = {
            region: '',
            commercialRegion: '',
            city: '',
            district: '',
        };
        this.associatedShippingOption = {
            optionId: '',
            shippingAddressId: []
        }
    }
}

export enum PrecompteRate {
    Simple = 0.02,
    Real = 0.05,
    withholding = 0.1
}

export interface UserResponse {
    data: User[];
    count: number;
}

export interface UserParams {
    userLname?: string;
    userDepartment?: string;
    userId?: string;
    range?: {
        start: Date;
        end: Date;
    };
    offset?: number;
    limit?: number;
    enable?: boolean;
    name?: string;
}