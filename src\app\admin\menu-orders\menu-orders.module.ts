import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MenuOrdersRoutingModule } from '../menu-orders/menu-orders-routing.module';
import { SharedModule } from '../../shared/shared.module';
import { NbCardModule, NbIconModule, NbButtonModule, NbTableModule, NbBadgeModule, NbDatepickerModule, NbDialogModule, NbInputModule, NbListModule, NbSelectModule, NbSpinnerModule, NbTooltipModule, NbAccordionModule } from '@nebular/theme';
import { MenuOrdersComponent, OrderStatusPipe } from '../menu-orders/menu-orders.component';
import { FormsModule } from '@angular/forms';


@NgModule({
    declarations: [
        MenuOrdersComponent,
      OrderStatusPipe
  
    ],
    imports: [
      CommonModule,
      MenuOrdersRoutingModule,
      SharedModule,
      NbIconModule,
      NbInputModule,
      NbCardModule,
      NbListModule,
      NbBadgeModule,
      NbTooltipModule,
      NbDialogModule.forChild(),
      NbButtonModule,
      NbSpinnerModule,
      NbButtonModule,
      NbSelectModule,
      FormsModule,
      NbDatepickerModule,
      NbAccordionModule
    ]
  })
export class MenuOrdersModule { } 