import { MenuReportingService } from '../../menu-reporting/menu-reporting.service';
import { RenderTypeValue } from './../../../shared/models/render-type.enum';
import { Component, OnInit } from '@angular/core';

@Component({
  selector: 'clw-tile-aes-render',
  templateUrl: './tile-aes-render.component.html',
  styles: [
  ]
})
export class TileAesRenderComponent implements OnInit {

  isLoading: boolean;
  data: any;

  constructor(
    private reportingSrv: MenuReportingService
  ) { }

  async ngOnInit() {
    this.refresh();
  }

  async refresh() {
    this.isLoading = true;
    try {
      const query = {
        FreightHandlingCode: RenderTypeValue.RENDER
      }
      const res = await this.reportingSrv.getStatusAEs(query);
      this.data = this.normalizeLabels(res);
      this.isLoading = false;
    } catch (error) {
      console.log(error);
      this.isLoading = false;
    }
  }

  normalizeLabels(data): any {
    const normalizedData = {};
  
    Object.keys(data).forEach(key => {
      const normalizedKey = key
        .normalize("NFD") // Normaliser l'Unicode pour séparer les accents des lettres
        .replace(/[\u0300-\u036f]/g, '') // Supprimer les accents
        .replace(/ /g, ''); // Supprimer les espaces
  
      normalizedData[normalizedKey] = data[key];
    });
  
    return normalizedData;
  }


}
