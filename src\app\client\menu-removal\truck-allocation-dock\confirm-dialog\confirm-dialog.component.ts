import { Component, OnInit, ViewEncapsulation } from '@angular/core';
import { NbDialogRef } from '@nebular/theme';
import { RemovalService } from '../../removal-management/removal.service';

@Component({
  selector: 'clw-confirm-dialog',
  templateUrl: './confirm-dialog.component.html',
  styles: [
  ],
  encapsulation: ViewEncapsulation.None
})
export class ConfirmDialogComponent implements OnInit {
  isLoading: boolean;
  selectedTruck: any;

  constructor(
    private dialogRef: NbDialogRef<ConfirmDialogComponent>,
    private removalSrv: RemovalService
  ) { }

  ngOnInit(): void {
    this.selectedTruck = this.removalSrv.selectedTruck;
  }

  close(close: boolean) { this.dialogRef.close(close) }

}
