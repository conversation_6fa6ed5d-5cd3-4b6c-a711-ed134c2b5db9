import { CommonService } from 'src/app/shared/services/common.service';
import { CategoryProduct } from './../../../shared/models/category-product';
import { ProductsService } from './../../menu-products/products/products.service';
import { StoreService } from './../../../shared/services/store.service';
import { Packaging } from './../../../shared/models/packaging';
import { Product } from './../../../shared/models/product';
import { Store } from './../../../shared/models/store';
import { NbToastrService, NbDialogRef, NbDialogService } from '@nebular/theme';
import { Price } from './../../../shared/models/price';
import { Component, OnInit } from '@angular/core';
import { PricesService } from './prices.service';
import { Router } from '@angular/router';
import { Location } from '@angular/common';

@Component({
  selector: 'clw-prices',
  templateUrl: './prices.component.html',
  styles: [
  ]
})
export class PricesComponent implements OnInit {
  prices: Price[];
  stores: Store[];
  products: Product[];
  packagings: Packaging[];
  categories: CategoryProduct[];

  isLoading: boolean;
  endIndex = 20;
  startIndex = 1;
  limit = 20;
  total!: number;
  offset ;
  dialogRef: NbDialogRef<any>;
  selectedPrice: Price;
  selectedStore: Store;
  selectedProduct: Product;
  selectedPackaging: Packaging;
  selectedCategory: CategoryProduct;
  amount: any;

  refStore: any;
  refProduct: any;
  refPackaging: any;
  refCategory: any;


  constructor(
    private priceSrv: PricesService,
    private toastSrv: NbToastrService,
    private dialogSvr: NbDialogService,
    private storeSrv: StoreService,
    private productSrv: ProductsService,
    private commonSrv: CommonService,
    private router: Router,
    private location: Location

  ) { }

  async ngOnInit(): Promise<void> {
    this.isLoading = true;
    try {
      await this.getAllPrices();
      await this.getAllFeatures();
    } catch (error) {
      this.toastSrv.danger('Impossible de charger cette page', 'Erreur de connexion');
      console.log(error);
    } finally {
      this.isLoading = false;
    }
  }



  async getAllFeatures(): Promise<void> {
    try {
      this.isLoading = true;
      const response = await this.storeSrv.getStores({});
      this.stores = response.data;
      this.products = await this.productSrv.getProducts({});
      const response2 = await this.storeSrv.getProductTypes();
      this.categories = response2.data;
      const response3 = await this.storeSrv.getPackagings();
      this.packagings = response3.data;
    } catch (error) {
      console.log(error);
      this.toastSrv.danger('Nous n\'avons pas pu recupérer la liste de tout les caractéristiques', 'Erreur de connexion');
    }
  }
  async getAllPrices(): Promise<void> {
    const options = {
      limit: this.limit, offset: this.offset, storeRef: this.refStore, productRef: this.refProduct, categoryCode:this.refCategory, packagingCode: this.refPackaging
    }
    try {
      this.isLoading = true;
      const { data, total } = await this.priceSrv.getPrices(options);
      this.prices = data;
      this.total = total;
      this.endIndex = (this.offset * this.limit < total) ? this.offset * this.limit : total;
      this.startIndex = (total === 0) ? total : this.startIndex || 1;
    } catch (error) {
      this.toastSrv.danger('Impossile de récupérer la liste des prix', 'Erreur connexion !');
    } finally { this.isLoading = false; }
  }

  async nextPage(): Promise<any> {
    if (this.endIndex >= this.total) {
      this.endIndex = this.total;
      return;
    }
    this.offset += 1;
    this.startIndex = (this.offset - 1) * this.limit + 1;
    this.endIndex = this.offset * this.limit;
    if (this.startIndex <= 0) { this.startIndex = 1; }
    return await this.getAllPrices();
  }
  async search(){await this.getAllPrices()}

  async previousPage(): Promise<any> {
    if (this.startIndex === 0 || this.startIndex === 1) { return; }
    this.offset -= 1;
    if (this.offset <= 0) { this.offset ; }
    this.startIndex = (this.offset - 1) * this.limit;
    if (this.startIndex === 0) { this.startIndex = 1; }
    this.endIndex = this.offset * this.limit;
    return await this.getAllPrices();
  }

  openModal(dailog: any): void {
    this.dialogRef = this.dialogSvr.open(dailog, { closeOnBackdropClick: false, autoFocus: true });
    this.dialogRef.onClose.subscribe(() => {
      this.resetFields();

    })
  }

  openDeleteModal(dialog: any, price: Price) {
    this.selectedPrice = { ...price };
    this.openModal(dialog);

  }

  openAddModal(dialog: any) {
    this.openModal(dialog);
  }

  openEditModal(dialog: any, price: Price) {
    this.selectedPrice = { ...price };
    this.selectedCategory = this.categories.find((elt) => {
      return elt.code == this.selectedPrice.categoryCode;
    });
    this.selectedPackaging = this.packagings.find((elt) => {
      return elt.code == this.selectedPrice.packagingCode;
    });
    this.selectedProduct = this.products.find((elt) => {
      return elt.erpRef == this.selectedPrice.productRef;
    });
    this.selectedStore = this.stores.find((elt) => {
      return elt.jdeReference == this.selectedPrice.storeRef;
    });
    this.amount = this.selectedPrice.amount;
    this.priceSrv.currentPrice = this.selectedPrice;
    this.openModal(dialog);
  }

  openDetailModal(dialog: any, price: Price) {
    this.selectedPrice = { ...price };
    this.openModal(dialog);
  }
  filterStore(store: Store) {
    this.refStore = store?.jdeReference;
  }
  filterProduct(product: Product) {
    this.refProduct = product?.erpRef;
  }
  filterCategory(category: CategoryProduct) {
    this.refCategory = category?.code;
  }
  filterPackaging(packaging: Packaging) {
    this.refPackaging = packaging?.code;
  }



  async deletePrice() {
    this.isLoading = true;
    try {
      this.priceSrv.currentPrice = this.selectedPrice;
      await this.priceSrv.deletePrices();
      this.getAllPrices();
      this.dialogRef.close();
      this.toastSrv.success('Nous avons supprimé ce Prix avec succès', 'Suppression Réussi');
    } catch (error) {
      this.toastSrv.danger('Nous n\'avons pas pu supprimer ce prix', 'Erreur de suppression');
    } finally {
      this.isLoading = false
    }
  }

  async addPrice(): Promise<any> {
    this.isLoading = true;
    try {
      if (!this.selectedStore || !this.selectedCategory || !this.selectedPackaging || !this.selectedProduct) {
        return this.toastSrv.danger('Veuillez renseigner tout les champs', 'Données incorrectes');
      }
      if (!this.amount || this.amount == '' || parseInt(this.amount) == 0 || parseInt(this.amount) < 0) {
        return this.toastSrv.danger('Veuillez renseigner un montant correct', 'Données incorrectes');
      }

      if (this.isPriceExist()) {
        return this.toastSrv.danger('Il existe déjà un prix ayant les mêmes caractéristiques que celui que vous avez renseigner', 'Données incorrectes');
      }

      this.selectedPrice = {
        amount: this.amount,
        storeRef: this.selectedStore.jdeReference,
        store: {
          _id: this.selectedStore._id.toString(),
          label: this.selectedStore.label,
        },
        productRef: this.selectedProduct.erpRef,
        product: {
          _id: this.selectedProduct._id.toString(),
          label: this.selectedProduct.label,
          normLabel: this.selectedProduct.normLabel,
          img: this.selectedProduct.img,
        },
        packagingCode: this.selectedPackaging.code,
        packaging: {
          _id: this.selectedPackaging._id.toString(),
          label: this.selectedPackaging.label,
          unit: this.selectedPackaging.unit,
        },
        categoryCode: this.selectedCategory.code,
        productCategory: {
          _id: this.selectedCategory._id.toString(),
          label: this.selectedCategory.label,
        }
      }
      await this.priceSrv.addPrices(this.selectedPrice);
      await this.getAllPrices();
      this.dialogRef.close();
      this.toastSrv.success('Le prix a été ajouté avec succès', 'Prix Ajouté');
    } catch (error) {
      this.toastSrv.danger('Nous n\'avons pas pu ajouter ce prix', 'Erreur de connexion');
    } finally {
      this.isLoading = false;
    }
  }
  async editPrice(): Promise<any> {
    this.isLoading = true;
    try {
      if (!this.selectedStore || !this.selectedCategory || !this.selectedPackaging || !this.selectedProduct) {
        return this.toastSrv.danger('Veuillez renseigner tout les champs', 'Données incorrectes');
      }
      if (!this.amount || this.amount == '' || parseInt(this.amount) == 0 || parseInt(this.amount) < 0) {
        return this.toastSrv.danger('Veuillez renseigner un montant correct', 'Données incorrectes');
      }

      if (this.isPriceExist()) {
        return this.toastSrv.danger('Il existe déjà un prix ayant les mêmes caractéristiques que celui que vous avez renseigner', 'Données incorrectes');
      }

      this.selectedPrice = {
        amount: this.amount,
        storeRef: this.selectedStore.jdeReference,
        store: {
          _id: this.selectedStore._id.toString(),
          label: this.selectedStore.label,
        },
        productRef: this.selectedProduct.erpRef,
        product: {
          _id: this.selectedProduct._id.toString(),
          label: this.selectedProduct.label,
          normLabel: this.selectedProduct.normLabel,
          img: this.selectedProduct.img,
        },
        packagingCode: this.selectedPackaging.code,
        packaging: {
          _id: this.selectedPackaging._id.toString(),
          label: this.selectedPackaging.label,
          unit: this.selectedPackaging.unit,
        },
        categoryCode: this.selectedCategory.code,
        productCategory: {
          _id: this.selectedCategory._id.toString(),
          label: this.selectedCategory.label,
        }
      }
      await this.priceSrv.editPrices(this.selectedPrice);
      await this.getAllPrices();
      this.dialogRef.close();
      this.toastSrv.success('Prix modifié avec succès', 'Prix modifié');
    } catch (error) {
      this.toastSrv.danger('Nous n\'avons pas pu modifier ce prix', 'Erreur de connexion');
    } finally {
      this.isLoading = false;
    }
  }

  resetFields() {
    this.selectedPackaging = null;
    this.selectedCategory = null;
    this.selectedPrice = null;
    this.selectedStore = null;
    this.selectedProduct = null;
    this.amount = '';
  }

  isPriceExist() {
    for (const element of this.prices) {
      if (this.selectedProduct.erpRef === element.productRef && this.selectedCategory.code === element.categoryCode && this.selectedPackaging.code === element.packagingCode && this.selectedStore.jdeReference === element.storeRef) {
        return true;
      }
    }
    return false
  }

  goBack() {
    if (window.history.length > 1) {
      this.location.back()
    } else {
      this.router.navigate(['/home'])
    }
  }
}
