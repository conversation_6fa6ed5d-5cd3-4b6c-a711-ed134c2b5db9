.pagination-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    // padding: 0.5rem;
    background-color: transparent;
    // margin: 0.5rem 0;

    .pagination-info {
        color: #2d3e50;
        font-size: 13px;
        font-weight: 500;
    }

    .pagination-controls {
        display: flex;
        gap: 0.5rem;
        align-items: center;

        nb-icon {
            cursor: pointer;
            font-size: 1.2rem;
            transition: all 0.3s ease;

            &:hover:not([disabled]) {
                color: #3366ff;
            }

            &[disabled] {
                cursor: not-allowed;
                opacity: 0.5;
            }
        }
    }

    .items-per-page {
        select {
            padding: 0.5rem;
            border-radius: 4px;
            border: 1px solid #dee2e6;
            background-color: white;
            cursor: pointer;
            transition: all 0.3s ease;

            &:hover {
                border-color: #007bff;
            }
        }
    }
}
