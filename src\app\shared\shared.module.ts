import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { HeaderHomeComponent } from './components/header-home/header-home.component';
import { NbButtonModule, NbContextMenuModule, NbIconModule, NbMenuModule, NbSelectModule, NbSidebarModule, NbSpinnerModule, NbToastrModule, NbTooltipModule } from '@nebular/theme';
import { NbEvaIconsModule } from '@nebular/eva-icons';
import { HeaderOrderComponent } from './components/header-order/header-order.component';
import { MenuComponent } from './components/menu/menu.component';
import { TruncateStringPipe } from './pipes/truncate-string.pipe';
import { CurveTextPipe } from './pipes/curve-text.pipe';
import { GetImgStylePipe } from './pipes/get-img-style.pipe';
import { GetFreightHandlingCodePipe, GetStatusRemovalsPipe, GetStatusTruckPipe, GetStoreLabelPipe } from './pipes/get-status-removals.pipe';
import { GetColorStatusInspectionsPipe, GetColorStatusRemovalsPipe } from './pipes/get-color-status-removals.pipe';
import { GetNbMinRemovalsPipe, GetStatusLabelRemovalsPipe, GetStatusTruckInspectionsPipe } from './pipes/get-status-label-removals.pipe';
import { GetDockPipe } from './pipes/get-dock.pipe';
import { ColorStyleWaitingPipe } from './pipes/color-style-waiting.pipe';
import { FilterDockCodePipe } from './pipes/get-filtered-dock-code.pipe';
import { AuthModalComponent } from './components/auth-modal/auth-modal.component';
import { FormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { NewHeaderHomeComponent } from './components/new-header-home/new-header-home.component';
import { PaginationComponent } from './components/pagination/pagination.component';
import { PaginationService } from './services/pagination.service';

@NgModule({
  declarations: [
    TruncateStringPipe,
    CurveTextPipe,
    GetImgStylePipe,
    FilterDockCodePipe,
    GetStatusRemovalsPipe,
    GetColorStatusRemovalsPipe,
    GetStatusLabelRemovalsPipe,
    GetNbMinRemovalsPipe,
    GetFreightHandlingCodePipe,
    GetDockPipe,
    GetStatusTruckPipe,
    GetStatusTruckInspectionsPipe,
    GetStoreLabelPipe,
    ColorStyleWaitingPipe,
    GetColorStatusInspectionsPipe,
    AuthModalComponent,
    MenuComponent,
    HeaderHomeComponent,
    HeaderOrderComponent,
    AuthModalComponent,
    NewHeaderHomeComponent,
    PaginationComponent,
  ],
  exports: [
    GetDockPipe,
    CurveTextPipe,
    MenuComponent,
    GetImgStylePipe,
    GetStoreLabelPipe,
    FilterDockCodePipe,
    TruncateStringPipe,
    HeaderHomeComponent,
    HeaderOrderComponent,
    GetStatusTruckPipe,
    GetNbMinRemovalsPipe,
    GetStatusRemovalsPipe,
    ColorStyleWaitingPipe,
    GetFreightHandlingCodePipe,
    GetColorStatusRemovalsPipe,
    GetStatusLabelRemovalsPipe,
    GetStatusTruckInspectionsPipe,
    GetColorStatusInspectionsPipe,
    AuthModalComponent,
    RouterModule,
    NewHeaderHomeComponent,
    PaginationComponent,
  ],
  imports: [
    CommonModule,
    NbIconModule,
    NbMenuModule,
    NbSelectModule,
    NbToastrModule,
    NbButtonModule,
    NbButtonModule,
    NbSidebarModule,
    NbTooltipModule,
    NbEvaIconsModule,
    NbContextMenuModule,
    FormsModule,
    RouterModule,
    NbSpinnerModule
  ],
  providers: [
    PaginationService
  ]
})
export class SharedModule { }
