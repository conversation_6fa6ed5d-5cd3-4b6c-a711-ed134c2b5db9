<div class="tile-chart-container">
  <nb-card accent="success" nbSpinnerStatus="primary" [nbSpinner]="isLoading" nbSpinnerMessage="Chargement des données">
    <nb-card-header class="card-header-wrapper">
      <div class="card-header">
        Répartition des sorties par produit
      </div>
    </nb-card-header>
    <nb-card-body>
      <div class="chart-contain">
        <canvas id="canvas-product" height="250px" width="450px">{{product}}</canvas>
      </div>
    </nb-card-body>
  </nb-card>
</div>
