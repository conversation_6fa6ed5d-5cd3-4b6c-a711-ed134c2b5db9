import { Component, OnInit, ViewEncapsulation } from '@angular/core';
import { StatusRemovals } from 'src/app/shared/models/status-removal.enum';
import { MenuReportingService } from '../../menu-reporting/menu-reporting.service';
import { RenderTypeValue } from 'src/app/shared/models/render-type.enum';

@Component({
  selector: 'clw-tile-inspection',
  templateUrl: './tile-inspection.component.html',
  styles: [
  ],
  encapsulation: ViewEncapsulation.None
})
export class TileInspectionComponent implements OnInit {
  isLoading: boolean;
  data: any;

  constructor(
    private reportingSrv: MenuReportingService
  ) { }

  async ngOnInit() {
    this.isLoading = true;
    await this.refresh();
  }

  getStatusLabel(status: any) {
    if (status === StatusRemovals.NOT_ASSIGNED) { return 'BON non assigné' }
    if (status === StatusRemovals.ASSIGNED) { return 'BON assigné' }
    if (status === StatusRemovals.WAITING_VALIDATION) { return 'BON en Attente de validation' }
    if (status === StatusRemovals.QUEUED) { return 'BON en file d attente' }
    if (status === StatusRemovals.LOADED) { return 'BON sur le quai' }
    if (status === StatusRemovals.BROKEN_DOWN) { return 'BON dans le nouveau parking' }
    if (status === StatusRemovals.NEW_PARKING) { return 'BON Bloqué' }
    return 'BON VIDE';
  }

  async refresh() {
    this.isLoading = true;
    try {
      const query = {
        FreightHandlingCode: RenderTypeValue.RENDER
      }

      this.data = await this.reportingSrv.getNumberStatusAEs(query);
      this.isLoading = false;
    } catch (error) {
      console.log(error);
    } finally {
      this.isLoading = false;
    }
  }

}
