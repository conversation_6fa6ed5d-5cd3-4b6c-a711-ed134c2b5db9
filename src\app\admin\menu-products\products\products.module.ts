import { NbIconModule, NbDatepickerModule, NbSelectModule, NbButtonModule, NbInputModule, NbCardModule, NbSpinnerModule, NbTooltipModule, NbToastrService, NbToastrModule, NbAutocompleteModule } from '@nebular/theme';
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { ProductsRoutingModule } from './products-routing.module';
import { ProductsComponent } from './products.component';
import { FormsModule } from '@angular/forms';



@NgModule({
  declarations: [
    ProductsComponent
  ],
  imports: [
    CommonModule,
    ProductsRoutingModule,
    NbIconModule, 
    NbDatepickerModule,
    NbSelectModule,
    NbButtonModule,
    FormsModule, 
    NbInputModule,
    NbCardModule,
    NbSpinnerModule,
    NbTooltipModule,
    NbToastrModule,
    NbAutocompleteModule

  ]
})
export class ProductsModule { }
