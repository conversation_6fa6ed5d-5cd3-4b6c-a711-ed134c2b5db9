import { CompanyCategory } from "../enums/Company-category.enum";
import { UserCategory } from "../enums/user-category.enum";
import { Address } from "../types";
import { Store } from "./store";
import { Company } from "./user";


export class GlobalShipping {
  _id?: string;
  startRef: Store;
  endRef: string;
  label: string;
  location?: string;
  amount: number;
  deliveryDate?: number;
  enable?: boolean;
  created_at?: number;
  address: Address;


}
export class CompanyShipping extends GlobalShipping {
  companyId: string;
  erpShipToId: string;
  erpShipToDesc: string;
  deliveryDate?: number;
  category: CompanyCategory;

  constructor() {
    super();
    this.companyId = '';
    this.erpShipToId = '';
    this.erpShipToDesc = '';
  }
}
export class ParticularShipping extends GlobalShipping {
  category: UserCategory;
  retrievePoint?: Partial<Company>;
  location?: string;
  deliveryDate?: number;


  constructor(category: number) {
    super();
    this.category = category;
    this.address = { region: "", city: '', district: '', commercialRegion: '' };
  }
}

export declare type Shipping = ParticularShipping | GlobalShipping | CompanyShipping;
