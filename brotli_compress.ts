const { readdirSync, readFileSync, writeFileSync } = require('fs');
const { compress } = require('brotli');
const { gzip } = require('node-gzip');

const brotliSettings = {
  extension: 'br',
  skipLarger: true,
  mode: 1, // 0 = generic, 1 = text, 2 = font (WOFF2)
  quality: 10, // 0 - 11,
  lgwin: 12, // default
  threshold: 10240
};

readdirSync('dist/cimencam-logistique-web/browser/').forEach(file => {
  if (file.endsWith('.js') || file.endsWith('.css') || file.endsWith('.html') || file.endsWith('.ttf') || file.endsWith('.txt')) {
    const result = compress(readFileSync('dist/cimencam-logistique-web/browser/' + file));
    writeFileSync('dist/cimencam-logistique-web/browser/' + file + '.br', result);
    gzip(readFileSync('dist/cimencam-logistique-web/browser/' + file)).then((resultGzip: any) => {
      writeFileSync('dist/cimencam-logistique-web/browser/' + file + '.gz', resultGzip);
    });
  }
});
